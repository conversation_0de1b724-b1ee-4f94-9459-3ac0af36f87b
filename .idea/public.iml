<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/wp-content/plugins/formello/includes" isTestSource="false" packagePrefix="Formello" />
      <sourceFolder url="file://$MODULE_DIR$/wp-content/plugins/getwid/includes" isTestSource="false" packagePrefix="Getwid" />
      <sourceFolder url="file://$MODULE_DIR$/wp-content/plugins/wp-graphql/src" isTestSource="false" packagePrefix="WPGraphQL" />
      <sourceFolder url="file://$MODULE_DIR$/wp-content/plugins/wp-whatsapp/includes" isTestSource="false" packagePrefix="NTA_WhatsApp" />
      <sourceFolder url="file://$MODULE_DIR$/wp-content/themes/tc-aluminum/inc" isTestSource="false" packagePrefix="TC_Aluminium" />
      <sourceFolder url="file://$MODULE_DIR$/wp-includes" isTestSource="false" packagePrefix="PHPMailer" />
      <sourceFolder url="file://$MODULE_DIR$/wp-includes/SimplePie/src" isTestSource="false" packagePrefix="SimplePie" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>