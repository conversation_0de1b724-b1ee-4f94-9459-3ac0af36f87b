# Translation of Plugins - All-in-One WP Migration - Stable (latest release) in English (UK)
# This file is distributed under the same license as the Plugins - All-in-One WP Migration - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-03-28 11:16:23+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.0\n"
"Language: en_GB\n"
"Project-Id-Version: Plugins - All-in-One WP Migration - Stable (latest release)\n"

#: lib/view/reset/index.php:39
msgid "Reset Hub Demo"
msgstr "Reset Hub Demo"

#: lib/view/reset/index.php:39
msgid "Upgrade to Premium "
msgstr "Upgrade to Premium"

#: lib/view/reset/index.php:37
msgid "Please note, the features displayed below are part of an image showcasing the potential of Reset Hub in its Premium version. To activate and enjoy these advanced features, <a href=\"https://servmask.com/products/unlimited-extension\" title=\"Upgrade to Premium\" target=\"_blank\">upgrade to Premium now</a>! Elevate your website management experience with these exclusive functionalities and priority support."
msgstr "Please note, the features displayed below are part of an image showcasing the potential of Reset Hub in its Premium version. To activate and enjoy these advanced features, <a href=\"https://servmask.com/products/unlimited-extension\" title=\"Upgrade to Premium\" target=\"_blank\">upgrade to Premium now</a>! Elevate your website management experience with these exclusive functionalities and priority support."

#: lib/view/reset/index.php:35
msgid "Experience Full Functionality with Premium!"
msgstr "Experience full functionality with Premium!"

#: lib/view/reset/index.php:34
msgid "Star"
msgstr "Star"

#: lib/controller/class-ai1wm-main-controller.php:674
#: lib/controller/class-ai1wm-main-controller.php:675
msgid "Reset Hub"
msgstr "Reset Hub"

#: lib/view/main/premium-badge.php:31
msgid "Premium"
msgstr "Premium"

#: lib/view/schedules/index.php:109
msgid "Leverage the simplicity of Dropbox for your backup needs. Direct your scheduled backups to be stored in Dropbox. It's secure, straightforward, and keeps your backups at your fingertips."
msgstr "Leverage the simplicity of Dropbox for your backup needs. Direct your scheduled backups to be stored in Dropbox. It's secure, straightforward, and keeps your backups at your fingertips."

#: lib/view/schedules/index.php:161
msgid "Tailor your backup schedules to fit the complexity of your WordPress Multisite. Choose to export the entire network or only a selection of subsites according to your requirements. Effortless management for even the most intricate site networks."
msgstr "Tailor your backup schedules to fit the complexity of your WordPress Multisite. Choose to export the entire network or only a selection of subsites according to your requirements. Effortless management for even the most intricate site networks."

#: lib/view/schedules/index.php:148
msgid "We've got you covered with an array of supported storage providers. Whether you prefer Box, Amazon S3, WebDav or something else, you can choose the one that fits your needs best. Secure your backups exactly where you want them."
msgstr "We've got you covered with an array of supported storage providers. Whether you prefer Box, Amazon S3, WebDAV, or something else, you can choose the one that fits your needs best. Secure your backups exactly where you want them."

#: lib/view/schedules/index.php:135
msgid "Enjoy the flexibility of FTP storage. Direct your scheduled backups to your own FTP server. You'll have full control over your data, providing you with a versatile and private storage solution."
msgstr "Enjoy the flexibility of FTP storage. Direct your scheduled backups to your own FTP server. You'll have full control over your data, providing you with a versatile and private storage solution."

#: lib/view/schedules/index.php:122
msgid "Harness the power of OneDrive for your backups. Set up your scheduled backups to be saved directly in your OneDrive. It's secure, integrated with your Microsoft account, and keeps your data readily accessible."
msgstr "Harness the power of OneDrive for your backups. Set up your scheduled backups to be saved directly in your OneDrive. It's secure, integrated with your Microsoft account, and keeps your data readily accessible."

#: lib/view/schedules/index.php:96
msgid "Benefit from the robustness of Google Drive. Schedule your backups to be saved directly to your Google Drive account. Simple, secure, and integrated into a platform you already use."
msgstr "Benefit from the robustness of Google Drive. Schedule your backups to be saved directly to your Google Drive account. Simple, secure, and integrated into a platform you already use."

#: lib/view/schedules/index.php:83
msgid "Manage your storage effectively with our flexible retention settings. Decide how many backups you want to keep at a time. Old backups are automatically cleared, keeping your storage neat and efficient."
msgstr "Manage your storage effectively with our flexible retention settings. Decide how many backups you want to keep at a time. Old backups are automatically cleared, keeping your storage neat and efficient."

#: lib/view/schedules/index.php:75 lib/view/schedules/index.php:80
msgid "Retention settings"
msgstr "Retention settings"

#: lib/view/schedules/index.php:70
msgid "Stay informed, not overwhelmed. Tailor your notification preferences to get updates that matter to you. Whether it's the status of each backup, or just critical alerts, control what you want to be notified about."
msgstr "Stay informed, not overwhelmed. Tailor your notification preferences to get updates that matter to you. Whether it's the status of each backup, or just critical alerts, control about what you want to be notified."

#: lib/view/schedules/index.php:62 lib/view/schedules/index.php:67
msgid "Notification settings"
msgstr "Notification settings"

#: lib/view/schedules/index.php:57
msgid "Never worry about forgetting to back up your site again. Choose from various scheduling options, from daily to monthly, and we'll automate the rest. Backups happen like clockwork, giving you peace of mind and a solid safety net"
msgstr "Never worry about forgetting to back up your site again. Choose from various scheduling options, from daily to monthly, and we'll automate the rest. Backups happen like clockwork, giving you peace of mind and a solid safety net"

#: lib/view/schedules/index.php:54 lib/view/schedules/index.php:67
#: lib/view/schedules/index.php:80 lib/view/schedules/index.php:93
#: lib/view/schedules/index.php:106 lib/view/schedules/index.php:119
#: lib/view/schedules/index.php:132 lib/view/schedules/index.php:145
#: lib/view/schedules/index.php:158
msgid "Enable this feature"
msgstr "Enable this feature"

#: lib/view/schedules/index.php:49
msgid "Backup scheduler"
msgstr "Backup scheduler"

#: lib/view/schedules/index.php:43 lib/view/schedules/index.php:153
#: lib/view/schedules/index.php:158
msgid "Multisite Schedules"
msgstr "Multisite Schedules"

#: lib/view/schedules/index.php:42 lib/view/schedules/index.php:140
#: lib/view/schedules/index.php:145
msgid "More Storage Providers"
msgstr "More Storage Providers"

#: lib/view/schedules/index.php:41 lib/view/schedules/index.php:127
#: lib/view/schedules/index.php:132
msgid "FTP Storage"
msgstr "FTP Storage"

#: lib/view/schedules/index.php:40 lib/view/schedules/index.php:114
#: lib/view/schedules/index.php:119
msgid "OneDrive Storage"
msgstr "OneDrive Storage"

#: lib/view/schedules/index.php:39 lib/view/schedules/index.php:101
#: lib/view/schedules/index.php:106
msgid "Dropbox Storage"
msgstr "Dropbox Storage"

#: lib/view/schedules/index.php:38 lib/view/schedules/index.php:88
#: lib/view/schedules/index.php:93
msgid "Google Drive Storage"
msgstr "Google Drive Storage"

#: lib/view/schedules/index.php:37
msgid "Retention Settings"
msgstr "Retention Settings"

#: lib/view/schedules/index.php:36
msgid "Notification Settings"
msgstr "Notification Settings"

#: lib/view/schedules/index.php:35 lib/view/schedules/index.php:54
msgid "Backup Scheduler"
msgstr "Backup Scheduler"

#: lib/controller/class-ai1wm-main-controller.php:683
#: lib/controller/class-ai1wm-main-controller.php:684
msgid "Schedules"
msgstr "Schedules"

#: lib/controller/class-ai1wm-main-controller.php:948
msgid "<a href=\"https://servmask.com/products/unlimited-extension\" target=\"_blank\">Get unlimited</a>"
msgstr "<a href=\"https://servmask.com/products/unlimited-extension\" target=\"_blank\">Get unlimited</a>"

#: functions.php:68 functions.php:110
msgid "Your archive file name contains invalid characters. It cannot contain: < > : \" | ? * \\0. <a href=\"https://help.servmask.com/knowledgebase/invalid-archive-name/\" target=\"_blank\">Technical details</a>"
msgstr "Your archive file name contains invalid characters. It cannot contain: < > : \" | ? * \\0. <a href=\"https://help.servmask.com/knowledgebase/invalid-archive-name/\" target=\"_blank\">Technical details</a>"

#: functions.php:43
msgid "Your storage directory name contains invalid characters. It cannot contain: < > : \" | ? * \\0. <a href=\"https://help.servmask.com/knowledgebase/invalid-storage-name/\" target=\"_blank\">Technical details</a>"
msgstr "Your storage directory name contains invalid characters. It cannot contain: < > : \" | ? * \\0. <a href=\"https://help.servmask.com/knowledgebase/invalid-storage-name/\" target=\"_blank\">Technical details</a>"

#: lib/model/import/class-ai1wm-import-check-encryption.php:59
msgid "Backup is encrypted. Please provide decryption password: "
msgstr "Backup is encrypted. Please provide decryption password: "

#: lib/view/export/advanced-settings.php:63
msgid "Password-protect and encrypt backups"
msgstr "Password-protect and encrypt backups"

#: lib/view/export/advanced-settings.php:50
msgid "A password is required"
msgstr "A password is required"

#: lib/view/export/advanced-settings.php:43
msgid "Protect this backup with a password"
msgstr "Protect this backup with a password"

#: lib/model/import/class-ai1wm-import-users.php:64
msgid "Done preparing users."
msgstr "Done preparing users."

#: lib/model/import/class-ai1wm-import-users.php:38
msgid "Preparing users..."
msgstr "Preparing users..."

#: lib/model/import/class-ai1wm-import-check-encryption.php:48
msgid "Importing an encrypted backup is not supported on this server. <a href=\"https://help.servmask.com/knowledgebase/unable-to-encrypt-and-decrypt-backups/\" target=\"_blank\">Technical details</a>"
msgstr "Importing an encrypted backup is not supported on this server. <a href=\"https://help.servmask.com/knowledgebase/unable-to-encrypt-and-decrypt-backups/\" target=\"_blank\">Technical details</a>"

#: lib/model/import/class-ai1wm-import-check-decryption-password.php:59
msgid "The decryption password is not valid."
msgstr "The decryption password is not valid."

#: lib/model/import/class-ai1wm-import-check-decryption-password.php:52
msgid "Done validating the decryption password."
msgstr "Done validating the decryption password."

#: lib/controller/class-ai1wm-main-controller.php:937
#: lib/controller/class-ai1wm-main-controller.php:1144
#: lib/view/export/advanced-settings.php:55
msgid "The passwords do not match"
msgstr "The passwords do not match"

#: lib/controller/class-ai1wm-main-controller.php:936
#: lib/controller/class-ai1wm-main-controller.php:1143
#: lib/view/export/advanced-settings.php:53
msgid "Repeat the password"
msgstr "Repeat the password"

#: lib/controller/class-ai1wm-main-controller.php:934
#: lib/controller/class-ai1wm-main-controller.php:1141
msgid "Submit"
msgstr "Submit"

#: lib/controller/class-ai1wm-main-controller.php:933
#: lib/controller/class-ai1wm-main-controller.php:1140
msgid "Please enter a password to import the file"
msgstr "Please enter a password to import the file"

#: lib/controller/class-ai1wm-main-controller.php:932
#: lib/controller/class-ai1wm-main-controller.php:1139
msgid "The backup is encrypted"
msgstr "The backup is encrypted"

#: lib/controller/class-ai1wm-main-controller.php:917
msgid "Unable to check decryption password. Refresh the page and try again"
msgstr "Unable to check decryption password. Refresh the page and try again"

#: functions.php:2116
msgid "Unable to decrypt data."
msgstr "Unable to decrypt data."

#: functions.php:2094
msgid "Unable to obtain cipher length."
msgstr "Unable to obtain cipher length."

#: functions.php:2079
msgid "Unable to encrypt data."
msgstr "Unable to encrypt data."

#: functions.php:2074
msgid "Unable to generate random bytes."
msgstr "Unable to generate random bytes."

#: lib/view/backups/backups-list.php:122
msgid "List"
msgstr "List"

#: lib/view/backups/backups-list.php:120
msgid "Show backup content"
msgstr "Show backup content"

#: lib/view/backups/backups-list.php:113
msgid "Downloading is not possible because backups directory is not accessible."
msgstr "Downloading is not possible because the backups directory is not accessible."

#: lib/controller/class-ai1wm-main-controller.php:1138
msgid "Reading..."
msgstr "Reading..."

#: lib/controller/class-ai1wm-main-controller.php:1137
msgid "List the content of the backup"
msgstr "List the content of the backup"

#: lib/controller/class-ai1wm-main-controller.php:1136
msgid "Error while downloading file"
msgstr "Error while downloading file"

#: lib/controller/class-ai1wm-main-controller.php:1135
msgid "Error while reading backup content"
msgstr "Error while reading backup content"

#: lib/controller/class-ai1wm-main-controller.php:1134
msgid "Error"
msgstr "Error"

#: lib/controller/class-ai1wm-backups-controller.php:185
msgid "Unable to list backup content"
msgstr "Unable to list backup content"

#: lib/view/backups/backups-list.php:93
msgid "More"
msgstr "More"

#: lib/controller/class-ai1wm-main-controller.php:812
#: lib/controller/class-ai1wm-main-controller.php:1133
msgid "You have %d backups"
msgstr "You have %d backups"

#: lib/controller/class-ai1wm-main-controller.php:811
#: lib/controller/class-ai1wm-main-controller.php:1132
#: lib/view/main/backups.php:31
msgid "You have %d backup"
msgid_plural "You have %d backups"
msgstr[0] "You have %d backup"
msgstr[1] "You have %d backups"

#: lib/view/updater/error.php:31
msgid "Error: %s"
msgstr "Error: %s"

#: lib/model/export/class-ai1wm-export-themes.php:80
#: lib/model/export/class-ai1wm-export-themes.php:119
msgid "Archiving %d theme files...<br />%d%% complete"
msgstr "Archiving %d theme files...<br />%d%% complete"

#: lib/model/export/class-ai1wm-export-enumerate-themes.php:106
msgid "Done retrieving a list of WordPress theme files."
msgstr "Successfully retrieved a list of WordPress theme files."

#: lib/model/export/class-ai1wm-export-enumerate-themes.php:51
msgid "Retrieving a list of WordPress theme files..."
msgstr "Retrieving a list of WordPress theme files..."

#: lib/model/import/class-ai1wm-import-options.php:34
msgid "Preparing options..."
msgstr "Preparing options..."

#: lib/model/export/class-ai1wm-export-enumerate-plugins.php:100
msgid "Done retrieving a list of WordPress plugin files."
msgstr "Done retrieving a list of WordPress plugin files."

#: lib/model/export/class-ai1wm-export-enumerate-plugins.php:51
msgid "Retrieving a list of WordPress plugin files..."
msgstr "Retrieving a list of WordPress plugin files..."

#: lib/view/main/contact-support.php:31
msgid "Contact Support"
msgstr "Contact Support"

#: lib/model/import/class-ai1wm-import-validate.php:48
msgid "The file type that you have tried to import is not compatible with this plugin. Please ensure that your file is a <strong>.wpress</strong> file that was created with the All-in-One WP migration plugin. <a href=\"https://help.servmask.com/knowledgebase/invalid-backup-file/\" target=\"_blank\">Technical details</a>"
msgstr "The file type that you have tried to import is not compatible with this plugin. Please ensure that your file is a <strong>.wpress</strong> file that was created with the All-in-One WP migration plugin. <a href=\"https://help.servmask.com/knowledgebase/invalid-backup-file/\" target=\"_blank\">Technical details</a>"

#: lib/controller/class-ai1wm-export-controller.php:77
msgid "Unable to export. Error code: %s. %s"
msgstr "Unable to export. Error code: %s. %s"

#: lib/model/import/class-ai1wm-import-permalinks.php:42
msgid "Done getting WordPress permalinks settings."
msgstr "Finished getting WordPress permalinks settings."

#: lib/model/import/class-ai1wm-import-permalinks.php:36
msgid "Getting WordPress permalinks settings..."
msgstr "Getting WordPress permalinks settings..."

#: lib/view/import/avada.php:41
msgid "» <a class=\"ai1wm-no-underline\" href=\"https://wordpress.org/support/view/plugin-reviews/all-in-one-wp-migration?rate=5#postform\" target=\"_blank\">Optionally, review the plugin</a>. (opens a new window)"
msgstr "» <a class=\"ai1wm-no-underline\" href=\"https://wordpress.org/support/view/plugin-reviews/all-in-one-wp-migration?rate=5#postform\" target=\"_blank\">Optionally, review the plugin</a>. (opens a new window)"

#: lib/view/import/avada.php:40
msgid "» <a class=\"ai1wm-no-underline\" href=\"https://theme-fusion.com/documentation/avada/installation-maintenance/important-update-information/#clear-caches\" target=\"_blank\">Reset Avada Fusion Builder cache</a>. (opens a new window)<br />"
msgstr "» <a class=\"ai1wm-no-underline\" href=\"https://theme-fusion.com/documentation/avada/installation-maintenance/important-update-information/#clear-caches\" target=\"_blank\">Reset Avada Fusion Builder cache</a>. (opens a new window)<br />"

#: lib/view/import/avada.php:37
msgid "» <a class=\"ai1wm-no-underline\" href=\"https://oxygenbuilder.com/documentation/other/importing-exporting/#resigning\" target=\"_blank\">Re-sign Oxygen Builder shortcodes</a>. (opens a new window)<br />"
msgstr "» <a class=\"ai1wm-no-underline\" href=\"https://oxygenbuilder.com/documentation/other/importing-exporting/#resigning\" target=\"_blank\">Re-sign Oxygen Builder shortcodes</a>. (opens a new window)<br />"

#: lib/view/import/avada.php:31
msgid "» Permalinks are set to default. <a class=\"ai1wm-no-underline\" href=\"https://help.servmask.com/knowledgebase/permalinks-are-set-to-default/\" target=\"_blank\">Why?</a> (opens a new window)<br />"
msgstr "» Permalinks are set to default. <a class=\"ai1wm-no-underline\" href=\"https://help.servmask.com/knowledgebase/permalinks-are-set-to-default/\" target=\"_blank\">Why?</a> (opens a new window)<br />"

#: lib/view/import/avada.php:33
msgid "» <a class=\"ai1wm-no-underline\" href=\"%s\" target=\"_blank\">Save permalinks structure</a>. (opens a new window)<br />"
msgstr "» <a class=\"ai1wm-no-underline\" href=\"%s\" target=\"_blank\">Save permalinks structure</a>. (opens a new window)<br />"

#: lib/model/export/class-ai1wm-export-enumerate-tables.php:80
msgid "Done retrieving a list of WordPress database tables."
msgstr "Done retrieving a list of WordPress database tables."

#: lib/model/export/class-ai1wm-export-enumerate-tables.php:46
msgid "Retrieving a list of WordPress database tables..."
msgstr "Retrieving a list of WordPress database tables..."

#: lib/view/main/translate.php:31
msgid "Translate"
msgstr "Translate"

#: lib/view/import/done.php:31
msgid "» Permalinks are set to default. <a class=\"ai1wm-no-underline\" href=\"https://help.servmask.com/knowledgebase/permalinks-are-set-to-default/\" target=\"_blank\">Why?</a> (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://wordpress.org/support/view/plugin-reviews/all-in-one-wp-migration?rate=5#postform\" target=\"_blank\">Optionally, review the plugin</a>. (opens a new window)"
msgstr "» Permalinks are set to default. <a class=\"ai1wm-no-underline\" href=\"https://help.servmask.com/knowledgebase/permalinks-are-set-to-default/\" target=\"_blank\">Why?</a> (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://wordpress.org/support/view/plugin-reviews/all-in-one-wp-migration?rate=5#postform\" target=\"_blank\">Optionally, review the plugin</a>. (opens a new window)"

#: lib/view/import/oxygen.php:31
msgid "» Permalinks are set to default. <a class=\"ai1wm-no-underline\" href=\"https://help.servmask.com/knowledgebase/permalinks-are-set-to-default/\" target=\"_blank\">Why?</a> (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://oxygenbuilder.com/documentation/other/importing-exporting/#resigning\" target=\"_blank\">Re-sign Oxygen Builder shortcodes</a>. (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://wordpress.org/support/view/plugin-reviews/all-in-one-wp-migration?rate=5#postform\" target=\"_blank\">Optionally, review the plugin</a>. (opens a new window)"
msgstr "» Permalinks are set to default. <a class=\"ai1wm-no-underline\" href=\"https://help.servmask.com/knowledgebase/permalinks-are-set-to-default/\" target=\"_blank\">Why?</a> (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://oxygenbuilder.com/documentation/other/importing-exporting/#resigning\" target=\"_blank\">Re-sign Oxygen Builder shortcodes</a>. (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://wordpress.org/support/view/plugin-reviews/all-in-one-wp-migration?rate=5#postform\" target=\"_blank\">Optionally, review the plugin</a>. (opens a new window)"

#: lib/view/import/oxygen.php:39
msgid "» <a class=\"ai1wm-no-underline\" href=\"%s\" target=\"_blank\">Save permalinks structure</a>. (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://oxygenbuilder.com/documentation/other/importing-exporting/#resigning\" target=\"_blank\">Re-sign Oxygen Builder shortcodes</a>. (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://wordpress.org/support/view/plugin-reviews/all-in-one-wp-migration?rate=5#postform\" target=\"_blank\">Optionally, review the plugin</a>. (opens a new window)"
msgstr "» <a class=\"ai1wm-no-underline\" href=\"%s\" target=\"_blank\">Save permalinks structure</a>. (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://oxygenbuilder.com/documentation/other/importing-exporting/#resigning\" target=\"_blank\">Re-sign Oxygen Builder shortcodes</a>. (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://wordpress.org/support/view/plugin-reviews/all-in-one-wp-migration?rate=5#postform\" target=\"_blank\">Optionally, review the plugin</a>. (opens a new window)"

#: lib/model/export/class-ai1wm-export-media.php:80
#: lib/model/export/class-ai1wm-export-media.php:119
msgid "Archiving %d media files...<br />%d%% complete"
msgstr "Archiving %d media files…<br />%d%% complete"

#: lib/model/export/class-ai1wm-export-content.php:80
#: lib/model/export/class-ai1wm-export-content.php:119
msgid "Archiving %d content files...<br />%d%% complete"
msgstr "Archiving %d content files…<br />%d%% complete"

#: lib/model/export/class-ai1wm-export-enumerate-content.php:106
msgid "Done retrieving a list of WordPress content files."
msgstr "Done retrieving a list of WordPress content files."

#: lib/model/export/class-ai1wm-export-enumerate-content.php:51
msgid "Retrieving a list of WordPress content files..."
msgstr "Retrieving a list of WordPress content files…"

#: lib/model/export/class-ai1wm-export-enumerate-media.php:93
msgid "Done retrieving a list of WordPress media files."
msgstr "Done retrieving a list of WordPress media files."

#: lib/model/export/class-ai1wm-export-enumerate-media.php:51
msgid "Retrieving a list of WordPress media files..."
msgstr "Retrieving a list of WordPress media files…"

#: lib/controller/class-ai1wm-main-controller.php:968
#: lib/controller/class-ai1wm-main-controller.php:1127
msgid "There is not enough space available on the disk.<br />Free up %s of disk space."
msgstr "There is not enough space available on the disk.<br />Free up %s of disk space."

#: lib/controller/class-ai1wm-main-controller.php:925
#: lib/controller/class-ai1wm-main-controller.php:1117
msgid "I have enough disk space"
msgstr "I have enough disk space"

#: lib/model/import/class-ai1wm-import-options.php:75
msgid "Done preparing options."
msgstr "Done preparing options."

#: lib/model/class-ai1wm-compatibility.php:50
msgid "%s is not the latest version. You must update the plugin before you can use it. "
msgstr "%s is not the latest version. You must update the plugin before you can use it. "

#: lib/model/import/class-ai1wm-import-confirm.php:83
msgid "Your backup is from a PHP %s but the site that you are importing to is PHP %s. This could cause the import to fail. Technical details: https://help.servmask.com/knowledgebase/migrate-wordpress-from-php-5-to-php-7/"
msgstr "Your backup is from a PHP %s, but the site that you are importing to is PHP %s. This could cause the import to fail. Technical details: https://help.servmask.com/knowledgebase/migrate-wordpress-from-php-5-to-php-7/"

#: lib/view/backups/backups-list.php:46 lib/view/backups/index.php:49
msgid "Refreshing backup list..."
msgstr "Refreshing backup list..."

#: lib/view/import/import-permissions.php:34
msgid "<h3>Site could not be imported</h3><p>Please make sure that storage directory <strong>%s</strong> has read and write permissions.</p><p><a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a></p>"
msgstr "<h3>Site could not be imported</h3><p>Please make sure that storage directory <strong>%s</strong> has read and write permissions.</p><p><a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a></p>"

#: lib/view/export/export-permissions.php:34
msgid "<h3>Site could not be exported</h3><p>Please make sure that storage directory <strong>%s</strong> has read and write permissions.</p><p><a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a></p>"
msgstr "<h3>Site could not be exported</h3><p>Please make sure that storage directory <strong>%s</strong> has read and write permissions.</p><p><a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a></p>"

#: lib/view/backups/backups-permissions.php:34
msgid "<h3>Site could not be restored</h3><p>Please make sure that storage directory <strong>%s</strong> has read and write permissions.</p><p><a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a></p>"
msgstr "<h3>Site could not be restored</h3><p>Please make sure that storage directory <strong>%s</strong> has read and write permissions.</p><p><a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a></p>"

#: lib/view/main/missing-role-capability-notice.php:34
msgid "All-in-One WP Migration: Your current profile role does not have Export/Import capabilities enabled. <a href=\"https://help.servmask.com/knowledgebase/how-to-add-import-and-export-capabilities-to-wordpress-users/\" target=\"_blank\">Technical details</a>"
msgstr "All-in-One WP Migration: Your current profile role does not have Export/Import capabilities enabled. <a href=\"https://help.servmask.com/knowledgebase/how-to-add-import-and-export-capabilities-to-wordpress-users/\" target=\"_blank\">Technical details</a>"

#: lib/view/import/button-file.php:31
msgid "To choose a file please go inside the link and click on the browse button."
msgstr "To choose a file please go inside the link and click on the browse button."

#: lib/view/import/done.php:38
msgid "» <a class=\"ai1wm-no-underline\" href=\"%s\" target=\"_blank\">Save permalinks structure</a>. (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://wordpress.org/support/view/plugin-reviews/all-in-one-wp-migration?rate=5#postform\" target=\"_blank\">Optionally, review the plugin</a>. (opens a new window)"
msgstr "» <a class=\"ai1wm-no-underline\" href=\"%s\" target=\"_blank\">Save permalinks structure</a>. (opens a new window)<br />» <a class=\"ai1wm-no-underline\" href=\"https://wordpress.org/support/view/plugin-reviews/all-in-one-wp-migration?rate=5#postform\" target=\"_blank\">Optionally, review the plugin</a>. (opens a new window)"

#: lib/controller/class-ai1wm-main-controller.php:1126
msgid "\"Restore\" functionality is available in a <a href=\"https://servmask.com/products/unlimited-extension\" target=\"_blank\">paid extension</a>.<br />You could also download the backup and then use \"Import from file\"."
msgstr "\"Restore\" functionality is available in a <a href=\"https://servmask.com/products/unlimited-extension\" target=\"_blank\">paid extension</a>.<br />You could also download the backup and then use \"Import from file\"."

#: lib/controller/class-ai1wm-main-controller.php:922
#: lib/controller/class-ai1wm-main-controller.php:1113
msgid "Finish"
msgstr "Finish"

#: lib/view/backups/backups-list.php:64
msgid "Click to set a label for this backup"
msgstr "Click to set a label for this backup"

#: lib/model/export/class-ai1wm-export-download.php:64
msgid "<a href=\"%s\" class=\"ai1wm-button-green ai1wm-emphasize ai1wm-button-download\" title=\"%s\" download=\"%s\"><span>Download %s</span><em>Size: %s</em></a>"
msgstr "<a href=\"%s\" class=\"ai1wm-button-green ai1wm-emphasize ai1wm-button-download\" title=\"%s\" download=\"%s\"><span>Download %s</span><em>Size: %s</em></a>"

#: lib/controller/class-ai1wm-export-controller.php:92
msgid "Unable to export: %s"
msgstr "Unable to export: %s"

#: lib/controller/class-ai1wm-import-controller.php:94
msgid "Unable to import: %s"
msgstr "Unable to import: %s"

#: lib/controller/class-ai1wm-import-controller.php:77
#: lib/controller/class-ai1wm-import-controller.php:85
msgid "Unable to import. Error code: %s. %s"
msgstr "Unable to import. Error code: %s. %s"

#: lib/model/import/class-ai1wm-import-confirm.php:48
msgid "The import process will overwrite your website including the database, media, plugins, and themes. Are you sure to proceed?"
msgstr "The import process will overwrite your website including the database, media, plugins, and themes. Are you sure to proceed?"

#: lib/model/import/class-ai1wm-import-validate.php:115
msgid "Please make sure that your file was exported using <strong>All-in-One WP Migration</strong> plugin. <a href=\"https://help.servmask.com/knowledgebase/invalid-backup-file/\" target=\"_blank\">Technical details</a>"
msgstr "Please make sure that your file was exported using <strong>All-in-One WP Migration</strong> plugin. <a href=\"https://help.servmask.com/knowledgebase/invalid-backup-file/\" target=\"_blank\">Technical details</a>"

#: lib/model/import/class-ai1wm-import-validate.php:92
msgid "The archive file is corrupted. Follow <a href=\"https://help.servmask.com/knowledgebase/corrupted-archive/\" target=\"_blank\">this article</a> to resolve the problem."
msgstr "The archive file is corrupted. Follow <a href=\"https://help.servmask.com/knowledgebase/corrupted-archive/\" target=\"_blank\">this article</a> to resolve the problem."

#: lib/model/import/class-ai1wm-import-validate.php:37
msgid "Your PHP is 32-bit. In order to import your file, please change your PHP version to 64-bit and try again. <a href=\"https://help.servmask.com/knowledgebase/php-32bit/\" target=\"_blank\">Technical details</a>"
msgstr "Your PHP is 32-bit. In order to import your file, please change your PHP version to 64-bit and try again. <a href=\"https://help.servmask.com/knowledgebase/php-32bit/\" target=\"_blank\">Technical details</a>"

#: lib/model/import/class-ai1wm-import-confirm.php:89
msgid "<i class=\"ai1wm-import-info\">Your backup is from a PHP %s but the site that you are importing to is PHP %s. This could cause the import to fail. <a href=\"https://help.servmask.com/knowledgebase/migrate-wordpress-from-php-5-to-php-7/\" target=\"_blank\">Technical details</a></i>"
msgstr "<i class=\"ai1wm-import-info\">Your backup is from a PHP %s, but the site that you are importing to is PHP %s. This could cause the import to fail. <a href=\"https://help.servmask.com/knowledgebase/migrate-wordpress-from-php-5-to-php-7/\" target=\"_blank\">Technical details</a></i>"

#: lib/model/import/class-ai1wm-import-confirm.php:54
msgid "The import process will overwrite your website including the database, media, plugins, and themes. Please ensure that you have a backup of your data before proceeding to the next step."
msgstr "The import process will overwrite your website including the database, media, plugins, and themes. Please ensure that you have a backup of your data before proceeding to the next step."

#: lib/model/class-ai1wm-compatibility.php:52
msgid "<strong>%s</strong> is not the latest version. You must <a href=\"%s\">update the plugin</a> before you can use it. <br />"
msgstr "<strong>%s</strong> is not the latest version. You must <a href=\"%s\">update the plugin</a> before you can use it. <br />"

#: lib/controller/class-ai1wm-main-controller.php:960
msgid "The file that you are trying to import is over the maximum upload file size limit of <strong>%s</strong>.<br />You can remove this restriction by purchasing our <a href=\"https://servmask.com/products/unlimited-extension\" target=\"_blank\">Unlimited Extension</a>."
msgstr "The file that you are trying to import is over the maximum upload file size limit of <strong>%s</strong>.<br />You can remove this restriction by purchasing our <a href=\"https://servmask.com/products/unlimited-extension\" target=\"_blank\">Unlimited Extension</a>."

#: lib/controller/class-ai1wm-main-controller.php:953
#: lib/model/import/class-ai1wm-import-upload.php:55
msgid "The file type that you have tried to upload is not compatible with this plugin. Please ensure that your file is a <strong>.wpress</strong> file that was created with the All-in-One WP migration plugin. <a href=\"https://help.servmask.com/knowledgebase/invalid-backup-file/\" target=\"_blank\">Technical details</a>"
msgstr "The file type that you have tried to upload is not compatible with this plugin. Please ensure that your file is a <strong>.wpress</strong> file that was created with the All-in-One WP migration plugin. <a href=\"https://help.servmask.com/knowledgebase/invalid-backup-file/\" target=\"_blank\">Technical details</a>"

#: lib/view/main/multisite-notice.php:34
msgid "WordPress Multisite is supported via our All-in-One WP Migration Multisite Extension. You can get a copy of it here"
msgstr "WordPress Multisite is supported via our All-in-One WP Migration Multisite Extension. You can get a copy of it here"

#: lib/view/main/backups-htaccess-notice.php:35
#: lib/view/main/backups-index-html-notice.php:35
#: lib/view/main/backups-index-php-notice.php:35
#: lib/view/main/backups-robots-txt-notice.php:35
#: lib/view/main/backups-webconfig-notice.php:35
#: lib/view/main/storage-index-html-notice.php:35
#: lib/view/main/storage-index-php-notice.php:35
#: lib/view/main/wordpress-htaccess-notice.php:35
msgid "All-in-One WP Migration is not able to create <strong>%s</strong> file. Try to change permissions of the parent folder or send us an email at <a href=\"mailto:<EMAIL>\"><EMAIL></a> for assistance."
msgstr "All-in-One WP Migration is not able to create <strong>%s</strong> file. Try to change permissions of the parent folder or send us an email at <a href=\"mailto:<EMAIL>\"><EMAIL></a> for assistance."

#: lib/view/main/backups-path-notice.php:35
#: lib/view/main/storage-path-notice.php:35
msgid "All-in-One WP Migration is not able to create <strong>%s</strong> folder. You will need to create this folder and grant it read/write/execute permissions (0777) for the All-in-One WP Migration plugin to function properly."
msgstr "All-in-One WP Migration is not able to create <strong>%s</strong> folder. You will need to create this folder and grant it read/write/execute permissions (0777) for the All-in-One WP Migration plugin to function properly."

#: lib/view/import/pro.php:36
msgid "or"
msgstr "or"

#: lib/view/import/pro.php:35
msgid "How-to: Increase maximum upload file size"
msgstr "How-to: Increase maximum upload file size"

#: lib/view/import/pro.php:32
msgid "Maximum upload file size: <strong>%s</strong>."
msgstr "Maximum upload file size: <strong>%s</strong>."

#: lib/model/import/class-ai1wm-import-upload.php:79
msgid "The file is too large for this server."
msgstr "The file is too large for this server."

#: lib/controller/class-ai1wm-main-controller.php:944
msgid "<a href=\"https://help.servmask.com/2018/10/27/how-to-increase-maximum-upload-file-size-in-wordpress/\" target=\"_blank\">How-to: Increase maximum upload file size</a> or "
msgstr "<a href=\"https://help.servmask.com/2018/10/27/how-to-increase-maximum-upload-file-size-in-wordpress/\" target=\"_blank\">How-to: Increase maximum upload file size</a> or "

#: lib/controller/class-ai1wm-main-controller.php:939
msgid "Your file exceeds the maximum upload size for this site: <strong>%s</strong><br />%s%s"
msgstr "Your file exceeds the maximum upload size for this site: <strong>%s</strong><br />%s%s"

#: lib/view/import/import-buttons.php:39
msgid "Drag & Drop a backup to import it"
msgstr "Drag & Drop a backup to import it"

#: lib/model/export/class-ai1wm-export-database.php:77
#: lib/model/export/class-ai1wm-export-database.php:185
msgid "Exporting database...<br />%d%% complete<br />%s records saved"
msgstr "Exporting database...<br />%d%% complete<br />%s records saved"

#: lib/controller/class-ai1wm-main-controller.php:1125
msgid "Restoring a backup is available via Unlimited extension. <a href=\"https://servmask.com/products/unlimited-extension\" target=\"_blank\">Get it here</a>"
msgstr "Restoring a backup is available via Unlimited extension. <a href=\"https://servmask.com/products/unlimited-extension\" target=\"_blank\">Get it here</a>"

#: lib/controller/class-ai1wm-main-controller.php:271
msgid "All-in-One WP Migration Command"
msgstr "All-in-One WP Migration Command"

#: lib/controller/class-ai1wm-main-controller.php:799
#: lib/controller/class-ai1wm-main-controller.php:1097
#: lib/model/export/class-ai1wm-export-init.php:43
msgid "Preparing to export..."
msgstr "Preparing to export..."

#: lib/view/backups/backups-list.php:82
msgid "%s ago"
msgstr "%s ago"

#: lib/controller/class-ai1wm-main-controller.php:1264
msgid "Your purchase ID is invalid, please <a href=\"mailto:<EMAIL>\">contact us</a>"
msgstr "Your purchase ID is invalid, please <a href=\"mailto:<EMAIL>\">contact us</a>"

#: lib/controller/class-ai1wm-main-controller.php:1124
msgid "Are you sure you want to delete this file?"
msgstr "Are you sure you want to delete this file?"

#: lib/controller/class-ai1wm-main-controller.php:927
#: lib/controller/class-ai1wm-main-controller.php:1119
msgid "Please do not close this browser window or your import will fail"
msgstr "Please do not close this browser window or your import will fail"

#: lib/controller/class-ai1wm-main-controller.php:926
#: lib/controller/class-ai1wm-main-controller.php:1118
msgid "Continue"
msgstr "Continue"

#: lib/controller/class-ai1wm-main-controller.php:924
#: lib/controller/class-ai1wm-main-controller.php:1116
msgid "Proceed"
msgstr "Proceed"

#: lib/controller/class-ai1wm-main-controller.php:923
#: lib/controller/class-ai1wm-main-controller.php:1115
msgid "Stop import"
msgstr "Stop import"

#: lib/controller/class-ai1wm-main-controller.php:920
#: lib/controller/class-ai1wm-main-controller.php:1112
msgid "Please wait, stopping the import..."
msgstr "Please wait, stopping the import..."

#: lib/controller/class-ai1wm-main-controller.php:919
#: lib/controller/class-ai1wm-main-controller.php:1111
msgid "Unable to stop the import. Refresh the page and try again"
msgstr "Unable to stop the import. Refresh the page and try again"

#: lib/controller/class-ai1wm-main-controller.php:918
#: lib/controller/class-ai1wm-main-controller.php:1110
msgid "Unable to prepare blogs on import. Refresh the page and try again"
msgstr "Unable to prepare blogs on import. Refresh the page and try again"

#: lib/controller/class-ai1wm-main-controller.php:916
#: lib/controller/class-ai1wm-main-controller.php:1109
msgid "Unable to confirm the import. Refresh the page and try again"
msgstr "Unable to confirm the import. Refresh the page and try again"

#: lib/controller/class-ai1wm-main-controller.php:915
#: lib/controller/class-ai1wm-main-controller.php:1108
msgid "Unable to start the import. Refresh the page and try again"
msgstr "Unable to start the import. Refresh the page and try again"

#: lib/controller/class-ai1wm-main-controller.php:912
#: lib/controller/class-ai1wm-main-controller.php:1105
msgid "You are about to stop importing your website, are you sure?"
msgstr "You are about to stop importing your website, are you sure?"

#: lib/controller/class-ai1wm-main-controller.php:729
#: lib/controller/class-ai1wm-main-controller.php:810
#: lib/controller/class-ai1wm-main-controller.php:931
#: lib/controller/class-ai1wm-main-controller.php:1123
msgid "Thanks for submitting your request!"
msgstr "Thanks for submitting your request!"

#: lib/controller/class-ai1wm-main-controller.php:728
#: lib/controller/class-ai1wm-main-controller.php:809
#: lib/controller/class-ai1wm-main-controller.php:930
#: lib/controller/class-ai1wm-main-controller.php:1122
msgid "Thanks for submitting your feedback!"
msgstr "Thanks for submitting your feedback!"

#: lib/controller/class-ai1wm-main-controller.php:727
#: lib/controller/class-ai1wm-main-controller.php:808
#: lib/controller/class-ai1wm-main-controller.php:929
#: lib/controller/class-ai1wm-main-controller.php:1121
msgid "How may we help you?"
msgstr "How may we help you?"

#: lib/controller/class-ai1wm-main-controller.php:726
#: lib/controller/class-ai1wm-main-controller.php:807
#: lib/controller/class-ai1wm-main-controller.php:928
#: lib/controller/class-ai1wm-main-controller.php:1120
msgid "Leave plugin developers any feedback here"
msgstr "Leave plugin developers any feedback here"

#: lib/controller/class-ai1wm-main-controller.php:806
#: lib/controller/class-ai1wm-main-controller.php:1104
msgid "Stop export"
msgstr "Stop export"

#: lib/controller/class-ai1wm-main-controller.php:805
#: lib/controller/class-ai1wm-main-controller.php:921
#: lib/controller/class-ai1wm-main-controller.php:1103
#: lib/controller/class-ai1wm-main-controller.php:1114
msgid "Close"
msgstr "Close"

#: lib/controller/class-ai1wm-main-controller.php:804
#: lib/controller/class-ai1wm-main-controller.php:1102
msgid "Please wait, stopping the export..."
msgstr "Please wait, stopping the export..."

#: lib/controller/class-ai1wm-main-controller.php:803
#: lib/controller/class-ai1wm-main-controller.php:1101
msgid "Unable to stop the export. Refresh the page and try again"
msgstr "Unable to stop the export. Refresh the page and try again"

#: lib/controller/class-ai1wm-main-controller.php:802
#: lib/controller/class-ai1wm-main-controller.php:1100
msgid "Unable to run the export. Refresh the page and try again"
msgstr "Unable to run the export. Refresh the page and try again"

#: lib/controller/class-ai1wm-main-controller.php:801
#: lib/controller/class-ai1wm-main-controller.php:1099
msgid "Unable to start the export. Refresh the page and try again"
msgstr "Unable to start the export. Refresh the page and try again"

#: lib/controller/class-ai1wm-main-controller.php:798
#: lib/controller/class-ai1wm-main-controller.php:1096
msgid "You are about to stop exporting your website, are you sure?"
msgstr "You are about to stop exporting your website, are you sure?"

#: functions.php:1695
msgid "Unable to authenticate the secret key. <a href=\"https://help.servmask.com/knowledgebase/invalid-secret-key/\" target=\"_blank\">Technical details</a>"
msgstr "Unable to authenticate the secret key. <a href=\"https://help.servmask.com/knowledgebase/invalid-secret-key/\" target=\"_blank\">Technical details</a>"

#: functions.php:1561
msgid "Unable to get current pointer position of %s. <a href=\"https://help.servmask.com/knowledgebase/php-32bit/\" target=\"_blank\">Technical details</a>"
msgstr "Unable to get current pointer position of %s. <a href=\"https://help.servmask.com/knowledgebase/php-32bit/\" target=\"_blank\">Technical details</a>"

#: functions.php:1544
msgid "Unable to seek to offset %d on %s. <a href=\"https://help.servmask.com/knowledgebase/php-32bit/\" target=\"_blank\">Technical details</a>"
msgstr "Unable to seek to offset %d on %s. <a href=\"https://help.servmask.com/knowledgebase/php-32bit/\" target=\"_blank\">Technical details</a>"

#: functions.php:1522
msgid "Unable to read file: %s. <a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a>"
msgstr "Unable to read file: %s. <a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a>"

#: functions.php:1502
msgid "Out of disk space. Unable to write to: %s. <a href=\"https://help.servmask.com/knowledgebase/out-of-disk-space/\" target=\"_blank\">Technical details</a>"
msgstr "Out of disk space. Unable to write to: %s. <a href=\"https://help.servmask.com/knowledgebase/out-of-disk-space/\" target=\"_blank\">Technical details</a>"

#: functions.php:1496 functions.php:1580
msgid "Unable to write to: %s. <a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a>"
msgstr "Unable to write to: %s. <a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a>"

#: functions.php:1477
msgid "Unable to open %s with mode %s. <a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a>"
msgstr "Unable to open %s with mode %s. <a href=\"https://help.servmask.com/knowledgebase/invalid-file-permissions/\" target=\"_blank\">Technical details</a>"

#: functions.php:63 functions.php:105
msgid "Unable to locate archive path. <a href=\"https://help.servmask.com/knowledgebase/invalid-archive-path/\" target=\"_blank\">Technical details</a>"
msgstr "Unable to locate archive path. <a href=\"https://help.servmask.com/knowledgebase/invalid-archive-path/\" target=\"_blank\">Technical details</a>"

#: functions.php:38
msgid "Unable to locate storage path. <a href=\"https://help.servmask.com/knowledgebase/invalid-storage-path/\" target=\"_blank\">Technical details</a>"
msgstr "Unable to locate storage path. <a href=\"https://help.servmask.com/knowledgebase/invalid-storage-path/\" target=\"_blank\">Technical details</a>"

#: lib/view/common/leave-feedback.php:59
msgid "I agree that by filling in the contact form with my data, I authorize All-in-One WP Migration to use my <strong>email</strong> to reply to my requests for information. <a href=\"https://www.iubenda.com/privacy-policy/946881\" target=\"_blank\">Privacy policy</a>"
msgstr "I agree that by filling in the contact form with my data, I authorise All-in-One WP Migration to use my <strong>email</strong> to reply to my requests for information. <a href=\"https://www.iubenda.com/privacy-policy/946881\" target=\"_blank\">Privacy policy</a>"

#: lib/model/export/class-ai1wm-export-config.php:180
msgid "Done preparing configuration file."
msgstr "Done preparing configuration file."

#: lib/model/export/class-ai1wm-export-config.php:36
msgid "Preparing configuration file..."
msgstr "Preparing configuration file..."

#: lib/model/export/class-ai1wm-export-config-file.php:73
msgid "Done archiving configuration file."
msgstr "Done archiving configuration file."

#: lib/model/export/class-ai1wm-export-config-file.php:61
#: lib/model/export/class-ai1wm-export-config-file.php:96
msgid "Archiving configuration file...<br />%d%% complete"
msgstr "Archiving configuration file...<br />%d%% complete"

#: lib/view/backups/backups-list.php:88
msgid "2GB+"
msgstr "2GB+"

#: lib/model/import/class-ai1wm-import-validate.php:119
msgid "Done unpacking archive."
msgstr "Done unpacking archive."

#: lib/model/import/class-ai1wm-import-validate.php:82
#: lib/model/import/class-ai1wm-import-validate.php:139
msgid "Unpacking archive...<br />%d%% complete"
msgstr "Unpacking archive...<br />%d%% complete"

#: lib/model/export/class-ai1wm-export-database-file.php:78
msgid "Done archiving database."
msgstr "Done archiving database."

#: lib/model/export/class-ai1wm-export-database-file.php:66
#: lib/model/export/class-ai1wm-export-database-file.php:101
msgid "Archiving database...<br />%d%% complete"
msgstr "Archiving database...<br />%d%% complete"

#: lib/model/import/class-ai1wm-import-database.php:78
#: lib/model/import/class-ai1wm-import-database.php:1007
msgid "Restoring database...<br />%d%% complete"
msgstr "Restoring database...<br />%d%% complete"

#: lib/model/export/class-ai1wm-export-compatibility.php:35
#: lib/model/import/class-ai1wm-import-compatibility.php:35
msgid "Checking extensions compatibility..."
msgstr "Checking extensions compatibility..."

#: lib/model/import/class-ai1wm-import-database.php:987
msgid "Done restoring database."
msgstr "Done restoring database."

#: lib/controller/class-ai1wm-main-controller.php:913
#: lib/controller/class-ai1wm-main-controller.php:1106
msgid "Preparing to import..."
msgstr "Preparing to import..."

#: lib/controller/class-ai1wm-main-controller.php:1374
msgid "Monthly"
msgstr "Monthly"

#: lib/controller/class-ai1wm-main-controller.php:1370
msgid "Weekly"
msgstr "Weekly"

#: lib/view/export/advanced-settings.php:97
msgid "Do <strong>not</strong> export must-use plugins (files)"
msgstr "Do <strong>not</strong> export must-use plugins (files)"

#: lib/model/import/class-ai1wm-import-mu-plugins.php:61
msgid "Done activating mu-plugins."
msgstr "Done activating mu-plugins."

#: lib/model/import/class-ai1wm-import-mu-plugins.php:35
msgid "Activating mu-plugins..."
msgstr "Activating mu-plugins..."

#: lib/view/export/advanced-settings.php:121
msgid "Do <strong>not</strong> replace email domain (sql)"
msgstr "Do <strong>not</strong> replace email domain (sql)"

#: lib/model/import/class-ai1wm-import-upload.php:91
msgid "Unrecognized error %s during upload."
msgstr "Unrecognised error %s during upload."

#: lib/model/import/class-ai1wm-import-upload.php:88
msgid "A PHP extension stopped the file upload."
msgstr "A PHP extension stopped the file upload."

#: lib/model/import/class-ai1wm-import-upload.php:85
msgid "Failed to write file to disk."
msgstr "Failed to write file to disk."

#: lib/model/import/class-ai1wm-import-upload.php:82
msgid "Missing a temporary folder."
msgstr "Missing a temporary folder."

#: lib/model/import/class-ai1wm-import-upload.php:70
msgid "Unable to upload the file because %s"
msgstr "Unable to upload the file because %s"

#: lib/model/import/class-ai1wm-import-upload.php:42
msgid "Missing tmp_name in upload file."
msgstr "Missing tmp_name in upload file."

#: lib/model/import/class-ai1wm-import-upload.php:38
msgid "Missing error key in upload file."
msgstr "Missing error key in upload file."

#: lib/model/import/class-ai1wm-import-upload.php:34
msgid "Missing upload file."
msgstr "Missing upload file."

#: lib/model/import/class-ai1wm-import-blogs.php:150
msgid "Done preparing blogs."
msgstr "Done preparing blogs."

#: lib/model/import/class-ai1wm-import-blogs.php:140
msgid "Unable to import <strong>WordPress Network</strong> into WordPress <strong>Single</strong> site."
msgstr "Unable to import <strong>WordPress Network</strong> into WordPress <strong>Single</strong> site."

#: lib/model/import/class-ai1wm-import-blogs.php:137
msgid "At least <strong>one WordPress</strong> site should be presented in the archive."
msgstr "At least <strong>one WordPress</strong> site should be presented in the archive."

#: lib/model/import/class-ai1wm-import-blogs.php:134
msgid "The archive should contain <strong>Single WordPress</strong> site! Please revisit your export settings."
msgstr "The archive should contain <strong>Single WordPress</strong> site! Please revisit your export settings."

#: lib/model/import/class-ai1wm-import-blogs.php:35
msgid "Preparing blogs..."
msgstr "Preparing blogs..."

#: lib/model/class-ai1wm-feedback.php:78
msgid "Something went wrong: %s"
msgstr "Something went wrong: %s"

#: lib/model/class-ai1wm-feedback.php:61
msgid "Please accept feedback term conditions."
msgstr "Please accept feedback term conditions."

#: lib/model/class-ai1wm-feedback.php:56
msgid "Please enter comments in the text area."
msgstr "Please enter comments in the text area."

#: lib/model/class-ai1wm-feedback.php:46
msgid "Your email is not valid."
msgstr "Your email is not valid."

#: lib/model/class-ai1wm-feedback.php:51
msgid "Feedback type is not valid."
msgstr "Feedback type is invalid."

#: lib/view/updater/modal.php:55
msgid "There is an update available. To update, you must enter your"
msgstr "There is an update available. To update, you must enter your"

#: lib/view/updater/modal.php:48
msgid "Discard"
msgstr "Discard"

#: lib/view/updater/modal.php:46
msgid "Save"
msgstr "Save"

#: lib/view/updater/modal.php:42
msgid "here"
msgstr "here"

#: lib/view/updater/modal.php:41
msgid "Don't have a Purchase ID? You can find your Purchase ID"
msgstr "Don't have a Purchase ID? You can find your Purchase ID"

#: lib/view/updater/modal.php:37 lib/view/updater/modal.php:56
msgid "Purchase ID"
msgstr "Purchase ID"

#: lib/view/updater/modal.php:34
msgid "To update your plugin/extension to the latest version, please fill your Purchase ID below."
msgstr "To update your plugin/extension to the latest version, please fill your Purchase ID below."

#: lib/view/updater/modal.php:33
msgid "Enter your Purchase ID"
msgstr "Enter your Purchase ID"

#: lib/controller/class-ai1wm-main-controller.php:1263
#: lib/view/updater/check.php:31
msgid "Check for updates"
msgstr "Check for updates"

#. Author of the plugin
#: all-in-one-wp-migration.php
msgid "ServMask"
msgstr "ServMask"

#. Description of the plugin
#: all-in-one-wp-migration.php
msgid "Migration tool for all your blog data. Import or Export your blog content with a single click."
msgstr "Migration tool for all your blog data. Import or Export your blog content with a single click."

#. Plugin URI of the plugin
#. Author URI of the plugin
#: all-in-one-wp-migration.php
msgid "https://servmask.com/"
msgstr "https://servmask.com/"

#. Plugin Name of the plugin
#: all-in-one-wp-migration.php
msgid "All-in-One WP Migration"
msgstr "All-in-One WP Migration"

#: lib/view/import/index.php:37
msgid "Import Site"
msgstr "Import Site"

#: lib/view/import/pro.php:39
msgid "Get unlimited"
msgstr "Get unlimited"

#: lib/view/import/import-buttons.php:43
msgid "Import From"
msgstr "Import From"

#: lib/view/export/index.php:37
msgid "Export Site"
msgstr "Export Site"

#: lib/view/export/help-section.php:51
msgid "Once the file is successfully downloaded on your computer, you can import it to any of your WordPress sites."
msgstr "Once the file is successfully downloaded on your computer, you can import it to any of your WordPress sites."

#: lib/view/export/help-section.php:47
msgid "Press \"Export\" button and the site archive file will pop up in your browser."
msgstr "Press \"Export\" button and the site archive file will pop up in your browser."

#: lib/view/export/help-section.php:43
msgid "In the advanced settings section you can configure more precisely the way of exporting."
msgstr "In the advanced settings section you can configure more precisely the way of exporting."

#: lib/view/export/help-section.php:37
msgid "Quick hints"
msgstr "Quick hints"

#: lib/view/export/help-section.php:33
msgid "You can then use the import functionality provided by this plugin to import the zipped file onto any other WordPress sites that you have administrator access to."
msgstr "You can then use the import functionality provided by this plugin to import the zipped file onto any other WordPress sites that you have administrator access to."

#: lib/view/export/help-section.php:32
msgid "This screen allows you to export database, media files, themes and plugins as one zipped file."
msgstr "This screen allows you to export database, media files, themes and plugins as one zipped file."

#: lib/view/export/find-replace.php:52
msgid "Add"
msgstr "Add"

#: lib/view/export/find-replace.php:39
msgid "in the database"
msgstr "in the database"

#: lib/view/export/find-replace.php:38
msgid "<another-text>"
msgstr "<another-text>"

#: lib/view/export/find-replace.php:37 lib/view/export/find-replace.php:45
msgid "Replace with"
msgstr "Replace with"

#: lib/view/export/find-replace.php:36
msgid "<text>"
msgstr "<text>"

#: lib/view/export/find-replace.php:35 lib/view/export/find-replace.php:44
msgid "Find"
msgstr "Find"

#: lib/view/export/export-buttons.php:35
msgid "Export To"
msgstr "Export To"

#: lib/view/export/button-file.php:31 lib/view/import/button-file.php:32
msgid "File"
msgstr "File"

#: lib/view/export/advanced-settings.php:115
msgid "Do <strong>not</strong> export database (sql)"
msgstr "Do <strong>not</strong> export database (sql)"

#: lib/view/export/advanced-settings.php:104
msgid "Do <strong>not</strong> export plugins (files)"
msgstr "Do <strong>not</strong> export plugins (files)"

#: lib/view/export/advanced-settings.php:88
msgid "Do <strong>not</strong> export themes (files)"
msgstr "Do <strong>not</strong> export themes (files)"

#: lib/view/export/advanced-settings.php:82
msgid "Do <strong>not</strong> export media library (files)"
msgstr "Do <strong>not</strong> export media library (files)"

#: lib/view/export/advanced-settings.php:76
msgid "Do <strong>not</strong> export post revisions"
msgstr "Do <strong>not</strong> export post revisions"

#: lib/view/export/advanced-settings.php:70
msgid "Do <strong>not</strong> export spam comments"
msgstr "Do <strong>not</strong> export spam comments"

#: lib/view/export/advanced-settings.php:36
msgid "(click to expand)"
msgstr "(click to expand)"

#: lib/view/export/advanced-settings.php:35
msgid "Advanced options"
msgstr "Advanced options"

#: lib/view/common/share-buttons.php:63
msgid "Tweet"
msgstr "Tweet"

#: lib/view/common/leave-feedback.php:64
msgid "Cancel"
msgstr "Cancel"

#: lib/view/common/leave-feedback.php:67
msgid "Send"
msgstr "Send"

#: lib/view/common/leave-feedback.php:54
msgid "Leave plugin developers any feedback here.."
msgstr "Leave plugin developers any feedback here.."

#: lib/view/common/leave-feedback.php:51
msgid "Enter your email address.."
msgstr "Enter your email address.."

#: lib/view/common/leave-feedback.php:44
msgid "I need help with this plugin"
msgstr "I need help with this plugin"

#: lib/view/common/leave-feedback.php:37
msgid "I have ideas to improve this plugin"
msgstr "I have ideas to improve this plugin"

#: lib/controller/class-ai1wm-main-controller.php:935
#: lib/controller/class-ai1wm-main-controller.php:1142
#: lib/view/export/advanced-settings.php:48
msgid "Enter a password"
msgstr "Enter a password"

#: lib/view/common/sidebar-right.php:39
msgid "Leave Feedback"
msgstr "Leave Feedback"

#: lib/view/backups/index.php:57
msgid "Create backup"
msgstr "Create backup"

#: lib/view/backups/index.php:52
msgid "There are no backups available at this time, why not create a new one?"
msgstr "There are no backups available at this time, why not create a new one?"

#: lib/view/backups/backups-list.php:127 lib/view/backups/backups-list.php:129
msgid "Delete"
msgstr "Delete"

#: lib/view/backups/backups-list.php:99 lib/view/backups/backups-list.php:101
msgid "Restore"
msgstr "Restore"

#: lib/view/backups/backups-list.php:106 lib/view/backups/backups-list.php:108
#: lib/view/backups/backups-list.php:115
msgid "Download"
msgstr "Download"

#: lib/view/backups/backups-list.php:38
msgid "Size"
msgstr "Size"

#: lib/view/backups/backups-list.php:37
msgid "Date"
msgstr "Date"

#: lib/view/backups/backups-list.php:36
msgid "Name"
msgstr "Name"

#: lib/model/import/class-ai1wm-import-done.php:363
#: lib/model/import/class-ai1wm-import-done.php:365
#: lib/model/import/class-ai1wm-import-done.php:367
msgid "Your site has been imported successfully!"
msgstr "Your site has been imported successfully!"

#: lib/model/import/class-ai1wm-import-content.php:83
#: lib/model/import/class-ai1wm-import-content.php:206
msgid "Restoring %d files...<br />%d%% complete"
msgstr "Restoring %d files...<br />%d%% complete"

#: lib/model/export/class-ai1wm-export-download.php:35
msgid "Renaming exported file..."
msgstr "Renaming exported file..."

#: lib/model/export/class-ai1wm-export-database.php:159
msgid "Done exporting database."
msgstr "Done exporting database."

#: lib/model/export/class-ai1wm-export-plugins.php:80
#: lib/model/export/class-ai1wm-export-plugins.php:119
msgid "Archiving %d plugin files...<br />%d%% complete"
msgstr "Archiving %d plugin files...<br />%d%% complete"

#: lib/model/import/class-ai1wm-import-enumerate.php:50
msgid "Done retrieving a list of all WordPress files."
msgstr "Done retrieving a list of all WordPress files."

#: lib/model/import/class-ai1wm-import-enumerate.php:35
msgid "Retrieving a list of all WordPress files..."
msgstr "Retrieving a list of all WordPress files..."

#: lib/model/export/class-ai1wm-export-archive.php:42
msgid "Done creating an empty archive."
msgstr "Done creating an empty archive."

#: lib/model/export/class-ai1wm-export-archive.php:35
msgid "Creating an empty archive..."
msgstr "Creating an empty archive..."

#: lib/controller/class-ai1wm-main-controller.php:663
#: lib/controller/class-ai1wm-main-controller.php:664
#: lib/view/backups/index.php:37
msgid "Backups"
msgstr "Backups"

#: lib/controller/class-ai1wm-main-controller.php:653
#: lib/controller/class-ai1wm-main-controller.php:654
msgid "Import"
msgstr "Import"

#: lib/controller/class-ai1wm-main-controller.php:643
#: lib/controller/class-ai1wm-main-controller.php:644
msgid "Export"
msgstr "Export"

#: lib/view/main/multisite-notice.php:42
msgid "Get multisite"
msgstr "Get multisite"

#: lib/controller/class-ai1wm-import-controller.php:96
#: lib/controller/class-ai1wm-import-controller.php:97
#: lib/controller/class-ai1wm-main-controller.php:914
#: lib/controller/class-ai1wm-main-controller.php:1107
msgid "Unable to import"
msgstr "Unable to import"

#: lib/controller/class-ai1wm-export-controller.php:94
#: lib/controller/class-ai1wm-export-controller.php:95
#: lib/controller/class-ai1wm-main-controller.php:800
#: lib/controller/class-ai1wm-main-controller.php:1098
msgid "Unable to export"
msgstr "Unable to export"