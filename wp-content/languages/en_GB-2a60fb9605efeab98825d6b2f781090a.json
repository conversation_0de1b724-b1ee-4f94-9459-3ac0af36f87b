{"translation-revision-date": "2024-11-14 20:13:08+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "template\u0004%s (Copy)": ["%s (Copy)"], "field\u0004Edit %s": ["Edit %s"], "Determines the order of pages. Pages with the same order value are sorted alphabetically. Negative order values are supported.": ["Determines the order of pages. Pages with the same order value are sorted alphabetically. Negative order values are supported."], "Order updated.": ["Order updated."], "Determines the order of pages.": ["Determines the order of pages."], "verb\u0004View": ["View"], "Select item": ["Select item"], "View revisions (%s)": ["View revisions (%s)"], "patterns-export": ["patterns-export"], "action label\u0004Duplicate pattern": ["Duplicate pattern"], "action label\u0004Duplicate": ["Duplicate"], "Some errors occurred while permanently deleting the items: %s": ["Some errors occurred while permanently deleting the items: %s"], "An error occurred while permanently deleting the items: %s": ["An error occurred while permanently deleting the items: %s"], "An error occurred while permanently deleting the items.": ["An error occurred while permanently deleting the items."], "An error occurred while permanently deleting the item.": ["An error occurred while permanently deleting the item."], "The items were permanently deleted.": ["The items were permanently deleted."], "\"%s\" permanently deleted.": ["\"%s\" permanently deleted."], "Permanently delete": ["Permanently delete"], "Export as JSON": ["Export as JSON"], "An error occurred while duplicating the page.": ["An error occurred while duplicating the page."], "An error occurred while updating the order": ["An error occurred while updating the order"], "No title": ["No title"], "\"%s\" successfully created.": ["\"%s\" successfully created."], "Order": ["Order"], "Close": ["Close"], "Cancel": ["Cancel"], "Save": ["Save"], "Title": ["Title"]}}, "comment": {"reference": "wp-includes/js/dist/fields.js"}}