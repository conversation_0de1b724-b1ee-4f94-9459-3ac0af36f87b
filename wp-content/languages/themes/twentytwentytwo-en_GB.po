# Translation of Themes - Twenty Twenty-Two in English (UK)
# This file is distributed under the same license as the Themes - Twenty Twenty-Two package.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-06-04 23:57:03+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: en_GB\n"
"Project-Id-Version: Themes - Twenty Twenty-Two\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Built on a solidly designed foundation, Twenty Twenty-Two embraces the idea that everyone deserves a truly unique website. The theme’s subtle styles are inspired by the diversity and versatility of birds: its typography is lightweight yet strong, its color palette is drawn from nature, and its layout elements sit gently on the page. The true richness of Twenty Twenty-Two lies in its opportunity for customization. The theme is built to take advantage of the Site Editor features introduced in WordPress 5.9, which means that colors, typography, and the layout of every single page on your site can be customized to suit your vision. It also includes dozens of block patterns, opening the door to a wide range of professionally designed layouts in just a few clicks. Whether you’re building a single-page website, a blog, a business website, or a portfolio, Twenty Twenty-Two will help you create a site that is uniquely yours."
msgstr "Built on a solidly designed foundation, Twenty Twenty-Two embraces the idea that everyone deserves a truly unique website. The diversity and versatility of birds inspire the theme’s subtle styles: its typography is lightweight yet strong, its colour palette is drawn from nature, and its layout elements sit gently on the page. The true richness of Twenty Twenty-Two lies in its opportunity for customisation. The theme is built to take advantage of the Site Editor features introduced in WordPress 5.9, which means that colours, typography, and the layout of every single page on your site can be customised to suit your vision. It also includes dozens of block patterns, opening the door to a wide range of professionally designed layouts in just a few clicks. Whether you’re building a single-page website, a blog, a business website, or a portfolio, Twenty Twenty-Two will help you create a site that is uniquely yours."

#. Theme Name of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Two"
msgstr "Twenty Twenty-Two"

#: styles/swiss.json
msgctxt "Duotone name"
msgid "Default filter"
msgstr "Default filter"

#: styles/swiss.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: styles/swiss.json
msgctxt "Style variation name"
msgid "Swiss"
msgstr "Swiss"

#: styles/pink.json
msgctxt "Font family name"
msgid "IBM Plex Mono"
msgstr "IBM Plex Mono"

#: styles/pink.json
msgctxt "Font family name"
msgid "IBM Plex Sans"
msgstr "IBM Plex Sans"

#: styles/pink.json
msgctxt "Style variation name"
msgid "Pink"
msgstr "Pink"

#: styles/blue.json
msgctxt "Font family name"
msgid "DM Sans"
msgstr "DM Sans"

#: styles/blue.json
msgctxt "Style variation name"
msgid "Blue"
msgstr "Blue"

#: inc/patterns/hidden-404.php:14
msgid "Search"
msgstr "Search"

#: inc/patterns/hidden-404.php:14
msgctxt "label"
msgid "Search"
msgstr "Search"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Footer"

#: theme.json
msgctxt "Template part name"
msgid "Header (Dark, small)"
msgstr "Header (dark, small)"

#: theme.json
msgctxt "Template part name"
msgid "Header (Dark, large)"
msgstr "Header (dark, large)"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Header"

#: theme.json
msgctxt "Custom template name"
msgid "Page (No Separators)"
msgstr "Page (no separators)"

#: theme.json
msgctxt "Custom template name"
msgid "Single Post (No Separators)"
msgstr "Single post (no separators)"

#: theme.json
msgctxt "Custom template name"
msgid "Page (Large Header)"
msgstr "Page (large header)"

#: theme.json
msgctxt "Custom template name"
msgid "Blank"
msgstr "Blank"

#: theme.json
msgctxt "Duotone name"
msgid "Primary and tertiary"
msgstr "Primary and tertiary"

#: theme.json
msgctxt "Duotone name"
msgid "Primary and secondary"
msgstr "Primary and secondary"

#: theme.json
msgctxt "Duotone name"
msgid "Primary and background"
msgstr "Primary and background"

#: theme.json
msgctxt "Duotone name"
msgid "Foreground and tertiary"
msgstr "Foreground and tertiary"

#: theme.json
msgctxt "Duotone name"
msgid "Foreground and secondary"
msgstr "Foreground and secondary"

#: theme.json
msgctxt "Duotone name"
msgid "Foreground and background"
msgstr "Foreground and background"

#: theme.json
msgctxt "Gradient name"
msgid "Diagonal background to tertiary"
msgstr "Diagonal background to tertiary"

#: theme.json
msgctxt "Gradient name"
msgid "Diagonal tertiary to background"
msgstr "Diagonal tertiary to background"

#: theme.json
msgctxt "Gradient name"
msgid "Diagonal background to secondary"
msgstr "Diagonal background to secondary"

#: theme.json
msgctxt "Gradient name"
msgid "Diagonal secondary to background"
msgstr "Diagonal secondary to background"

#: theme.json
msgctxt "Gradient name"
msgid "Diagonal primary to foreground"
msgstr "Diagonal primary to foreground"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical tertiary to background"
msgstr "Vertical tertiary to background"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical secondary to background"
msgstr "Vertical secondary to background"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical secondary to tertiary"
msgstr "Vertical secondary to tertiary"

#: styles/blue.json styles/pink.json styles/swiss.json theme.json
msgctxt "Color name"
msgid "Tertiary"
msgstr "Tertiary"

#: styles/blue.json styles/pink.json styles/swiss.json theme.json
msgctxt "Color name"
msgid "Secondary"
msgstr "Secondary"

#: styles/blue.json styles/pink.json styles/swiss.json theme.json
msgctxt "Color name"
msgid "Primary"
msgstr "Primary"

#: styles/blue.json styles/pink.json styles/swiss.json theme.json
msgctxt "Color name"
msgid "Background"
msgstr "Background"

#: styles/blue.json styles/pink.json styles/swiss.json theme.json
msgctxt "Color name"
msgid "Foreground"
msgstr "Foreground"

#: theme.json
msgctxt "Font family name"
msgid "Source Serif Pro"
msgstr "Source Serif Pro"

#: theme.json
msgctxt "Font family name"
msgid "System Font"
msgstr "System font"

#: inc/patterns/query-text-grid.php:6
msgid "Text-based grid of posts"
msgstr "Text-based grid of posts"

#: inc/patterns/query-simple-blog.php:6
msgid "Simple blog posts"
msgstr "Simple blog posts"

#: inc/patterns/query-large-titles.php:6
msgid "Large post titles"
msgstr "Large post titles"

#: inc/patterns/query-irregular-grid.php:6
msgid "Irregular grid of posts"
msgstr "Irregular grid of posts"

#: inc/patterns/query-image-grid.php:6
msgid "Grid of image posts"
msgstr "Grid of image posts"

#: inc/patterns/query-grid.php:6
msgid "Grid of posts"
msgstr "Grid of posts"

#: inc/patterns/query-default.php:6
msgid "Default posts"
msgstr "Default posts"

#: inc/patterns/page-sidebar-poster.php:56
msgid "The Grand Theater<br>154 Eastern Avenue<br>Maryland NY, 12345"
msgstr "The Grand Theater<br>154 Eastern Avenue<br>Maryland NY, 12345"

#: inc/patterns/page-sidebar-poster.php:52
msgid "Location"
msgstr "Location"

#: inc/patterns/page-sidebar-poster.php:44
msgid "February, 12 2021"
msgstr "12 February 2021"

#: inc/patterns/page-sidebar-poster.php:40
msgid "Date"
msgstr "Date"

#: inc/patterns/page-sidebar-poster.php:14
msgid "<em>Flutter</em>, a collection of bird-related ephemera"
msgstr "<em>Flutter</em>, a collection of bird-related ephemera"

#: inc/patterns/page-sidebar-poster.php:6
msgid "Poster with right sidebar"
msgstr "Poster with right sidebar"

#: inc/patterns/page-sidebar-grid-posts.php:6
msgid "Grid of posts with left sidebar"
msgstr "Grid of posts with left sidebar"

#: inc/patterns/page-sidebar-blog-posts.php:6
msgid "Blog posts with left sidebar"
msgstr "Blog posts with left sidebar"

#: inc/patterns/page-sidebar-blog-posts-right.php:80
msgid "Tags"
msgstr "Tags"

#: inc/patterns/page-sidebar-blog-posts-right.php:6
msgid "Blog posts with right sidebar"
msgstr "Blog posts with right sidebar"

#: inc/patterns/page-layout-two-columns.php:58
msgid "POSTS"
msgstr "POSTS"

#: inc/patterns/page-layout-two-columns.php:21
msgid "WELCOME"
msgstr "WELCOME"

#: inc/patterns/page-layout-two-columns.php:10
msgid "<em>Goldfinch </em><br><em>&amp; Sparrow</em>"
msgstr "<em>Goldfinch </em><br><em>and sparrow</em>"

#: inc/patterns/page-layout-two-columns.php:6
msgid "Page layout with two columns"
msgstr "Page layout with two columns"

#: inc/patterns/page-layout-image-text-and-video.php:53
msgid "Oh hello. My name’s Angelo, and you’ve found your way to my blog. I write about a range of topics, but lately I’ve been sharing my hopes for next year."
msgstr "Oh, hello. My name’s Angelo, and you’ve found your way to my blog. I write about a range of topics, but lately I’ve been sharing my hopes for next year."

#: inc/patterns/page-layout-image-text-and-video.php:42
msgid "An illustration of a bird in flight"
msgstr "An illustration of a bird in flight"

#: inc/patterns/page-layout-image-text-and-video.php:21
msgid "Screening"
msgstr "Screening"

#: inc/patterns/page-layout-image-text-and-video.php:6
msgid "Page layout with image, text and video"
msgstr "Page layout with image, text, and video"

#: inc/patterns/page-layout-image-and-text.php:27
#: inc/patterns/page-layout-two-columns.php:36
msgid "Oh hello. My name’s Angelo, and I operate this blog. I was born in Portland, but I currently live in upstate New York. You may recognize me from publications with names like <a href=\"#\">Eagle Beagle</a> and <a href=\"#\">Mourning Dive</a>. I write for a living.<br><br>I usually use this blog to catalog extensive lists of birds and other things that I find interesting. If you find an error with one of my lists, please keep it to yourself.<br><br>If that’s not your cup of tea, <a href=\"#\">I definitely recommend this tea</a>. It’s my favorite."
msgstr "Oh, hello. My name’s Angelo, and I operate this blog. I was born in Portland, but I currently live in upstate New York. You may recognise me from publications with names like <a href=\"#\">Eagle Beagle</a> and <a href=\"#\">Mourning Dive</a>. I write for a living.<br><br>I usually use this blog to catalogue extensive lists of birds and other things that I find interesting. If you find an error with one of my lists, please keep it to yourself.<br><br>If that’s not your cup of tea, <a href=\"#\">I definitely recommend this tea</a>. It’s my favourite."

#: inc/patterns/page-layout-image-and-text.php:15
msgctxt "Short for to be determined"
msgid "TBD"
msgstr "TBD"

#: inc/patterns/page-layout-image-and-text.php:10
msgid "<em>Watching Birds </em><br><em>in the Garden</em>"
msgstr "<em>Watching birds </em><br><em>in the garden</em>"

#: inc/patterns/page-layout-image-and-text.php:6
msgid "Page layout with image and text"
msgstr "Page layout with image and text"

#: inc/patterns/page-about-solid-color.php:22
msgid "Oh hello. My name’s Edvard, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show every Tuesday evening at 11PM EDT. Listen in sometime!"
msgstr "Oh, hello. My name’s Edvard, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show every Tuesday evening at 23:00 EDT. Listen in sometime!"

#: inc/patterns/page-about-solid-color.php:14
msgid "Edvard<br>Smith"
msgstr "Edvard<br>Smith"

#: inc/patterns/page-about-solid-color.php:6
msgid "About page on solid color background"
msgstr "About page on solid colour background"

#: inc/patterns/page-about-simple-dark.php:22
msgid "Oh hello. My name’s Jesús, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show on Tuesday evenings at 11PM EDT."
msgstr "Oh, hello. My name’s Jesús, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show on Tuesday evenings at 23:00 EDT."

#: inc/patterns/page-about-simple-dark.php:18
msgid "Jesús<br>Rodriguez"
msgstr "Jesús<br>Rodriguez"

#: inc/patterns/page-about-simple-dark.php:6
msgid "Simple dark about page"
msgstr "Simple dark about page"

#: inc/patterns/page-about-media-right.php:20
msgid "Oh hello. My name’s Emery, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show on Tuesday evenings at 11PM EDT."
msgstr "Oh, hello. My name’s Emery, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show on Tuesday evenings at 23:00 EDT."

#: inc/patterns/page-about-media-right.php:16
msgid "Emery<br>Driscoll"
msgstr "Emery<br>Driscoll"

#: inc/patterns/page-about-media-right.php:9
msgid "An image of a bird flying"
msgstr "An image of a bird flying"

#: inc/patterns/page-about-media-right.php:6
msgid "About page with media on the right"
msgstr "About page with media on the right"

#: inc/patterns/page-about-media-left.php:21
msgid "Oh hello. My name’s Doug, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show on Tuesday evenings at 11PM EDT."
msgstr "Oh, hello. My name’s Doug, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show on Tuesday evenings at 23:00 EDT."

#: inc/patterns/page-about-media-left.php:17
msgid "Stilton"
msgstr "Stilton"

#: inc/patterns/page-about-media-left.php:17
msgid "Doug"
msgstr "Doug"

#: inc/patterns/page-about-media-left.php:9
#: inc/patterns/page-sidebar-poster.php:26
msgid "Image of a bird on a branch"
msgstr "Image of a bird on a branch"

#: inc/patterns/page-about-media-left.php:6
msgid "About page with media on the left"
msgstr "About page with media on the left"

#: inc/patterns/page-about-links.php:21
msgid "A podcast about birds"
msgstr "A podcast about birds"

#: inc/patterns/page-about-links.php:17
msgid "Swoop"
msgstr "Swoop"

#: inc/patterns/page-about-links.php:6
msgid "About page links"
msgstr "About page links"

#: inc/patterns/page-about-links-dark.php:42
#: inc/patterns/page-about-links.php:46
msgid "About the hosts"
msgstr "About the hosts"

#: inc/patterns/page-about-links-dark.php:38
#: inc/patterns/page-about-links.php:42
msgid "Support the show"
msgstr "Support the show"

#: inc/patterns/page-about-links-dark.php:34
#: inc/patterns/page-about-links.php:38
msgid "Listen on Spotify"
msgstr "Listen on Spotify"

#: inc/patterns/page-about-links-dark.php:30
#: inc/patterns/page-about-links.php:34
msgid "Listen on iTunes Podcasts"
msgstr "Listen on iTunes Podcasts"

#: inc/patterns/page-about-links-dark.php:26
#: inc/patterns/page-about-links.php:30
msgid "Watch our videos"
msgstr "Watch our videos"

#: inc/patterns/page-about-links-dark.php:17
msgid "A trouble of hummingbirds"
msgstr "A trouble of hummingbirds"

#: inc/patterns/page-about-links-dark.php:13
#: inc/patterns/page-about-links.php:10
msgid "Logo featuring a flying bird"
msgstr "Logo featuring a flying bird"

#: inc/patterns/page-about-links-dark.php:6
msgid "About page links (dark)"
msgstr "About page links (dark)"

#: inc/patterns/page-about-large-image-and-buttons.php:6
msgid "About page with large image and buttons"
msgstr "About page with large image and buttons"

#: inc/patterns/page-about-large-image-and-buttons.php:59
msgid "Join my mailing list"
msgstr "Join my mailing list"

#: inc/patterns/page-about-large-image-and-buttons.php:51
msgid "Learn about my process"
msgstr "Learn about my process"

#: inc/patterns/page-about-large-image-and-buttons.php:43
msgid "Read about me"
msgstr "Read about me"

#: inc/patterns/page-about-large-image-and-buttons.php:33
msgid "Take a class"
msgstr "Take a class"

#: inc/patterns/page-about-large-image-and-buttons.php:25
msgid "Support my studio"
msgstr "Support my studio"

#: inc/patterns/page-about-large-image-and-buttons.php:17
msgid "Purchase my work"
msgstr "Purchase my work"

#: inc/patterns/hidden-bird.php:9 inc/patterns/hidden-heading-and-bird.php:10
msgid "Heading and bird image"
msgstr "Heading and bird image"

#: inc/patterns/hidden-404.php:12
msgid "This page could not be found. Maybe try a search?"
msgstr "This page could not be found. Maybe try a search?"

#: inc/patterns/hidden-404.php:9
msgctxt "Error code for a webpage that is not found."
msgid "404"
msgstr "404"

#: inc/patterns/hidden-404.php:6
msgid "404 content"
msgstr "404 content"

#: inc/patterns/header-with-tagline.php:6
msgid "Header with tagline"
msgstr "Header with tagline"

#: inc/patterns/header-title-navigation-social.php:6
msgid "Title, navigation, and social links header"
msgstr "Title, navigation, and social links header"

#: inc/patterns/header-title-and-button.php:6
msgid "Title and button header"
msgstr "Title and button header"

#: inc/patterns/header-text-only-with-tagline-black-background.php:6
msgid "Text-only header with tagline and background"
msgstr "Text-only header with tagline and background"

#: inc/patterns/header-text-only-green-background.php:6
#: inc/patterns/header-text-only-salmon-background.php:6
msgid "Text-only header with background"
msgstr "Text-only header with background"

#: inc/patterns/header-stacked.php:6
msgid "Logo and navigation header"
msgstr "Logo and navigation header"

#: inc/patterns/header-small-dark.php:6
msgid "Small header with dark background"
msgstr "Small header with dark background"

#: inc/patterns/header-logo-navigation-social-black-background.php:6
msgid "Logo, navigation, and social links header with background"
msgstr "Logo, navigation, and social links header with background"

#: inc/patterns/header-logo-navigation-offset-tagline.php:6
msgid "Logo, navigation, and offset tagline Header"
msgstr "Logo, navigation, and offset tagline header"

#: inc/patterns/header-logo-navigation-gray-background.php:6
msgid "Logo and navigation header with background"
msgstr "Logo and navigation header with background"

#: inc/patterns/header-large-dark.php:24
#: inc/patterns/hidden-heading-and-bird.php:14
msgid "<em>The Hatchery</em>: a blog about my adventures in bird watching"
msgstr "<em>The Hatchery</em>: a blog about my adventures in bird watching"

#: inc/patterns/header-large-dark.php:6
msgid "Large header with dark background"
msgstr "Large header with dark background"

#: inc/patterns/header-image-background.php:11
msgid "Illustration of a flying bird"
msgstr "Illustration of a flying bird"

#: inc/patterns/header-image-background.php:6
msgid "Header with image background"
msgstr "Header with image background"

#: inc/patterns/header-image-background-overlay.php:6
msgid "Header with image background and overlay"
msgstr "Header with image background and overlay"

#: inc/patterns/header-default.php:6
msgid "Default header"
msgstr "Default header"

#: inc/patterns/header-centered-title-navigation-social.php:6
msgid "Centered header with navigation, social links, and background"
msgstr "Centred header with navigation, social links, and background"

#: inc/patterns/header-centered-logo.php:6
msgid "Header with centered logo"
msgstr "Header with centred logo"

#: inc/patterns/header-centered-logo-black-background.php:6
msgid "Header with centered logo and background"
msgstr "Header with centred logo and background"

#: inc/patterns/general-wide-image-intro-buttons.php:31
msgid "Learn More"
msgstr "Learn more"

#: inc/patterns/general-wide-image-intro-buttons.php:16
msgid "Welcome to<br>the Aviary"
msgstr "Welcome to<br>the aviary"

#: inc/patterns/general-wide-image-intro-buttons.php:6
msgid "Wide image with introduction and buttons"
msgstr "Wide image with introduction and buttons"

#: inc/patterns/general-video-trailer.php:16
#: inc/patterns/general-wide-image-intro-buttons.php:22
msgid "A film about hobbyist bird watchers, a catalog of different birds, paired with the noises they make. Each bird is listed by their scientific name so things seem more official."
msgstr "A film about hobbyist bird watchers, a catalogue of different birds, paired with the noises they make. Each bird is listed by their scientific name, so things seem more official."

#: inc/patterns/general-video-trailer.php:12
#: inc/patterns/page-layout-image-text-and-video.php:49
msgid "Extended Trailer"
msgstr "Extended trailer"

#: inc/patterns/general-video-trailer.php:6
msgid "Video trailer"
msgstr "Video trailer"

#: inc/patterns/general-video-header-details.php:41
msgid "Angelo Tso<br>Edward Stilton<br>Amy Jensen<br>Boston Bell<br>Shay Ford"
msgstr "Angelo Tso<br>Edward Stilton<br>Amy Jensen<br>Boston Bell<br>Shay Ford"

#: inc/patterns/general-video-header-details.php:35
msgid "Jesús Rodriguez<br>Doug Stilton<br>Emery Driscoll<br>Megan Perry<br>Rowan Price"
msgstr "Jesús Rodriguez<br>Doug Stilton<br>Emery Driscoll<br>Megan Perry<br>Rowan Price"

#: inc/patterns/general-video-header-details.php:29
msgid "Featuring"
msgstr "Featuring"

#: inc/patterns/general-video-header-details.php:11
#: inc/patterns/page-layout-image-text-and-video.php:11
msgid "<em>Warble</em>, a film about <br>hobbyist bird watchers."
msgstr "<em>Warble</em>, a film about <br>hobbyist bird watchers."

#: inc/patterns/general-video-header-details.php:6
msgid "Video with header and details"
msgstr "Video with header and details"

#: inc/patterns/general-two-images-text.php:42
#: inc/patterns/general-wide-image-intro-buttons.php:35
#: inc/patterns/page-layout-image-text-and-video.php:30
msgid "Buy Tickets"
msgstr "Buy tickets"

#: inc/patterns/general-two-images-text.php:29
#: inc/patterns/page-layout-image-text-and-video.php:25
msgid "May 14th, 2022 @ 7:00PM<br>The Vintagé Theater,<br>245 Arden Rd.<br>Gardenville, NH"
msgstr "14 May 2022 @ 19:00<br>The Vintagé Theater,<br>245 Arden Rd.<br>Gardenville, NH"

#: inc/patterns/general-two-images-text.php:25
msgid "SCREENING"
msgstr "SCREENING"

#: inc/patterns/general-two-images-text.php:17
#: inc/patterns/general-wide-image-intro-buttons.php:10
#: inc/patterns/header-large-dark.php:29 inc/patterns/header-small-dark.php:25
#: inc/patterns/hidden-bird.php:12 inc/patterns/hidden-heading-and-bird.php:19
msgid "Illustration of a bird flying."
msgstr "Illustration of a bird flying."

#: inc/patterns/general-two-images-text.php:11
msgid "Illustration of a bird sitting on a branch."
msgstr "Illustration of a bird sitting on a branch."

#: inc/patterns/general-two-images-text.php:6
msgid "Two images with text"
msgstr "Two images with text"

#: inc/patterns/general-subscribe.php:16
msgid "Join our mailing list"
msgstr "Join our mailing list"

#: inc/patterns/general-subscribe.php:11
msgid "Watch birds<br>from your inbox"
msgstr "Watch birds<br>from your inbox"

#: inc/patterns/general-subscribe.php:6
msgid "Subscribe callout"
msgstr "Subscribe callout"

#: inc/patterns/general-pricing-table.php:84
msgid "$150"
msgstr "£150"

#: inc/patterns/general-pricing-table.php:79
msgid "Play a leading role for our community by joining at the Falcon level. This level earns you a seat on our board, where you can help plan future birdwatching expeditions."
msgstr "Play a leading role for our community by joining at the Falcon level. This level earns you a seat on our board, where you can help plan future birdwatching expeditions."

#: inc/patterns/general-pricing-table.php:75
msgid "Falcon"
msgstr "Falcon"

#: inc/patterns/general-pricing-table.php:71
msgctxt "Third item in a numbered list."
msgid "3"
msgstr "3"

#: inc/patterns/general-pricing-table.php:56
msgid "$75"
msgstr "£75"

#: inc/patterns/general-pricing-table.php:51
msgid "Join at the Sparrow level and become a member of our flock! You’ll receive our newsletter, plus a bird pin that you can wear with pride when you’re out in nature."
msgstr "Join at the Sparrow level and become a member of our flock! You’ll receive our newsletter, plus a bird pin that you can wear with pride when you’re out in nature."

#: inc/patterns/general-pricing-table.php:47
msgid "Sparrow"
msgstr "Sparrow"

#: inc/patterns/general-pricing-table.php:43
msgctxt "Second item in a numbered list."
msgid "2"
msgstr "2"

#: inc/patterns/general-pricing-table.php:28
msgid "$25"
msgstr "£25"

#: inc/patterns/general-pricing-table.php:23
msgid "Help support our growing community by joining at the Pigeon level. Your support will help pay our writers, and you’ll get access to our exclusive newsletter."
msgstr "Help support our growing community by joining at the Pigeon level. Your support will help pay our writers, and you’ll get access to our exclusive newsletter."

#: inc/patterns/general-pricing-table.php:19
msgid "Pigeon"
msgstr "Pigeon"

#: inc/patterns/general-pricing-table.php:15
msgctxt "First item in a numbered list."
msgid "1"
msgstr "1"

#: inc/patterns/general-pricing-table.php:6
msgid "Pricing table"
msgstr "Pricing table"

#: inc/patterns/general-list-events.php:103
msgid "Emery Driscoll"
msgstr "Emery Driscoll"

#: inc/patterns/general-list-events.php:97
msgid "May 20th, 2022, 6 PM"
msgstr "20 May 2022, 18:00"

#: inc/patterns/general-list-events.php:79
msgid "Amy Jensen"
msgstr "Amy Jensen"

#: inc/patterns/general-list-events.php:73
msgid "May 18th, 2022, 7 PM"
msgstr "18 May 2022, 19:00"

#: inc/patterns/general-list-events.php:61
#: inc/patterns/general-list-events.php:109
msgid "The Swell Theater<br>120 River Rd.<br>Rainfall, NH"
msgstr "The Swell Theater<br>120 River Rd.<br>Rainfall, NH"

#: inc/patterns/general-list-events.php:55
msgid "Doug Stilton"
msgstr "Doug Stilton"

#: inc/patterns/general-list-events.php:49
msgid "May 16th, 2022, 6 PM"
msgstr "16 May 2022, 18:00"

#: inc/patterns/general-list-events.php:37
#: inc/patterns/general-list-events.php:85
msgid "The Vintagé Theater<br>245 Arden Rd.<br>Gardenville, NH"
msgstr "The Vintagé Theater<br>245 Arden Rd.<br>Gardenville, NH"

#: inc/patterns/general-list-events.php:31
msgid "Jesús Rodriguez"
msgstr "Jesús Rodriguez"

#: inc/patterns/general-list-events.php:25
msgid "May 14th, 2022, 6 PM"
msgstr "14 May 2022, 18:00"

#: inc/patterns/general-list-events.php:11
msgid "Speaker Series"
msgstr "Speaker series"

#: inc/patterns/general-list-events.php:6
msgid "List of events"
msgstr "List of events"

#: inc/patterns/general-layered-images-with-duotone.php:10
#: inc/patterns/page-sidebar-blog-posts-right.php:58
msgid "Illustration of a flying bird."
msgstr "Illustration of a flying bird."

#: inc/patterns/general-layered-images-with-duotone.php:9
#: inc/patterns/header-image-background-overlay.php:11
msgid "Painting of ducks in the water."
msgstr "Painting of ducks in the water."

#: inc/patterns/general-layered-images-with-duotone.php:6
msgid "Layered images with duotone"
msgstr "Layered images with duotone"

#: inc/patterns/general-large-list-names.php:30
msgid "Read more"
msgstr "Read more"

#: inc/patterns/general-large-list-names.php:21
msgid "Jesús Rodriguez, Doug Stilton, Emery Driscoll, Megan Perry, Rowan Price, Angelo Tso, Edward Stilton, Amy Jensen, Boston Bell, Shay Ford, Lee Cunningham, Evelynn Ray, Landen Reese, Ewan Hart, Jenna Chan, Phoenix Murray, Mel Saunders, Aldo Davidson, Zain Hall."
msgstr "Jesús Rodriguez, Doug Stilton, Emery Driscoll, Megan Perry, Rowan Price, Angelo Tso, Edward Stilton, Amy Jensen, Boston Bell, Shay Ford, Lee Cunningham, Evelynn Ray, Landen Reese, Ewan Hart, Jenna Chan, Phoenix Murray, Mel Saunders, Aldo Davidson, Zain Hall."

#: inc/patterns/general-large-list-names.php:11
#: inc/patterns/page-sidebar-poster.php:32
msgid "An icon representing binoculars."
msgstr "An icon representing binoculars."

#: inc/patterns/general-large-list-names.php:6
msgid "Large list of names"
msgstr "Large list of names"

#: inc/patterns/general-image-with-caption.php:15
msgid "A beautiful bird featuring a surprising set of color feathers."
msgstr "A beautiful bird featuring a surprising set of coloured feathers."

#: inc/patterns/general-image-with-caption.php:11
msgid "Hummingbird"
msgstr "Hummingbird"

#: inc/patterns/general-image-with-caption.php:10
msgid "Hummingbird illustration"
msgstr "Hummingbird illustration"

#: inc/patterns/general-image-with-caption.php:6
msgid "Image with caption"
msgstr "Image with caption"

#: inc/patterns/general-featured-posts.php:6
msgid "Featured posts"
msgstr "Featured posts"

#: inc/patterns/general-divider-light.php:6
msgid "Divider with image and color (light)"
msgstr "Divider with image and colour (light)"

#: inc/patterns/general-divider-dark.php:6
msgid "Divider with image and color (dark)"
msgstr "Divider with image and colour (dark)"

#: inc/patterns/footer-title-tagline-social.php:6
msgid "Footer with title, tagline, and social links on a dark background"
msgstr "Footer with title, tagline, and social links on a dark background"

#: inc/patterns/footer-social-copyright.php:6
msgid "Footer with social links and copyright"
msgstr "Footer with social links and copyright"

#: inc/patterns/footer-query-title-citation.php:6
msgid "Footer with query, title, and citation"
msgstr "Footer with query, title, and citation"

#: inc/patterns/footer-query-images-title-citation.php:6
msgid "Footer with query, featured images, title, and citation"
msgstr "Footer with query, featured images, title, and citation"

#: inc/patterns/footer-navigation.php:6
msgid "Footer with navigation and citation"
msgstr "Footer with navigation and citation"

#: inc/patterns/footer-navigation-copyright.php:20
#: inc/patterns/footer-social-copyright.php:24
msgid "© Site Title"
msgstr "© Site title"

#: inc/patterns/footer-navigation-copyright.php:6
msgid "Footer with navigation and copyright"
msgstr "Footer with navigation and copyright"

#: inc/patterns/footer-logo.php:6
msgid "Footer with logo and citation"
msgstr "Footer with logo and citation"

#: inc/patterns/footer-default.php:6
msgid "Default footer"
msgstr "Default footer"

#: inc/patterns/footer-dark.php:6
msgid "Dark footer with title and citation"
msgstr "Dark footer with title and citation"

#: inc/patterns/footer-blog.php:50 inc/patterns/footer-dark.php:18
#: inc/patterns/footer-default.php:18 inc/patterns/footer-logo.php:18
#: inc/patterns/footer-navigation.php:20
#: inc/patterns/footer-query-images-title-citation.php:35
#: inc/patterns/footer-query-title-citation.php:33
msgid "https://wordpress.org"
msgstr "https://en-gb.wordpress.org"

#. Translators: WordPress link.
#: inc/patterns/footer-blog.php:49 inc/patterns/footer-dark.php:17
#: inc/patterns/footer-default.php:17 inc/patterns/footer-logo.php:17
#: inc/patterns/footer-navigation.php:19
#: inc/patterns/footer-query-images-title-citation.php:34
#: inc/patterns/footer-query-title-citation.php:32
msgid "Proudly powered by %s"
msgstr "Proudly powered by %s"

#: inc/patterns/footer-blog.php:31
#: inc/patterns/page-sidebar-blog-posts-right.php:74
msgid "Categories"
msgstr "Categories"

#: inc/patterns/footer-blog.php:23 inc/patterns/general-featured-posts.php:10
msgid "Latest posts"
msgstr "Latest posts"

#: inc/patterns/footer-blog.php:6
msgid "Blog footer"
msgstr "Blog footer"

#: inc/patterns/footer-about-title-logo.php:17 inc/patterns/footer-blog.php:17
msgid "We are a rogue collective of bird watchers. We’ve been known to sneak through fences, climb perimeter walls, and generally trespass in order to observe the rarest of birds."
msgstr "We are a rogue collective of bird watchers. We’ve been known to sneak through fences, climb perimeter walls, and generally trespass in order to observe the rarest of birds."

#: inc/patterns/footer-about-title-logo.php:13 inc/patterns/footer-blog.php:13
msgid "About us"
msgstr "About us"

#: inc/patterns/footer-about-title-logo.php:6
msgid "Footer with text, title, and logo"
msgstr "Footer with text, title, and logo"

#: inc/block-patterns.php:21
msgid "Pages"
msgstr "Pages"

#: inc/block-patterns.php:20
msgid "Query"
msgstr "Query"

#: inc/block-patterns.php:19
msgid "Headers"
msgstr "Headers"

#: inc/block-patterns.php:18
msgid "Footers"
msgstr "Footers"

#: inc/block-patterns.php:17
msgid "Featured"
msgstr "Featured"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentytwo/"
msgstr "https://en-gb.wordpress.org/themes/twentytwentytwo/"

#. Author URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/"
msgstr "https://en-gb.wordpress.org/"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "the WordPress team"
