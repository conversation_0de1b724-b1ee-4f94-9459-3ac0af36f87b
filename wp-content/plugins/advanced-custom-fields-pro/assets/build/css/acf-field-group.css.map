{"version": 3, "file": "acf-field-group.css", "mappings": ";;;AAAA,gBAAgB;ACAhB;;;;8FAAA;AAMA;AAOA;AAQA;AAgBA;;;;8FAAA;ACrCA;;;;8FAAA;ACAA;;;;8FAAA;AAOA;;;EAGC;EACA;AHkBD;;AGbC;;EAEC;AHgBF;;AGZA;;;;8EAAA;AAKA;;;EAGC;AHeD;;AGZA;EACC;AHeD;;AGZA;EACC;AHeD;;AGZA;EACC;AHeD;;AGXA;;;;8EAAA;AAKA;EACC;EASA;EAKA;EAgBA;EAeA;EAUA;EAyCA;AH5ED;AGlBC;EAEE;EACA;AHmBH;AGdC;EACC;AHgBF;AGVE;EAEE;AHWJ;AGRG;EALD;IAME;EHWF;AACF;AGJC;EACC;EACA;AHMF;AGJE;EAJD;IAKE;EHOD;AACF;AGJC;EAAkB;AHOnB;AGNC;EAAiB;EAAY;AHU9B;AGTC;EAAgB;AHYjB;AGXC;EAAiB;AHclB;AGTE;EAAkB;AHYpB;AGXE;EAAiB;AHcnB;AGbE;EAAgB;EAAa;AHiB/B;AGhBE;EAAiB;AHmBnB;AGbE;EACC;AHeH;AGZE;EACC;AHcH;AGZG;EACC;AHcJ;AGXG;EACC;AHaJ;AGVG;EACC;EACA;AHYJ;AGTG;EAEE;EACA;EACA,4BFvFM;ADiGX;AGNG;EACC;EACA;AHQJ;AGJE;EACC;AHMH;AGDC;EACC;AHGF;AGAC;EACC;EACA;EA8CA;EAOA;AHjDF;AGAG;;EAEC;AHEJ;AGGE;EACC;EACA;EACA;AHDH;AGEG;EACC;EACA;EACA;EAEA;EACA,WAFY;EAGZ,YAHY;EAIZ,yBFjIO;EEkIP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHDJ;AGGG;EACC;EACA;EACA,yBF3LU;AD0Ld;AGME;EACC;EACA;EACA;AHJH;AGSG;EACC;AHPJ;AGcE;EACC,qBF1LkB;AD8KrB;;AGoBE;EAEE;EACA;AHlBJ;;AGwBA;AACA;EACC;EACA;EAEA;EA+BA;EAMA;EA0DA;EA2BA;;;;;;;;;;;;;GAAA;EAgBA;EAcA;EAWA;AHrKD;AGGC;EACC;EAEC;EACA;EACA;EAED,kBFrKU;EEsKV;AHHF;AGKE;EACC;AHHH;AGQC;EACC;EACA;EACA;EACA;EACA;AHNF;AGSE;EACC;AHPH;AGaC;EACC;AHXF;AGkBE;EACC;EACA;EACA;EACA;AHhBH;AGmBE;EACC;AHjBH;AGoBE;EACC;EACA;EACA;EACA;EACA;AHlBH;AGqBE;EACC;EACA;EAEC;AHpBJ;AGuBG;EAPD;IAQE;IAEC;EHrBH;AACF;AGwBG;EACC;AHtBJ;AGwBI;EACC;AHtBL;AG2BG;EACC;AHzBJ;AG2BI;EAAU;AHxBd;AG2BG;EACC;AHzBJ;AGkCE;EACC;AHhCH;AGmCE;EACC,mBFjVQ;EEkVR;EACA;EACA;EACA;EACA;AHjCH;AGmCG;EACC;AHjCJ;AGmCI;EACC;AHjCL;AG8DG;EACC;EACA;AH5DJ;AGoEC;EACC;EACA;AHlEF;AGoEE;EACC;AHlEH;AGwEC;EACC;AHtEF;;AG4EA;;;;8EAAA;AAQC;EACC;AH5EF;AG+EC;EACC;AH7EF;AG+EE;EACC;AH7EH;AGgFE;EACC;AH9EH;AGiFE;EACC;AH/EH;AGkFE;EACC;AHhFH;AGmFE;EACC;EACA;AHjFH;AGmFG;EACC;EACA;EACA;AHjFJ;AGmFI;EACC;EACA;EACA;AHjFL;AGuFE;EACC;AHrFH;AGyFE;EACC;AHvFH;AG8FG;EACC;EACA;AH5FJ;;AGmGA;;;;8EAAA;AAMA;EACC;EACA;AHjGD;;AGoGA;EAEC;IACC;EHlGA;AACF;AGuGA;;;;8EAAA;AAMA;EACC;EACA;EACA;AHtGD;;AGyGA;EACC;EACA;EACA;AHtGD;;AG0GA;;;;8EAAA;AASC;;;;;EAKC;AH3GF;AG+GC;EACC;AH7GF;AGgHC;EACC;AH9GF;AGkHC;;EAEC;AHhHF;;AGoHA;;;;8EAAA;AASC;;;;;EAKC;AHrHF;AGyHC;EACC;AHvHF;AG0HC;EACC;AHxHF;AG4HC;EACC;AH1HF;;AGgIA;;;;8EAAA;AAMA;;;EAGC;AH9HD;;AGiIA;EACC;AH9HD;;AGiIA;EACC;AH9HD;;AGkIA;;;;8EAAA;AAMA;;;EAGC;AHhID;;AGoIA;;;;8EAAA;AAYE;;;EACC;AHtIH;AGyIE;;;EACC;EACA;AHrIH;AGwIE;;;EACC;AHpIH;;AG8IE;EACC;AH3IH;AG8IE;EACC;AH5IH;;AGmJA;;;;8FAAA;AAQC;EACC;EACA;AHnJF;AGsJC;EACC;EACA;EACA;AHpJF;;AGyJA;;;;8FAAA;AAMA;EACC;AHvJD;;AG0JA;;;;8EAAA;AAMA;EAEC;;;IAGC;IACA;IACA;EHzJA;EG4JD;IACC;IACA;EH1JA;EG6JD;IACC;IACA;EH3JA;AACF;AGgKA;;;;8EAAA;AASE;;EAEC,yBFtrBQ;ADohBX;;AIhkBA;;;;+FAAA;AAKA;EACC;AJmkBD;;AIhkBA;;;;+FAAA;AAOC;EACC,cH4CS;ADqhBX;;AI5jBA;;;;+FAAA;AAMA;;EACC;EACA;AJ+jBD;;AI5jBA;;EACC;EACA;AJgkBD;;AI7jBA;;;;;EACC;EACA;AJokBD;;AIhjBA;;;;+FAAA;AAQC;EACC;AJgjBF;AI7iBC;EACC;AJ+iBF;AI5iBC;EACC;AJ8iBF;AI3iBC;;;;;EACC;AJijBF;AI9iBC;;;;;EACC;AJojBF;AIjjBC;EACC;AJmjBF;AIhjBC;EACC;AJkjBF;AI/iBC;EACC;AJijBF;;AI5iBA;;;;+FAAA;AAKA;EAEC,cH1DU;ADwmBX;;AI3iBA;;;;+FAAA;AAOC;;EAEC;AJ4iBF;;AIviBA;;;;+FAAA;AASA;;;;+FAAA;AAKA;EACC;EACA;AJsiBD;;AIpiBA;EACC;EACA;AJuiBD;;AKzrBA;EAEC;;;;iGAAA;EAuCA;;;;iGAAA;EAcA;;;;iGAAA;EAcA;;;;iGAAA;EAeA;;;;iGAAA;EA6CA;;;;iGAAA;EAsEA;;;;iGAAA;EAkBA;;;;iGAAA;EAkBA;;;;iGAAA;EAqCA;;;;iGAAA;EAwGA;;;;iGAAA;EAqCA;;;;iGAAA;EAkCA;;;;iGAAA;EASA;;;;iGAAA;EAyHA;;;;iGAAA;EA+BA;;;;iGAAA;AL4KD;AKrwBC;;;;;EAKC;EACA;EAEC;EACA;EAED;EACA,qBJ4BS;EI3BT,6CJmEa;EIlEb,kBJ8DU;EI5DV,cJ4BS;ADwuBX;AKlwBE;;;;;EACC,0BJgEO;EI/DP,qBJiCQ;ADuuBX;AKrwBE;;;;;EACC,yBJaQ;EIZR;AL2wBH;AKxwBE;;;;;EACC,cJYQ;ADkwBX;AKlwBE;EACC,yBJLQ;EIMR,cJFQ;ADswBX;AKxvBE;;EAEC;AL0vBH;AKhvBC;EACC;EAEC;EACA;EAED;EACA;ALgvBF;AKxuBC;EACC;EACA;EAEC;EACA;EAED;EACA;EACA;ALwuBF;AKruBE;EAEC,cJ1CQ;ADgxBX;AKnuBE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALquBH;AK/tBE;EAEE;EACA;EAED;AL+tBH;AKttBC;;EAEC;EACA;EACA;EACA;EAEC;EACA;EACA,qBJ/FQ;EIiGT;EACA;ALstBF;AKptBE;;EACC,yBJ7FQ;EI8FR,qBJzFQ;ADgzBX;AKptBE;;;EAEC,yBJnGQ;EIoGR,qBJ/FQ;ADszBX;AKrtBG;;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALytBJ;AKptBE;;EACC;ALutBH;AKptBE;;EACC,yBJxIQ;EIyIR,qBJtIQ;AD61BX;AK7sBI;;;EACC;ALitBL;AKhsBG;EACC;ALksBJ;AKjrBG;EACC;ALmrBJ;AKpqBE;;;;EAGE;ALuqBJ;AKnqBE;;EAEE;ALqqBJ;AKlqBG;;EAEE;ALoqBL;AK7pBE;;EACC;EACA;EACA;ALgqBH;AKtpBC;EACC;EACA;EACA;EACA,yBJ1OS;EI2OT;ALwpBF;AKtpBE;EACC,yBJ7OQ;ADq4BX;AKrpBE;EACC;ALupBH;AKppBE;EACC,yBJxOQ;AD83BX;AKppBG;EACC,yBJ1OO;ADg4BX;AKnpBG;EACC;ALqpBJ;AKhpBE;;EAEC;ALkpBH;AK/oBE;EACC;EACA;EACA;EACA;EACA;ALipBH;AK5oBC;EACC;EACA;AL8oBF;AK5oBE;EACC;EACA;EACA;EAEC;EACA;EACA;AL6oBJ;AK1oBG;EAEE;AL2oBL;AKvoBG;EAEE;ALwoBL;AKpoBG;EACC;EAEC;EACA;ALqoBL;AK3nBG;EAEE;EACA;AL4nBL;AKxnBG;EAEE;EACA;ALynBL;AK7mBC;EACC;EACA;EAEC;EAGA;EACA;EACA;EACA;EAED;EACA;EACA,kBJzTU;EI2TT;EACA;EACA,qBJnVQ;EIqVT;ALymBF;AKvmBE;EACC,qBJvVQ;EIwVR;EACA;ALymBH;AK9lBC;EACC;EACA;EACA;EAEC;EACA;EAED;EACA;EACA;EACA,qBJhXS;EIiXT,kBJ3VU;EI6VV,cJnXS;ADg9BX;AK3lBE;EACC;EACA,qBJvXQ;EIwXR,cJxXQ;ADq9BX;AK3lBE;EACC;EACA,0BJ/VO;EIgWP,cJ7XQ;AD09BX;AKnlBC;EACC;ALqlBF;AK3kBE;EACC;EACA;AL6kBH;AK1kBE;EACC;EAEC;EACA;EAED;EAEC;EACA;EACA,qBJ/aO;EIibR,6CJzYY;EI0YZ,kBJ9YS;EIgZT,cJhbQ;ADu/BX;AKpkBE;EACC,0BJ7YO;EI8YP,qBJ5aQ;EI6aR,kBJtZS;AD49BZ;AKpkBG;EACC;ALskBJ;AKjkBI;EACC;EACA;ALmkBL;AK5jBI;EACC;EACA;AL8jBL;AKvjBE;EACC;EAEC;ALwjBJ;AKrjBG;EACC;EACA;ALujBJ;AKljBE;EAEE;EACA;EACA;EACA;ALmjBJ;AK/iBE;EACC;EACA;EAEC;EACA;EAED;EACA;EACA;AL+iBH;AK7iBG;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAIZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,yBJtgBO;ADojCX;AK3iBG;EACC,yBJtgBO;ADmjCX;AKjiBC;EACC;EACA;EACA;ALmiBF;AKjiBE;EAEC,WADY;EAEZ,YAFY;EAGZ,yBJ/hBQ;ADikCX;AK/hBE;EAEE;ALgiBJ;AK5hBE;EAEE;AL6hBJ;AKlhBC;EACC;EACA;EACA;EACA;ALohBF;AKlhBE;EACC;EACA;ALohBH;;AK7gBC;EACC;EACA;ALghBF;;AMtoCA;;;;8EAAA;AAOC;;;;EAIC,iBLqFU;ADkjCZ;;AMloCA;;;;8EAAA;AAOC;EACC,iBLwEU;AD2jCZ;;AM9nCA;;;;8EAAA;AAOC;EACC;AN+nCF;;AMznCA;;;;8EAAA;AAOC;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;ANunCH;;AMjnCA;;;;8EAAA;AAOC;EACC;EACA;EACA;EACA,6CLSa;ADymCf;AMhnCE;EAEE;EACA;EACA,yBLtCO;ADupCX;AM9mCG;;EAEC;ANgnCJ;AM7mCG;EACC;AN+mCJ;;AMtmCA;;;;8EAAA;AAOC;EACC,yBLlES;EKoER;EACA;EACA,yBLpEQ;EKuER;EACA;EACA,4BLzEQ;AD6qCX;AMjmCE;EACC;EACA;EACA;EAEC;EACA;EAGD,cLhFQ;EKiFR;ANgmCH;;AMzlCA;;;;8EAAA;AAOC;EAEE;EACA;EACA,yBLxGQ;ADisCX;AMplCG;EACC;ANslCJ;AM9kCG;EACC;EACA;EACA;EACA;EACA,mBL1HO;EK2HP;ANglCJ;AM3kCI;EACC;AN6kCL;AM1kCI;EACC;EACA;EACA;EACA;EACA,mBLzIM;EK0IN;AN4kCL;AMrkCE;EACC;ANukCH;AMpkCE;EACC;EACA,yBL5IQ;ADktCX;AMnkCE;EACC,yBLjJQ;EKkJR;EACA;ANqkCH;AMnkCG;EACC;ANqkCJ;AMnkCI;EACC;ANqkCL;AM/jCE;EACC;EACA;ANikCH;AM9jCE;EACC;EACA;EACA;EACA;EAEA,cLhLQ;AD+uCX;AM7jCG;EACC;EACA;EACA;EACA;EACA;EACA;AN+jCJ;AMzjCI;EACC;AN2jCL;AMxjCI;EACC;AN0jCL;AM3iCA;;;;8EAAA;AAOC;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED;EAEC;EACA;EACA,yBL9OQ;ADsxCX;AMriCE;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;ANoiCJ;;AM5hCA;;;;8EAAA;AAKA;EACC;EAEC;EACA;EAED;EAEC;EACA;EACA,0BLtQS;ADkyCX;;AMvhCA;;;;8EAAA;AAKA;EAEE;EACA;EACA;EACA;ANyhCF;AMthCC;EAEE;ANuhCH;;AMjhCA;;;;8EAAA;AAKA;EACC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;ANmhCF;;AM/gCA;;;;8EAAA;AAKA;EACC;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;AN+gCF;AM5gCC;EAhBD;IAkBG;IACA;EN8gCD;AACF;AM3gCC;;EAEC;AN6gCF;AM3gCE;;EACC;AN8gCH;AMzgCC;EACC;AN2gCF;;AMtgCA;;;;8EAAA;AAMA;;EAGE;EAGA;EACA;EACA,yBLzXS;AD83CX;;AMjgCA;EAEE;ANmgCF;;AM//BA;;;;8EAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGA;EACA;EACA,yBL7ZS;AD05CX;AM1/BC;EAxBD;IA0BG;EN4/BD;AACF;;AMv/BA;EAEE;EACA;EACA;EACA;ANy/BF;;AMr/BA;;;;8EAAA;AAOC;EACC,mBL3bS;EK6bR,4BLrbQ;AD06CX;AMl/BE;EAEE;EACA;EAGA;EAGA;EACA;EACA,4BLzcO;ADw7CX;AMz+BC;;EAEC;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EAGA;EACA;EACA,4BLjeQ;ADu8CX;AMn+BE;;EACC;EACA;EAEC;EACA;EACA;EACA;EAED;ANo+BH;AMl+BG;;EACC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED;EACA;EACA;EAEC;EACA;EACA;EAED;EACA,cLhgBO;EKkgBP;ANg+BJ;AM99BI;;EACC,cLlgBM;ADm+CX;AM99BI;;EACC;ANi+BL;AM59BG;;EACC;EAEC,4BLrgBM;EKugBP,cLhhBO;AD6+CX;;AMp9BA;EAEE;ANs9BF;;AMl9BA;EAEE;ANo9BF;AMj9BC;EALD;IAOG;ENm9BD;AACF;;AM98BA;;;;8EAAA;AAOC;EAEE;EACA;EACA;EACA;AN88BH;AM38BE;EACC;AN68BH;AMx8BC;EACC;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;ANu8BH;AMl8BC;EACC;EAEC;EACA;EACA;EACA;ANm8BH;AMh8BE;EACC;EAEC;EACA;EACA;EACA;ANi8BJ;AM77BE;EACC;AN+7BH;AM57BE;EACC;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EAGA;EACA;EACA,0BL/nBO;ADwjDX;AMl7BC;EACC;ANo7BF;AMh7BC;EACC;ANk7BF;;AM36BE;EAEE;EACA;EAED;EAEC;EACA;EACA,2BL5pBO;ADukDX;;AMr6BA;;;;8EAAA;AAOC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ANs6BF;AMn6BC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEC;EACA;EAGD;EACA;EACA;ANk6BF;AMh6BE;EACC;ANk6BH;AM/5BE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,WADY;EAEZ,YAFY;EAGZ,yBLptBQ;EKqtBR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ANg6BH;;AMt5BE;EACC;EACA;ANy5BH;;AMn5BA;;;;8EAAA;AAOC;EACC;EAEC;ANm5BH;AMh5BE;EACC;EACA;EACA;EACA;EAEA;EACA,WAFY;EAGZ,YAHY;EAKX;EAED,yBL3wBQ;EK4wBR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN+4BH;AM54BE;EACC;AN84BH;;AMp4BE;EAEE;EACA;ANs4BJ;AMn4BG;EACC;EACA;EACA;EACA;ANq4BJ;AM/3BG;EACC;EACA;ANi4BJ;AM93BG;EACC;EACA;ANg4BJ;AM73BG;EACC;EACA;AN+3BJ;;AMv3BC;EAEE;ANy3BH;AMr3BE;EAEE;EACA;ANs3BJ;;AM/2BA;;;;8EAAA;AAOC;EACC;EAEC;EACA;AN+2BH;AM52BE;EAPD;IASG;EN82BF;AACF;AMz2BC;EACC;EAEC;EAGA;EACA;ANw2BH;AMr2BE;EACC;EACA;EAEC;EAGA;EACA;EACA;EAGA;EACA;EACA,yBL34BO;AD6uDX;AM/1BG;EAjBD;IAmBG;ENi2BH;EM91BE;IACC;IACA;IACA;IACA;IACA;ENg2BH;EM91BG;IACC;ENg2BJ;AACF;;AMp1BA;;;;8EAAA;AAOC;;EAEC;EACA;EACA;EACA;EAEC;EACA;EAED,yBL37BS;EK47BT,qBLz7BS;EK07BT,6CLl5Ba;EKm5Bb,cLz7BS;AD4wDX;AMh1BC;EACC;ANk1BF;AM/0BC;EACC;ANi1BF;;AM50BA;;;;8EAAA;AAKA;EACC;AN+0BD;;AM50BA;EACC;AN+0BD;;AM50BA;;;;8EAAA;AAKA;EACC;EACA;EAEC;EACA;AN80BF;AM30BC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AN60BF;AM30BE;;;EAGC;AN60BH;AM10BE;EAGE;EACA;EAED;EACA,cLp/BQ;AD6zDX;AMt0BE;EAGE;EACA;EAED;EACA,cLhgCQ;ADq0DX;AMn0BG;EAGE;ANm0BL;AM5zBE;EACC;EAEC;AN6zBJ;AMzzBE;EAEE;AN0zBJ;;AMlzBA;;;;8EAAA;AASE;EACC;ANizBH;;AM1yBA;;;;8EAAA;AAOC;EACC;AN2yBF;AMzyBE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EAEC;EACA;EAGA;EACA;EACA,4BL1kCO;ADk3DX;AMryBG;;;;EAME;EAED,cLhlCO;ADo3DX;AMjyBG;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ANmyBJ;AMjyBI;EAEC;EACA;EACA;EACA;EACA,WALY;EAMZ,YANY;EAOZ,yBLvmCM;EKwmCN;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ANkyBL;AM/xBI;EACC,yBL5mCM;AD64DX;AM1xBE;EACC;EACA;EAEC;EACA;EACA;EACA;EAED;AN0xBH;AMxxBG;EAEE;EACA;ANyxBL;AMhxBG;EAEE;ANixBL;;AMvwBA;;;;8EAAA;AAOC;EACC;ANwwBF;;AMnwBA;;;;8EAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EAGA;EACA;EAGA;EACA;EACA,yBL1sCS;AD28DX;AM9vBC;EACC;EACA;EACA;EACA;EAEA,cLhtCS;AD+8DX;AM7vBE;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAKX;EAED,yBL5tCQ;EK6tCR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN4vBH;;AMtvBA;;;;8EAAA;AAKA;EACC;ANyvBD;AMvvBC;EACC;EACA;EACA;EACA;EAEC;EAED;EAEC;EACA;EACA,qBL3uCa;EK6uCd;EACA;EACA;ANqvBF;;AMhvBA;;;;8EAAA;AAKA;EACC;EAEC;EACA;EACA,yBLrxCS;ADugEX;AMhvBC;EACC;EAEC;EACA;ANivBH;AM9uBE;EAEE;EACA;EACA,0BLlyCO;ADihEX;;AMvuBA;;;;8EAAA;AAQC;;EAEE;ANuuBH;AMjuBE;;EACC;ANouBH;AMluBG;;EACC;EACA;ANquBJ;AMnuBI;;EACC;ANsuBL;AMnuBI;;EAEE;EACA;EAED,yBL30CM;EK40CN;EAEA,cLz0CM;AD4iEX;AM9tBG;;EACC;ANiuBJ;;AMxtBA;EAEE;EAGA;EACA;EACA,yBLn2CS;AD2jEX;;AMptBA;;;;8EAAA;AAQC;;EACC;ANqtBF;;AMhtBA;;;;8EAAA;AAKA;EACC;ANmtBD;AMjtBC;EACC;ANmtBF;;AM/sBA;;;;8EAAA;AAQC;;EACC;ANgtBF;AM7sBC;;EACC;ANgtBF;;AM3sBA;;;;8EAAA;AAOE;EACC;AN4sBH;AMtsBG;EACC;ANwsBJ;;AMlsBA;;;;8EAAA;AAQC;;EAEC;ANksBF;;AM7rBA;;;;8EAAA;AAQC;EACC;AN6rBF;AM3rBE;EACC;AN6rBH;;AO/qEA;;;;+FAAA;AAKA;EACC;EACA;EACA,kBN4EW;EM1EV;EACA;EACA;EAED,6CNyEc;ADumEf;AO7qEC;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;AP8qEH;AOzqEC;EACC;AP2qEF;AOvqEC;EACC;EACA;APyqEF;AOrqEC;EACC;EAEC;EACA;APsqEH;;AOhqEA;EACC;EACA;APmqED;;AOhqEA;EACC;APmqED;;AOhqEA;EACC;APmqED;;AOhqEA;EACC;APmqED;;AOhqEA;EACC;APmqED;;AOhqEA;;;;+FAAA;AAOC;EACC;EACA,yBNhCS;EMiCT,cNjCS;ADksEX;AO/pEC;EACC;EACA,yBNrCS;EMsCT,cNtCS;ADusEX;AO9pEC;EACC;EACA,6CNLa;EMMb;EACA;EACA;EACA;EACA;APgqEF;AO7pEC;;;EAGC;EACA;EACA;AP+pEF;AO5pEC;EACC;EACA;AP8pEF;AO3pEC;EAUC;EACA;EAEC;EACA;EAGA;EACA;EAGA;EACA;EACA;EAED,kBNtDU;EMuDV,6CNpDa;ADksEf;AOvqEE;EACC;EAEC;EACA;EACA,yBNzEO;ADivEX;AOlpEE;EA5BD;IA6BE;IAEC;IACA;EPopEF;AACF;AOhpEE;EACC;EAEC;EACA;APipEJ;AO7oEE;;EAEC;AP+oEH;AO5oEE;EAEE;EACA;EACA;AP6oEJ;AOzoEE;EACC;EAEC;EACA;EACA;AP0oEJ;AOpoEC;EACC,yBN3IS;EM4IT;EACA;EACA;EAEC;EAGA;APmoEH;AOhoEE;EACC;EACA;EACA;APkoEH;AO/nEE;EACC;EACA;APioEH;AO9nEE;EACC;EACA;APgoEH;AO7nEG;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAKX;EAED,yBN3KO;EM4KP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AP4nEJ;AO1nEa;EACR;AP4nEL;;AOrnEE;EACC;EACA;APwnEH;AOtnEG;EACC;APwnEJ;AOrnEG;EACC;APunEJ;AOpnEG;EAEE;APqnEL;AOlnEI;EAEE;APmnEN;;AOvmEA;;;;+FAAA;AAMA;EACC;EACA;APymED;;AOtmEA;;;;+FAAA;AAWC;EAA4B;APomE7B;AOpmE4D;EAAU;APumEtE;AOrmEC;EAAiC;APwmElC;AOtmEC;EAA6C,0BAN9B;AP+mEhB;AOnmEE;EAA4B;APsmE9B;AOtmE6D;EAAU;APymEvE;AOvmEE;EAAiC;AP0mEnC;AOxmEE;EAA6C,0BAN9B;APinEjB;AOrmEG;EAA4B;APwmE/B;AOxmE8D;EAAU;AP2mExE;AOzmEG;EAAiC;AP4mEpC;AO1mEG;EAA6C,0BAN9B;APmnElB;AOvmEI;EAA4B;AP0mEhC;AO1mE+D;EAAU;AP6mEzE;AO3mEI;EAAiC;AP8mErC;AO5mEI;EAA6C,0BAN9B;APqnEnB,C", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/acf-field-group.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_variables.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_mixins.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_field-group.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_typography.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_admin-inputs.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_edit-field-group.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_sub-field-groups.scss"], "sourcesContent": ["@charset \"UTF-8\";\n/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n/* colors */\n/* acf-field */\n/* responsive */\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n/*--------------------------------------------------------------------------------------------\n*\n*\tField Group\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-field-group-fields > .inside,\n#acf-field-group-locations > .inside,\n#acf-field-group-options > .inside {\n  padding: 0;\n  margin: 0;\n}\n\n.postbox .handle-order-higher,\n.postbox .handle-order-lower {\n  display: none;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Postbox: Publish\n*\n*----------------------------------------------------------------------------*/\n#minor-publishing-actions,\n#misc-publishing-actions #visibility,\n#misc-publishing-actions .edit-timestamp {\n  display: none;\n}\n\n#minor-publishing {\n  border-bottom: 0 none;\n}\n\n#misc-pub-section {\n  border-bottom: 0 none;\n}\n\n#misc-publishing-actions .misc-pub-section {\n  border-bottom-color: #F5F5F5;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Postbox: Fields\n*\n*----------------------------------------------------------------------------*/\n#acf-field-group-fields {\n  border: 0 none;\n  /* links */\n  /* Field type */\n  /* table header */\n  /* show keys */\n  /* hide tabs */\n  /* fields */\n}\n#acf-field-group-fields .inside {\n  border-top-width: 0;\n  border-top-style: none;\n}\n#acf-field-group-fields a {\n  text-decoration: none;\n}\n#acf-field-group-fields .li-field-type .field-type-icon {\n  margin-right: 8px;\n}\n@media screen and (max-width: 600px) {\n  #acf-field-group-fields .li-field-type .field-type-icon {\n    display: none;\n  }\n}\n#acf-field-group-fields .li-field-order {\n  width: 64px;\n  justify-content: center;\n}\n@media screen and (max-width: 880px) {\n  #acf-field-group-fields .li-field-order {\n    width: 32px;\n  }\n}\n#acf-field-group-fields .li-field-label {\n  width: calc(50% - 64px);\n}\n#acf-field-group-fields .li-field-name {\n  width: 25%;\n  word-break: break-word;\n}\n#acf-field-group-fields .li-field-key {\n  display: none;\n}\n#acf-field-group-fields .li-field-type {\n  width: 25%;\n}\n#acf-field-group-fields.show-field-keys .li-field-label {\n  width: calc(35% - 64px);\n}\n#acf-field-group-fields.show-field-keys .li-field-name {\n  width: 15%;\n}\n#acf-field-group-fields.show-field-keys .li-field-key {\n  width: 25%;\n  display: flex;\n}\n#acf-field-group-fields.show-field-keys .li-field-type {\n  width: 25%;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-tab-bar {\n  display: none;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main {\n  padding: 0;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main.acf-field-settings-main-general {\n  padding-top: 32px;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field {\n  margin-bottom: 32px;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field-setting-wrapper {\n  padding-top: 0;\n  border-top: none;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field-settings-split .acf-field {\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-main .acf-field-setting-first_day {\n  padding-top: 0;\n  border-top: none;\n}\n#acf-field-group-fields.hide-tabs .acf-field-settings-footer {\n  margin-top: 32px;\n}\n#acf-field-group-fields .acf-field-list-wrap {\n  border: #ccd0d4 solid 1px;\n}\n#acf-field-group-fields .acf-field-list {\n  background: #f5f5f5;\n  margin-top: -1px;\n  /* no fields */\n  /* empty */\n}\n#acf-field-group-fields .acf-field-list .acf-tbody > .li-field-name,\n#acf-field-group-fields .acf-field-list .acf-tbody > .li-field-key {\n  align-items: flex-start;\n}\n#acf-field-group-fields .acf-field-list .copyable:not(.copy-unsupported) {\n  cursor: pointer;\n  display: inline-flex;\n  align-items: center;\n}\n#acf-field-group-fields .acf-field-list .copyable:not(.copy-unsupported):hover:after {\n  content: \"\";\n  display: block;\n  padding-left: 5px;\n  display: inline-flex;\n  width: 12px;\n  height: 12px;\n  background-color: #667085;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n  -webkit-mask-image: url(\"../../images/icons/icon-copy.svg\");\n  mask-image: url(\"../../images/icons/icon-copy.svg\");\n  background-size: cover;\n}\n#acf-field-group-fields .acf-field-list .copyable:not(.copy-unsupported).copied:hover:after {\n  -webkit-mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n  mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n  background-color: #49ad52;\n}\n#acf-field-group-fields .acf-field-list .no-fields-message {\n  padding: 15px 15px;\n  background: #fff;\n  display: none;\n}\n#acf-field-group-fields .acf-field-list.-empty .no-fields-message {\n  display: block;\n}\n.acf-admin-3-8 #acf-field-group-fields .acf-field-list-wrap {\n  border-color: #dfdfdf;\n}\n\n.rtl #acf-field-group-fields .li-field-type .field-type-icon {\n  margin-left: 8px;\n  margin-right: 0;\n}\n\n/* field object */\n.acf-field-object {\n  border-top: #eeeeee solid 1px;\n  background: #fff;\n  /* sortable */\n  /* meta */\n  /* handle */\n  /* open */\n  /*\n  \t// debug\n  \t&[data-save=\"meta\"] {\n  \t\t> .handle {\n  \t\t\tborder-left: #ffb700 solid 5px !important;\n  \t\t}\n  \t}\n\n  \t&[data-save=\"settings\"] {\n  \t\t> .handle {\n  \t\t\tborder-left: #0ec563 solid 5px !important;\n  \t\t}\n  \t}\n  */\n  /* hover */\n  /* settings */\n  /* conditional logic */\n}\n.acf-field-object.ui-sortable-helper {\n  overflow: hidden !important;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #A5D2E7 !important;\n  border-radius: 8px;\n  filter: drop-shadow(0px 10px 20px rgba(16, 24, 40, 0.14)) drop-shadow(0px 1px 3px rgba(16, 24, 40, 0.1));\n}\n.acf-field-object.ui-sortable-helper:before {\n  display: none !important;\n}\n.acf-field-object.ui-sortable-placeholder {\n  box-shadow: 0 -1px 0 0 #DFDFDF;\n  visibility: visible !important;\n  background: #F9F9F9;\n  border-top-color: transparent;\n  min-height: 54px;\n}\n.acf-field-object.ui-sortable-placeholder:after, .acf-field-object.ui-sortable-placeholder:before {\n  visibility: hidden;\n}\n.acf-field-object > .meta {\n  display: none;\n}\n.acf-field-object > .handle a {\n  -webkit-transition: none;\n  -moz-transition: none;\n  -o-transition: none;\n  transition: none;\n}\n.acf-field-object > .handle li {\n  word-wrap: break-word;\n}\n.acf-field-object > .handle strong {\n  display: block;\n  padding-bottom: 0;\n  font-size: 14px;\n  line-height: 14px;\n  min-height: 14px;\n}\n.acf-field-object > .handle .row-options {\n  display: block;\n  opacity: 0;\n  margin-top: 5px;\n}\n@media screen and (max-width: 880px) {\n  .acf-field-object > .handle .row-options {\n    opacity: 1;\n    margin-bottom: 0;\n  }\n}\n.acf-field-object > .handle .row-options a {\n  margin-right: 4px;\n}\n.acf-field-object > .handle .row-options a:hover {\n  color: #044767;\n}\n.acf-field-object > .handle .row-options a.delete-field {\n  color: #a00;\n}\n.acf-field-object > .handle .row-options a.delete-field:hover {\n  color: #f00;\n}\n.acf-field-object > .handle .row-options.active {\n  visibility: visible;\n}\n.acf-field-object.open + .acf-field-object {\n  border-top-color: #E1E1E1;\n}\n.acf-field-object.open > .handle {\n  background: #2a9bd9;\n  border: #2696d3 solid 1px;\n  text-shadow: #268FBB 0 1px 0;\n  color: #fff;\n  position: relative;\n  margin: 0 -1px 0 -1px;\n}\n.acf-field-object.open > .handle a {\n  color: #fff !important;\n}\n.acf-field-object.open > .handle a:hover {\n  text-decoration: underline !important;\n}\n.acf-field-object:hover > .handle .row-options, .acf-field-object.-hover > .handle .row-options, .acf-field-object:focus-within > .handle .row-options {\n  opacity: 1;\n  margin-bottom: 0;\n}\n.acf-field-object > .settings {\n  display: none;\n  width: 100%;\n}\n.acf-field-object > .settings > .acf-table {\n  border: none;\n}\n.acf-field-object .rule-groups {\n  margin-top: 20px;\n}\n\n/*----------------------------------------------------------------------------\n*\n* Postbox: Locations\n*\n*----------------------------------------------------------------------------*/\n.rule-groups h4 {\n  margin: 3px 0;\n}\n.rule-groups .rule-group {\n  margin: 0 0 5px;\n}\n.rule-groups .rule-group h4 {\n  margin: 0 0 3px;\n}\n.rule-groups .rule-group td.param {\n  width: 35%;\n}\n.rule-groups .rule-group td.operator {\n  width: 20%;\n}\n.rule-groups .rule-group td.add {\n  width: 40px;\n}\n.rule-groups .rule-group td.remove {\n  width: 28px;\n  vertical-align: middle;\n}\n.rule-groups .rule-group td.remove a {\n  width: 22px;\n  height: 22px;\n  visibility: hidden;\n}\n.rule-groups .rule-group td.remove a:before {\n  position: relative;\n  top: -2px;\n  font-size: 16px;\n}\n.rule-groups .rule-group tr:hover td.remove a {\n  visibility: visible;\n}\n.rule-groups .rule-group select:empty {\n  background: #f8f8f8;\n}\n.rule-groups:not(.rule-groups-multiple) .rule-group:first-child tr:first-child td.remove a {\n  /* Don't allow user to delete the only rule group */\n  visibility: hidden !important;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tOptions\n*\n*----------------------------------------------------------------------------*/\n#acf-field-group-options tr[data-name=hide_on_screen] li {\n  float: left;\n  width: 33%;\n}\n\n@media (max-width: 1100px) {\n  #acf-field-group-options tr[data-name=hide_on_screen] li {\n    width: 50%;\n  }\n}\n/*----------------------------------------------------------------------------\n*\n*\tConditional Logic\n*\n*----------------------------------------------------------------------------*/\ntable.conditional-logic-rules {\n  background: transparent;\n  border: 0 none;\n  border-radius: 0;\n}\n\ntable.conditional-logic-rules tbody td {\n  background: transparent;\n  border: 0 none !important;\n  padding: 5px 2px !important;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Tab\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-tab .acf-field-setting-name,\n.acf-field-object-tab .acf-field-setting-instructions,\n.acf-field-object-tab .acf-field-setting-required,\n.acf-field-object-tab .acf-field-setting-warning,\n.acf-field-object-tab .acf-field-setting-wrapper {\n  display: none;\n}\n.acf-field-object-tab .li-field-name {\n  visibility: hidden;\n}\n.acf-field-object-tab p:first-child {\n  margin: 0.5em 0;\n}\n.acf-field-object-tab li.acf-settings-type-presentation,\n.acf-field-object-tab .acf-field-settings-main-presentation {\n  display: none !important;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Accordion\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-accordion .acf-field-setting-name,\n.acf-field-object-accordion .acf-field-setting-instructions,\n.acf-field-object-accordion .acf-field-setting-required,\n.acf-field-object-accordion .acf-field-setting-warning,\n.acf-field-object-accordion .acf-field-setting-wrapper {\n  display: none;\n}\n.acf-field-object-accordion .li-field-name {\n  visibility: hidden;\n}\n.acf-field-object-accordion p:first-child {\n  margin: 0.5em 0;\n}\n.acf-field-object-accordion .acf-field-setting-instructions {\n  display: block;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Message\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-message tr[data-name=name],\n.acf-field-object-message tr[data-name=instructions],\n.acf-field-object-message tr[data-name=required] {\n  display: none !important;\n}\n\n.acf-field-object-message .li-field-name {\n  visibility: hidden;\n}\n\n.acf-field-object-message textarea {\n  height: 175px !important;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Separator\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-separator tr[data-name=name],\n.acf-field-object-separator tr[data-name=instructions],\n.acf-field-object-separator tr[data-name=required] {\n  display: none !important;\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Date Picker\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-date-picker .acf-radio-list li,\n.acf-field-object-time-picker .acf-radio-list li,\n.acf-field-object-date-time-picker .acf-radio-list li {\n  line-height: 25px;\n}\n.acf-field-object-date-picker .acf-radio-list span,\n.acf-field-object-time-picker .acf-radio-list span,\n.acf-field-object-date-time-picker .acf-radio-list span {\n  display: inline-block;\n  min-width: 10em;\n}\n.acf-field-object-date-picker .acf-radio-list input[type=text],\n.acf-field-object-time-picker .acf-radio-list input[type=text],\n.acf-field-object-date-time-picker .acf-radio-list input[type=text] {\n  width: 100px;\n}\n\n.acf-field-object-date-time-picker .acf-radio-list span {\n  min-width: 15em;\n}\n.acf-field-object-date-time-picker .acf-radio-list input[type=text] {\n  width: 200px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSlug\n*\n*--------------------------------------------------------------------------------------------*/\n#slugdiv .inside {\n  padding: 12px;\n  margin: 0;\n}\n#slugdiv input[type=text] {\n  width: 100%;\n  height: 28px;\n  font-size: 14px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRTL\n*\n*--------------------------------------------------------------------------------------------*/\nhtml[dir=rtl] .acf-field-object.open > .handle {\n  margin: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Device\n*\n*----------------------------------------------------------------------------*/\n@media only screen and (max-width: 850px) {\n  tr.acf-field,\n  td.acf-label,\n  td.acf-input {\n    display: block !important;\n    width: auto !important;\n    border: 0 none !important;\n  }\n  tr.acf-field {\n    border-top: #ededed solid 1px !important;\n    margin-bottom: 0 !important;\n  }\n  td.acf-label {\n    background: transparent !important;\n    padding-bottom: 0 !important;\n  }\n}\n/*----------------------------------------------------------------------------\n*\n*  Subtle background on accordion & tab fields to separate them from others\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-object-tab,\n.post-type-acf-field-group #acf-field-group-fields .acf-field-object-accordion {\n  background-color: #F9FAFB;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #wpcontent {\n  line-height: 140%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Links\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group a {\n  color: #0783BE;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headings\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-h1, .post-type-acf-field-group h1,\n.acf-headerbar h1 {\n  font-size: 21px;\n  font-weight: 400;\n}\n\n.acf-h2, .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner h2, .acf-page-title, .post-type-acf-field-group h2,\n.acf-headerbar h2 {\n  font-size: 18px;\n  font-weight: 400;\n}\n\n.acf-h3, .post-type-acf-field-group .acf-field-settings-fc_head label, .post-type-acf-field-group #acf-popup .acf-popup-box .title h1,\n.post-type-acf-field-group #acf-popup .acf-popup-box .title h2,\n.post-type-acf-field-group #acf-popup .acf-popup-box .title h3,\n.post-type-acf-field-group #acf-popup .acf-popup-box .title h4, .post-type-acf-field-group h3,\n.acf-headerbar h3 {\n  font-size: 16px;\n  font-weight: 400;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Paragraphs\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .p1 {\n  font-size: 15px;\n}\n.post-type-acf-field-group .p2, .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p {\n  font-size: 14px;\n}\n.post-type-acf-field-group .p3 {\n  font-size: 13.5px;\n}\n.post-type-acf-field-group .p4, .post-type-acf-field-group .acf-field-list .acf-sortable-handle, .acf-field-list .post-type-acf-field-group .acf-sortable-handle, .post-type-acf-field-group .acf-field-object .handle li.li-field-label a.edit-field, .post-type-acf-field-group .acf-field-object .handle li, .post-type-acf-field-group .acf-thead li, .post-type-acf-field-group .acf-input .select2-container.-acf .select2-selection__rendered, .post-type-acf-field-group .button, .post-type-acf-field-group input[type=text],\n.post-type-acf-field-group input[type=search],\n.post-type-acf-field-group input[type=number],\n.post-type-acf-field-group textarea,\n.post-type-acf-field-group select {\n  font-size: 13px;\n}\n.post-type-acf-field-group .p5, .post-type-acf-field-group .acf-field-setting-display_format .acf-radio-list li label code, .acf-field-setting-display_format .acf-radio-list li label .post-type-acf-field-group code,\n.post-type-acf-field-group .acf-field-setting-return_format .acf-radio-list li label code,\n.acf-field-setting-return_format .acf-radio-list li label .post-type-acf-field-group code, .post-type-acf-field-group .acf-field-group-settings-footer .acf-created-on, .acf-field-group-settings-footer .post-type-acf-field-group .acf-created-on, .post-type-acf-field-group .acf-fields .acf-field-settings-tab-bar li a, .acf-fields .acf-field-settings-tab-bar li .post-type-acf-field-group a,\n.post-type-acf-field-group .acf-fields .acf-tab-wrap .acf-tab-group li a,\n.acf-fields .acf-tab-wrap .acf-tab-group li .post-type-acf-field-group a {\n  font-size: 12.5px;\n}\n.post-type-acf-field-group .p6, .post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p.acf-small, .post-type-acf-field-group .acf-field-object .handle li.li-field-label .row-options a, .post-type-acf-field-group .acf-small {\n  font-size: 12px;\n}\n.post-type-acf-field-group .p7 {\n  font-size: 11.5px;\n}\n.post-type-acf-field-group .p8 {\n  font-size: 11px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Page titles\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-page-title {\n  color: #344054;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide old / native WP titles from pages\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-settings-wrap h1,\n.post-type-acf-field-group #acf-admin-tools h1 {\n  display: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small\n*\n*---------------------------------------------------------------------------------------------*/\n/*---------------------------------------------------------------------------------------------\n*\n*  Link focus style\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group a:focus {\n  box-shadow: none;\n  outline: none;\n}\n\n.post-type-acf-field-group a:focus-visible {\n  box-shadow: 0 0 0 1px #4f94d4, 0 0 2px 1px rgba(79, 148, 212, 0.8);\n  outline: 1px solid transparent;\n}\n\n.post-type-acf-field-group {\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  All Inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Read only text inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Number fields\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Textarea\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Select\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Button & Checkbox base styling\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Buttons\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Checkboxes\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Buttons & Checkbox lists\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  ACF Switch\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  File input button\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Action Buttons\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Edit field group header\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Select2 inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  ACF label\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Tooltip for field name field setting (result of a fix for keyboard navigation)\n  *\n  *---------------------------------------------------------------------------------------------*/\n}\n.post-type-acf-field-group input[type=text],\n.post-type-acf-field-group input[type=search],\n.post-type-acf-field-group input[type=number],\n.post-type-acf-field-group textarea,\n.post-type-acf-field-group select {\n  box-sizing: border-box;\n  height: 40px;\n  padding-right: 12px;\n  padding-left: 12px;\n  background-color: #fff;\n  border-color: #D0D5DD;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  color: #344054;\n}\n.post-type-acf-field-group input[type=text]:focus,\n.post-type-acf-field-group input[type=search]:focus,\n.post-type-acf-field-group input[type=number]:focus,\n.post-type-acf-field-group textarea:focus,\n.post-type-acf-field-group select:focus {\n  outline: 3px solid #EBF5FA;\n  border-color: #399CCB;\n}\n.post-type-acf-field-group input[type=text]:disabled,\n.post-type-acf-field-group input[type=search]:disabled,\n.post-type-acf-field-group input[type=number]:disabled,\n.post-type-acf-field-group textarea:disabled,\n.post-type-acf-field-group select:disabled {\n  background-color: #F9FAFB;\n  color: #808a9e;\n}\n.post-type-acf-field-group input[type=text]::placeholder,\n.post-type-acf-field-group input[type=search]::placeholder,\n.post-type-acf-field-group input[type=number]::placeholder,\n.post-type-acf-field-group textarea::placeholder,\n.post-type-acf-field-group select::placeholder {\n  color: #98A2B3;\n}\n.post-type-acf-field-group input[type=text]:read-only {\n  background-color: #F9FAFB;\n  color: #98A2B3;\n}\n.post-type-acf-field-group .acf-field.acf-field-number .acf-label,\n.post-type-acf-field-group .acf-field.acf-field-number .acf-input input[type=number] {\n  max-width: 180px;\n}\n.post-type-acf-field-group textarea {\n  box-sizing: border-box;\n  padding-top: 10px;\n  padding-bottom: 10px;\n  height: 80px;\n  min-height: 56px;\n}\n.post-type-acf-field-group select {\n  min-width: 160px;\n  max-width: 100%;\n  padding-right: 40px;\n  padding-left: 12px;\n  background-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  background-position: right 10px top 50%;\n  background-size: 20px;\n}\n.post-type-acf-field-group select:hover, .post-type-acf-field-group select:focus {\n  color: #0783BE;\n}\n.post-type-acf-field-group select::before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 5px;\n  left: 5px;\n  width: 20px;\n  height: 20px;\n  background-color: red;\n}\n.post-type-acf-field-group.rtl select {\n  padding-right: 12px;\n  padding-left: 40px;\n  background-position: left 10px top 50%;\n}\n.post-type-acf-field-group input[type=radio],\n.post-type-acf-field-group input[type=checkbox] {\n  box-sizing: border-box;\n  width: 16px;\n  height: 16px;\n  padding: 0;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #98A2B3;\n  background: #fff;\n  box-shadow: none;\n}\n.post-type-acf-field-group input[type=radio]:hover,\n.post-type-acf-field-group input[type=checkbox]:hover {\n  background-color: #EBF5FA;\n  border-color: #0783BE;\n}\n.post-type-acf-field-group input[type=radio]:checked, .post-type-acf-field-group input[type=radio]:focus-visible,\n.post-type-acf-field-group input[type=checkbox]:checked,\n.post-type-acf-field-group input[type=checkbox]:focus-visible {\n  background-color: #EBF5FA;\n  border-color: #0783BE;\n}\n.post-type-acf-field-group input[type=radio]:checked:before, .post-type-acf-field-group input[type=radio]:focus-visible:before,\n.post-type-acf-field-group input[type=checkbox]:checked:before,\n.post-type-acf-field-group input[type=checkbox]:focus-visible:before {\n  content: \"\";\n  position: relative;\n  top: -1px;\n  left: -1px;\n  width: 16px;\n  height: 16px;\n  margin: 0;\n  padding: 0;\n  background-color: transparent;\n  background-size: cover;\n  background-repeat: no-repeat;\n  background-position: center;\n}\n.post-type-acf-field-group input[type=radio]:active,\n.post-type-acf-field-group input[type=checkbox]:active {\n  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 0px 0px rgba(255, 54, 54, 0.25);\n}\n.post-type-acf-field-group input[type=radio]:disabled,\n.post-type-acf-field-group input[type=checkbox]:disabled {\n  background-color: #F9FAFB;\n  border-color: #D0D5DD;\n}\n.post-type-acf-field-group.rtl input[type=radio]:checked:before, .post-type-acf-field-group.rtl input[type=radio]:focus-visible:before,\n.post-type-acf-field-group.rtl input[type=checkbox]:checked:before,\n.post-type-acf-field-group.rtl input[type=checkbox]:focus-visible:before {\n  left: 1px;\n}\n.post-type-acf-field-group input[type=radio]:checked:before, .post-type-acf-field-group input[type=radio]:focus:before {\n  background-image: url(\"../../images/field-states/radio-active.svg\");\n}\n.post-type-acf-field-group input[type=checkbox]:checked:before, .post-type-acf-field-group input[type=checkbox]:focus:before {\n  background-image: url(\"../../images/field-states/checkbox-active.svg\");\n}\n.post-type-acf-field-group .acf-radio-list li input[type=radio],\n.post-type-acf-field-group .acf-radio-list li input[type=checkbox],\n.post-type-acf-field-group .acf-checkbox-list li input[type=radio],\n.post-type-acf-field-group .acf-checkbox-list li input[type=checkbox] {\n  margin-right: 6px;\n}\n.post-type-acf-field-group .acf-radio-list.acf-bl li,\n.post-type-acf-field-group .acf-checkbox-list.acf-bl li {\n  margin-bottom: 8px;\n}\n.post-type-acf-field-group .acf-radio-list.acf-bl li:last-of-type,\n.post-type-acf-field-group .acf-checkbox-list.acf-bl li:last-of-type {\n  margin-bottom: 0;\n}\n.post-type-acf-field-group .acf-radio-list label,\n.post-type-acf-field-group .acf-checkbox-list label {\n  display: flex;\n  align-items: center;\n  align-content: center;\n}\n.post-type-acf-field-group .acf-switch {\n  width: 42px;\n  height: 24px;\n  border: none;\n  background-color: #D0D5DD;\n  border-radius: 12px;\n}\n.post-type-acf-field-group .acf-switch:hover {\n  background-color: #98A2B3;\n}\n.post-type-acf-field-group .acf-switch:active {\n  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 0px 0px rgba(255, 54, 54, 0.25);\n}\n.post-type-acf-field-group .acf-switch.-on {\n  background-color: #0783BE;\n}\n.post-type-acf-field-group .acf-switch.-on:hover {\n  background-color: #066998;\n}\n.post-type-acf-field-group .acf-switch.-on .acf-switch-slider {\n  left: 20px;\n}\n.post-type-acf-field-group .acf-switch .acf-switch-off,\n.post-type-acf-field-group .acf-switch .acf-switch-on {\n  visibility: hidden;\n}\n.post-type-acf-field-group .acf-switch .acf-switch-slider {\n  width: 20px;\n  height: 20px;\n  border: none;\n  border-radius: 100px;\n  box-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);\n}\n.post-type-acf-field-group .acf-field-true-false {\n  display: flex;\n  align-items: flex-start;\n}\n.post-type-acf-field-group .acf-field-true-false .acf-label {\n  order: 2;\n  display: block;\n  align-items: center;\n  margin-top: 2px;\n  margin-bottom: 0;\n  margin-left: 12px;\n}\n.post-type-acf-field-group .acf-field-true-false .acf-label label {\n  margin-bottom: 0;\n}\n.post-type-acf-field-group .acf-field-true-false .acf-label .acf-tip {\n  margin-left: 12px;\n}\n.post-type-acf-field-group .acf-field-true-false .acf-label .description {\n  display: block;\n  margin-top: 2px;\n  margin-left: 0;\n}\n.post-type-acf-field-group.rtl .acf-field-true-false .acf-label {\n  margin-right: 12px;\n  margin-left: 0;\n}\n.post-type-acf-field-group.rtl .acf-field-true-false .acf-tip {\n  margin-right: 12px;\n  margin-left: 0;\n}\n.post-type-acf-field-group input::file-selector-button {\n  box-sizing: border-box;\n  min-height: 40px;\n  margin-right: 16px;\n  padding-top: 8px;\n  padding-right: 16px;\n  padding-bottom: 8px;\n  padding-left: 16px;\n  background-color: transparent;\n  color: #0783BE !important;\n  border-radius: 6px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #0783BE;\n  text-decoration: none;\n}\n.post-type-acf-field-group input::file-selector-button:hover {\n  border-color: #066998;\n  cursor: pointer;\n  color: #066998 !important;\n}\n.post-type-acf-field-group .button {\n  display: inline-flex;\n  align-items: center;\n  height: 40px;\n  padding-right: 16px;\n  padding-left: 16px;\n  background-color: transparent;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #0783BE;\n  border-radius: 6px;\n  color: #0783BE;\n}\n.post-type-acf-field-group .button:hover {\n  background-color: #f3f9fc;\n  border-color: #0783BE;\n  color: #0783BE;\n}\n.post-type-acf-field-group .button:focus {\n  background-color: #f3f9fc;\n  outline: 3px solid #EBF5FA;\n  color: #0783BE;\n}\n.post-type-acf-field-group .edit-field-group-header {\n  display: block !important;\n}\n.post-type-acf-field-group .acf-input .select2-container.-acf .select2-selection {\n  border: none;\n  line-height: 1;\n}\n.post-type-acf-field-group .acf-input .select2-container.-acf .select2-selection__rendered {\n  box-sizing: border-box;\n  padding-right: 0;\n  padding-left: 0;\n  background-color: #fff;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #D0D5DD;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  color: #344054;\n}\n.post-type-acf-field-group .acf-input .select2-container--focus {\n  outline: 3px solid #EBF5FA;\n  border-color: #399CCB;\n  border-radius: 6px;\n}\n.post-type-acf-field-group .acf-input .select2-container--focus .select2-selection__rendered {\n  border-color: #399CCB !important;\n}\n.post-type-acf-field-group .acf-input .select2-container--focus.select2-container--below.select2-container--open .select2-selection__rendered {\n  border-bottom-right-radius: 0 !important;\n  border-bottom-left-radius: 0 !important;\n}\n.post-type-acf-field-group .acf-input .select2-container--focus.select2-container--above.select2-container--open .select2-selection__rendered {\n  border-top-right-radius: 0 !important;\n  border-top-left-radius: 0 !important;\n}\n.post-type-acf-field-group .acf-input .select2-container .select2-search--inline .select2-search__field {\n  margin: 0;\n  padding-left: 6px;\n}\n.post-type-acf-field-group .acf-input .select2-container .select2-search--inline .select2-search__field:focus {\n  outline: none;\n  border: none;\n}\n.post-type-acf-field-group .acf-input .select2-container--default .select2-selection--multiple .select2-selection__rendered {\n  padding-top: 0;\n  padding-right: 6px;\n  padding-bottom: 0;\n  padding-left: 6px;\n}\n.post-type-acf-field-group .acf-input .select2-selection__clear {\n  width: 18px;\n  height: 18px;\n  margin-top: 12px;\n  margin-right: 0;\n  text-indent: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.post-type-acf-field-group .acf-input .select2-selection__clear:before {\n  content: \"\";\n  display: block;\n  width: 14px;\n  height: 14px;\n  top: 0;\n  left: 0;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-close.svg\");\n  mask-image: url(\"../../images/icons/icon-close.svg\");\n  background-color: #98A2B3;\n}\n.post-type-acf-field-group .acf-input .select2-selection__clear:hover::before {\n  background-color: #1D2939;\n}\n.post-type-acf-field-group .acf-label {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n.post-type-acf-field-group .acf-label .acf-icon-help {\n  width: 18px;\n  height: 18px;\n  background-color: #98A2B3;\n}\n.post-type-acf-field-group .acf-label label {\n  margin-bottom: 0;\n}\n.post-type-acf-field-group .acf-label .description {\n  margin-top: 2px;\n}\n.post-type-acf-field-group .acf-field-setting-name .acf-tip {\n  position: absolute;\n  top: 0;\n  left: 654px;\n  color: #98A2B3;\n}\n.post-type-acf-field-group .acf-field-setting-name .acf-tip .acf-icon-help {\n  width: 18px;\n  height: 18px;\n}\n\n.rtl.post-type-acf-field-group .acf-field-setting-name .acf-tip {\n  left: auto;\n  right: 654px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Container sizes\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .metabox-holder.columns-1 #acf-field-group-fields,\n.post-type-acf-field-group .metabox-holder.columns-1 #acf-field-group-options,\n.post-type-acf-field-group .metabox-holder.columns-1 .meta-box-sortables.ui-sortable,\n.post-type-acf-field-group .metabox-holder.columns-1 .notice {\n  max-width: 1440px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Max width for notices in 1 column edit field group layout\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group.columns-1 .notice {\n  max-width: 1440px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Widen edit field group headerbar for 2 column layout\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group.columns-2 .acf-headerbar .acf-headerbar-inner {\n  max-width: 100%;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Post stuff\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group #poststuff {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap {\n  overflow: hidden;\n  border: none;\n  border-radius: 0 0 8px 8px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty {\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty .acf-thead,\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty .acf-tfoot {\n  display: none;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap.-empty .no-fields-message {\n  min-height: 280px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table header\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-thead {\n  background-color: #F9FAFB;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n.post-type-acf-field-group .acf-thead li {\n  display: flex;\n  align-items: center;\n  min-height: 48px;\n  padding-top: 0;\n  padding-bottom: 0;\n  color: #344054;\n  font-weight: 500;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table body\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-field-object {\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.post-type-acf-field-group .acf-field-object:hover .acf-sortable-handle:before {\n  display: inline-flex;\n}\n.post-type-acf-field-group .acf-field-object.acf-field-is-endpoint:before {\n  display: block;\n  content: \"\";\n  height: 2px;\n  width: 100%;\n  background: #D0D5DD;\n  margin-top: -1px;\n}\n.post-type-acf-field-group .acf-field-object.acf-field-is-endpoint.acf-field-object-accordion:before {\n  display: none;\n}\n.post-type-acf-field-group .acf-field-object.acf-field-is-endpoint.acf-field-object-accordion:after {\n  display: block;\n  content: \"\";\n  height: 2px;\n  width: 100%;\n  background: #D0D5DD;\n  z-index: 500;\n}\n.post-type-acf-field-group .acf-field-object:hover {\n  background-color: #f7fbfd;\n}\n.post-type-acf-field-group .acf-field-object.open {\n  background-color: #fff;\n  border-top-color: #A5D2E7;\n}\n.post-type-acf-field-group .acf-field-object.open .handle {\n  background-color: #D8EBF5;\n  border: none;\n  text-shadow: none;\n}\n.post-type-acf-field-group .acf-field-object.open .handle a {\n  color: #0783BE !important;\n}\n.post-type-acf-field-group .acf-field-object.open .handle a.delete-field {\n  color: #a00 !important;\n}\n.post-type-acf-field-group .acf-field-object ul.acf-hl {\n  display: flex;\n  align-items: stretch;\n}\n.post-type-acf-field-group .acf-field-object .handle li {\n  display: flex;\n  align-items: top;\n  flex-wrap: wrap;\n  min-height: 60px;\n  color: #344054;\n}\n.post-type-acf-field-group .acf-field-object .handle li.li-field-label {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n  width: auto;\n}\n.post-type-acf-field-group .acf-field-object .handle li.li-field-label strong {\n  font-weight: 500;\n}\n.post-type-acf-field-group .acf-field-object .handle li.li-field-label .row-options {\n  width: 100%;\n}\n/*----------------------------------------------------------------------------\n*\n*  Table footer\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-tfoot {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  min-height: 80px;\n  box-sizing: border-box;\n  padding-top: 8px;\n  padding-right: 24px;\n  padding-bottom: 8px;\n  padding-left: 24px;\n  background-color: #fff;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.post-type-acf-field-group .acf-tfoot .acf-fr {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Edit field settings\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-field-object .settings {\n  box-sizing: border-box;\n  padding-top: 0;\n  padding-bottom: 0;\n  background-color: #fff;\n  border-left-width: 4px;\n  border-left-style: solid;\n  border-left-color: #6BB5D8;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Main field settings container\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-main {\n  padding-top: 32px;\n  padding-right: 0;\n  padding-bottom: 32px;\n  padding-left: 0;\n}\n.acf-field-settings-main .acf-field:last-of-type {\n  margin-bottom: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field label\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-label {\n  display: block;\n  justify-content: space-between;\n  align-items: center;\n  align-content: center;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 6px;\n  margin-left: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Single field\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field {\n  box-sizing: border-box;\n  width: 100%;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 32px;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 72px;\n  padding-bottom: 0;\n  padding-left: 72px;\n}\n@media screen and (max-width: 600px) {\n  .acf-field-settings .acf-field {\n    padding-right: 12px;\n    padding-left: 12px;\n  }\n}\n.acf-field-settings .acf-field .acf-label,\n.acf-field-settings .acf-field .acf-input {\n  max-width: 600px;\n}\n.acf-field-settings .acf-field .acf-label.acf-input-sub,\n.acf-field-settings .acf-field .acf-input.acf-input-sub {\n  max-width: 100%;\n}\n.acf-field-settings .acf-field .acf-input-wrap {\n  overflow: visible;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field separators\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field.acf-field-setting-label,\n.acf-field-settings .acf-field-setting-wrapper {\n  padding-top: 24px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n\n.acf-field-settings .acf-field-setting-wrapper {\n  margin-top: 24px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Edit fields footer\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field-settings-footer {\n  display: flex;\n  align-items: center;\n  min-height: 72px;\n  box-sizing: border-box;\n  width: 100%;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 72px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n@media screen and (max-width: 600px) {\n  .acf-field-settings .acf-field-settings-footer {\n    padding-left: 12px;\n  }\n}\n\n.rtl .acf-field-settings .acf-field-settings-footer {\n  padding-top: 0;\n  padding-right: 72px;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Tabs\n*\n*----------------------------------------------------------------------------*/\n.acf-fields .acf-tab-wrap {\n  background: #F9FAFB;\n  border-bottom-color: #1D2939;\n}\n.acf-fields .acf-tab-wrap .acf-tab-group {\n  padding-right: 24px;\n  padding-left: 24px;\n  border-top-width: 0;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n.acf-fields .acf-field-settings-tab-bar,\n.acf-fields .acf-tab-wrap .acf-tab-group {\n  display: flex;\n  align-items: stretch;\n  min-height: 48px;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 24px;\n  margin-top: 0;\n  margin-bottom: 0;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n.acf-fields .acf-field-settings-tab-bar li,\n.acf-fields .acf-tab-wrap .acf-tab-group li {\n  display: flex;\n  align-items: center;\n  margin-top: 0;\n  margin-right: 24px;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding: 0;\n}\n.acf-fields .acf-field-settings-tab-bar li a,\n.acf-fields .acf-tab-wrap .acf-tab-group li a {\n  box-sizing: border-box;\n  display: inline-flex;\n  align-items: center;\n  height: 100%;\n  padding-top: 3px;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  background: none;\n  border-top: none;\n  border-right: none;\n  border-bottom-width: 3px;\n  border-bottom-style: solid;\n  border-bottom-color: transparent;\n  border-left: none;\n  color: #667085;\n  font-weight: normal;\n}\n.acf-fields .acf-field-settings-tab-bar li a:hover,\n.acf-fields .acf-tab-wrap .acf-tab-group li a:hover {\n  color: #1D2939;\n}\n.acf-fields .acf-field-settings-tab-bar li a:hover,\n.acf-fields .acf-tab-wrap .acf-tab-group li a:hover {\n  background-color: transparent;\n}\n.acf-fields .acf-field-settings-tab-bar li.active a,\n.acf-fields .acf-tab-wrap .acf-tab-group li.active a {\n  background: none;\n  border-bottom-color: #0783BE;\n  color: #1D2939;\n}\n\n#acf-field-group-options .acf-fields .acf-tab-wrap .acf-tab-group li.active a {\n  padding-top: 2px;\n}\n\n.acf-field-editor .acf-field-settings-tab-bar {\n  padding-left: 72px;\n}\n@media screen and (max-width: 600px) {\n  .acf-field-editor .acf-field-settings-tab-bar {\n    padding-left: 12px;\n  }\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field group settings\n*\n*----------------------------------------------------------------------------*/\n#acf-field-group-options .field-group-settings-tab {\n  padding-top: 24px;\n  padding-right: 24px;\n  padding-bottom: 24px;\n  padding-left: 24px;\n}\n#acf-field-group-options .field-group-settings-tab .acf-field:last-of-type {\n  padding: 0;\n}\n#acf-field-group-options .acf-field {\n  border: none;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 24px;\n  padding-left: 0;\n}\n#acf-field-group-options .field-group-setting-split-container {\n  display: flex;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n#acf-field-group-options .field-group-setting-split-container .field-group-setting-split {\n  box-sizing: border-box;\n  padding-top: 24px;\n  padding-right: 24px;\n  padding-bottom: 24px;\n  padding-left: 24px;\n}\n#acf-field-group-options .field-group-setting-split-container .field-group-setting-split:nth-child(1) {\n  flex: 1 0 auto;\n}\n#acf-field-group-options .field-group-setting-split-container .field-group-setting-split:nth-child(2n) {\n  flex: 1 0 auto;\n  max-width: 320px;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 32px;\n  padding-right: 32px;\n  padding-left: 32px;\n  border-left-width: 1px;\n  border-left-style: solid;\n  border-left-color: #EAECF0;\n}\n#acf-field-group-options .acf-field[data-name=description] {\n  max-width: 600px;\n}\n#acf-field-group-options .acf-button-group {\n  display: inline-flex;\n}\n\n.rtl #acf-field-group-options .field-group-setting-split-container .field-group-setting-split:nth-child(2n) {\n  margin-right: 32px;\n  margin-left: 0;\n  border-left: none;\n  border-right-width: 1px;\n  border-right-style: solid;\n  border-right-color: #EAECF0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Reorder handles\n*\n*----------------------------------------------------------------------------*/\n.acf-field-list .li-field-order {\n  padding: 0;\n  display: flex;\n  flex-direction: row;\n  flex-wrap: nowrap;\n  justify-content: center;\n  align-content: stretch;\n  align-items: stretch;\n  background-color: transparent;\n}\n.acf-field-list .acf-sortable-handle {\n  display: flex;\n  flex-direction: row;\n  flex-wrap: nowrap;\n  justify-content: center;\n  align-content: flex-start;\n  align-items: flex-start;\n  width: 100%;\n  height: 100%;\n  position: relative;\n  padding-top: 11px;\n  padding-bottom: 8px;\n  background-color: transparent;\n  border: none;\n  border-radius: 0;\n}\n.acf-field-list .acf-sortable-handle:hover {\n  cursor: grab;\n}\n.acf-field-list .acf-sortable-handle:before {\n  content: \"\";\n  display: none;\n  position: absolute;\n  top: 16px;\n  left: 8px;\n  width: 16px;\n  height: 16px;\n  width: 12px;\n  height: 12px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n  -webkit-mask-image: url(\"../../images/icons/icon-draggable.svg\");\n  mask-image: url(\"../../images/icons/icon-draggable.svg\");\n}\n\n.rtl .acf-field-list .acf-sortable-handle:before {\n  left: 0;\n  right: 8px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Expand / collapse field icon\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object .li-field-label {\n  position: relative;\n  padding-left: 40px;\n}\n.acf-field-object .li-field-label:before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  left: 6px;\n  display: inline-flex;\n  width: 18px;\n  height: 18px;\n  margin-top: -2px;\n  background-color: #667085;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n}\n.acf-field-object .li-field-label:hover:before {\n  cursor: pointer;\n}\n\n.rtl .acf-field-object .li-field-label {\n  padding-left: 0;\n  padding-right: 40px;\n}\n.rtl .acf-field-object .li-field-label:before {\n  left: 0;\n  right: 6px;\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n}\n.rtl .acf-field-object.open .li-field-label:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n}\n.rtl .acf-field-object.open .acf-input-sub .li-field-label:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n}\n.rtl .acf-field-object.open .acf-input-sub .acf-field-object.open .li-field-label:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n}\n\n.acf-thead .li-field-label {\n  padding-left: 40px;\n}\n.rtl .acf-thead .li-field-label {\n  padding-left: 0;\n  padding-right: 40px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Conditional logic layout\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-main-conditional-logic .acf-conditional-toggle {\n  display: flex;\n  padding-right: 72px;\n  padding-left: 72px;\n}\n@media screen and (max-width: 600px) {\n  .acf-field-settings-main-conditional-logic .acf-conditional-toggle {\n    padding-left: 12px;\n  }\n}\n.acf-field-settings-main-conditional-logic .acf-field {\n  flex-wrap: wrap;\n  margin-bottom: 0;\n  padding-right: 0;\n  padding-left: 0;\n}\n.acf-field-settings-main-conditional-logic .acf-field .rule-groups {\n  flex: 0 1 100%;\n  order: 3;\n  margin-top: 32px;\n  padding-top: 32px;\n  padding-right: 72px;\n  padding-left: 72px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n@media screen and (max-width: 600px) {\n  .acf-field-settings-main-conditional-logic .acf-field .rule-groups {\n    padding-left: 12px;\n  }\n  .acf-field-settings-main-conditional-logic .acf-field .rule-groups table.acf-table tbody tr {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: flex-start;\n    align-content: flex-start;\n    align-items: flex-start;\n  }\n  .acf-field-settings-main-conditional-logic .acf-field .rule-groups table.acf-table tbody tr td {\n    flex: 1 1 100%;\n  }\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Prefix & append styling\n*\n*----------------------------------------------------------------------------*/\n.acf-input .acf-input-prepend,\n.acf-input .acf-input-append {\n  display: inline-flex;\n  align-items: center;\n  height: 100%;\n  min-height: 40px;\n  padding-right: 12px;\n  padding-left: 12px;\n  background-color: #F9FAFB;\n  border-color: #D0D5DD;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  color: #667085;\n}\n.acf-input .acf-input-prepend {\n  border-radius: 6px 0 0 6px;\n}\n.acf-input .acf-input-append {\n  border-radius: 0 6px 6px 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  ACF input wrap\n*\n*----------------------------------------------------------------------------*/\n.acf-input-wrap {\n  display: flex;\n}\n\n.acf-field-settings-main-presentation .acf-input-wrap {\n  display: flex;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Empty state\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message {\n  display: flex;\n  justify-content: center;\n  padding-top: 48px;\n  padding-bottom: 48px;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  align-content: center;\n  align-items: flex-start;\n  text-align: center;\n  max-width: 400px;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner img,\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner h2,\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p {\n  flex: 1 0 100%;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner h2 {\n  margin-top: 32px;\n  margin-bottom: 0;\n  padding: 0;\n  color: #344054;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p {\n  margin-top: 12px;\n  margin-bottom: 0;\n  padding: 0;\n  color: #667085;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner p.acf-small {\n  margin-top: 32px;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner img {\n  max-width: 284px;\n  margin-bottom: 0;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message .no-fields-message-inner .acf-btn {\n  margin-top: 32px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Hide add title prompt label\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-headerbar #title-prompt-text {\n  display: none;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Modal styling\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-popup .acf-popup-box {\n  min-width: 480px;\n}\n.post-type-acf-field-group #acf-popup .acf-popup-box .title {\n  display: flex;\n  align-items: center;\n  align-content: center;\n  justify-content: space-between;\n  min-height: 64px;\n  box-sizing: border-box;\n  margin: 0;\n  padding-right: 24px;\n  padding-left: 24px;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n.post-type-acf-field-group #acf-popup .acf-popup-box .title h1,\n.post-type-acf-field-group #acf-popup .acf-popup-box .title h2,\n.post-type-acf-field-group #acf-popup .acf-popup-box .title h3,\n.post-type-acf-field-group #acf-popup .acf-popup-box .title h4 {\n  padding-left: 0;\n  color: #344054;\n}\n.post-type-acf-field-group #acf-popup .acf-popup-box .title .acf-icon {\n  display: block;\n  position: relative;\n  top: auto;\n  right: auto;\n  width: 22px;\n  height: 22px;\n  background-color: transparent;\n  color: transparent;\n}\n.post-type-acf-field-group #acf-popup .acf-popup-box .title .acf-icon:before {\n  display: inline-flex;\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 22px;\n  height: 22px;\n  background-color: #667085;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n  -webkit-mask-image: url(\"../../images/icons/icon-close-circle.svg\");\n  mask-image: url(\"../../images/icons/icon-close-circle.svg\");\n}\n.post-type-acf-field-group #acf-popup .acf-popup-box .title .acf-icon:hover:before {\n  background-color: #0783BE;\n}\n.post-type-acf-field-group #acf-popup .acf-popup-box .inner {\n  box-sizing: border-box;\n  margin: 0;\n  padding-top: 24px;\n  padding-right: 24px;\n  padding-bottom: 24px;\n  padding-left: 24px;\n  border-top: none;\n}\n.post-type-acf-field-group #acf-popup .acf-popup-box .inner p {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.post-type-acf-field-group #acf-popup .acf-popup-box #acf-move-field-form .acf-field-select {\n  margin-top: 0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Hide original #post-body-content from edit field group page\n*\n*----------------------------------------------------------------------------*/\n.acf-admin-single-field-group #post-body-content {\n  display: none;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Settings section footer\n*\n*----------------------------------------------------------------------------*/\n.acf-field-group-settings-footer {\n  display: flex;\n  justify-content: space-between;\n  align-content: stretch;\n  align-items: center;\n  position: relative;\n  min-height: 88px;\n  margin-right: -24px;\n  margin-bottom: -24px;\n  margin-left: -24px;\n  padding-right: 24px;\n  padding-left: 24px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.acf-field-group-settings-footer .acf-created-on {\n  display: inline-flex;\n  justify-content: flex-start;\n  align-content: stretch;\n  align-items: center;\n  color: #667085;\n}\n.acf-field-group-settings-footer .acf-created-on:before {\n  content: \"\";\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-time.svg\");\n  mask-image: url(\"../../images/icons/icon-time.svg\");\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Conditional logic enabled badge\n*\n*----------------------------------------------------------------------------*/\n.conditional-logic-badge {\n  display: none;\n}\n.conditional-logic-badge.is-enabled {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  overflow: hidden;\n  margin-left: 8px;\n  background-color: rgba(82, 170, 89, 0.4);\n  border-width: 1px;\n  border-style: solid;\n  border-color: #52AA59;\n  border-radius: 100px;\n  text-indent: 100%;\n  white-space: nowrap;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Split field settings\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-split {\n  display: flex;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.acf-field-settings-split .acf-field {\n  margin: 0;\n  padding-top: 32px;\n  padding-bottom: 32px;\n}\n.acf-field-settings-split .acf-field:nth-child(2n) {\n  border-left-width: 1px;\n  border-left-style: solid;\n  border-left-color: #EAECF0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Display & return format\n*\n*----------------------------------------------------------------------------*/\n.acf-field-setting-display_format .acf-label,\n.acf-field-setting-return_format .acf-label {\n  margin-bottom: 6px;\n}\n.acf-field-setting-display_format .acf-radio-list li,\n.acf-field-setting-return_format .acf-radio-list li {\n  display: flex;\n}\n.acf-field-setting-display_format .acf-radio-list li label,\n.acf-field-setting-return_format .acf-radio-list li label {\n  display: inline-flex;\n  width: 100%;\n}\n.acf-field-setting-display_format .acf-radio-list li label span,\n.acf-field-setting-return_format .acf-radio-list li label span {\n  flex: 1 1 auto;\n}\n.acf-field-setting-display_format .acf-radio-list li label code,\n.acf-field-setting-return_format .acf-radio-list li label code {\n  padding-right: 8px;\n  padding-left: 8px;\n  background-color: #F2F4F7;\n  border-radius: 4px;\n  color: #475467;\n}\n.acf-field-setting-display_format .acf-radio-list li input[type=text],\n.acf-field-setting-return_format .acf-radio-list li input[type=text] {\n  height: 32px;\n}\n\n.acf-field-settings .acf-field-setting-first_day {\n  padding-top: 32px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Image and Gallery fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-image .acf-hl[data-cols=\"3\"] > li,\n.acf-field-object-gallery .acf-hl[data-cols=\"3\"] > li {\n  width: auto;\n}\n\n/*----------------------------------------------------------------------------\n*\n* Appended fields fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field-appended {\n  overflow: auto;\n}\n.acf-field-settings .acf-field-appended .acf-input {\n  float: left;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Flexible widths for image minimum / maximum size fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field.acf-field-setting-min_width .acf-input,\n.acf-field-settings .acf-field.acf-field-setting-max_width .acf-input {\n  max-width: none;\n}\n.acf-field-settings .acf-field.acf-field-setting-min_width .acf-input-wrap input[type=text],\n.acf-field-settings .acf-field.acf-field-setting-max_width .acf-input-wrap input[type=text] {\n  max-width: 81px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Temporary fix to hide pagination setting for repeaters used as subfields.\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-field-object-flexible-content .acf-field-setting-pagination {\n  display: none;\n}\n.post-type-acf-field-group .acf-field-object-repeater .acf-field-object-repeater .acf-field-setting-pagination {\n  display: none;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Flexible content field width\n*\n*----------------------------------------------------------------------------*/\n.acf-admin-single-field-group .acf-field-object-flexible-content .acf-is-subfields .acf-field-object .acf-label,\n.acf-admin-single-field-group .acf-field-object-flexible-content .acf-is-subfields .acf-field-object .acf-input {\n  max-width: 600px;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Fix default value checkbox focus state\n*\n*----------------------------------------------------------------------------*/\n.acf-admin-single-field-group .acf-field.acf-field-true-false.acf-field-setting-default_value .acf-true-false {\n  border: none;\n}\n.acf-admin-single-field-group .acf-field.acf-field-true-false.acf-field-setting-default_value .acf-true-false input[type=checkbox] {\n  margin-right: 0;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Sub-fields layout\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub {\n  max-width: 100%;\n  overflow: hidden;\n  border-radius: 8px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #dbdfe5;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-sub-field-list-header {\n  display: flex;\n  justify-content: space-between;\n  align-content: stretch;\n  align-items: center;\n  min-height: 64px;\n  padding-right: 24px;\n  padding-left: 24px;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-list-wrap {\n  box-shadow: none;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-hl.acf-tfoot {\n  min-height: 64px;\n  align-items: center;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input.acf-input-sub {\n  max-width: 100%;\n  margin-right: 0;\n  margin-left: 0;\n}\n\n.post-type-acf-field-group .acf-input-sub .acf-field-object .acf-sortable-handle {\n  width: 100%;\n  height: 100%;\n}\n\n.post-type-acf-field-group .acf-field-object:hover .acf-input-sub .acf-sortable-handle:before {\n  display: none;\n}\n\n.post-type-acf-field-group .acf-field-object:hover .acf-input-sub .acf-field-list .acf-field-object:hover .acf-sortable-handle:before {\n  display: block;\n}\n\n.post-type-acf-field-group .acf-field-object .acf-is-subfields .acf-thead .li-field-label:before {\n  display: none;\n}\n\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object.open {\n  border-top-color: #dbdfe5;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Flexible content field\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group i.acf-icon.-duplicate.duplicate-layout {\n  margin: 0 auto !important;\n  background-color: #667085;\n  color: #667085;\n}\n.post-type-acf-field-group i.acf-icon.acf-icon-trash.delete-layout {\n  margin: 0 auto !important;\n  background-color: #667085;\n  color: #667085;\n}\n.post-type-acf-field-group button.acf-btn.acf-btn-tertiary.acf-field-setting-fc-duplicate, .post-type-acf-field-group button.acf-btn.acf-btn-tertiary.acf-field-setting-fc-delete {\n  background-color: #ffffff !important;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  width: 32px;\n  height: 32px !important;\n  min-height: 32px;\n  padding: 0;\n}\n.post-type-acf-field-group button.add-layout.acf-btn.acf-btn-primary.add-field,\n.post-type-acf-field-group .acf-sub-field-list-header a.acf-btn.acf-btn-secondary.add-field,\n.post-type-acf-field-group .acf-field-list-wrap.acf-is-subfields a.acf-btn.acf-btn-secondary.add-field {\n  height: 32px !important;\n  min-height: 32px;\n  margin-left: 5px;\n}\n.post-type-acf-field-group .acf-field.acf-field-setting-fc_layout {\n  background-color: #ffffff;\n  margin-bottom: 16px;\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout {\n  overflow: hidden;\n  width: calc(100% - 144px);\n  margin-right: 72px;\n  margin-left: 72px;\n  padding-right: 0;\n  padding-left: 0;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #dbdfe5;\n  border-radius: 8px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-field-layout-settings.open {\n  background-color: #ffffff;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n@media screen and (max-width: 768px) {\n  .post-type-acf-field-group .acf-field-setting-fc_layout {\n    width: calc(100% - 16px);\n    margin-right: 8px;\n    margin-left: 8px;\n  }\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-input-sub {\n  max-width: 100%;\n  margin-right: 0;\n  margin-left: 0;\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-label,\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-input {\n  max-width: 100% !important;\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-input-sub {\n  margin-right: 32px;\n  margin-bottom: 32px;\n  margin-left: 32px;\n}\n.post-type-acf-field-group .acf-field-setting-fc_layout .acf-fc-meta {\n  max-width: 100%;\n  padding-top: 24px;\n  padding-right: 32px;\n  padding-left: 32px;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head {\n  background-color: #F9FAFB;\n  border-radius: 8px 8px 0px 0px;\n  display: flex;\n  min-height: 64px;\n  margin-bottom: 0px;\n  padding-right: 24px;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fc_draggable {\n  min-height: 64px;\n  padding-left: 24px;\n  display: flex;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head span.toggle-indicator {\n  pointer-events: none;\n  margin-top: 7px;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head label {\n  display: inline-flex;\n  align-items: center;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head label:before {\n  content: \"\";\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n}\n.rtl.post-type-acf-field-group .acf-field-settings-fc_head label:before {\n  padding-right: 10px;\n}\n\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions {\n  display: flex;\n  align-items: center;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions .acf-fc-add-layout {\n  margin-left: 10px;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions .acf-fc-add-layout .add-field {\n  margin-left: 0px !important;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions li {\n  margin-right: 4px;\n}\n.post-type-acf-field-group .acf-field-settings-fc_head .acf-fl-actions li:last-of-type {\n  margin-right: 0;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Field open / closed icon state\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-field-object.open > .handle > .acf-tbody > .li-field-label::before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Different coloured levels (current 5 supported)\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object .handle {\n  background-color: transparent;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object .handle:hover {\n  background-color: #f9f2fb;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object.open .handle {\n  background-color: #f5eaf9;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object .settings {\n  border-left-color: #BF7DD7;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object .handle {\n  background-color: transparent;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object .handle:hover {\n  background-color: #ebf7f4;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object.open .handle {\n  background-color: #e3f4f0;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-field-object .settings {\n  border-left-color: #7CCDB9;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle {\n  background-color: transparent;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle:hover {\n  background-color: #fcf5f2;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object.open .handle {\n  background-color: #fbeee9;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .settings {\n  border-left-color: #E29473;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle {\n  background-color: transparent;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .handle:hover {\n  background-color: #fafbfb;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object.open .handle {\n  background-color: #f4f6f7;\n}\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-input-sub .acf-input-sub .acf-input-sub .acf-field-object .settings {\n  border-left-color: #A3B1B9;\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n\n/* colors */\n$acf_blue: #2a9bd9;\n$acf_notice: #2a9bd9;\n$acf_error: #d94f4f;\n$acf_success: #49ad52;\n$acf_warning: #fd8d3b;\n\n/* acf-field */\n$field_padding: 15px 12px;\n$field_padding_x: 12px;\n$field_padding_y: 15px;\n$fp: 15px 12px;\n$fy: 15px;\n$fx: 12px;\n\n/* responsive */\n$md: 880px;\n$sm: 640px;\n\n// Admin.\n$wp-card-border: #ccd0d4;\t\t\t// Card border.\n$wp-card-border-1: #d5d9dd;\t\t  // Card inner border 1: Structural (darker).\n$wp-card-border-2: #eeeeee;\t\t  // Card inner border 2: Fields (lighter).\n$wp-input-border: #7e8993;\t\t   // Input border.\n\n// Admin 3.8\n$wp38-card-border: #E5E5E5;\t\t  // Card border.\n$wp38-card-border-1: #dfdfdf;\t\t// Card inner border 1: Structural (darker).\n$wp38-card-border-2: #eeeeee;\t\t// Card inner border 2: Fields (lighter).\n$wp38-input-border: #dddddd;\t\t // Input border.\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Grays\n$gray-50:  #F9FAFB;\n$gray-100: #F2F4F7;\n$gray-200: #EAECF0;\n$gray-300: #D0D5DD;\n$gray-400: #98A2B3;\n$gray-500: #667085;\n$gray-600: #475467;\n$gray-700: #344054;\n$gray-800: #1D2939;\n$gray-900: #101828;\n\n// Blues\n$blue-50:  #EBF5FA;\n$blue-100: #D8EBF5;\n$blue-200: #A5D2E7;\n$blue-300: #6BB5D8;\n$blue-400: #399CCB;\n$blue-500: #0783BE;\n$blue-600: #066998;\n$blue-700: #044E71;\n$blue-800: #033F5B;\n$blue-900: #032F45;\n\n// Utility\n$color-info:\t#2D69DA;\n$color-success:\t#52AA59;\n$color-warning:\t#F79009;\n$color-danger:\t#D13737;\n\n$color-primary: $blue-500;\n$color-primary-hover: $blue-600;\n$color-secondary: $gray-500;\n$color-secondary-hover: $gray-400;\n\n// Gradients\n$gradient-pro: linear-gradient(90.52deg, #2C9FB8 0.44%, #A45CFF 113.3%);\n\n// Border radius\n$radius-sm:\t4px;\n$radius-md: 6px;\n$radius-lg: 8px;\n\n// Elevations / Box shadows\n$elevation-01: 0px 1px 2px rgba($gray-900, 0.10);\n\n// Input & button focus outline\n$outline: 3px solid $blue-50;\n\n// Link colours\n$link-color: $blue-500;\n\n// Responsive\n$max-width: 1440px;", "/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n@mixin clearfix() {\n\t&:after {\n\t\tdisplay: block;\n\t\tclear: both;\n\t\tcontent: \"\";\n\t}\n}\n\n@mixin border-box() {\n\t-webkit-box-sizing: border-box;\n\t-moz-box-sizing: border-box;\n\tbox-sizing: border-box;\n}\n\n@mixin centered() {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n}\n\n@mixin animate( $properties: 'all' ) {\n\t-webkit-transition: $properties 0.3s ease;  // Safari 3.2+, Chrome\n    -moz-transition: $properties 0.3s ease;  \t// Firefox 4-15\n    -o-transition: $properties 0.3s ease;  \t\t// Opera 10.5–12.00\n    transition: $properties 0.3s ease;  \t\t// Firefox 16+, Opera 12.50+\n}\n\n@mixin rtl() {\n\thtml[dir=\"rtl\"] & {\n\t\ttext-align: right;\n\t\t@content;\n\t}\n}\n\n@mixin wp-admin( $version: '3-8' ) {\n\t.acf-admin-#{$version} & {\n\t\t@content;\n\t}\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tField Group\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Reset postbox inner padding.\n#acf-field-group-fields > .inside,\n#acf-field-group-locations > .inside,\n#acf-field-group-options > .inside {\n\tpadding: 0;\n\tmargin: 0;\n}\n\n// Hide metabox order buttons added in WP 5.5.\n.postbox {\n\t.handle-order-higher,\n\t.handle-order-lower {\n\t\tdisplay: none;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Postbox: Publish\n*\n*----------------------------------------------------------------------------*/\n#minor-publishing-actions,\n#misc-publishing-actions #visibility,\n#misc-publishing-actions .edit-timestamp {\n\tdisplay: none;\n}\n\n#minor-publishing {\n\tborder-bottom: 0 none;\n}\n\n#misc-pub-section {\n\tborder-bottom: 0 none;\n}\n\n#misc-publishing-actions .misc-pub-section {\n\tborder-bottom-color: #F5F5F5;\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*  Postbox: Fields\n*\n*----------------------------------------------------------------------------*/\n#acf-field-group-fields {\n\tborder: 0 none;\n\n\t.inside {\n\t\tborder-top: {\n\t\t\twidth: 0;\n\t\t\tstyle: none;\n\t\t};\n\t}\n\n\t/* links */\n\ta {\n\t\ttext-decoration: none;\n\t}\n\n\t/* Field type */\n\t.li-field-type {\n\n\t\t.field-type-icon {\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t};\n\n\t\t\t@media screen and (max-width: 600px) {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/* table header */\n\t.li-field-order {\n\t\twidth: 64px;\n\t\tjustify-content: center;\n\n\t\t@media screen and (max-width: $md) {\n\t\t\twidth: 32px;\n\t\t}\n\n\t}\n\t.li-field-label { width: calc(50% - 64px); }\n\t.li-field-name { width: 25%; word-break: break-word; }\n\t.li-field-key { display: none; }\n\t.li-field-type { width: 25%; }\n\n\t/* show keys */\n\t&.show-field-keys {\n\n\t\t.li-field-label { width: calc(35% - 64px); };\n\t\t.li-field-name { width: 15%; };\n\t\t.li-field-key { width: 25%;  display: flex;  };\n\t\t.li-field-type { width: 25%; };\n\n\t}\n\n\t/* hide tabs */\n\t&.hide-tabs {\n\t\t.acf-field-settings-tab-bar {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t.acf-field-settings-main {\n\t\t\tpadding: 0;\n\n\t\t\t&.acf-field-settings-main-general {\n\t\t\t\tpadding-top: 32px;\n\t\t\t}\n\n\t\t\t.acf-field {\n\t\t\t\tmargin-bottom: 32px;\n\t\t\t}\n\n\t\t\t.acf-field-setting-wrapper {\n\t\t\t\tpadding-top: 0;\n\t\t\t\tborder-top: none;\n\t\t\t}\n\n\t\t\t.acf-field-settings-split .acf-field {\n\t\t\t\tborder-bottom: {\n\t\t\t\t\twidth: 1px;\n\t\t\t\t\tstyle: solid;\n\t\t\t\t\tcolor: $gray-200;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-field-setting-first_day {\n\t\t\t\tpadding-top: 0;\n\t\t\t\tborder-top: none;\n\t\t\t}\n\t\t}\n\n\t\t.acf-field-settings-footer {\n\t\t\tmargin-top: 32px;\n\t\t}\n\t}\n\n\t/* fields */\n\t.acf-field-list-wrap {\n\t\tborder: $wp-card-border solid 1px;\n\t}\n\n\t.acf-field-list {\n\t\tbackground: #f5f5f5;\n\t\tmargin-top: -1px;\n\n\t\t.acf-tbody {\n\n\t\t\t> .li-field-name,\n\t\t\t> .li-field-key {\n\t\t\t\talign-items: flex-start;\n\t\t\t}\n\n\t\t}\n\n\t\t.copyable:not(.copy-unsupported) {\n\t\t\tcursor: pointer;\n\t\t\tdisplay: inline-flex;\n\t\t\talign-items: center;\n\t\t\t&:hover:after {\n\t\t\t\tcontent: '';\n\t\t\t\tdisplay: block;\n\t\t\t\tpadding-left: 5px;\n\t\t\t\t$icon-size: 12px;\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tbackground-color: $gray-500;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\ttext-indent: 500%;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\toverflow: hidden;\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-copy.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-copy.svg');\n\t\t\t\tbackground-size: cover;\n\t\t\t}\n\t\t\t&.copied:hover:after {\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-check-circle-solid.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-check-circle-solid.svg');\n\t\t\t\tbackground-color: $acf_success;\n\t\t\t}\n\t\t}\n\n\t\t/* no fields */\n\t\t.no-fields-message {\n\t\t\tpadding: 15px 15px;\n\t\t\tbackground: #fff;\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t/* empty */\n\t\t&.-empty {\n\t\t\t.no-fields-message {\n\t\t\t\tdisplay: block;\n\t\t\t}\n\t\t}\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin('3-8') {\n\t\t.acf-field-list-wrap {\n\t\t\tborder-color: $wp38-card-border-1;\n\t\t}\n\t}\n}\n\n\n.rtl #acf-field-group-fields {\n\t.li-field-type {\n\t\t.field-type-icon {\n\t\t\tmargin: {\n\t\t\t\tleft: 8px;\n\t\t\t\tright: 0;\n\t\t\t};\n\t\t}\n\t}\n}\n\n/* field object */\n.acf-field-object {\n\tborder-top: $wp38-card-border-2 solid 1px;\n\tbackground: #fff;\n\n\t/* sortable */\n\t&.ui-sortable-helper {\n\t\toverflow: hidden !important;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $blue-200 !important;\n\t\t};\n\t\tborder-radius: $radius-lg;\n\t\tfilter: drop-shadow(0px 10px 20px rgba(16, 24, 40, 0.14)) drop-shadow(0px 1px 3px rgba(16, 24, 40, 0.1));\n\n\t\t&:before {\n\t\t\tdisplay: none !important;\n\t\t}\n\n\t}\n\n\t&.ui-sortable-placeholder {\n\t\tbox-shadow: 0 -1px 0 0 #DFDFDF;\n\t\tvisibility: visible !important;\n\t\tbackground: #F9F9F9;\n\t\tborder-top-color: transparent;\n\t\tmin-height: 54px;\n\n\t\t// hide tab field separator\n\t\t&:after, &:before {\n\t\t\tvisibility: hidden;\n\t\t}\n\t}\n\n\n\t/* meta */\n\t> .meta {\n\t\tdisplay: none;\n\t}\n\n\n\t/* handle */\n\t> .handle {\n\n\t\ta {\n\t\t\t-webkit-transition: none;\n\t\t\t-moz-transition: none;\n\t\t\t-o-transition: none;\n\t\t\ttransition: none;\n\t\t}\n\n\t\tli {\n\t\t\tword-wrap: break-word;\n\t\t}\n\n\t\tstrong {\n\t\t\tdisplay: block;\n\t\t\tpadding-bottom: 0;\n\t\t\tfont-size: 14px;\n\t\t\tline-height: 14px;\n\t\t\tmin-height: 14px;\n\t\t}\n\n\t\t.row-options {\n\t\t\tdisplay: block;\n\t\t\topacity: 0;\n\t\t\tmargin: {\n\t\t\t\ttop: 5px;\n\t\t\t};\n\n\t\t\t@media screen and (max-width: 880px) {\n\t\t\t\topacity: 1;\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\ta {\n\t\t\t\tmargin-right: 4px;\n\n\t\t\t\t&:hover {\n\t\t\t\t\tcolor: darken($color-primary-hover, 10%);\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\ta.delete-field {\n\t\t\t\tcolor: #a00;\n\n\t\t\t\t&:hover { color: #f00; }\n\t\t\t}\n\n\t\t\t&.active {\n\t\t\t\tvisibility: visible;\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/* open */\n\t&.open {\n\n\t\t+ .acf-field-object {\n\t\t\tborder-top-color: #E1E1E1;\n\t\t}\n\n\t\t> .handle {\n\t\t\tbackground: $acf_blue;\n\t\t\tborder: darken($acf_blue, 2%) solid 1px;\n\t\t\ttext-shadow: #268FBB 0 1px 0;\n\t\t\tcolor: #fff;\n\t\t\tposition: relative;\n\t\t\tmargin: 0 -1px 0 -1px;\n\n\t\t\ta {\n\t\t\t\tcolor: #fff !important;\n\n\t\t\t\t&:hover {\n\t\t\t\t\ttext-decoration: underline !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t}\n\n\n\t/*\n\t// debug\n\t&[data-save=\"meta\"] {\n\t\t> .handle {\n\t\t\tborder-left: #ffb700 solid 5px !important;\n\t\t}\n\t}\n\n\t&[data-save=\"settings\"] {\n\t\t> .handle {\n\t\t\tborder-left: #0ec563 solid 5px !important;\n\t\t}\n\t}\n*/\n\n\n\t/* hover */\n\t&:hover, &.-hover, &:focus-within {\n\n\t\t> .handle {\n\n\t\t\t.row-options {\n\t\t\t\topacity: 1;\n\t\t\t\tmargin-bottom: 0;\n\t\t\t}\n\n\t\t}\n\t}\n\n\n\t/* settings */\n\t> .settings {\n\t\tdisplay: none;\n\t\twidth: 100%;\n\n\t\t> .acf-table {\n\t\t\tborder: none;\n\t\t}\n\t}\n\n\n\t/* conditional logic */\n\t.rule-groups {\n\t\tmargin-top: 20px;\n\t}\n\n}\n\n\n/*----------------------------------------------------------------------------\n*\n* Postbox: Locations\n*\n*----------------------------------------------------------------------------*/\n\n.rule-groups {\n\n\th4 {\n\t\tmargin: 3px 0;\n\t}\n\n\t.rule-group {\n\t\tmargin: 0 0 5px;\n\n\t\th4 {\n\t\t\tmargin: 0 0 3px;\n\t\t}\n\n\t\ttd.param {\n\t\t\twidth: 35%;\n\t\t}\n\n\t\ttd.operator {\n\t\t\twidth: 20%;\n\t\t}\n\n\t\ttd.add {\n\t\t\twidth: 40px;\n\t\t}\n\n\t\ttd.remove {\n\t\t\twidth: 28px;\n\t\t\tvertical-align: middle;\n\n\t\t\ta {\n\t\t\t\twidth: 22px;\n\t\t\t\theight: 22px;\n\t\t\t\tvisibility: hidden;\n\n\t\t\t\t&:before {\n\t\t\t\t\tposition: relative;\n\t\t\t\t\ttop: -2px;\n\t\t\t\t\tfont-size: 16px;\n\t\t\t\t}\n\n\t\t\t}\n\t\t}\n\n\t\ttr:hover td.remove a {\n\t\t\tvisibility: visible;\n\t\t}\n\n\t\t// empty select\n\t\tselect:empty {\n\t\t\tbackground: #f8f8f8;\n\t\t}\n\t}\n\n\n\t&:not(.rule-groups-multiple) {\n\t\t.rule-group {\n\t\t\t&:first-child tr:first-child td.remove a {\n\t\t\t\t/* Don't allow user to delete the only rule group */\n\t\t\t\tvisibility: hidden !important;\n\t\t\t}\n\t\t}\n\t}\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tOptions\n*\n*----------------------------------------------------------------------------*/\n\n#acf-field-group-options tr[data-name=\"hide_on_screen\"] li {\n\tfloat: left;\n\twidth: 33%;\n}\n\n@media (max-width: 1100px) {\n\n\t#acf-field-group-options tr[data-name=\"hide_on_screen\"] li {\n\t\twidth: 50%;\n\t}\n\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tConditional Logic\n*\n*----------------------------------------------------------------------------*/\n\ntable.conditional-logic-rules {\n\tbackground: transparent;\n\tborder: 0 none;\n\tborder-radius: 0;\n}\n\ntable.conditional-logic-rules tbody td {\n\tbackground: transparent;\n\tborder: 0 none !important;\n\tpadding: 5px 2px !important;\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Tab\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-object-tab {\n\n\t// hide setting\n\t.acf-field-setting-name,\n\t.acf-field-setting-instructions,\n\t.acf-field-setting-required,\n\t.acf-field-setting-warning,\n\t.acf-field-setting-wrapper {\n\t\tdisplay: none;\n\t}\n\n\t// hide name\n\t.li-field-name {\n\t\tvisibility: hidden;\n\t}\n\n\tp:first-child {\n\t\tmargin: 0.5em 0;\n\t}\n\n\t// hide presentation setting tabs.\n\tli.acf-settings-type-presentation,\n\t.acf-field-settings-main-presentation {\n\t\tdisplay: none !important;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Accordion\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-object-accordion {\n\n\t// hide setting\n\t.acf-field-setting-name,\n\t.acf-field-setting-instructions,\n\t.acf-field-setting-required,\n\t.acf-field-setting-warning,\n\t.acf-field-setting-wrapper {\n\t\tdisplay: none;\n\t}\n\n\t// hide name\n\t.li-field-name {\n\t\tvisibility: hidden;\n\t}\n\n\tp:first-child {\n\t\tmargin: 0.5em 0;\n\t}\n\n\t// show settings\n\t.acf-field-setting-instructions {\n\t\tdisplay: block;\n\t}\n\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Message\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-object-message tr[data-name=\"name\"],\n.acf-field-object-message tr[data-name=\"instructions\"],\n.acf-field-object-message tr[data-name=\"required\"] {\n\tdisplay: none !important;\n}\n\n.acf-field-object-message .li-field-name {\n\tvisibility: hidden;\n}\n\n.acf-field-object-message textarea {\n\theight: 175px !important;\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Separator\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-object-separator tr[data-name=\"name\"],\n.acf-field-object-separator tr[data-name=\"instructions\"],\n.acf-field-object-separator tr[data-name=\"required\"] {\n\tdisplay: none !important;\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*\tField: Date Picker\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-object-date-picker,\n.acf-field-object-time-picker,\n.acf-field-object-date-time-picker {\n\n\t.acf-radio-list {\n\n\t\tli {\n\t\t\tline-height: 25px;\n\t\t}\n\n\t\tspan {\n\t\t\tdisplay: inline-block;\n\t\t\tmin-width: 10em;\n\t\t}\n\n\t\tinput[type=\"text\"] {\n\t\t\twidth: 100px;\n\t\t}\n\t}\n\n}\n\n.acf-field-object-date-time-picker {\n\n\t.acf-radio-list {\n\n\t\tspan {\n\t\t\tmin-width: 15em;\n\t\t}\n\n\t\tinput[type=\"text\"] {\n\t\t\twidth: 200px;\n\t\t}\n\t}\n\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSlug\n*\n*--------------------------------------------------------------------------------------------*/\n\n#slugdiv {\n\n\t.inside {\n\t\tpadding: 12px;\n\t\tmargin: 0;\n\t}\n\n\tinput[type=\"text\"] {\n\t\twidth: 100%;\n\t\theight: 28px;\n\t\tfont-size: 14px;\n\t}\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRTL\n*\n*--------------------------------------------------------------------------------------------*/\n\nhtml[dir=\"rtl\"] .acf-field-object.open > .handle {\n\tmargin: 0\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Device\n*\n*----------------------------------------------------------------------------*/\n\n@media only screen and (max-width: 850px) {\n\n\ttr.acf-field,\n\ttd.acf-label,\n\ttd.acf-input {\n\t\tdisplay: block !important;\n\t\twidth: auto !important;\n\t\tborder: 0 none !important;\n\t}\n\n\ttr.acf-field {\n\t\tborder-top: #ededed solid 1px !important;\n\t\tmargin-bottom: 0 !important;\n\t}\n\n\ttd.acf-label {\n\t\tbackground: transparent !important;\n\t\tpadding-bottom: 0 !important;\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Subtle background on accordion & tab fields to separate them from others\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t#acf-field-group-fields {\n\n\t\t.acf-field-object-tab,\n\t\t.acf-field-object-accordion {\n\t\t\tbackground-color: $gray-50;\n\t\t}\n\n\t}\n\n}\n", "/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #wpcontent {\n\tline-height: 140%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Links\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\ta {\n\t\tcolor: $blue-500;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headings\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-h1 {\n\tfont-size: 21px;\n\tfont-weight: 400;\n}\n\n.acf-h2 {\n\tfont-size: 18px;\n\tfont-weight: 400;\n}\n\n.acf-h3 {\n\tfont-size: 16px;\n\tfont-weight: 400;\n}\n\n.post-type-acf-field-group,\n.acf-headerbar {\n\n\th1 {\n\t\t@extend .acf-h1;\n\t}\n\n\th2 {\n\t\t@extend .acf-h2;\n\t}\n\n\th3 {\n\t\t@extend .acf-h3;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Paragraphs\n*\n*---------------------------------------------------------------------------------------------*/\n\n.post-type-acf-field-group {\n\n\t.p1 {\n\t\tfont-size: 15px;\n\t}\n\t\n\t.p2 {\n\t\tfont-size: 14px;\n\t}\n\t\n\t.p3 {\n\t\tfont-size: 13.5px;\n\t}\n\t\n\t.p4 {\n\t\tfont-size: 13px;\n\t}\n\t\n\t.p5 {\n\t\tfont-size: 12.5px;\n\t}\n\t\n\t.p6 {\n\t\tfont-size: 12px;\n\t}\n\t\n\t.p7 {\n\t\tfont-size: 11.5px;\n\t}\n\t\n\t.p8 {\n\t\tfont-size: 11px;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Page titles\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-page-title {\n\t@extend .acf-h2;\n\tcolor: $gray-700;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide old / native WP titles from pages\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t.acf-settings-wrap h1,\n\t#acf-admin-tools h1 {\n\t\tdisplay: none;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-small {\n\t@extend .p6;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Link focus style\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group a:focus {\n\tbox-shadow: none;\n\toutline: none;\n}\n.post-type-acf-field-group a:focus-visible {\n\tbox-shadow: 0 0 0 1px #4f94d4, 0 0 2px 1px rgb(79 148 212 / 80%);\n\toutline: 1px solid transparent;\n}", ".post-type-acf-field-group {\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  All Inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"text\"],\n\tinput[type=\"search\"],\n\tinput[type=\"number\"],\n\ttextarea,\n\tselect {\n\t\tbox-sizing: border-box;\n\t\theight: 40px;\n\t\tpadding: {\n\t\t\tright: 12px;\n\t\t\tleft: 12px;\n\t\t};\n\t\tbackground-color: #fff;\n\t\tborder-color: $gray-300;\n\t\tbox-shadow: $elevation-01;\n\t\tborder-radius: $radius-md;\n\t\t@extend .p4;\n\t\tcolor: $gray-700;\n\n\t\t&:focus {\n\t\t\toutline: $outline;\n\t\t\tborder-color: $blue-400;\n\t\t}\n\n\t\t&:disabled {\n\t\t\tbackground-color: $gray-50;\n\t\t\tcolor: lighten($gray-500, 10%);\n\t\t}\n\n\t\t&::placeholder {\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Read only text inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"text\"] {\n\n\t\t&:read-only {\n\t\t\tbackground-color: $gray-50;\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Number fields\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-field.acf-field-number {\n\n\t\t.acf-label,\n\t\t.acf-input input[type=\"number\"] {\n\t\t\tmax-width: 180px;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Textarea\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\ttextarea {\n\t\tbox-sizing: border-box;\n\t\tpadding: {\n\t\t\ttop: 10px;\n\t\t\tbottom: 10px;\n\t\t};\n\t\theight: 80px;\n\t\tmin-height: 56px;\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Select\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tselect {\n\t\tmin-width: 160px;\n\t\tmax-width: 100%;\n\t\tpadding: {\n\t\t\tright: 40px;\n\t\t\tleft: 12px;\n\t\t};\n\t\tbackground-image: url('../../images/icons/icon-chevron-down.svg');\n\t\tbackground-position: right 10px top 50%;\n\t\tbackground-size: 20px;\n\t\t@extend .p4;\n\n\t\t&:hover,\n\t\t&:focus {\n\t\t\tcolor: $blue-500;\n\t\t}\n\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\ttop: 5px;\n\t\t\tleft: 5px;\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t\tbackground-color: red;\n\t\t}\n\n\t}\n\n\t&.rtl {\n\t\tselect {\n\t\t\tpadding: {\n\t\t\t\tright: 12px;\n\t\t\t\tleft: 40px;\n\t\t\t};\n\t\t\tbackground-position: left 10px top 50%;\n\t\t}\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Button & Checkbox base styling\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"radio\"],\n\tinput[type=\"checkbox\"] {\n\t\tbox-sizing: border-box;\n\t\twidth: 16px;\n\t\theight: 16px;\n\t\tpadding: 0;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-400;\n\t\t};\n\t\tbackground: #fff;\n\t\tbox-shadow: none;\n\n\t\t&:hover {\n\t\t\tbackground-color: $blue-50;\n\t\t\tborder-color: $blue-500;\n\t\t}\n\n\t\t&:checked,\n\t\t&:focus-visible {\n\t\t\tbackground-color: $blue-50;\n\t\t\tborder-color: $blue-500;\n\n\t\t\t&:before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: relative;\n\t\t\t\ttop: -1px;\n\t\t\t\tleft: -1px;\n\t\t\t\twidth: 16px;\n\t\t\t\theight: 16px;\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tbackground-size: cover;\n\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\tbackground-position: center;\n\t\t\t}\n\n\t\t}\n\n\t\t&:active {\n\t\t\tbox-shadow: 0px 0px 0px 3px $blue-50, 0px 0px 0px rgba(255, 54, 54, 0.25);\n\t\t}\n\n\t\t&:disabled {\n\t\t\tbackground-color: $gray-50;\n\t\t\tborder-color: $gray-300;\n\t\t}\n\n\t}\n\n\t&.rtl {\n\t\tinput[type=\"radio\"],\n\t\tinput[type=\"checkbox\"] {\n\t\t\t&:checked,\n\t\t\t&:focus-visible {\n\t\t\t\t&:before {\n\t\t\t\t\tleft: 1px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Buttons\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"radio\"] {\n\n\t\t&:checked,\n\t\t&:focus {\n\n\t\t\t&:before {\n\t\t\t\tbackground-image: url('../../images/field-states/radio-active.svg');\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Checkboxes\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"checkbox\"] {\n\n\t\t&:checked,\n\t\t&:focus {\n\n\t\t\t&:before {\n\t\t\t\tbackground-image: url('../../images/field-states/checkbox-active.svg');\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Buttons & Checkbox lists\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-radio-list,\n\t.acf-checkbox-list {\n\n\t\tli input[type=\"radio\"],\n\t\tli input[type=\"checkbox\"] {\n\t\t\tmargin: {\n\t\t\t\tright: 6px;\n\t\t\t};\n\t\t}\n\n\t\t&.acf-bl li {\n\t\t\tmargin: {\n\t\t\t\tbottom: 8px;\n\t\t\t};\n\n\t\t\t&:last-of-type {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\n\t\t}\n\n\t\tlabel {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\talign-content: center;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  ACF Switch\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-switch {\n\t\twidth: 42px;\n\t\theight: 24px;\n\t\tborder: none;\n\t\tbackground-color: $gray-300;\n\t\tborder-radius: 12px;\n\n\t\t&:hover {\n\t\t\tbackground-color: $gray-400;\n\t\t}\n\n\t\t&:active {\n\t\t\tbox-shadow: 0px 0px 0px 3px $blue-50, 0px 0px 0px rgba(255, 54, 54, 0.25);\n\t\t}\n\n\t\t&.-on {\n\t\t\tbackground-color: $color-primary;\n\n\t\t\t&:hover {\n\t\t\t\tbackground-color: $color-primary-hover;\n\t\t\t}\n\n\t\t\t.acf-switch-slider {\n\t\t\t\tleft: 20px;\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-switch-off,\n\t\t.acf-switch-on {\n\t\t\tvisibility: hidden;\n\t\t}\n\n\t\t.acf-switch-slider {\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t\tborder: none;\n\t\t\tborder-radius: 100px;\n\t\t\tbox-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);\n\t\t}\n\n\t}\n\n\t.acf-field-true-false {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\n\t\t.acf-label {\n\t\t\torder: 2;\n\t\t\tdisplay: block;\n\t\t\talign-items: center;\n\t\t\tmargin: {\n\t\t\t\ttop: 2px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 12px;\n\t\t\t};\n\n\t\t\tlabel {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-tip {\n\t\t\t\tmargin: {\n\t\t\t\t\tleft: 12px;\n\t\t\t\t};\n\t\t\t}\n\t\t\t\n\t\t\t.description {\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 2px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\t\t\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t&.rtl {\n\t\t.acf-field-true-false {\n\t\t\t.acf-label {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-tip {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  File input button\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\n\tinput::file-selector-button {\n\t\tbox-sizing: border-box;\n\t\tmin-height: 40px;\n\t\tmargin: {\n\t\t\tright: 16px;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 8px;\n\t\t\tright: 16px;\n\t\t\tbottom: 8px;\n\t\t\tleft: 16px;\n\t\t};\n\t\tbackground-color: transparent;\n\t\tcolor: $color-primary !important;\n\t\tborder-radius: $radius-md;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $color-primary;\n\t\t};\n\t\ttext-decoration: none;\n\n\t\t&:hover {\n\t\t\tborder-color: $color-primary-hover;\n\t\t\tcursor: pointer;\n\t\t\tcolor: $color-primary-hover !important;\n\t\t}\n\n\t}\n\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Action Buttons\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.button {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\theight: 40px;\n\t\tpadding: {\n\t\t\tright: 16px;\n\t\t\tleft: 16px;\n\t\t};\n\t\tbackground-color: transparent;\n\t\tborder-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: $blue-500;\n\t\tborder-radius: $radius-md;\n\t\t@extend .p4;\n\t\tcolor: $blue-500;\n\n\t\t&:hover {\n\t\t\tbackground-color: lighten($blue-50, 2%);\n\t\t\tborder-color: $color-primary;\n\t\t\tcolor: $color-primary;\n\t\t}\n\t\t&:focus {\n\t\t\tbackground-color: lighten($blue-50, 2%);\n\t\t\toutline: $outline;\n\t\t\tcolor: $color-primary;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Edit field group header\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.edit-field-group-header {\n\t\tdisplay: block !important;\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Select2 inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-input {\n\n\t\t.select2-container.-acf .select2-selection {\n\t\t\tborder: none;\n\t\t\tline-height: 1;\n\t\t}\n\n\t\t.select2-container.-acf .select2-selection__rendered {\n\t\t\tbox-sizing: border-box;\n\t\t\tpadding: {\n\t\t\t\tright: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\tbackground-color: #fff;\n\t\t\tborder: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-300;\n\t\t\t};\n\t\t\tbox-shadow: $elevation-01;\n\t\t\tborder-radius: $radius-md;\n\t\t\t@extend .p4;\n\t\t\tcolor: $gray-700;\n\t\t}\n\n\t\t.select2-container--focus {\n\t\t\toutline: $outline;\n\t\t\tborder-color: $blue-400;\n\t\t\tborder-radius: $radius-md;\n\n\t\t\t.select2-selection__rendered {\n\t\t\t\tborder-color: $blue-400 !important;\n\t\t\t}\n\n\t\t\t&.select2-container--below.select2-container--open {\n\n\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\tborder-bottom-right-radius: 0 !important;\n\t\t\t\t\tborder-bottom-left-radius: 0 !important;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t&.select2-container--above.select2-container--open {\n\n\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\tborder-top-right-radius: 0 !important;\n\t\t\t\t\tborder-top-left-radius: 0 !important;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t.select2-container .select2-search--inline .select2-search__field {\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\tleft: 6px;\n\t\t\t};\n\n\t\t\t&:focus {\n\t\t\t\toutline: none;\n\t\t\t\tborder: none;\n\t\t\t}\n\n\t\t}\n\n\t\t.select2-container--default .select2-selection--multiple .select2-selection__rendered {\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 6px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 6px;\n\t\t\t};\n\t\t}\n\n\t\t.select2-selection__clear {\n\t\t\twidth: 18px;\n\t\t\theight: 18px;\n\t\t\tmargin: {\n\t\t\t\ttop: 12px;\n\t\t\t\tright: 0;\n\t\t\t};\n\t\t\ttext-indent: 100%;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\n\t\t\t&:before {\n\t\t\t\tcontent: '';\n\t\t\t\t$icon-size: 14px;\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 0;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-close.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-close.svg');\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t}\n\n\t\t\t&:hover::before {\n\t\t\t\tbackground-color: $gray-800;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  ACF label\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\n\t\t.acf-icon-help {\n\t\t\t$icon-size: 18px;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tbackground-color: $gray-400;\n\t\t}\n\n\t\tlabel {\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t}\n\t\t\n\t\t.description {\n\t\t\tmargin: {\n\t\t\t\ttop: 2px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Tooltip for field name field setting (result of a fix for keyboard navigation)\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-field-setting-name .acf-tip {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 654px;\n\t\tcolor: #98A2B3;\n\n\t\t.acf-icon-help {\n\t\t\twidth: 18px;\n\t\t\theight: 18px;\n\t\t}\n\t}\n\n}\n\n.rtl.post-type-acf-field-group {\n\t.acf-field-setting-name .acf-tip {\n\t\tleft: auto;\n\t\tright: 654px;\n\t}\n}\n", "/*----------------------------------------------------------------------------\n*\n*  Container sizes\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .metabox-holder.columns-1 {\n\n\t#acf-field-group-fields,\n\t#acf-field-group-options,\n\t.meta-box-sortables.ui-sortable,\n\t.notice {\n\t\tmax-width: $max-width;\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Max width for notices in 1 column edit field group layout\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group.columns-1 {\n\n\t.notice {\n\t\tmax-width: $max-width;\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Widen edit field group headerbar for 2 column layout\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group.columns-2 {\n\n\t.acf-headerbar .acf-headerbar-inner{\n\t\tmax-width: 100%;\n\t}\n\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*  Post stuff\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t#poststuff {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t#acf-field-group-fields .acf-field-list-wrap {\n\t\toverflow: hidden;\n\t\tborder: none;\n\t\tborder-radius: 0 0 $radius-lg $radius-lg;\n\t\tbox-shadow: $elevation-01;\n\n\t\t&.-empty {\n\t\t\tborder-top: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\n\t\t\t.acf-thead,\n\t\t\t.acf-tfoot {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\n\t\t\t.no-fields-message {\n\t\t\t\tmin-height: 280px;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table header\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t.acf-thead {\n\t\tbackground-color: $gray-50;\n\t\tborder-top: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t};\n\t\tborder-bottom: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t};\n\n\t\tli {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmin-height: 48px;\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t\t@extend .p4;\n\t\t\tcolor: $gray-700;\n\t\t\tfont-weight: 500;\n\t\t}\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table body\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t.acf-field-object {\n\t\tborder-top: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t};\n\n\t\t&:hover {\n\n\t\t\t.acf-sortable-handle:before {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t}\n\n\t\t}\n\n\t\t// Add divider to show which fields have endpoint\n\t\t&.acf-field-is-endpoint {\n\n\t\t\t&:before {\n\t\t\t\tdisplay: block;\n\t\t\t\tcontent: \"\";\n\t\t\t\theight: 2px;\n\t\t\t\twidth: 100%;\n\t\t\t\tbackground: $gray-300;\n\t\t\t\tmargin-top: -1px;\n\t\t\t}\n\n\t\t\t&.acf-field-object-accordion {\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\t&:after {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tcontent: \"\";\n\t\t\t\t\theight: 2px;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tbackground: $gray-300;\n\t\t\t\t\tz-index: 500;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t&:hover {\n\t\t\tbackground-color: lighten($blue-50, 3%);\n\t\t}\n\n\t\t&.open {\n\t\t\tbackground-color: #fff;\n\t\t\tborder-top-color: $blue-200;\n\t\t}\n\n\t\t&.open .handle {\n\t\t\tbackground-color: $blue-100;\n\t\t\tborder: none;\n\t\t\ttext-shadow: none;\n\n\t\t\ta {\n\t\t\t\tcolor: $link-color !important;\n\n\t\t\t\t&.delete-field {\n\t\t\t\t\tcolor: #a00 !important;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\tul.acf-hl {\n\t\t\tdisplay: flex;\n\t\t\talign-items: stretch;\n\t\t}\n\n\t\t.handle li {\n\t\t\tdisplay: flex;\n\t\t\talign-items: top;\n\t\t\tflex-wrap: wrap;\n\t\t\tmin-height: 60px;\n\t\t\t@extend .p4;\n\t\t\tcolor: $gray-700;\n\n\t\t\t&.li-field-label {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tjustify-content: flex-start;\n\t\t\t\talign-content: flex-start;\n\t\t\t\talign-items: flex-start;\n\t\t\t\twidth: auto;\n\n\t\t\t\ta.edit-field {\n\t\t\t\t\t@extend .p4;\n\t\t\t\t}\n\n\t\t\t\tstrong {\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t}\n\n\t\t\t\t.row-options {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t}\n\n\t\t\t\t.row-options a {\n\t\t\t\t\t@extend .p6;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Table footer\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t.acf-tfoot {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: flex-end;\n\t\tmin-height: 80px;\n\t\tbox-sizing: border-box;\n\t\tpadding: {\n\t\t\ttop: 8px;\n\t\t\tright: 24px;\n\t\t\tbottom: 8px;\n\t\t\tleft: 24px;\n\t\t};\n\t\tbackground-color: #fff;\n\t\tborder-top: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t};\n\n\t\t.acf-fr {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t}\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Edit field settings\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-field-object .settings {\n\tbox-sizing: border-box;\n\tpadding: {\n\t\ttop: 0;\n\t\tbottom: 0;\n\t};\n\tbackground-color: #fff;\n\tborder-left: {\n\t\twidth: 4px;\n\t\tstyle: solid;\n\t\tcolor: $blue-300;\n\t}\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*  Main field settings container\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-main  {\n\tpadding: {\n\t\ttop: 32px;\n\t\tright: 0;\n\t\tbottom: 32px;\n\t\tleft: 0;\n\t};\n\n\t.acf-field:last-of-type {\n\t\tmargin: {\n\t\t\tbottom: 0;\n\t\t};\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field label\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-label {\n\tdisplay: block;\n\tjustify-content: space-between;\n\talign-items: center;\n\talign-content: center;\n\tmargin: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 6px;\n\t\tleft: 0;\n\t};\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Single field\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field {\n\tbox-sizing: border-box;\n\twidth: 100%;\n\tmargin: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 32px;\n\t\tleft: 0;\n\t}\n\tpadding: {\n\t\ttop: 0;\n\t\tright: 72px;\n\t\tbottom: 0;\n\t\tleft: 72px;\n\t};\n\n\t@media screen and (max-width: 600px) {\n\t\tpadding: {\n\t\t\tright: 12px;\n\t\t\tleft: 12px;\n\t\t};\n\t}\n\n\t.acf-label,\n\t.acf-input {\n\t\tmax-width: 600px;\n\n\t\t&.acf-input-sub {\n\t\t\tmax-width: 100%;\n\t\t}\n\n\t}\n\n\t.acf-input-wrap {\n\t\toverflow: visible;\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field separators\n*\n*----------------------------------------------------------------------------*/\n\n.acf-field-settings .acf-field.acf-field-setting-label,\n.acf-field-settings .acf-field-setting-wrapper {\n\tpadding: {\n\t\ttop: 24px;\n\t};\n\tborder-top: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t};\n}\n\n.acf-field-settings .acf-field-setting-wrapper {\n\tmargin: {\n\t\ttop: 24px;\n\t};\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Edit fields footer\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field-settings-footer {\n\tdisplay: flex;\n\talign-items: center;\n\tmin-height: 72px;\n\tbox-sizing: border-box;\n\twidth: 100%;\n\tmargin: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t}\n\tpadding: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 72px;\n\t};\n\tborder-top: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t};\n\n\t@media screen and (max-width: 600px) {\n\t\tpadding: {\n\t\t\tleft: 12px;\n\t\t};\n\t}\n\n}\n\n.rtl .acf-field-settings .acf-field-settings-footer {\n\tpadding: {\n\t\ttop: 0;\n\t\tright: 72px;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t};\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Tabs\n*\n*----------------------------------------------------------------------------*/\n.acf-fields {\n\n\t.acf-tab-wrap {\n\t\tbackground: $gray-50;\n\t\tborder-bottom: {\n\t\t\tcolor: $gray-800;\n\t\t};\n\n\t\t.acf-tab-group {\n\t\t\tpadding: {\n\t\t\t\tright: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t};\n\t\t\tborder-top: {\n\t\t\t\twidth: 0;\n\t\t\t};\n\t\t\tborder-bottom: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t.acf-field-settings-tab-bar,\n\t.acf-tab-wrap .acf-tab-group  {\n\t\tdisplay: flex;\n\t\talign-items: stretch;\n\t\tmin-height: 48px;\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 24px;\n\t\t};\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tbottom: 0;\n\t\t};\n\t\tborder-bottom: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t};\n\n\t\tli {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\tpadding: 0;\n\n\t\t\ta {\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\talign-items: center;\n\t\t\t\theight: 100%;\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 3px;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t\tbackground: none;\n\t\t\t\tborder-top: none;\n\t\t\t\tborder-right: none;\n\t\t\t\tborder-bottom: {\n\t\t\t\t\twidth: 3px;\n\t\t\t\t\tstyle: solid;\n\t\t\t\t\tcolor: transparent;\n\t\t\t\t};\n\t\t\t\tborder-left: none;\n\t\t\t\tcolor: $gray-500;\n\t\t\t\t@extend .p5;\n\t\t\t\tfont-weight: normal;\n\n\t\t\t\t&:hover {\n\t\t\t\t\tcolor: $gray-800;\n\t\t\t\t}\n\n\t\t\t\t&:hover {\n\t\t\t\t\tbackground-color: transparent;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t&.active a {\n\t\t\t\tbackground: none;\n\t\t\t\tborder-bottom: {\n\t\t\t\t\tcolor: $color-primary;\n\t\t\t\t};\n\t\t\t\tcolor: $gray-800;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\n#acf-field-group-options .acf-fields .acf-tab-wrap .acf-tab-group li.active a {\n\tpadding: {\n\t\ttop: 2px;\n\t};\n}\n\n.acf-field-editor .acf-field-settings-tab-bar {\n\tpadding: {\n\t\tleft: 72px;\n\t};\n\n\t@media screen and (max-width: 600px) {\n\t\tpadding: {\n\t\t\tleft: 12px;\n\t\t};\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Field group settings\n*\n*----------------------------------------------------------------------------*/\n#acf-field-group-options {\n\n\t.field-group-settings-tab {\n\t\tpadding: {\n\t\t\ttop: 24px;\n\t\t\tright: 24px;\n\t\t\tbottom: 24px;\n\t\t\tleft: 24px;\n\t\t};\n\n\t\t.acf-field:last-of-type {\n\t\t\tpadding: 0;\n\t\t}\n\n\t}\n\n\t.acf-field {\n\t\tborder: none;\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 24px;\n\t\t\tleft: 0;\n\t\t}\n\t}\n\n\t// Split layout\n\t.field-group-setting-split-container {\n\t\tdisplay: flex;\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\n\t\t.field-group-setting-split {\n\t\t\tbox-sizing: border-box;\n\t\t\tpadding: {\n\t\t\t\ttop: 24px;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t};\n\t\t}\n\n\t\t.field-group-setting-split:nth-child(1) {\n\t\t\tflex: 1 0 auto;\n\t\t}\n\n\t\t.field-group-setting-split:nth-child(2n) {\n\t\t\tflex: 1 0 auto;\n\t\t\tmax-width: 320px;\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 32px;\n\t\t\t}\n\t\t\tpadding: {\n\t\t\t\tright: 32px;\n\t\t\t\tleft: 32px;\n\t\t\t}\n\t\t\tborder-left: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t// Description field\n\t.acf-field[data-name=\"description\"] {\n\t\tmax-width: 600px;\n\t}\n\n\t// Button group\n\t.acf-button-group {\n\t\tdisplay: inline-flex;\n\t}\n\n}\n\n.rtl #acf-field-group-options {\n\t.field-group-setting-split-container {\n\t\t.field-group-setting-split:nth-child(2n) {\n\t\t\tmargin: {\n\t\t\t\tright: 32px;\n\t\t\t\tleft: 0;\n\t\t\t}\n\t\t\tborder-left: none;\n\t\t\tborder-right: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Reorder handles\n*\n*----------------------------------------------------------------------------*/\n.acf-field-list {\n\n\t.li-field-order {\n\t\tpadding: 0;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: nowrap;\n\t\tjustify-content: center;\n\t\talign-content: stretch;\n\t\talign-items: stretch;\n\t\tbackground-color: transparent;\n\t}\n\n\t.acf-sortable-handle {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tflex-wrap: nowrap;\n\t\tjustify-content: center;\n\t\talign-content: flex-start;\n\t\talign-items: flex-start;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tposition: relative;\n\t\tpadding: {\n\t\t\ttop: 11px;\n\t\t\tbottom: 8px;\n\t\t};\n\t\t@extend .p4;\n\t\tbackground-color: transparent;\n\t\tborder: none;\n\t\tborder-radius: 0;\n\n\t\t&:hover {\n\t\t\tcursor: grab;\n\t\t}\n\n\t\t&:before {\n\t\t\tcontent: '';\n\t\t\tdisplay: none;\n\t\t\tposition: absolute;\n\t\t\ttop: 16px;\n\t\t\tleft: 8px;\n\t\t\twidth: 16px;\n\t\t\theight: 16px;\n\t\t\t$icon-size: 12px;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tbackground-color: $gray-400;\n\t\t\tborder: none;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: contain;\n\t\t\tmask-size: contain;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t\ttext-indent: 500%;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\t\t\t-webkit-mask-image: url('../../images/icons/icon-draggable.svg');\n\t\t\tmask-image: url('../../images/icons/icon-draggable.svg');\n\t\t}\n\n\t}\n\n}\n\n.rtl .acf-field-list {\n\n\t.acf-sortable-handle {\n\t\t&:before {\n\t\t\tleft: 0;\n\t\t\tright: 8px;\n\t\t}\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Expand / collapse field icon\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object {\n\n\t.li-field-label {\n\t\tposition: relative;\n\t\tpadding: {\n\t\t\tleft: 40px;\n\t\t}\n\n\t\t&:before {\n\t\t\tcontent: '';\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\tleft: 6px;\n\t\t\t$icon-size: 18px;\n\t\t\tdisplay: inline-flex;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tmargin: {\n\t\t\t\ttop: -2px;\n\t\t\t};\n\t\t\tbackground-color: $gray-500;\n\t\t\tborder: none;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: contain;\n\t\t\tmask-size: contain;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t\ttext-indent: 500%;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\t\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\t\tmask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\t}\n\n\t\t&:hover:before {\n\t\t\tcursor: pointer;\n\t\t}\n\n\t}\n\n}\n\n.rtl {\n\t.acf-field-object {\n\n\t\t.li-field-label {\n\t\t\tpadding: {\n\t\t\t\tleft: 0;\n\t\t\t\tright: 40px;\n\t\t\t}\n\n\t\t\t&:before {\n\t\t\t\tleft: 0;\n\t\t\t\tright: 6px;\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\t\t}\n\t\t}\n\n\t\t// Open\n\t\t&.open {\n\t\t\t.li-field-label:before {\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\t\t}\n\n\t\t\t.acf-input-sub .li-field-label:before {\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-right.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-chevron-right.svg');\n\t\t\t}\n\n\t\t\t.acf-input-sub .acf-field-object.open .li-field-label:before {\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\t\t}\n\t\t}\n\n\t}\n}\n\n.acf-thead {\n\t.li-field-label {\n\t\tpadding: {\n\t\t\tleft: 40px;\n\t\t};\n\t}\n\t.rtl & {\n\t\t.li-field-label {\n\t\t\tpadding: {\n\t\t\t\tleft: 0;\n\t\t\t\tright: 40px;\n\t\t\t};\n\t\t}\n\t}\n}\n\n\n/*----------------------------------------------------------------------------\n*\n*  Conditional logic layout\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-main-conditional-logic {\n\n\t.acf-conditional-toggle {\n\t\tdisplay: flex;\n\t\tpadding: {\n\t\t\tright: 72px;\n\t\t\tleft: 72px;\n\t\t};\n\n\t\t@media screen and (max-width: 600px) {\n\t\t\tpadding: {\n\t\t\t\tleft: 12px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t.acf-field {\n\t\tflex-wrap: wrap;\n\t\tmargin: {\n\t\t\tbottom: 0;\n\t\t};\n\t\tpadding: {\n\t\t\tright: 0;\n\t\t\tleft: 0;\n\t\t};\n\n\t\t.rule-groups {\n\t\t\tflex: 0 1 100%;\n\t\t\torder: 3;\n\t\t\tmargin: {\n\t\t\t\ttop: 32px;\n\t\t\t};\n\t\t\tpadding: {\n\t\t\t\ttop: 32px;\n\t\t\t\tright: 72px;\n\t\t\t\tleft: 72px;\n\t\t\t};\n\t\t\tborder-top: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\n\t\t\t@media screen and (max-width: 600px) {\n\t\t\t\tpadding: {\n\t\t\t\t\tleft: 12px;\n\t\t\t\t};\n\n\t\t\t\ttable.acf-table tbody tr {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\tjustify-content: flex-start;\n\t\t\t\t\talign-content: flex-start;\n\t\t\t\t\talign-items: flex-start;\n\n\t\t\t\t\ttd {\n\t\t\t\t\t\tflex: 1 1 100%;\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Prefix & append styling\n*\n*----------------------------------------------------------------------------*/\n.acf-input {\n\n\t.acf-input-prepend,\n\t.acf-input-append {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\theight: 100%;\n\t\tmin-height: 40px;\n\t\tpadding: {\n\t\t\tright: 12px;\n\t\t\tleft: 12px;\n\t\t};\n\t\tbackground-color: $gray-50;\n\t\tborder-color: $gray-300;\n\t\tbox-shadow: $elevation-01;\n\t\tcolor: $gray-500;\n\t}\n\n\t.acf-input-prepend {\n\t\tborder-radius: $radius-md 0 0 $radius-md;\n\t}\n\n\t.acf-input-append {\n\t\tborder-radius: 0 $radius-md $radius-md 0;\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  ACF input wrap\n*\n*----------------------------------------------------------------------------*/\n.acf-input-wrap {\n\tdisplay: flex;\n}\n\n.acf-field-settings-main-presentation .acf-input-wrap {\n\tdisplay: flex;\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Empty state\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list.-empty .no-fields-message {\n\tdisplay: flex;\n\tjustify-content: center;\n\tpadding: {\n\t\ttop: 48px;\n\t\tbottom: 48px;\n\t};\n\n\t.no-fields-message-inner {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: center;\n\t\talign-content: center;\n\t\talign-items: flex-start;\n\t\ttext-align: center;\n\t\tmax-width: 400px;\n\n\t\timg,\n\t\th2,\n\t\tp {\n\t\t\tflex: 1 0 100%;\n\t\t}\n\n\t\th2 {\n\t\t\t@extend .acf-h2;\n\t\t\tmargin: {\n\t\t\t\ttop: 32px;\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t\tpadding: 0;\n\t\t\tcolor: $gray-700;\n\t\t}\n\n\t\tp {\n\t\t\t@extend .p2;\n\t\t\tmargin: {\n\t\t\t\ttop: 12px;\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t\tpadding: 0;\n\t\t\tcolor: $gray-500;\n\n\t\t\t&.acf-small {\n\t\t\t\t@extend .p6;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 32px;\n\t\t\t\t};\n\t\t\t}\n\n\t\t}\n\n\n\t\timg {\n\t\t\tmax-width: 284px;\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t}\n\n\t\t.acf-btn {\n\t\t\tmargin: {\n\t\t\t\ttop: 32px;\n\t\t\t};\n\t\t}\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Hide add title prompt label\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t.acf-headerbar {\n\n\t\t#title-prompt-text {\n\t\t\tdisplay: none;\n\t\t}\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Modal styling\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t#acf-popup .acf-popup-box {\n\t\tmin-width: 480px;\n\n\t\t.title {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\talign-content: center;\n\t\t\tjustify-content: space-between;\n\t\t\tmin-height: 64px;\n\t\t\tbox-sizing: border-box;\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\tright: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t};\n\t\t\tborder-bottom: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\n\t\t\th1,\n\t\t\th2,\n\t\t\th3,\n\t\t\th4 {\n\t\t\t\t@extend .acf-h3;\n\t\t\t\tpadding: {\n\t\t\t\t\tleft: 0;\n\t\t\t\t}\n\t\t\t\tcolor: $gray-700;\n\t\t\t}\n\n\t\t\t.acf-icon {\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: relative;\n\t\t\t\ttop: auto;\n\t\t\t\tright: auto;\n\t\t\t\twidth: 22px;\n\t\t\t\theight: 22px;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tcolor: transparent;\n\n\t\t\t\t&:before {\n\t\t\t\t\t$icon-size: 22px;\n\t\t\t\t\tdisplay: inline-flex;\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t\twidth: $icon-size;\n\t\t\t\t\theight: $icon-size;\n\t\t\t\t\tbackground-color: $gray-500;\n\t\t\t\t\tborder: none;\n\t\t\t\t\tborder-radius: 0;\n\t\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\t\tmask-size: contain;\n\t\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t\t-webkit-mask-position: center;\n\t\t\t\t\tmask-position: center;\n\t\t\t\t\ttext-indent: 500%;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-close-circle.svg');\n\t\t\t\t\tmask-image: url('../../images/icons/icon-close-circle.svg');\n\t\t\t\t}\n\n\t\t\t\t&:hover:before {\n\t\t\t\t\tbackground-color: $color-primary;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t.inner {\n\t\t\tbox-sizing: border-box;\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\ttop: 24px;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t};\n\t\t\tborder-top: none;\n\n\t\t\tp {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t}\n\n\t\t// Custom styling for move custom field modal\n\t\t#acf-move-field-form {\n\n\t\t\t.acf-field-select {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Hide original #post-body-content from edit field group page\n*\n*----------------------------------------------------------------------------*/\n.acf-admin-single-field-group {\n\n\t#post-body-content {\n\t\tdisplay: none;\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Settings section footer\n*\n*----------------------------------------------------------------------------*/\n.acf-field-group-settings-footer {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-content: stretch;\n\talign-items: center;\n\tposition: relative;\n\tmin-height: 88px;\n\tmargin: {\n\t\tright: -24px;\n\t\tbottom: -24px;\n\t\tleft: -24px;\n\t};\n\tpadding: {\n\t\tright: 24px;\n\t\tleft: 24px;\n\t};\n\tborder-top: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t};\n\n\t.acf-created-on {\n\t\tdisplay: inline-flex;\n\t\tjustify-content: flex-start;\n\t\talign-content: stretch;\n\t\talign-items: center;\n\t\t@extend .p5;\n\t\tcolor: $gray-500;\n\n\t\t&:before {\n\t\t\tcontent: '';\n\t\t\t$icon-size: 20px;\n\t\t\tdisplay: inline-block;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t};\n\t\t\tbackground-color: $gray-400;\n\t\t\tborder: none;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: contain;\n\t\t\tmask-size: contain;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t\t-webkit-mask-image: url('../../images/icons/icon-time.svg');\n\t\t\tmask-image: url('../../images/icons/icon-time.svg');\n\t\t}\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Conditional logic enabled badge\n*\n*----------------------------------------------------------------------------*/\n.conditional-logic-badge {\n\tdisplay: none;\n\n\t&.is-enabled {\n\t\tdisplay: inline-block;\n\t\twidth: 6px;\n\t\theight: 6px;\n\t\toverflow: hidden;\n\t\tmargin: {\n\t\t\tleft: 8px;\n\t\t};\n\t\tbackground-color: rgba($color-success,.4);\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $color-success;\n\t\t};\n\t\tborder-radius: 100px;\n\t\ttext-indent: 100%;\n\t\twhite-space: nowrap;\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Split field settings\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings-split {\n\tdisplay: flex;\n\tborder-top: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t};\n\t.acf-field {\n\t\tmargin: 0;\n\t\tpadding: {\n\t\t\ttop: 32px;\n\t\t\tbottom: 32px;\n\t\t};\n\n\t\t&:nth-child(2n) {\n\t\t\tborder-left: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\t\t};\n\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Display & return format\n*\n*----------------------------------------------------------------------------*/\n.acf-field-setting-display_format,\n.acf-field-setting-return_format {\n\n\t.acf-label {\n\t\tmargin: {\n\t\t\tbottom: 6px;\n\t\t};\n\t}\n\n\t.acf-radio-list {\n\n\t\tli {\n\t\t\tdisplay: flex;\n\n\t\t\tlabel {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\twidth: 100%;\n\n\t\t\t\tspan {\n\t\t\t\t\tflex: 1 1 auto;\n\t\t\t\t}\n\n\t\t\t\tcode {\n\t\t\t\t\tpadding: {\n\t\t\t\t\t\tright: 8px;\n\t\t\t\t\t\tleft: 8px;\n\t\t\t\t\t};\n\t\t\t\t\tbackground-color: $gray-100;\n\t\t\t\t\tborder-radius: 4px;\n\t\t\t\t\t@extend .p5;\n\t\t\t\t\tcolor: $gray-600;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tinput[type=\"text\"] {\n\t\t\t\theight: 32px;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\n.acf-field-settings .acf-field-setting-first_day {\n\tpadding: {\n\t\ttop: 32px;\n\t};\n\tborder-top: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t};\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Image and Gallery fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-object-image,\n.acf-field-object-gallery {\n\n\t.acf-hl[data-cols=\"3\"] > li {\n\t\twidth: auto;\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n* Appended fields fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field-appended {\n\toverflow: auto;\n\n\t.acf-input {\n\t\tfloat: left;\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Flexible widths for image minimum / maximum size fields\n*\n*----------------------------------------------------------------------------*/\n.acf-field-settings .acf-field.acf-field-setting-min_width,\n.acf-field-settings .acf-field.acf-field-setting-max_width {\n\n\t.acf-input {\n\t\tmax-width: none;\n\t}\n\n\t.acf-input-wrap input[type=\"text\"] {\n\t\tmax-width: 81px;\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Temporary fix to hide pagination setting for repeaters used as subfields.\n*\n*----------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\t.acf-field-object-flexible-content {\n\t\t.acf-field-setting-pagination {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n\n\t.acf-field-object-repeater {\n\t\t.acf-field-object-repeater {\n\t\t\t.acf-field-setting-pagination {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Flexible content field width\n*\n*----------------------------------------------------------------------------*/\n\n.acf-admin-single-field-group .acf-field-object-flexible-content .acf-is-subfields .acf-field-object {\n\n\t.acf-label,\n\t.acf-input {\n\t\tmax-width: 600px;\n\t}\n\n}\n\n/*----------------------------------------------------------------------------\n*\n*  Fix default value checkbox focus state\n*\n*----------------------------------------------------------------------------*/\n\n.acf-admin-single-field-group {\n\n\t.acf-field.acf-field-true-false.acf-field-setting-default_value .acf-true-false {\n\t\tborder: none;\n\n\t\tinput[type=\"checkbox\"] {\n\t\t\tmargin-right: 0;\n\t\t}\n\n\t}\n\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Sub-fields layout\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub {\n\tmax-width: 100%;\n\toverflow: hidden;\n\tborder-radius: $radius-lg;\n\tborder: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: darken($gray-200, 5%);\n\t};\n\tbox-shadow: $elevation-01;\n\n\t// Header\n\t.acf-sub-field-list-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-content: stretch;\n\t\talign-items: center;\n\t\tmin-height: 64px;\n\t\tpadding: {\n\t\t\tright: 24px;\n\t\t\tleft: 24px;\n\t\t};\n\t}\n\n\t// Main sub-fields wrapper\n\t.acf-field-list-wrap {\n\t\tbox-shadow: none;\n\t}\n\n\t// Sub-field footer\n\t.acf-hl.acf-tfoot {\n\t\tmin-height: 64px;\n\t\talign-items: center;\n\t}\n\t\n\t// Secondary level sub-fields\n\t.acf-input.acf-input-sub {\n\t\tmax-width: 100%;\n\t\tmargin: {\n\t\t\tright: 0;\n\t\t\tleft: 0;\n\t\t};\n\t}\n\n}\n\n.post-type-acf-field-group .acf-input-sub .acf-field-object .acf-sortable-handle {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.post-type-acf-field-group .acf-field-object:hover .acf-input-sub .acf-sortable-handle:before {\n\tdisplay: none;\n}\n\n.post-type-acf-field-group .acf-field-object:hover .acf-input-sub .acf-field-list .acf-field-object:hover .acf-sortable-handle:before {\n\tdisplay: block;\n}\n\n.post-type-acf-field-group .acf-field-object .acf-is-subfields .acf-thead .li-field-label:before {\n\tdisplay: none;\n}\n\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub .acf-field-object.open {\n\tborder-top-color: darken($gray-200, 5%);\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Flexible content field\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\ti.acf-icon.-duplicate.duplicate-layout {\n\t\tmargin: 0 auto !important;\n\t\tbackground-color: $gray-500;\n\t\tcolor: $gray-500;\n\t}\n\ti.acf-icon.acf-icon-trash.delete-layout {\n\t\tmargin: 0 auto !important;\n\t\tbackground-color: $gray-500;\n\t\tcolor: $gray-500;\n\t}\n\n\tbutton.acf-btn.acf-btn-tertiary.acf-field-setting-fc-duplicate, button.acf-btn.acf-btn-tertiary.acf-field-setting-fc-delete {\n\t\tbackground-color: #ffffff !important;\n\t\tbox-shadow: $elevation-01;\n\t\tborder-radius: 6px;\n\t\twidth: 32px;\n\t\theight: 32px !important;\n\t\tmin-height: 32px;\n\t\tpadding: 0;\n\t}\n\n\tbutton.add-layout.acf-btn.acf-btn-primary.add-field,\n\t.acf-sub-field-list-header a.acf-btn.acf-btn-secondary.add-field, \n\t.acf-field-list-wrap.acf-is-subfields a.acf-btn.acf-btn-secondary.add-field {\n\t\theight: 32px !important;\n\t\tmin-height: 32px;\n\t\tmargin-left: 5px;\n\t}\n\n\t.acf-field.acf-field-setting-fc_layout {\n\t\tbackground-color: #ffffff;\n\t\tmargin-bottom: 16px;\n\t}\n\t\n\t.acf-field-setting-fc_layout {\n\t\t.acf-field-layout-settings.open {\n\t\t\tbackground-color: #ffffff;\n\t\t\tborder-top: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-200;\n\t\t\t};\n\t\t}\n\n\t\toverflow: hidden;\n\t\twidth: calc(100% - 144px);\n\t\tmargin: {\n\t\t\tright: 72px;\n\t\t\tleft: 72px;\n\t\t};\n\t\tpadding: {\n\t\t\tright: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: darken($gray-200, 5%);\n\t\t};\n\t\tborder-radius: $radius-lg;\n\t\tbox-shadow: $elevation-01;\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\twidth: calc(100% - 16px);\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t\tleft: 8px;\n\t\t\t};\n\t\t}\n\n\t\t// Secondary level sub-fields\n\t\t.acf-input-sub {\n\t\t\tmax-width: 100%;\n\t\t\tmargin: {\n\t\t\t\tright: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t}\n\n\t\t.acf-label,\n\t\t.acf-input {\n\t\t\tmax-width: 100% !important;\n\t\t}\n\n\t\t.acf-input-sub {\n\t\t\tmargin: {\n\t\t\t\tright: 32px;\n\t\t\t\tbottom: 32px;\n\t\t\t\tleft: 32px;\n\t\t\t};\n\t\t}\n\n\t\t.acf-fc-meta {\n\t\t\tmax-width: 100%;\n\t\t\tpadding: {\n\t\t\t\ttop: 24px;\n\t\t\t\tright: 32px;\n\t\t\t\tleft: 32px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t.acf-field-settings-fc_head {\n\t\tbackground-color: $gray-50;\n\t\tborder-radius: 8px 8px 0px 0px;\n\t\tdisplay: flex;\n\t\tmin-height: 64px;\n\t\tmargin: {\n\t\t\tbottom: 0px;\n\t\t};\n\t\tpadding: {\n\t\t\tright: 24px;\n\t\t};\n\n\t\t.acf-fc_draggable {\n\t\t\tmin-height: 64px;\n\t\t\tpadding-left: 24px;\n\t\t\tdisplay: flex;\n\t\t}\n\n\t\tspan.toggle-indicator {\n\t\t\tpointer-events: none;\n\t\t\tmargin-top: 7px;\n\t\t}\n\n\t\tlabel {\n\t\t\tdisplay: inline-flex;\n\t\t\talign-items: center;\n\t\t\t@extend .acf-h3;\n\n\t\t\t&:before {\n\t\t\t\tcontent: '';\n\t\t\t\t$icon-size: 20px;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 8px;\n\t\t\t\t};\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\n\t\t\t\t@at-root .rtl#{&} {\n\t\t\t\t\tpadding-right: 10px;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-fl-actions {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.acf-fc-add-layout {\n\t\t\t\tmargin-left: 10px;\n\t\t\t}\n\n\t\t\t.acf-fc-add-layout .add-field {\n\t\t\t\tmargin-left: 0px !important;\n\t\t\t}\n\n\t\t\tli {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 4px;\n\t\t\t\t};\n\n\t\t\t\t&:last-of-type {\n\t\t\t\t\tmargin: {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Field open / closed icon state\n*\n*---------------------------------------------------------------------------------------------*/\n\n.post-type-acf-field-group .acf-field-object.open > .handle > .acf-tbody > .li-field-label::before {\n\t-webkit-mask-image: url('../../images/icons/icon-chevron-up.svg');\n\tmask-image: url('../../images/icons/icon-chevron-up.svg');\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Different coloured levels (current 5 supported)\n*\n*---------------------------------------------------------------------------------------------*/\n\n.post-type-acf-field-group #acf-field-group-fields .acf-field-list-wrap .acf-input-sub {\n\t\n\t// Second level\n\t$nested-color: #BF7DD7;\n\t// Row hover color \n\t.acf-field-object .handle { background-color: transparent; &:hover { background-color: lighten($nested-color, 30%); } }\n\t// Active row color \n\t.acf-field-object.open .handle { background-color: lighten($nested-color, 28%); }\n\t// Active border color \n\t.acf-field-object .settings { border-left: { color: $nested-color; }; }\n\t\n\t// Third level\n\t.acf-input-sub {\n\t\t$nested-color: #7CCDB9;\n\t\t// Row hover color \n\t\t.acf-field-object .handle { background-color: transparent; &:hover { background-color: lighten($nested-color, 30%); } }\n\t\t// Active row color \n\t\t.acf-field-object.open .handle { background-color: lighten($nested-color, 28%); }\n\t\t// Active border color \n\t\t.acf-field-object .settings { border-left: { color: $nested-color; }; }\n\t\t\n\t\t// Fourth level\n\t\t.acf-input-sub {\n\t\t\t$nested-color: #E29473;\n\t\t\t// Row hover color \n\t\t\t.acf-field-object .handle { background-color: transparent; &:hover { background-color: lighten($nested-color, 30%); } }\n\t\t\t// Active row color \n\t\t\t.acf-field-object.open .handle { background-color: lighten($nested-color, 28%); }\n\t\t\t// Active border color \n\t\t\t.acf-field-object .settings { border-left: { color: $nested-color; }; }\n\t\t\t\n\t\t\t// Fifth level\n\t\t\t.acf-input-sub {\n\t\t\t\t$nested-color: #A3B1B9;\n\t\t\t\t// Row hover color \n\t\t\t\t.acf-field-object .handle { background-color: transparent; &:hover { background-color: lighten($nested-color, 30%); } }\n\t\t\t\t// Active row color \n\t\t\t\t.acf-field-object.open .handle { background-color: lighten($nested-color, 28%); }\n\t\t\t\t// Active border color \n\t\t\t\t.acf-field-object .settings { border-left: { color: $nested-color; }; }\n\t\t\t}\n\t\t\t\n\t\t}\n\t\t\n\t}\n\t\n}"], "names": [], "sourceRoot": ""}