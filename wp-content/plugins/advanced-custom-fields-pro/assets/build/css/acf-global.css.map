{"version": 3, "file": "acf-global.css", "mappings": ";;;AAAA,gBAAgB;ACAhB;;;;8FAAA;AAMA;AAOA;AAQA;AAgBA;;;;8FAAA;ACrCA;;;;8FAAA;ACCA;;;;8FAAA;AAMA;AACA;EAAU;EAAY;EAAW;EAAkB;EAAgB;AHuBnE;;AGtBA;EAAe;EAAa;EAAgB;EAAW;AH6BvD;;AG5BA;EAAsB;AHgCtB;;AG7BA;AACA;;;EAGI;EACA;EACA;AHgCJ;;AG9BA;;;EAGI;AHiCJ;;AG7BA;AACA;EAAU;EAAY;EAAW;EAAkB;EAAgB;AHqCnE;;AGpCA;EAAe;EAAgB;EAAW;EAAY;AH2CtD;;AGxCA;AACA;EACC;AH2CD;;AGzCA;EACC;AH4CD;AG3CC;EAAI;AH8CL;;AG3CA;AACA;EAAU;AH+CV;;AG9CA;EAAU;AHkDV;;AGjDA;EAAU;AHqDV;;AGlDA;AACA;EAAU;AHsDV;;AGrDA;EAAU;AHyDV;;AGxDA;EAAU;AH4DV;;AGzDA;AACA;;EAEC;EACA;EACA;EACA;EACA;AH4DD;;AGxDA;AACA;EACC;AH2DD;;AGxDA;EACC;AH2DD;;AGvDA;AACA;EACC;AH0DD;;AGtDA;AACA;EACC;AHyDD;;AGrDA;AACA;EACC;EACA;EACA;EACA;EAEA;EACA;AHuDD;;AGpDA;EACC;EACA;EACA;EACA;EAEA;EACA;AHsDD;;AGlDA;AACA;EAAiB;AHsDjB;;AGrDA;EAAiB;AHyDjB;;AGvDA;EAA4B;AH2D5B;;AG1DA;EAA4B;AH8D5B;;AG5DA;AACA;EACC;EACA;EACA;EACA;AH+DD;;AG3DA;;;;+FAAA;AAMA;AACA;EACI,mBFjFO;EEkFP,kBFnDQ;EEoDR,cFxFO;EE0FT;EACA;EACA;EACA;EAEE;EAEA;EACH;EACA;EAGG;EAUA;AH+CJ;AGxDI;EACC;EACA;EACA;EACA;EACA;AH0DL;AGrDI;EACC;AHuDL;AGrDK;EACF;EACA;EACA;EACA;EACA;AHuDH;AGnDI;EACC;AHqDL;AGnDK;EACF;EACA;EACA;EACA;EACA;AHqDH;AGjDI;EACA;AHmDJ;AGjDI;EACD;EACA;EACA;EACA;EACA;AHmDH;AG/CI;EACA;AHiDJ;AG/CI;EACD;EACA;EACA;EACA;EACA;AHiDH;AG7CI;EACF;AH+CF;;AGzCA;AACA;EACC;AH4CD;AG1CC;EACC;EACA;AH4CF;AG1CE;EACC;AH4CH;AGzCE;EACC;AH2CH;;AGtCA;EACC;EACA;EACA;EACA;EACA;EACA;AHyCD;;AGtCA;EACC;EACA;AHyCD;;AGrCA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHwCD;AGtCC;EDnPA;EACA;EACA;EACA;AF4RD;;AGtCA;;;;8FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHyCD;AGvCC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHyCF;;AGpCA;EACC;AHuCD;;AGrCA;EACC;AHwCD;;AGtCA;EACC;EACA;AHyCD;;AGvCA;EACC;AH0CD;;AGxCA;EACC;AH2CD;;AGzCA;EACC;EAGA;AH0CD;;AGxCA;EACC;EAGA;AHyCD;;AGvCA;EACC;EAGA;AHwCD;;AGtCA;EACC;EAGA;AHuCD;;AGrCA;EACC;AHwCD;;AGtCA;EACC;EAGA;EACA;AHuCD;;AGrCA;EACC;AHwCD;;AGtCA;EACC;EAGA;AHuCD;;AGrCA;EACC;EAGA;AHsCD;;AGpCA;EACC;AHuCD;;AGrCA;EACC;EAGA;AHsCD;;AGpCA;EACC;EAGA;AHqCD;;AGnCA;EACC;AHsCD;;AGpCA;EACC;AHuCD;;AGnCA;EACC;AHsCD;AGrCC;EAEC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHsCF;AGpCC;EACC;EACA;AHsCF;AGpCC;EACC;AHsCF;;AGlCA;EACC;AHqCD;AGpCC;EAEC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHqCF;AGnCC;EACC;EACA;AHqCF;AGnCC;EACC;AHqCF;;AGhCA;EACC;EAGA;AHiCD;;AG/BA;EACC;EAGA;AHgCD;;AG5BA;EACC;EACA;EACA;AH+BD;;AG3BA;EACC;EACA;EACA;EACA;EACA;EACA;AH8BD;AG3BC;EACC;EACA;EACA;AH6BF;AG3BC;EAEC;EACA;EACA;AH4BF;AGxBC;EAEC;EACA;AHyBF;;AGpBA;EACC;EACA;EACA;AHuBD;;AGnBA;EACC;EACA;EACA;AHsBD;;AGlBA;EACC;EACA;EACA;AHqBD;;AGlBC;EACC;EACA;AHqBF;AGnBC;EAEC;AHoBF;;AGfA;EACC;EACA;EACA;AHkBD;AGhBC;EACC;EACA;AHkBF;AGhBC;EAEC;AHiBF;;AGZA;;EAEC;EACA;EACA;EACA;AHeD;AGVE;;;EAEC;AHaH;;AGRA;;;;8FAAA;AAKA;EACI;EACA;EACA;EACA;EAEA;EA+CH;AHpCD;AGVI;EACF;EACG;EACA;AHYL;AGVE;EACC;EACA;EACA;EACG;EACA;EACA;AHYN;AGPC;EACI;AHSL;AGNC;EACC;EACG;EACA;EACA;EACA;AHQL;AGLC;EACC;AHOF;AGJC;EACC;AHMF;AGHC;EACC;AHKF;AGDE;EACC;AHGH;AGEC;EACI;EACA;EACA;EACA;AHAL;AGEK;EACC;AHAN;AEpkBC;EC0kBC,qBFnlBiB;ADglBnB;AGIE;;EAEC,qBFtlBgB;ADolBnB;;AGOA;;;;8FAAA;AAMA;EACC;EACA;EACA;EACA;EACA;EACA,mBF/nBY;EEgoBZ;AHLD;AGOC;EACC;EACA;EACA;EACA;EACA;AHLF;AGQC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AHNF;AGOE;EACC;AHLH;AGUC;EACC;AHRF;AGYC;EACC,mBF7pBU;EE8pBV;AHVF;AGcC;EACC,mBFlqBY;EEmqBZ;AHZF;AGgBC;EACC,mBFvqBY;EEwqBZ;AHdF;;AGmBA;;;;8FAAA;AAMA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACG;EACA;EACA;EAEA;EAqBA;EAmBA;EAuBA;AH9EJ;AGmBK;;;EACF;EACA;EACA;EACA;EACG;EACA;AHfN;AGkBE;;EACC;EACA;AHfH;AG0BG;EACI,qBF1sBY;EE2sBf;AHxBJ;AG0BI;EACC;AHxBL;AGoCK;EACF;AHlCH;AGoCG;EACC,qBF7tBe;EE8tBf;AHlCJ;AGoCI;EACC;AHlCL;AGsCG;EACC;AHpCJ;AG4CI;EACC;AH1CL;AG+CM;;;EACC;EACH;AH3CJ;;AGkDA;AACA;EACC;EACA;EACA;EACA;EAEA;EACA;AHhDD;;AGoDA;AACA;EACC;EACA;EACA;EACA;EAEA;EACA;AHlDD;;AGqDA;;;;+FAAA;AAMA;;;EAGC;EACA;EACA;AHnDD;AGqDC;;;EACC;EAEC;EAED;EACA;AHnDF;;AGuDA;EACC;EACA;AHpDD;AGsDC;EACC;EACA;EACA;AHpDF;AEjvBC;EC0yBC,qBFlzBmB;AD4vBrB;;AG0DA;EACC;EACA;AHvDD;;AG0DA;;;;8FAAA;AAQC;EACC;AH1DF;AG6DC;EACC;AH3DF;AG8DC;EACC;AH5DF;AG8DE;EACC;AH5DH;;AGmEA;;;;8FAAA;AAMA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AHjED;AGoEC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AHlEF;AGqEC;EACC;EACA;EACA;EACA;AHnEF;AGuEC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHrEF;AE1zBC;EACC;AF4zBF;AGsEE;EACC;EACA;AHpEH;AGuEG;EACC;EACA;EACA;AHrEJ;AGwEI;EACC;EACA;AHtEL;AG2EE;EACC;EAGA;EACA;AH3EH;AG+EE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH7EH;AG+EG;EDt7BF;EACA;EACA;EACA;AF02BD;;AGmFA;EACC;EACA;AHhFD;AGmFC;EACC;EACA;AHjFF;AGmFE;EACC;AHjFH;AGsFC;EACC;AHpFF;;AGwFA;;;;8FAAA;AAMA;EACC;EACA;EACA;AHtFD;AEr5BC;EACC;EACA;EACA;AFu5BF;AGoFC;EACC;EACA;EACA;AHlFF;AGqFC;EACC;EACA;EACA;EACA;AHnFF;AGsFC;EACC;EACA;AHpFF;AGuFC;EACC;EACA;EACA;EACA;AHrFF;AGwFC;EACC;EACA;EACA;AHtFF;AGyFC;EACC;EACA;AHvFF;AG0FC;EACC;AHxFF;AG4FC;EAEC;;IAEC;IACA;IACA;IACA;EH3FD;AACF;;AGgGA;EACC;AH7FD;;AGiGA;EACC;AH9FD;;AGiGA;;;;8FAAA;AAQC;EACC;EACA;AHjGF;AGoGC;EACC;EACA;AHlGF;AGqGC;EACI;EACA;EACA;EACA;EACA;AHnGL;AGsGC;EACI;AHpGL;AGsGK;EACC;AHpGN;AGwGC;EACC;EACA;AHtGF;AGwGE;EACC;AHtGH;AG0GC;EACC;EACA;EACA;AHxGF;AG0GE;EACC;EACA;EACA;EACA;AHxGH;AG0GG;EAND;IAOE;EHvGF;AACF;AGyGG;EAVD;IAWE;EHtGF;AACF;AGyGE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHvGH;AG0GE;EACC;AHxGH;;AG6GA;;;;8FAAA;AAMA;EACC;EACA;AH3GD;AG6GC;EACC;EAEA;EACA;EACA;AH5GF;;AGkHA;AACA;EAA8B;AH9G9B;;AG+GA;EAA8B;AH3G9B;;AG4GA;EAA8B;AHxG9B;;AG2GA;AACA;EAEC;IACC;IACA;IACA;IACA;IACA;IACA;IACA;EHzGA;EG2GA;IACC;IACA;IACA;EHzGD;AACF;AGgHA;;;;8FAAA;AAMA;EACC;EACA;EAEA;EAUA;AHzHD;AGgHC;EACC;EACA;EACA;EACA;EACA;AH9GF;AGoHE;EACC;EACA;AHlHH;;AGwHA;AAEC;EAAK;EAAc;AHpHpB;;AGwHA;;;;8FAAA;AAMA;EAA0B;AHrH1B;;AGsHA;EAA0B;AHlH1B;;AGoHA;EACC;AHjHD;;AGoHA;EACI;AHjHJ;;AGoHA;EACC;EACA;AHjHD;;AGqHA;EACC;EACA;EACA;AHlHD;;AGqHA;EACC;EACA;EACA;AHlHD;;AGqHA;;EAEC;AHlHD;;AGqHA;EACC;AHlHD;;AGsHA;;;;+FAAA;AAMA;EAEC;EACA;EACA;EACA;EACA;AHrHD;AEvpCC;EACC;EACA;EACA;AFypCF;AGkHC;;EDtwCA;EACA;EACA;ECuwCC;AH9GF;AGiHC;EACC;EACA;AH/GF;AGkHC;EACC;EACA;EACA;AHhHF;AGkHE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,mBFjxCgB;ADiqCnB;AGsHE;EACC,mBFlxCkB;AD8pCrB;;AGyHA;AACA;EAEC;IACC;EHvHA;EGyHA;;IAEC;IACA;IACA;IACA;EHvHD;EG0HA;IACC;EHxHD;EG0HC;IACC;EHxHF;AACF;AG8HA;;;;+FAAA;AAMA;EACC;EACA;EACA;EAoBA;EAUA;EAOA;AH/JD;AG4HC;EACC;EACA;EACA;EACA;EACA;AH1HF;AG4HE;EACC;AH1HH;AG8HC;EACC;EACA;EACA;AH5HF;AGkIE;EACC;AHhIH;AGuIC;EACC;EACA;AHrIF;AG0IC;EACC;AHxIF;AG0IE;EACC;EACA;AHxIH;AG2IE;EACC;AHzIH;AEvtCC;ECs2CC,qBF92CmB;ADkuCrB;;AGiJA;;;;+FAAA;AAQC;EACC;AHjJF;AGoJC;EAMC;AHvJF;AGmJE;EACC;AHjJH;AGqJE;EAEE;EAED;EACA;EACA;AHrJH;AGuJG;EACC;AHrJJ;AGwJG;EAGE;AHxJL;AG4JG;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA,qBFr5CM;EEu5CP,kBFn3CQ;ADqtCZ;;AGwKC;EDl8CA;EACA;EACA;AF8xCD;AGqKE;EACC;AHnKH;AGsKE;EACC;EACA;EACA;EACA;EAGA;EACA;EACA;AHtKH;AGyKE;;;EAGC;AHvKH;;AG8KA;AACA;EACC;EACA;AH3KD;AG6KC;EACC;EACA;EACA;EACA;AH3KF;AG6KE;EACC;AH3KH;AG8KE;EACC;EACA;EACA;AH5KH;;AGkLA;AACA;EAEC;IACC;IACA;EHhLA;EGkLA;IACC;IACA;IACA;EHhLD;AACF;AGqLA;AACA;EA0BC;AH5MD;AGoLC;EACC;AHlLF;AGqLC;EACC;AHnLF;AGsLC;EACC;EACA;AHpLF;AGuLC;EACC;EACA;EACA;EACA,mBF7/CS;EE8/CT,qBF3/CS;EE4/CT;EACA;EACA,kBF19CU;ADqyCZ;AG0LE;EACC;AHxLH;;AG8LA;EACC;AH3LD;AG4LC;EACC;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACG;EACA;EACA;AH7LL;AG+LI;EACC;EACA;EACA;EACA;EAEA;EACH;EACA;EAEG;EACA;AH/LL;AGmME;EACC;AHjMH;;AGyMC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHtMF;AGwME;EACC;EACA;AHtMH;AGyME;;EAEC;EACA;AHvMH;AG2MC;EACC;EACA;EACA;EACA;EACA;EACA;AHzMF;AG4MC;EACC;AH1MF;AG4ME;EACC;AH1MH;AG6ME;EACC;EACA;AH3MH;AG+ME;EACC;AH7MH;AGgNE;EACC;AH9MH;AGmNC;EACC;IACC;EHjND;EGmNA;IACC;EHjND;AACF;;AGqNA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHlND;AGoNC;;;EAGC;EACA;EACA;EACA;AHlNF;AGqNC;EACC;EACA;EACA;AHnNF;AGqNE;EACC;EACA;EACA;AHnNH;AGqNE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHnNH;AGoNG;EACC;AHlNJ;AGuNC;EACC;EACA;EACA;EACA;EACA;AHrNF;AGwNC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;AHtNF;AGwNE;EACC;EACA;AHtNH;AG0NC;EACC;EACA;EACA;EACA;AHxNF;AG0NE;EACC;AHxNH;AG6NC;EAjFD;IAkFE;IACA;IACA;IACA;EH1NA;AACF;;AG6NA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH1ND;;AG8NA;;;;+FAAA;AAMA;EAQC;;IAEC;IACA;EHnOA;AACF;AGuOA;;;;8FAAA;AAQC;EAEE;EACA;EACA;EACA;AHzOH;AG4OE;EARD;IAUG;IACA;EH1OF;AACF;AG+OC;EAEE;EACA;AH9OH;AGiPE;EAND;IAQG;IACA;EH/OF;AACF;AGoPE;EADD;IAGG;EHlPF;AACF;;AGyPA;;;;+FAAA;AAKA;;EAEC;EACA,kBF5wDW;EE6wDX,6CF1wDc;ADohDf;AGwPC;;EAEE;EACA;EACA;EACA;AHtPH;AG0PC;;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;AH1PH;AG8PC;;;;EAGE;EACA;EACA;EACA;EAGA;EACA;EACA,yBFx1DQ;AD2lDX;AGiQC;;;;EAEC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGA;EACA;AHlQH;AGqQE;;;;;;;;EAGE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGD,cF73DQ;AD2nDX;AGuQC;;EAEE;EACA;EACA;EACA;AHrQH;;AG4QA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGA;EACA;EACA,4BF76DS;AD+pDX;AGiRC;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGD,cFz7DS;ADqqDX;AGuRC;EAEE;EACA;AHtRH;AG0RC;EACC,yBFv8DS;AD+qDX;;AG6RA;;;;+FAAA;AAOC;EAEE;AH7RH;AGgSE;EACC,qBF19DQ;AD4rDX;AGiSE;EATD;IAWG;IACA;EH/RF;AACF;AGoSC;EAEE;EACA;AHnSH;AGsSE;EAND;IAQG;IACA;EHpSF;AACF;AGwSC;EACC,qBFr/DS;AD+sDX;;AG2SA;;;;+FAAA;AAWG;;EAEC;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAGD;AHnTJ;;AG4TA;;;;+FAAA;AAOC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,kBFxgEU;AD6sDZ;AG8TC;EACC;AH5TF;;AGiUA;;;;8FAAA;AAOC;EACC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED,yBF/jES;EEikER;EACA;EACA,qBFjkEQ;EEmkET,kBF1iEU;ADsuDZ;AGuUE;EAEE;AHtUJ;;AG8UA;;;;8FAAA;AAKA;EACC;AH3UD;AG6UC;EAEE;AH5UH;;AGkVA;;;;8FAAA;AAOC;;EAEC;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAED;EAEC;EACA;EACA;EAED,kBFpmEU;EEqmEV,6CFlmEa;EEmmEb,cFvoES;ADgzDX;AGyVE;;EACC;EACA;EACA;EAEC;EACA;EACA;EACA;AHvVJ;AG2VE;;EACC;EAEC;EAED;EACA;AH1VH;AG8VE;;EAEE;EACA;AH5VJ;AGgWE;;EACC;EACA;EACA;AH7VH;AG+VG;;EAEE;EAGA;EAGD;AHjWJ;AGuWE;;EACC;EACA;EACA;AHpWH;AGsWG;;EACC;EAEA;EACA;EACA;EACA,WAJY;EAKZ,YALY;EAMZ,yBFxsEO;EEysEP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AHpWJ;AGuWG;;EACC,yBFptEO;ADg3DX;AG0WE;;EACC;EAEA;EACA;EACA;EACA;EACA;EACA,WANY;EAOZ,YAPY;EASX;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AH1WH;AG6WE;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,yBF5uEU;EE6uEV,kBF9tES;EE+tET,6CF3tEY;ADi3Df;AG6WE;;EACC;EAEC;EACA;AH3WJ;AGkXC;EAEE;AHjXH;AGsXC;EACC;EACA;AHpXF;AGsXE;EACC;EACA;AHpXH;AGuXE;EACC,yBF5wEa;ADu5DhB;AG2XC;;EAEC;EACA;AHzXF;AG2XE;;EACC;EACA;AHxXH;AG2XE;;EACC,yBF3xEY;ADm6Df;;AI1+DA;;;;+FAAA;AAKA;EACC;AJ6+DD;;AI1+DA;;;;+FAAA;AAOC;EACC,cH4CS;AD+7DX;;AIt+DA;;;;+FAAA;AAMA;;EACC;EACA;AJy+DD;;AIt+DA;;EACC;EACA;AJ0+DD;;AIv+DA;;;;;;;;;;EACC;EACA;AJm/DD;;AI/9DA;;;;+FAAA;AAQC;EACC;AJ+9DF;AI59DC;EACC;AJ89DF;AI39DC;EACC;AJ69DF;AI19DC;;;;;;;;;;;EACC;AJs+DF;AIn+DC;;EACC;AJs+DF;AIn+DC;EACC;AJq+DF;AIl+DC;;EACC;AJq+DF;AIl+DC;EACC;AJo+DF;;AI/9DA;;;;+FAAA;AAKA;EAEC,cH1DU;AD2hEX;;AI99DA;;;;+FAAA;AAOC;;EAEC;AJ+9DF;;AI19DA;;;;+FAAA;AASA;;;;+FAAA;AAKA;EACC;EACA;AJy9DD;;AIv9DA;EACC;EACA;AJ09DD;;AK5mEA;EAEC;;;;iGAAA;EAuCA;;;;iGAAA;EAcA;;;;iGAAA;EAcA;;;;iGAAA;EAeA;;;;iGAAA;EA6CA;;;;iGAAA;EAsEA;;;;iGAAA;EAkBA;;;;iGAAA;EAkBA;;;;iGAAA;EAqCA;;;;iGAAA;EAwGA;;;;iGAAA;EAqCA;;;;iGAAA;EAkCA;;;;iGAAA;EASA;;;;iGAAA;EAyHA;;;;iGAAA;EA+BA;;;;iGAAA;AL+lDD;AKxrEC;;;;;EAKC;EACA;EAEC;EACA;EAED;EACA,qBJ4BS;EI3BT,6CJmEa;EIlEb,kBJ8DU;EI5DV,cJ4BS;AD2pEX;AKrrEE;;;;;EACC,0BJgEO;EI/DP,qBJiCQ;AD0pEX;AKxrEE;;;;;EACC,yBJaQ;EIZR;AL8rEH;AK3rEE;;;;;EACC,cJYQ;ADqrEX;AKrrEE;EACC,yBJLQ;EIMR,cJFQ;ADyrEX;AK3qEE;;EAEC;AL6qEH;AKnqEC;EACC;EAEC;EACA;EAED;EACA;ALmqEF;AK3pEC;EACC;EACA;EAEC;EACA;EAED;EACA;EACA;AL2pEF;AKxpEE;EAEC,cJ1CQ;ADmsEX;AKtpEE;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ALwpEH;AKlpEE;EAEE;EACA;EAED;ALkpEH;AKzoEC;;EAEC;EACA;EACA;EACA;EAEC;EACA;EACA,qBJ/FQ;EIiGT;EACA;ALyoEF;AKvoEE;;EACC,yBJ7FQ;EI8FR,qBJzFQ;ADmuEX;AKvoEE;;;EAEC,yBJnGQ;EIoGR,qBJ/FQ;ADyuEX;AKxoEG;;;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AL4oEJ;AKvoEE;;EACC;AL0oEH;AKvoEE;;EACC,yBJxIQ;EIyIR,qBJtIQ;ADgxEX;AKhoEI;;;EACC;ALooEL;AKnnEG;EACC;ALqnEJ;AKpmEG;EACC;ALsmEJ;AKvlEE;;;;EAGE;AL0lEJ;AKtlEE;;EAEE;ALwlEJ;AKrlEG;;EAEE;ALulEL;AKhlEE;;EACC;EACA;EACA;ALmlEH;AKzkEC;EACC;EACA;EACA;EACA,yBJ1OS;EI2OT;AL2kEF;AKzkEE;EACC,yBJ7OQ;ADwzEX;AKxkEE;EACC;AL0kEH;AKvkEE;EACC,yBJxOQ;ADizEX;AKvkEG;EACC,yBJ1OO;ADmzEX;AKtkEG;EACC;ALwkEJ;AKnkEE;;EAEC;ALqkEH;AKlkEE;EACC;EACA;EACA;EACA;EACA;ALokEH;AK/jEC;EACC;EACA;ALikEF;AK/jEE;EACC;EACA;EACA;EAEC;EACA;EACA;ALgkEJ;AK7jEG;EAEE;AL8jEL;AK1jEG;EAEE;AL2jEL;AKvjEG;EACC;EAEC;EACA;ALwjEL;AK9iEG;EAEE;EACA;AL+iEL;AK3iEG;EAEE;EACA;AL4iEL;AKhiEC;EACC;EACA;EAEC;EAGA;EACA;EACA;EACA;EAED;EACA;EACA,kBJzTU;EI2TT;EACA;EACA,qBJnVQ;EIqVT;AL4hEF;AK1hEE;EACC,qBJvVQ;EIwVR;EACA;AL4hEH;AKjhEC;EACC;EACA;EACA;EAEC;EACA;EAED;EACA;EACA;EACA,qBJhXS;EIiXT,kBJ3VU;EI6VV,cJnXS;ADm4EX;AK9gEE;EACC;EACA,qBJvXQ;EIwXR,cJxXQ;ADw4EX;AK9gEE;EACC;EACA,0BJ/VO;EIgWP,cJ7XQ;AD64EX;AKtgEC;EACC;ALwgEF;AK9/DE;EACC;EACA;ALggEH;AK7/DE;EACC;EAEC;EACA;EAED;EAEC;EACA;EACA,qBJ/aO;EIibR,6CJzYY;EI0YZ,kBJ9YS;EIgZT,cJhbQ;AD06EX;AKv/DE;EACC,0BJ7YO;EI8YP,qBJ5aQ;EI6aR,kBJtZS;AD+4EZ;AKv/DG;EACC;ALy/DJ;AKp/DI;EACC;EACA;ALs/DL;AK/+DI;EACC;EACA;ALi/DL;AK1+DE;EACC;EAEC;AL2+DJ;AKx+DG;EACC;EACA;AL0+DJ;AKr+DE;EAEE;EACA;EACA;EACA;ALs+DJ;AKl+DE;EACC;EACA;EAEC;EACA;EAED;EACA;EACA;ALk+DH;AKh+DG;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAIZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,yBJtgBO;ADu+EX;AK99DG;EACC,yBJtgBO;ADs+EX;AKp9DC;EACC;EACA;EACA;ALs9DF;AKp9DE;EAEC,WADY;EAEZ,YAFY;EAGZ,yBJ/hBQ;ADo/EX;AKl9DE;EAEE;ALm9DJ;AK/8DE;EAEE;ALg9DJ;AKr8DC;EACC;EACA;EACA;EACA;ALu8DF;AKr8DE;EACC;EACA;ALu8DH;;AKh8DC;EACC;EACA;ALm8DF;;AMzjFA;;;;+FAAA;AAQC;EACC;ANyjFF;AMrjFC;EACC;ANujFF;AMnjFC;EAEE;EACA;EACA;EACA;EAED,kBL2DU;EK1DV;EACA;EACA,6CL2Da;ADw/Ef;AMjjFE;EACC,cLiBQ;EKhBR;ANmjFH;AMhjFE;EACC;EACA;ANkjFH;AM/iFE;;EAEC,cLSQ;ADwiFX;AM/iFG;;EACC;ANkjFJ;AM/iFG;;EAEE;EACA;EACA;ANijFL;AM9iFI;EAPD;;IAQE;IAEC;IACA;ENijFJ;AACF;AM5iFG;;EACC;EACA;AN+iFJ;AMziFE;;EAEC;EAEC;EACA;EAED;EACA,yBLrCQ;EKsCR,qBLpCQ;EKsCR;ANwiFH;AMtiFG;EAbD;;IAeG;IACA;ENyiFH;AACF;AMriFI;EADD;;IAEE;ENyiFH;AACF;AMniFE;;EAEC;EACA;EAEC;EACA;EACA;EACA;EAED;EACA;EAEC;EACA,4BLtEO;EKuEP;ANkiFJ;AM9hFG;EAnBD;;IAqBG;IACA;ENiiFH;AACF;AM5hFE;EACC;AN8hFH;AM1hFE;EACC;EACA;EACA;EACA;EACA;EAEC;EAED,cLhGQ;AD0nFX;AMthFE;EACC;EACA;EACA;EACA;EAEC;EAED;EACA,cL7GQ;ADmoFX;AMnhFE;EAEC,cLjHQ;ADqoFX;AMhhFE;;EAEC;ANkhFH;AMhhFG;;EAEE;ANkhFL;AM3gFE;EACC;IAAoB;EN8gFrB;AACF;AM3gFG;EACC;EACA;EACA;EACA;AN6gFJ;AMtgFG;EAEE;EACA;ANugFL;AMngFG;EAEE;EACA;ANogFL;AM7/EC;EAEE;EAGA;EACA;EACA;EACA;EAGD;EACA,cLjLS;AD2qFX;AMx/EE;EACC,cL1NS;ADotFZ;AMn/EC;;EAGE;ANo/EH;;AM9+EA;;;;8FAAA;AAUE;EACC;AN4+EH;AMz+EE;EACC;AN2+EH;AM1+EG;EAAU;AN6+Eb;AM1+EE;EAEE;EAED;AN0+EH;;AMl+EA;;;;8FAAA;AAOC;;EAEC;ANm+EF;;AM99EA;;;;+FAAA;AAOC;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAED,cLnQS;AD8tFX;;AMt9EA;;;;8FAAA;AAKA;EAEE;EACA;EACA;EACA;ANw9EF;AMr9EC;EACC;EAEC;EACA;EACA;EACA;ANs9EH;AMl9EC;EAlBD;IAmBE;IACA;IACA;IACA;ENq9EA;EMn9EA;IACC;ENq9ED;AACF;;AM98EC;EAEE;EACA;ANg9EH;AM58EC;EARD;IASE;IACA;IACA;IACA;EN+8EA;AACF;;AM58EA;;;;8FAAA;AAKA;EACC;EACA;EACA;EAEC;AN88EF;AM38EC;EAEE;EACA;EAED,cLhVS;AD2xFX;AMx8EE;EACC,cLnVQ;AD6xFX;;AMn8EA;;;;8FAAA;AAOC;EACC;EACA;ANo8EF;AMl8EE;EAEE;EACA;EACA;EACA;ANm8EJ;AM/7EE;EACC;EACA;ANi8EH;AM/7EG;EAEE;EACA;EACA;EACA;ANg8EL;AM77EI;EAEE;AN87EN;AMr7EE;EACC;ANu7EH;;AMh7EA;;;;8FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EAED;AN+6ED;AM56EC;EAIC;EACA;EACA;EACA;EACA;EAEC;AN06EH;AMt6EE;EAEC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,yBLnbQ;EKobR;EACA,uBAXY;EAYZ,eAZY;EAaZ;EACA;EACA;EACA;ANs6EH;AMh6EC;EACC;EACA;ANk6EF;AM95EC;EACC;EACA;ANg6EF;AM55EC;EACC;EACA;AN85EF;AM15EC;EACC;EACA;AN45EF;AMx5EC;EACC,qBLxdS;EKydT;AN05EF;AMx5EE;EACC,yBL5dQ;ADs3FX;AMp5EC;EACC;ANs5EF;AMp5EE;EACC,yBLrfQ;AD24FX;;AM/4EA;;;;+FAAA;AAKA;EACC;EACA;EAEC;EACA;ANi5EF;AM94EC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;ANg5EF;AM94EE;;;EAGC;ANg5EH;AM74EE;EAGE;EACA;EAED;EACA,cL5hBQ;ADw6FX;AMz4EE;EAGE;EACA;EAED;EACA,cLxiBQ;ADg7FX;AMt4EG;EACC;EACA;EAEC;ANu4EL;AM/3EE;EACC;EAEC;ANg4EJ;AM53EE;EAEE;AN63EJ;;AMn3EC;;;;EAIC;ANs3EF;AMj3EE;;EAEC;ANm3EH;AM92EC;EACC;ANg3EF;;AM32EA;;;;+FAAA;AAOC;EACC;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EAEA,yBLrmBS;EKsmBT;EACA,uBATY;EAUZ,eAVY;EAWZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AN02EF;AMv2EC;EACC;EACA;ANy2EF;;AMp2EA;;;;+FAAA;AAOC;EAEC;;;IAGC;ENo2ED;AACF;;AOziGA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,mBNqCU;EMpCV,cNiCU;AD2gGX;AO1iGC;EAZD;IAaE;EP6iGA;AACF;AO3iGC;EAEE;AP4iGH;AOziGE;EACC;EACA;EACA;AP2iGH;AOtiGC;EACC;EACA,cNQS;ADgiGX;AOriGC;EACC;EACA;EACA;EACA;EAEC;EAGA;EACA;EACA;EACA;EAGA;EACA;EACA;EAED,kBNyBU;EMvBV,cNZS;EMaT;APgiGF;AO9hGE;EACC,yBNdQ;EMeR;APgiGH;AO9hGE;EACC,yBNlBQ;EMmBR,cNzBQ;ADyjGX;AO9hGE;EAEE;EACA;EACA,qBN1BO;ADyjGX;AO5hGE;EACC;AP8hGH;AOzhGC;EACC;EACA;EAEC;EACA;EACA;EACA;AP0hGH;AOrhGC;EACC;IACC;EPuhGD;AACF;;AOlhGC;EACC;EACA;APqhGF;AOnhGE;EAEE;EACA;APohGJ;AO/gGC;EAEE;EACA;APghGH;;AO3gGA;;;;+FAAA;AASE;EACC;EAEC;EACA;APygGJ;AOjgGG;EACC;APmgGJ;AO5/FG;EACC,yBN5GO;AD0mGX;AOt/FE;EAEE;EACA;APu/FJ;AO/+FE;EAEC,mEADW;EAEX,2DAFW;APk/Fd;AOx+FE;EAEC,4DADW;EAEX,oDAFW;AP2+Fd;AOj+FE;EAEC,8DADW;EAEX,sDAFW;APo+Fd;;AQxqGA;;;;+FAAA;AAOC;EACC;ARyqGF;AQtqGC;EACC;ARwqGF;;AQnqGA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EAEC;EAGA;EACA;EACA;EACA;EAED;EACA,6CP+Cc;ADmnGf;AQhqGC;EACC;EACA;EACA;EACA;EACA,iBPiDU;ADinGZ;AQ/pGC;EAEE;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;AR8pGH;AQ1pGC;EA3CD;IA4CE;ER6pGA;AACF;AQ3pGC;EA/CD;IAgDE;IACA;IACA;IACA;IAEC;ER6pGD;AACF;AQ1pGC;EACC;EACA;EACA;AR4pGF;AQ1pGE;EALD;IAME;ER6pGD;EQ3pGC;;IAEC;ER6pGF;EQ1pGC;IAEE;ER2pGH;AACF;AQppGC;EACC;EACA;EACA;EACA;EACA;EACA;ARspGF;AQppGE;EACC;EACA;EACA;ARspGH;AQlpGC;EACC;ARopGF;AQlpGE;EAHD;IAIE;ERqpGD;AACF;AQlpGC;EACC;ARopGF;AQlpGE;EAEE;ARmpGJ;AQ/oGE;EACC,yBP1FQ;EO2FR;EACA;EACA;ARipGH;;AQ1oGA;;;;+FAAA;AAKA;EACC;EACA;EACA;EAEC;EAED;AR2oGD;AQzoGC;EATD;IAUE;IACA;IACA;IAEC;IAGA;IACA;ERyoGD;AACF;AQtoGC;EAtBD;IAuBE;IACA;ERyoGA;AACF;AQpoGE;EAFD;IAGE;IACA;IACA;IACA;IACA;ERuoGD;EQroGC;IACC;ERuoGF;EQpoGC;IACC;IACA;IACA;ERsoGF;EQpoGE;IACC;IACA;IACA;IACA;ERsoGH;AACF;AQ9nGC;EAEE;AR+nGH;;AQznGA;;EAEC;EACA;AR4nGD;AQ1nGC;;EAEE;EACA;AR4nGH;AQvnGE;;EAEE;EACA;ARynGJ;;ASl2GA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EAEC;EACA;EACA;EACA;EAED,yBR6CU;EQ5CV,kBRkEW;EQhEV;EACA;EACA;EAED;EACA;EACA;EACA;ATi2GD;AS/1GC;EACC;ATi2GF;AS91GC;EACC,yBR6BS;EQ5BT;EACA;ATg2GF;AS71GC;EACC;EAEC;EACA;EACA;EACA;AT81GH;ASz1GC;EACC;EACA;EACA,qBRSS;ADk1GX;ASz1GE;EACC;AT21GH;ASt1GC;EACC;EACA;EACA,qBRhBS;ADw2GX;ASt1GE;EACC;EACA,qBRnBQ;AD22GX;;ASj1GA;;;;+FAAA;AAOC;EAEC,WADY;EAEZ,YAFY;EAGZ,uBAHY;EAIZ,eAJY;EAMX;EACA;ATg1GH;AS10GE;EAEC,WADY;EAEZ,YAFY;EAGZ,uBAHY;EAIZ,eAJY;EAMX;EACA;AT00GJ;;ASj0GC;EAEE;EACA;ATm0GH;AS9zGE;EAEE;EACA;AT+zGJ;;ASzzGA;;;;+FAAA;AAOC;EACC;EACA;EACA;AT0zGF;;AUr8GA;;;;8FAAA;AAKA;EAEC;EACA,WAFY;EAGZ,YAHY;EAIZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AVu8GD;;AUp8GA;;;;8FAAA;AAKA;EAwHC;;;;gGAAA;AVo1GD;AUz8GC;EACC;EAEA;EAEA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EAEA;EACA;EAEA;EACA;EAEA,6CTgCa;ES/Bb;EAEA;EAEA;EACA;EACA;AVk8GF;AU/7GC;EACC;EACA;AVi8GF;AU97GC;EACC;EACA;AVg8GF;AU77GC;EACC;EACA;AV+7GF;AU57GC;EACC;EACA;AV87GF;AU37GC;EACC;EACA;AV67GF;AU17GC;EACC;EACA;AV47GF;AU17GE;EAEC;AV27GH;AUv7GC;EACC;EACA;AVy7GF;AUt7GC;EACC;EACA;AVw7GF;AUr7GC;;EAEC;EACA;AVu7GF;AUp7GC;;EAEC;EACA;AVs7GF;AUn7GC;EACC;EACA;AVq7GF;AUl7GC;;EAEC;EACA;AVo7GF;AUj7GC;;EAEC;EACA;AVm7GF;AUh7GC;EACC;EACA;AVk7GF;AU/6GC;EACC;EACA;AVi7GF;AUx6GE;EACC;AV06GH;AUx6GG;EAEC;EACA,WAFY;EAGZ,YAHY;EAIZ,yBTnHO;ESoHP;EACA;EACA,uBAPY;EAQZ,eARY;EASZ;EACA;EACA;EACA;EACA;EACA;AVy6GJ;AUv6GI;EACC;AVy6GL;;AUl6GA;;;;8FAAA;AASE;;;;;;;;EAEC;EACA;EACA;EACA;AVu6GH;AUr6GG;;;;;;;;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAKX;EAED,yBThKO;ESiKP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AV26GJ;;AU/5GG;;;;;;;;EAEE;EACA;AVw6GL;;AUh6GA;;EAEC;EACA;AVm6GD;;AU/5GA;EACC;EACA;AVk6GD;;AU95GA;EACC;EACA;AVi6GD;;AU75GA;EACC;EACA;EACA;EACA;EACA;AVg6GD;;AU75GA;;;;8FAAA;AASC;;;;EACC;AV+5GF;AU75GE;;;;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAIZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AVi6GH;AU15GG;;;;EACC;EACA;AV+5GJ;;AUz5GA;;;;+FAAA;AAUE;;;;;;;;EAEC;EACA;EACA;EACA;AV65GH;AU35GG;;;;;;;;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAKX;EAED,yBT/RO;ESgSP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AVi6GJ;AUr5GI;;;;;;;;EAEE;EACA;AV65GN;;AUp5GA;EACC;EACA;AVu5GD;;AUn5GA;EACC;EACA;AVs5GD;;AUl5GA;EACC;EACA;AVq5GD;;AUj5GA;EACC;EACA;AVo5GD;;AUj5GA;;;;8FAAA;AAMC;EAEC,WADY;EAEZ,YAFY;AVo5Gd;;AWjyHA;;;;8FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,yBVyCU;EUvCT;EACA;EACA,qBVuCS;EUrCV;AXkyHD;AWhyHC;EAEC;EACA,WAFY;EAGZ,YAHY;EAIZ;EACA,yBVgCS;EU/BT;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AXiyHF;;AW5xHA;;;;8FAAA;AAOA;EACC;EACA;AX6xHD;;AWzxHA;EACC;EACA;AX4xHD;;AWxxHA;EACC;EACA;AX2xHD;;AWvxHA;EACC;EACA;AX0xHD;;AWtxHA;EACC;EACA;AXyxHD;;AWrxHA;EACC;EACA;AXwxHD;;AWpxHA;EACC;EACA;AXuxHD;;AWnxHA;EACC;EACA;AXsxHD;;AWlxHA;EACC;EACA;AXqxHD;;AWjxHA;EACC;EACA;AXoxHD;;AWhxHA;EACC;EACA;AXmxHD;;AW/wHA;EACC;EACA;AXkxHD;;AW9wHA;EACC;EACA;AXixHD;;AW7wHA;EACC;EACA;AXgxHD;;AW5wHA;EACC;EACA;AX+wHD;;AW3wHA;EACC;EACA;AX8wHD;;AW1wHA;EACC;EACA;AX6wHD;;AWzwHA;EACC;EACA;AX4wHD;;AWxwHA;EACC;EACA;AX2wHD;;AWvwHA;EACC;EACA;AX0wHD;;AWtwHA;EACC;EACA;AXywHD;;AWrwHA;EACC;EACA;AXwwHD;;AWpwHA;EACC;EACA;AXuwHD;;AWnwHA;EACC;EACA;AXswHD;;AWlwHA;EACC;EACA;AXqwHD;;AWjwHA;EACC;EACA;AXowHD;;AWhwHA;EACC;EACA;AXmwHD;;AW/vHA;EACC;EACA;AXkwHD;;AW9vHA;EACC;EACA;AXiwHD;;AW7vHA;EACC;EACA;AXgwHD;;AW5vHA;EACC;EACA;AX+vHD;;AW3vHA;EACC;EACA;AX8vHD;;AW1vHA;EACC;EACA;AX6vHD;;AWzvHA;EACC;EACA;AX4vHD;;AWvvHA;EACC;EACA;AX0vHD;;AWtvHA;EACC;EACA;AXyvHD;;AY//HA;;;;+FAAA;AAOC;EACC;AZggIF;AY7/HC;EAEE;EACA;EACA;EACA;AZ8/HH;AY3/HE;EACC;EACA;EACA;EAEC;AZ4/HJ;AYz/HG;EARD;IASE;EZ4/HF;AACF;AYt/HC;EAEE;AZu/HH;AYn/HC;EACC;EACA;EACA;EACA;EACA;AZq/HF;AYn/HE;EAPD;IAQE;IACA;IACA;IACA;IACA;IACA;IACA;EZs/HD;AACF;;AYh/HA;;;;+FAAA;AASE;EACC;AZ++HH;AY3+HE;EAEE;AZ4+HJ;AYv+HE;EACC;EACA;EAEC;EACA;EACA;EACA;AZw+HJ;AYp+HE;EAEE;EACA;EACA;EACA;EAED;AZo+HH;AYl+HG;EACC;AZo+HJ;AYl+HI;EACC;EACA;AZo+HL;AYj+HI;EACC;AZm+HL;AYh+HI;EACC;EACA;EACA;AZk+HL;AY39HE;EACC;AZ69HH;AY19HE;EACC;AZ49HH;AY19HG;EACC;EACA;EACA,cXpFO;ADgjIX;AYz9HI;EACC;AZ29HL;AYp9HE;EAEE;EAGA;EACA;EACA,qBX1GO;EW4GR,kBXxES;AD0hIZ;AYh9HG;EACC;EACA;EACA;EACA;EACA;EACA;EAEC;EACA;EAGA;EACA;EACA,4BX7HM;AD4kIX;AY58HI;EACC;AZ88HL;;Aa7nIA;;;;+FAAA;AAKA;EACC;EACA;EACA;EACA;EACA;EACA;AbgoID;;Aa7nIA;;;EAGC;AbgoID;;Aa7nIA;;;;+FAAA;AAOC;EAEE;EACA;EACA;EACA;Ab6nIH;Aa1nIE;EAEE;EACA;EACA;EACA;Ab2nIJ;AavnIE;EAjBD;IAkBE;Eb0nID;AACF;;AapnIA;;;;+FAAA;AAOC;EACC;EAEC;EACA;EACA;AbonIH;;Aa9mIA;;;;+FAAA;AAKA;EACC;EAEC;AbgnIF;Aa7mIC;EAND;IAQG;IACA;Eb+mID;AACF;Aa5mIC;EAEE;Ab6mIH;AazmIC;EACC;Ab2mIF;AaxmIC;EAEE;EACA;AbymIH;AarmIC;EACC;AbumIF;;AalmIA;;;;+FAAA;AAKA;EACC;EACA;AbqmID;AajmIE;;EAGE;EACA;EACA;EACA;EAGD,cZhFQ;ADgrIX;Aa3lIC;EAEE;EACA;EAGA;EAGA;EACA;EACA,yBZrGQ;EYuGT,cZlGS;ADyrIX;AarlIE;EAEE;AbslIJ;AallIE;EAEE;EACA;AbmlIJ;AahlIG;EAEE;AbilIL;Aa7kIG;EAEC,cZ1HO;ADwsIX;AavkIC;EACC;AbykIF;;Ac5vIA;;;;8FAAA;AAOC;EACC;EACA;EACA;EAEC;EACA;EACA;EACA;EAED,oEb8Da;Ea7Db;EACA;EACA;EACA,kBb8DU;Ea7DV;Ad2vIF;AczvIE;EAjBD;IAkBE;Ed4vID;AACF;Ac1vIE;EACC;Ad4vIH;AczvIE;EACC;EACA;EACA;Ad2vIH;AcxvIE;EACC;EAEC;EACA;EAGD;EACA;EACA;AduvIH;AcpvIE;EAEC,WADY;EAEZ,YAFY;EAIX;EACA;EAED,yBbdQ;ADiwIX;;Ac5uIA;;;;8FAAA;AAOC;EACC;EACA;EACA;EACA;Ad6uIF;Ac3uIE;EAND;IAOE;IACA;IACA;IACA;IACA;Ed8uID;Ec5uIC;;IAEC;Ed8uIF;AACF;Ac1uIE;EACC;EAEC;Ad2uIJ;AcxuIG;EAND;IAQG;IACA;Ed0uIH;AACF;AcruIE;EACC;EACA;EACA;EACA;AduuIH;AcruIG;EAND;IAOE;EdwuIF;AACF;AcluIC;EACC;EACA;EACA;EACA;EACA,cbhFS;ADozIX;AcluIE;EACC;AdouIH;AcjuIE;EACC;EACA;AdmuIH;Ac9tIC;EAEE;EACA;EAGA;EACA;EAGD;EACA,cb1GS;ADq0IX;AcztIE;EAEE;Ad0tIJ;AcntIC;EACC;EACA;EACA;AdqtIF;AcntIE;EACC;AdqtIH;AcltIE;EAEE;EACA;AdmtIJ;Ac5sIC;EACC;EACA;EACA;EACA;EAEC;EACA;Ad6sIH;Ac1sIE;EAVD;IAWE;IACA;IACA;IACA;IACA;Ed6sID;AACF;Ac3sIE;EACC;EACA;EAEC;EACA;Ad4sIJ;AczsIG;EARD;IASE;Ed4sIF;AACF;Ac1sIG;EAZD;IAaE;Ed6sIF;AACF;Ac3sIG;EAEE;Ad4sIL;AcxsIG;EACC;EAEA;EACA,WAFY;EAGZ,YAHY;EAKX;EAED,yBbhKY;EaiKZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AdusIJ,C", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/acf-global.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_variables.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_mixins.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_global.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_typography.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_admin-inputs.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_list-table.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_admin-toolbar.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_acf-headerbar.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_btn.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_icons.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_field-type-icons.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_tools.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_updates.scss", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/sass/_pro-upsells.scss"], "sourcesContent": ["@charset \"UTF-8\";\n/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n/* colors */\n/* acf-field */\n/* responsive */\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n/*--------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*--------------------------------------------------------------------------------------------*/\n/* Horizontal List */\n.acf-hl {\n  padding: 0;\n  margin: 0;\n  list-style: none;\n  display: block;\n  position: relative;\n}\n\n.acf-hl > li {\n  float: left;\n  display: block;\n  margin: 0;\n  padding: 0;\n}\n\n.acf-hl > li.acf-fr {\n  float: right;\n}\n\n/* Horizontal List: Clearfix */\n.acf-hl:before, .acf-hl:after,\n.acf-bl:before, .acf-bl:after,\n.acf-cf:before, .acf-cf:after {\n  content: \"\";\n  display: block;\n  line-height: 0;\n}\n\n.acf-hl:after,\n.acf-bl:after,\n.acf-cf:after {\n  clear: both;\n}\n\n/* Block List */\n.acf-bl {\n  padding: 0;\n  margin: 0;\n  list-style: none;\n  display: block;\n  position: relative;\n}\n\n.acf-bl > li {\n  display: block;\n  margin: 0;\n  padding: 0;\n  float: none;\n}\n\n/* Visibility */\n.acf-hidden {\n  display: none !important;\n}\n\n.acf-empty {\n  display: table-cell !important;\n}\n.acf-empty * {\n  display: none !important;\n}\n\n/* Float */\n.acf-fl {\n  float: left;\n}\n\n.acf-fr {\n  float: right;\n}\n\n.acf-fn {\n  float: none;\n}\n\n/* Align */\n.acf-al {\n  text-align: left;\n}\n\n.acf-ar {\n  text-align: right;\n}\n\n.acf-ac {\n  text-align: center;\n}\n\n/* loading */\n.acf-loading,\n.acf-spinner {\n  display: inline-block;\n  height: 20px;\n  width: 20px;\n  vertical-align: text-top;\n  background: transparent url(../../images/spinner.gif) no-repeat 50% 50%;\n}\n\n/* spinner */\n.acf-spinner {\n  display: none;\n}\n\n.acf-spinner.is-active {\n  display: inline-block;\n}\n\n/* WP < 4.2 */\n.spinner.is-active {\n  display: inline-block;\n}\n\n/* required */\n.acf-required {\n  color: #f00;\n}\n\n/* show on hover */\n.acf-soh .acf-soh-target {\n  -webkit-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n  -moz-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n  -o-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n  transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n  visibility: hidden;\n  opacity: 0;\n}\n\n.acf-soh:hover .acf-soh-target {\n  -webkit-transition-delay: 0s;\n  -moz-transition-delay: 0s;\n  -o-transition-delay: 0s;\n  transition-delay: 0s;\n  visibility: visible;\n  opacity: 1;\n}\n\n/* show if value */\n.show-if-value {\n  display: none;\n}\n\n.hide-if-value {\n  display: block;\n}\n\n.has-value .show-if-value {\n  display: block;\n}\n\n.has-value .hide-if-value {\n  display: none;\n}\n\n/* select2 WP animation fix */\n.select2-search-choice-close {\n  -webkit-transition: none;\n  -moz-transition: none;\n  -o-transition: none;\n  transition: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  tooltip\n*\n*---------------------------------------------------------------------------------------------*/\n/* tooltip */\n.acf-tooltip {\n  background: #1D2939;\n  border-radius: 6px;\n  color: #D0D5DD;\n  padding-top: 8px;\n  padding-right: 12px;\n  padding-bottom: 10px;\n  padding-left: 12px;\n  position: absolute;\n  z-index: 900000;\n  max-width: 280px;\n  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);\n  /* tip */\n  /* positions */\n}\n.acf-tooltip:before {\n  border: solid;\n  border-color: transparent;\n  border-width: 6px;\n  content: \"\";\n  position: absolute;\n}\n.acf-tooltip.top {\n  margin-top: -8px;\n}\n.acf-tooltip.top:before {\n  top: 100%;\n  left: 50%;\n  margin-left: -6px;\n  border-top-color: #2F353E;\n  border-bottom-width: 0;\n}\n.acf-tooltip.right {\n  margin-left: 8px;\n}\n.acf-tooltip.right:before {\n  top: 50%;\n  margin-top: -6px;\n  right: 100%;\n  border-right-color: #2F353E;\n  border-left-width: 0;\n}\n.acf-tooltip.bottom {\n  margin-top: 8px;\n}\n.acf-tooltip.bottom:before {\n  bottom: 100%;\n  left: 50%;\n  margin-left: -6px;\n  border-bottom-color: #2F353E;\n  border-top-width: 0;\n}\n.acf-tooltip.left {\n  margin-left: -8px;\n}\n.acf-tooltip.left:before {\n  top: 50%;\n  margin-top: -6px;\n  left: 100%;\n  border-left-color: #2F353E;\n  border-right-width: 0;\n}\n.acf-tooltip .acf-overlay {\n  z-index: -1;\n}\n\n/* confirm */\n.acf-tooltip.-confirm {\n  z-index: 900001;\n}\n.acf-tooltip.-confirm a {\n  text-decoration: none;\n  color: #9ea3a8;\n}\n.acf-tooltip.-confirm a:hover {\n  text-decoration: underline;\n}\n.acf-tooltip.-confirm a[data-event=confirm] {\n  color: #F55E4F;\n}\n\n.acf-overlay {\n  position: fixed;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  cursor: default;\n}\n\n.acf-tooltip-target {\n  position: relative;\n  z-index: 900002;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  loading\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-loading-overlay {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  cursor: default;\n  z-index: 99;\n  background: rgba(249, 249, 249, 0.5);\n}\n.acf-loading-overlay i {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-icon\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-icon {\n  display: inline-block;\n  height: 28px;\n  width: 28px;\n  border: transparent solid 1px;\n  border-radius: 100%;\n  font-size: 20px;\n  line-height: 21px;\n  text-align: center;\n  text-decoration: none;\n  vertical-align: top;\n  box-sizing: border-box;\n}\n.acf-icon:before {\n  font-family: dashicons;\n  display: inline-block;\n  line-height: 1;\n  font-weight: 400;\n  font-style: normal;\n  speak: none;\n  text-decoration: inherit;\n  text-transform: none;\n  text-rendering: auto;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  width: 1em;\n  height: 1em;\n  vertical-align: middle;\n  text-align: center;\n}\n\n.acf-icon.-plus:before {\n  content: \"\\f543\";\n}\n\n.acf-icon.-minus:before {\n  content: \"\\f460\";\n}\n\n.acf-icon.-cancel:before {\n  content: \"\\f335\";\n  margin: -1px 0 0 -1px;\n}\n\n.acf-icon.-pencil:before {\n  content: \"\\f464\";\n}\n\n.acf-icon.-location:before {\n  content: \"\\f230\";\n}\n\n.acf-icon.-up:before {\n  content: \"\\f343\";\n  margin-top: -0.1em;\n}\n\n.acf-icon.-down:before {\n  content: \"\\f347\";\n  margin-top: 0.1em;\n}\n\n.acf-icon.-left:before {\n  content: \"\\f341\";\n  margin-left: -0.1em;\n}\n\n.acf-icon.-right:before {\n  content: \"\\f345\";\n  margin-left: 0.1em;\n}\n\n.acf-icon.-sync:before {\n  content: \"\\f463\";\n}\n\n.acf-icon.-globe:before {\n  content: \"\\f319\";\n  margin-top: 0.1em;\n  margin-left: 0.1em;\n}\n\n.acf-icon.-picture:before {\n  content: \"\\f128\";\n}\n\n.acf-icon.-check:before {\n  content: \"\\f147\";\n  margin-left: -0.1em;\n}\n\n.acf-icon.-dot-3:before {\n  content: \"\\f533\";\n  margin-top: -0.1em;\n}\n\n.acf-icon.-arrow-combo:before {\n  content: \"\\f156\";\n}\n\n.acf-icon.-arrow-up:before {\n  content: \"\\f142\";\n  margin-left: -0.1em;\n}\n\n.acf-icon.-arrow-down:before {\n  content: \"\\f140\";\n  margin-left: -0.1em;\n}\n\n.acf-icon.-search:before {\n  content: \"\\f179\";\n}\n\n.acf-icon.-link-ext:before {\n  content: \"\\f504\";\n}\n\n.acf-icon.-duplicate {\n  position: relative;\n}\n.acf-icon.-duplicate:before, .acf-icon.-duplicate:after {\n  content: \"\";\n  display: block;\n  box-sizing: border-box;\n  width: 46%;\n  height: 46%;\n  position: absolute;\n  top: 33%;\n  left: 23%;\n}\n.acf-icon.-duplicate:before {\n  margin: -1px 0 0 1px;\n  box-shadow: 2px -2px 0px 0px currentColor;\n}\n.acf-icon.-duplicate:after {\n  border: solid 2px currentColor;\n}\n\n.acf-icon.-trash {\n  position: relative;\n}\n.acf-icon.-trash:before, .acf-icon.-trash:after {\n  content: \"\";\n  display: block;\n  box-sizing: border-box;\n  width: 46%;\n  height: 46%;\n  position: absolute;\n  top: 33%;\n  left: 23%;\n}\n.acf-icon.-trash:before {\n  margin: -1px 0 0 1px;\n  box-shadow: 2px -2px 0px 0px currentColor;\n}\n.acf-icon.-trash:after {\n  border: solid 2px currentColor;\n}\n\n.acf-icon.-collapse:before {\n  content: \"\\f142\";\n  margin-left: -0.1em;\n}\n\n.-collapsed .acf-icon.-collapse:before {\n  content: \"\\f140\";\n  margin-left: -0.1em;\n}\n\nspan.acf-icon {\n  color: #555d66;\n  border-color: #b5bcc2;\n  background-color: #fff;\n}\n\na.acf-icon {\n  color: #555d66;\n  border-color: #b5bcc2;\n  background-color: #fff;\n  position: relative;\n  transition: none;\n  cursor: pointer;\n}\na.acf-icon:hover {\n  background: #f3f5f6;\n  border-color: #0071a1;\n  color: #0071a1;\n}\na.acf-icon.-minus:hover, a.acf-icon.-cancel:hover {\n  background: #f7efef;\n  border-color: #a10000;\n  color: #dc3232;\n}\na.acf-icon:active, a.acf-icon:focus {\n  outline: none;\n  box-shadow: none;\n}\n\n.acf-icon.-clear {\n  border-color: transparent;\n  background: transparent;\n  color: #444;\n}\n\n.acf-icon.light {\n  border-color: transparent;\n  background: #F5F5F5;\n  color: #23282d;\n}\n\n.acf-icon.dark {\n  border-color: transparent !important;\n  background: #23282D;\n  color: #eee;\n}\n\na.acf-icon.dark:hover {\n  background: #191E23;\n  color: #00b9eb;\n}\na.acf-icon.dark.-minus:hover, a.acf-icon.dark.-cancel:hover {\n  color: #D54E21;\n}\n\n.acf-icon.grey {\n  border-color: transparent !important;\n  background: #b4b9be;\n  color: #fff !important;\n}\n.acf-icon.grey:hover {\n  background: #00A0D2;\n  color: #fff;\n}\n.acf-icon.grey.-minus:hover, .acf-icon.grey.-cancel:hover {\n  background: #32373C;\n}\n\n.acf-icon.small,\n.acf-icon.-small {\n  width: 20px;\n  height: 20px;\n  line-height: 14px;\n  font-size: 14px;\n}\n.acf-icon.small.-duplicate:before, .acf-icon.small.-duplicate:after,\n.acf-icon.-small.-duplicate:before,\n.acf-icon.-small.-duplicate:after {\n  opacity: 0.8;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-box\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-box {\n  background: #FFFFFF;\n  border: 1px solid #ccd0d4;\n  position: relative;\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);\n  /* title */\n  /* footer */\n}\n.acf-box .title {\n  border-bottom: 1px solid #ccd0d4;\n  margin: 0;\n  padding: 15px;\n}\n.acf-box .title h3 {\n  display: flex;\n  align-items: center;\n  font-size: 14px;\n  line-height: 1em;\n  margin: 0;\n  padding: 0;\n}\n.acf-box .inner {\n  padding: 15px;\n}\n.acf-box h2 {\n  color: #333333;\n  font-size: 26px;\n  line-height: 1.25em;\n  margin: 0.25em 0 0.75em;\n  padding: 0;\n}\n.acf-box h3 {\n  margin: 1.5em 0 0;\n}\n.acf-box p {\n  margin-top: 0.5em;\n}\n.acf-box a {\n  text-decoration: none;\n}\n.acf-box i.dashicons-external {\n  margin-top: -1px;\n}\n.acf-box .footer {\n  border-top: 1px solid #ccd0d4;\n  padding: 12px;\n  font-size: 13px;\n  line-height: 1.5;\n}\n.acf-box .footer p {\n  margin: 0;\n}\n.acf-admin-3-8 .acf-box {\n  border-color: #E5E5E5;\n}\n.acf-admin-3-8 .acf-box .title,\n.acf-admin-3-8 .acf-box .footer {\n  border-color: #E5E5E5;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-notice\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-notice {\n  position: relative;\n  display: block;\n  color: #fff;\n  margin: 5px 0 15px;\n  padding: 3px 12px;\n  background: #2a9bd9;\n  border-left: #1f7db1 solid 3px;\n}\n.acf-notice p {\n  font-size: 13px;\n  line-height: 1.5;\n  margin: 0.5em 0;\n  text-shadow: none;\n  color: inherit;\n}\n.acf-notice .acf-notice-dismiss {\n  position: absolute;\n  top: 9px;\n  right: 12px;\n  background: transparent !important;\n  color: inherit !important;\n  border-color: #fff !important;\n  opacity: 0.75;\n}\n.acf-notice .acf-notice-dismiss:hover {\n  opacity: 1;\n}\n.acf-notice.-dismiss {\n  padding-right: 40px;\n}\n.acf-notice.-error {\n  background: #d94f4f;\n  border-color: #c92c2c;\n}\n.acf-notice.-success {\n  background: #49ad52;\n  border-color: #3a8941;\n}\n.acf-notice.-warning {\n  background: #fd8d3b;\n  border-color: #fc7009;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-table\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-table {\n  border: #ccd0d4 solid 1px;\n  background: #fff;\n  border-spacing: 0;\n  border-radius: 0;\n  table-layout: auto;\n  padding: 0;\n  margin: 0;\n  width: 100%;\n  clear: both;\n  box-sizing: content-box;\n  /* defaults */\n  /* thead */\n  /* tbody */\n  /* -clear */\n}\n.acf-table > tbody > tr > th, .acf-table > tbody > tr > td,\n.acf-table > thead > tr > th,\n.acf-table > thead > tr > td {\n  padding: 8px;\n  vertical-align: top;\n  background: #fff;\n  text-align: left;\n  border-style: solid;\n  font-weight: normal;\n}\n.acf-table > tbody > tr > th,\n.acf-table > thead > tr > th {\n  position: relative;\n  color: #333333;\n}\n.acf-table > thead > tr > th {\n  border-color: #d5d9dd;\n  border-width: 0 0 1px 1px;\n}\n.acf-table > thead > tr > th:first-child {\n  border-left-width: 0;\n}\n.acf-table > tbody > tr {\n  z-index: 1;\n}\n.acf-table > tbody > tr > td {\n  border-color: #eeeeee;\n  border-width: 1px 0 0 1px;\n}\n.acf-table > tbody > tr > td:first-child {\n  border-left-width: 0;\n}\n.acf-table > tbody > tr:first-child > td {\n  border-top-width: 0;\n}\n.acf-table.-clear {\n  border: 0 none;\n}\n.acf-table.-clear > tbody > tr > td, .acf-table.-clear > tbody > tr > th,\n.acf-table.-clear > thead > tr > td,\n.acf-table.-clear > thead > tr > th {\n  border: 0 none;\n  padding: 4px;\n}\n\n/* remove tr */\n.acf-remove-element {\n  -webkit-transition: all 0.25s ease-out;\n  -moz-transition: all 0.25s ease-out;\n  -o-transition: all 0.25s ease-out;\n  transition: all 0.25s ease-out;\n  transform: translate(50px, 0);\n  opacity: 0;\n}\n\n/* fade-up */\n.acf-fade-up {\n  -webkit-transition: all 0.25s ease-out;\n  -moz-transition: all 0.25s ease-out;\n  -o-transition: all 0.25s ease-out;\n  transition: all 0.25s ease-out;\n  transform: translate(0, -10px);\n  opacity: 0;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Fake table\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-thead,\n.acf-tbody,\n.acf-tfoot {\n  width: 100%;\n  padding: 0;\n  margin: 0;\n}\n.acf-thead > li,\n.acf-tbody > li,\n.acf-tfoot > li {\n  box-sizing: border-box;\n  padding-top: 14px;\n  font-size: 12px;\n  line-height: 14px;\n}\n\n.acf-thead {\n  border-bottom: #ccd0d4 solid 1px;\n  color: #23282d;\n}\n.acf-thead > li {\n  font-size: 14px;\n  line-height: 1.4;\n  font-weight: bold;\n}\n.acf-admin-3-8 .acf-thead {\n  border-color: #dfdfdf;\n}\n\n.acf-tfoot {\n  background: #f5f5f5;\n  border-top: #d5d9dd solid 1px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSettings\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-settings-wrap #poststuff {\n  padding-top: 15px;\n}\n.acf-settings-wrap .acf-box {\n  margin: 20px 0;\n}\n.acf-settings-wrap table {\n  margin: 0;\n}\n.acf-settings-wrap table .button {\n  vertical-align: middle;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-popup\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-popup {\n  position: fixed;\n  z-index: 900000;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  text-align: center;\n}\n#acf-popup .bg {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 0;\n  background: rgba(0, 0, 0, 0.25);\n}\n#acf-popup:before {\n  content: \"\";\n  display: inline-block;\n  height: 100%;\n  vertical-align: middle;\n}\n#acf-popup .acf-popup-box {\n  display: inline-block;\n  vertical-align: middle;\n  z-index: 1;\n  min-width: 300px;\n  min-height: 160px;\n  border-color: #aaaaaa;\n  box-shadow: 0 5px 30px -5px rgba(0, 0, 0, 0.25);\n  text-align: left;\n}\nhtml[dir=rtl] #acf-popup .acf-popup-box {\n  text-align: right;\n}\n#acf-popup .acf-popup-box .title {\n  min-height: 15px;\n  line-height: 15px;\n}\n#acf-popup .acf-popup-box .title .acf-icon {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n}\nhtml[dir=rtl] #acf-popup .acf-popup-box .title .acf-icon {\n  right: auto;\n  left: 10px;\n}\n#acf-popup .acf-popup-box .inner {\n  min-height: 50px;\n  padding: 0;\n  margin: 15px;\n}\n#acf-popup .acf-popup-box .loading {\n  position: absolute;\n  top: 45px;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 2;\n  background: rgba(0, 0, 0, 0.1);\n  display: none;\n}\n#acf-popup .acf-popup-box .loading i {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n}\n\n.acf-submit {\n  margin-bottom: 0;\n  line-height: 28px;\n}\n.acf-submit span {\n  float: right;\n  color: #999;\n}\n.acf-submit span.-error {\n  color: #dd4232;\n}\n.acf-submit .button {\n  margin-right: 5px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tupgrade notice\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-upgrade-notice {\n  position: relative;\n  background: #fff;\n  padding: 20px;\n}\n#acf-upgrade-notice:after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n#acf-upgrade-notice .col-content {\n  float: left;\n  width: 55%;\n  padding-left: 90px;\n}\n#acf-upgrade-notice .notice-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  align-content: flex-start;\n}\n#acf-upgrade-notice .col-actions {\n  float: right;\n  text-align: center;\n}\n#acf-upgrade-notice img {\n  float: left;\n  width: 64px;\n  height: 64px;\n  margin: 0 0 0 -90px;\n}\n#acf-upgrade-notice h2 {\n  display: inline-block;\n  font-size: 16px;\n  margin: 2px 0 6.5px;\n}\n#acf-upgrade-notice p {\n  padding: 0;\n  margin: 0;\n}\n#acf-upgrade-notice .button:before {\n  margin-top: 11px;\n}\n@media screen and (max-width: 640px) {\n  #acf-upgrade-notice .col-content,\n  #acf-upgrade-notice .col-actions {\n    float: none;\n    padding-left: 90px;\n    width: auto;\n    text-align: left;\n  }\n}\n\n#acf-upgrade-notice:has(.notice-container)::before, #acf-upgrade-notice:has(.notice-container)::after {\n  display: none;\n}\n\n#acf-upgrade-notice:has(.notice-container) {\n  padding-left: 20px !important;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tWelcome\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-wrap h1 {\n  margin-top: 0;\n  padding-top: 20px;\n}\n.acf-wrap .about-text {\n  margin-top: 0.5em;\n  min-height: 50px;\n}\n.acf-wrap .about-headline-callout {\n  font-size: 2.4em;\n  font-weight: 300;\n  line-height: 1.3;\n  margin: 1.1em 0 0.2em;\n  text-align: center;\n}\n.acf-wrap .feature-section {\n  padding: 40px 0;\n}\n.acf-wrap .feature-section h2 {\n  margin-top: 20px;\n}\n.acf-wrap .changelog {\n  list-style: disc;\n  padding-left: 15px;\n}\n.acf-wrap .changelog li {\n  margin: 0 0 0.75em;\n}\n.acf-wrap .acf-three-col {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n.acf-wrap .acf-three-col > div {\n  flex: 1;\n  align-self: flex-start;\n  min-width: 31%;\n  max-width: 31%;\n}\n@media screen and (max-width: 880px) {\n  .acf-wrap .acf-three-col > div {\n    min-width: 48%;\n  }\n}\n@media screen and (max-width: 640px) {\n  .acf-wrap .acf-three-col > div {\n    min-width: 100%;\n  }\n}\n.acf-wrap .acf-three-col h3 .badge {\n  display: inline-block;\n  vertical-align: top;\n  border-radius: 5px;\n  background: #fc9700;\n  color: #fff;\n  font-weight: normal;\n  font-size: 12px;\n  padding: 2px 5px;\n}\n.acf-wrap .acf-three-col img + h3 {\n  margin-top: 0.5em;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-hl cols\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-hl[data-cols] {\n  margin-left: -10px;\n  margin-right: -10px;\n}\n.acf-hl[data-cols] > li {\n  padding: 0 6px 0 10px;\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n\n/* sizes */\n.acf-hl[data-cols=\"2\"] > li {\n  width: 50%;\n}\n\n.acf-hl[data-cols=\"3\"] > li {\n  width: 33.333%;\n}\n\n.acf-hl[data-cols=\"4\"] > li {\n  width: 25%;\n}\n\n/* mobile */\n@media screen and (max-width: 640px) {\n  .acf-hl[data-cols] {\n    flex-wrap: wrap;\n    justify-content: flex-start;\n    align-content: flex-start;\n    align-items: flex-start;\n    margin-left: 0;\n    margin-right: 0;\n    margin-top: -10px;\n  }\n  .acf-hl[data-cols] > li {\n    flex: 1 1 100%;\n    width: 100% !important;\n    padding: 10px 0 0;\n  }\n}\n/*--------------------------------------------------------------------------------------------\n*\n*\tmisc\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-actions {\n  text-align: right;\n  z-index: 1;\n  /* hover */\n  /* rtl */\n}\n.acf-actions.-hover {\n  position: absolute;\n  display: none;\n  top: 0;\n  right: 0;\n  padding: 5px;\n}\nhtml[dir=rtl] .acf-actions.-hover {\n  right: auto;\n  left: 0;\n}\n\n/* ul compatibility */\nul.acf-actions li {\n  float: right;\n  margin-left: 4px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRTL\n*\n*--------------------------------------------------------------------------------------------*/\nhtml[dir=rtl] .acf-fl {\n  float: right;\n}\n\nhtml[dir=rtl] .acf-fr {\n  float: left;\n}\n\nhtml[dir=rtl] .acf-hl > li {\n  float: right;\n}\n\nhtml[dir=rtl] .acf-hl > li.acf-fr {\n  float: left;\n}\n\nhtml[dir=rtl] .acf-icon.logo {\n  left: 0;\n  right: auto;\n}\n\nhtml[dir=rtl] .acf-table thead th {\n  text-align: right;\n  border-right-width: 1px;\n  border-left-width: 0px;\n}\n\nhtml[dir=rtl] .acf-table > tbody > tr > td {\n  text-align: right;\n  border-right-width: 1px;\n  border-left-width: 0px;\n}\n\nhtml[dir=rtl] .acf-table > thead > tr > th:first-child,\nhtml[dir=rtl] .acf-table > tbody > tr > td:first-child {\n  border-right-width: 0;\n}\n\nhtml[dir=rtl] .acf-table > tbody > tr > td.order + td {\n  border-right-color: #e1e1e1;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  acf-postbox-columns\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-postbox-columns {\n  position: relative;\n  margin-top: -11px;\n  margin-bottom: -12px;\n  margin-left: -12px;\n  margin-right: 268px;\n}\n.acf-postbox-columns:after {\n  display: block;\n  clear: both;\n  content: \"\";\n}\n.acf-postbox-columns .acf-postbox-main,\n.acf-postbox-columns .acf-postbox-side {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n  padding: 0 12px 12px;\n}\n.acf-postbox-columns .acf-postbox-main {\n  float: left;\n  width: 100%;\n}\n.acf-postbox-columns .acf-postbox-side {\n  float: right;\n  width: 280px;\n  margin-right: -280px;\n}\n.acf-postbox-columns .acf-postbox-side:before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 1px;\n  height: 100%;\n  top: 0;\n  right: 0;\n  background: #d5d9dd;\n}\n.acf-admin-3-8 .acf-postbox-columns .acf-postbox-side:before {\n  background: #dfdfdf;\n}\n\n/* mobile */\n@media only screen and (max-width: 850px) {\n  .acf-postbox-columns {\n    margin: 0;\n  }\n  .acf-postbox-columns .acf-postbox-main,\n  .acf-postbox-columns .acf-postbox-side {\n    float: none;\n    width: auto;\n    margin: 0;\n    padding: 0;\n  }\n  .acf-postbox-columns .acf-postbox-side {\n    margin-top: 1em;\n  }\n  .acf-postbox-columns .acf-postbox-side:before {\n    display: none;\n  }\n}\n/*---------------------------------------------------------------------------------------------\n*\n*  acf-panel\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-panel {\n  margin-top: -1px;\n  border-top: 1px solid #d5d9dd;\n  border-bottom: 1px solid #d5d9dd;\n  /* open */\n  /* inside postbox */\n  /* fields */\n}\n.acf-panel .acf-panel-title {\n  margin: 0;\n  padding: 12px;\n  font-weight: bold;\n  cursor: pointer;\n  font-size: inherit;\n}\n.acf-panel .acf-panel-title i {\n  float: right;\n}\n.acf-panel .acf-panel-inside {\n  margin: 0;\n  padding: 0 12px 12px;\n  display: none;\n}\n.acf-panel.-open .acf-panel-inside {\n  display: block;\n}\n.postbox .acf-panel {\n  margin-left: -12px;\n  margin-right: -12px;\n}\n.acf-panel .acf-field {\n  margin: 20px 0 0;\n}\n.acf-panel .acf-field .acf-label label {\n  color: #555d66;\n  font-weight: normal;\n}\n.acf-panel .acf-field:first-child {\n  margin-top: 0;\n}\n.acf-admin-3-8 .acf-panel {\n  border-color: #dfdfdf;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Tools\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-admin-tools .notice {\n  margin-top: 10px;\n}\n#acf-admin-tools .acf-meta-box-wrap {\n  /* acf-fields */\n}\n#acf-admin-tools .acf-meta-box-wrap .inside {\n  border-top: none;\n}\n#acf-admin-tools .acf-meta-box-wrap .acf-fields {\n  margin-bottom: 24px;\n  border: none;\n  background: #fff;\n  border-radius: 0;\n}\n#acf-admin-tools .acf-meta-box-wrap .acf-fields .acf-field {\n  padding: 0;\n}\n#acf-admin-tools .acf-meta-box-wrap .acf-fields .acf-label {\n  margin-bottom: 16px;\n}\n#acf-admin-tools .acf-meta-box-wrap .acf-fields .acf-input {\n  padding-top: 16px;\n  padding-right: 16px;\n  padding-bottom: 16px;\n  padding-left: 16px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #D0D5DD;\n  border-radius: 6px;\n}\n\n.acf-meta-box-wrap .postbox {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.acf-meta-box-wrap .postbox .inside {\n  margin-bottom: 0;\n}\n.acf-meta-box-wrap .postbox .hndle {\n  font-size: 14px;\n  padding: 8px 12px;\n  margin: 0;\n  line-height: 1.4;\n  position: relative;\n  z-index: 1;\n  cursor: default;\n}\n.acf-meta-box-wrap .postbox .handlediv,\n.acf-meta-box-wrap .postbox .handle-order-higher,\n.acf-meta-box-wrap .postbox .handle-order-lower {\n  display: none;\n}\n\n/* grid */\n.acf-meta-box-wrap.-grid {\n  margin-left: 8px;\n  margin-right: 8px;\n}\n.acf-meta-box-wrap.-grid .postbox {\n  float: left;\n  clear: left;\n  width: 50%;\n  margin: 0 0 16px;\n}\n.acf-meta-box-wrap.-grid .postbox:nth-child(odd) {\n  margin-left: -8px;\n}\n.acf-meta-box-wrap.-grid .postbox:nth-child(even) {\n  float: right;\n  clear: right;\n  margin-right: -8px;\n}\n\n/* mobile */\n@media only screen and (max-width: 850px) {\n  .acf-meta-box-wrap.-grid {\n    margin-left: 0;\n    margin-right: 0;\n  }\n  .acf-meta-box-wrap.-grid .postbox {\n    margin-left: 0 !important;\n    margin-right: 0 !important;\n    width: 100%;\n  }\n}\n/* export tool */\n#acf-admin-tool-export {\n  /* panel: selection */\n}\n#acf-admin-tool-export p {\n  max-width: 800px;\n}\n#acf-admin-tool-export ul {\n  column-width: 200px;\n}\n#acf-admin-tool-export .acf-postbox-side .button {\n  margin: 0;\n  width: 100%;\n}\n#acf-admin-tool-export textarea {\n  display: block;\n  width: 100%;\n  min-height: 500px;\n  background: #F9FAFB;\n  border-color: #D0D5DD;\n  box-shadow: none;\n  padding: 7px;\n  border-radius: 6px;\n}\n#acf-admin-tool-export .acf-panel-selection .acf-label {\n  display: none;\n}\n\n.acf-css-tooltip {\n  position: relative;\n}\n.acf-css-tooltip:before {\n  content: attr(aria-label);\n  display: none;\n  position: absolute;\n  z-index: 999;\n  bottom: 100%;\n  left: 50%;\n  transform: translate(-50%, -8px);\n  background: #191e23;\n  border-radius: 2px;\n  padding: 5px 10px;\n  color: #fff;\n  font-size: 12px;\n  line-height: 1.4em;\n  white-space: pre;\n}\n.acf-css-tooltip:after {\n  content: \"\";\n  display: none;\n  position: absolute;\n  z-index: 998;\n  bottom: 100%;\n  left: 50%;\n  transform: translate(-50%, 4px);\n  border: solid 6px transparent;\n  border-top-color: #191e23;\n}\n.acf-css-tooltip:hover:before, .acf-css-tooltip:hover:after, .acf-css-tooltip:focus:before, .acf-css-tooltip:focus:after {\n  display: block;\n}\n\n.acf-diff .acf-diff-title {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 40px;\n  padding: 14px 16px;\n  background: #f3f3f3;\n  border-bottom: #dddddd solid 1px;\n}\n.acf-diff .acf-diff-title strong {\n  font-size: 14px;\n  display: block;\n}\n.acf-diff .acf-diff-title .acf-diff-title-left,\n.acf-diff .acf-diff-title .acf-diff-title-right {\n  width: 50%;\n  float: left;\n}\n.acf-diff .acf-diff-content {\n  position: absolute;\n  top: 70px;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  overflow: auto;\n}\n.acf-diff table.diff {\n  border-spacing: 0;\n}\n.acf-diff table.diff col.diffsplit.middle {\n  width: 0;\n}\n.acf-diff table.diff td, .acf-diff table.diff th {\n  padding-top: 0.25em;\n  padding-bottom: 0.25em;\n}\n.acf-diff table.diff tr td:nth-child(2) {\n  width: auto;\n}\n.acf-diff table.diff td:nth-child(3) {\n  border-left: #dddddd solid 1px;\n}\n@media screen and (max-width: 600px) {\n  .acf-diff .acf-diff-title {\n    height: 70px;\n  }\n  .acf-diff .acf-diff-content {\n    top: 100px;\n  }\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Modal\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-modal {\n  position: fixed;\n  top: 30px;\n  left: 30px;\n  right: 30px;\n  bottom: 30px;\n  z-index: 160000;\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.7);\n  background: #fcfcfc;\n}\n.acf-modal .acf-modal-title,\n.acf-modal .acf-modal-content,\n.acf-modal .acf-modal-toolbar {\n  box-sizing: border-box;\n  position: absolute;\n  left: 0;\n  right: 0;\n}\n.acf-modal .acf-modal-title {\n  height: 50px;\n  top: 0;\n  border-bottom: 1px solid #ddd;\n}\n.acf-modal .acf-modal-title h2 {\n  margin: 0;\n  padding: 0 16px;\n  line-height: 50px;\n}\n.acf-modal .acf-modal-title .acf-modal-close {\n  position: absolute;\n  top: 0;\n  right: 0;\n  height: 50px;\n  width: 50px;\n  border: none;\n  border-left: 1px solid #ddd;\n  background: transparent;\n  cursor: pointer;\n  color: #666;\n}\n.acf-modal .acf-modal-title .acf-modal-close:hover {\n  color: #00a0d2;\n}\n.acf-modal .acf-modal-content {\n  top: 50px;\n  bottom: 60px;\n  background: #fff;\n  overflow: auto;\n  padding: 16px;\n}\n.acf-modal .acf-modal-feedback {\n  position: absolute;\n  top: 50%;\n  margin: -10px 0;\n  left: 0;\n  right: 0;\n  text-align: center;\n  opacity: 0.75;\n}\n.acf-modal .acf-modal-feedback.error {\n  opacity: 1;\n  color: #b52727;\n}\n.acf-modal .acf-modal-toolbar {\n  height: 60px;\n  bottom: 0;\n  padding: 15px 16px;\n  border-top: 1px solid #ddd;\n}\n.acf-modal .acf-modal-toolbar .button {\n  float: right;\n}\n@media only screen and (max-width: 640px) {\n  .acf-modal {\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n  }\n}\n\n.acf-modal-backdrop {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: #000;\n  opacity: 0.7;\n  z-index: 159900;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Retina\n*\n*---------------------------------------------------------------------------------------------*/\n@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min--moz-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 2/1), only screen and (min-device-pixel-ratio: 2), only screen and (min-resolution: 192dpi), only screen and (min-resolution: 2dppx) {\n  .acf-loading,\n  .acf-spinner {\n    background-image: url(../../images/<EMAIL>);\n    background-size: 20px 20px;\n  }\n}\n/*--------------------------------------------------------------------------------------------\n*\n*  Wrap\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .wrap {\n  margin-top: 48px;\n  margin-right: 32px;\n  margin-bottom: 0;\n  margin-left: 12px;\n}\n@media screen and (max-width: 768px) {\n  .post-type-acf-field-group .wrap {\n    margin-right: 8px;\n    margin-left: 8px;\n  }\n}\n.post-type-acf-field-group.rtl .wrap {\n  margin-right: 12px;\n  margin-left: 32px;\n}\n@media screen and (max-width: 768px) {\n  .post-type-acf-field-group.rtl .wrap {\n    margin-right: 8px;\n    margin-left: 8px;\n  }\n}\n@media screen and (max-width: 768px) {\n  .post-type-acf-field-group #wpcontent {\n    padding-left: 0;\n  }\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Postbox & ACF Postbox\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .postbox,\n.post-type-acf-field-group .acf-box {\n  border: none;\n  border-radius: 8px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.post-type-acf-field-group .postbox .inside,\n.post-type-acf-field-group .acf-box .inside {\n  padding-top: 24px;\n  padding-right: 24px;\n  padding-bottom: 24px;\n  padding-left: 24px;\n}\n.post-type-acf-field-group .postbox .acf-postbox-inner,\n.post-type-acf-field-group .acf-box .acf-postbox-inner {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 24px;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n.post-type-acf-field-group .postbox .inner,\n.post-type-acf-field-group .postbox .inside,\n.post-type-acf-field-group .acf-box .inner,\n.post-type-acf-field-group .acf-box .inside {\n  margin-top: 0 !important;\n  margin-right: 0 !important;\n  margin-bottom: 0 !important;\n  margin-left: 0 !important;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n}\n.post-type-acf-field-group .postbox .postbox-header,\n.post-type-acf-field-group .postbox .title,\n.post-type-acf-field-group .acf-box .postbox-header,\n.post-type-acf-field-group .acf-box .title {\n  display: flex;\n  align-items: center;\n  box-sizing: border-box;\n  min-height: 64px;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 24px;\n  padding-bottom: 0;\n  padding-left: 24px;\n  border-bottom-width: 0;\n  border-bottom-style: none;\n}\n.post-type-acf-field-group .postbox .postbox-header h2,\n.post-type-acf-field-group .postbox .postbox-header h3,\n.post-type-acf-field-group .postbox .title h2,\n.post-type-acf-field-group .postbox .title h3,\n.post-type-acf-field-group .acf-box .postbox-header h2,\n.post-type-acf-field-group .acf-box .postbox-header h3,\n.post-type-acf-field-group .acf-box .title h2,\n.post-type-acf-field-group .acf-box .title h3 {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  color: #344054;\n}\n.post-type-acf-field-group .postbox .hndle,\n.post-type-acf-field-group .acf-box .hndle {\n  padding-top: 0;\n  padding-right: 24px;\n  padding-bottom: 0;\n  padding-left: 24px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Custom ACF postbox header\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-postbox-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  box-sizing: border-box;\n  min-height: 64px;\n  margin-top: -24px;\n  margin-right: -24px;\n  margin-bottom: 0;\n  margin-left: -24px;\n  padding-top: 0;\n  padding-right: 24px;\n  padding-bottom: 0;\n  padding-left: 24px;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n.acf-postbox-header h2.acf-postbox-title {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 24px;\n  padding-bottom: 0;\n  padding-left: 0;\n  color: #344054;\n}\n.rtl .acf-postbox-header h2.acf-postbox-title {\n  padding-right: 0;\n  padding-left: 24px;\n}\n.acf-postbox-header .acf-icon {\n  background-color: #98A2B3;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Screen options button & screen meta container\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #screen-meta-links {\n  margin-right: 32px;\n}\n.post-type-acf-field-group #screen-meta-links .show-settings {\n  border-color: #D0D5DD;\n}\n@media screen and (max-width: 768px) {\n  .post-type-acf-field-group #screen-meta-links {\n    margin-right: 16px;\n    margin-bottom: 0;\n  }\n}\n.post-type-acf-field-group.rtl #screen-meta-links {\n  margin-right: 0;\n  margin-left: 32px;\n}\n@media screen and (max-width: 768px) {\n  .post-type-acf-field-group.rtl #screen-meta-links {\n    margin-right: 0;\n    margin-left: 16px;\n  }\n}\n.post-type-acf-field-group #screen-meta {\n  border-color: #D0D5DD;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Postbox headings\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #poststuff .postbox-header h2,\n.post-type-acf-field-group #poststuff .postbox-header h3 {\n  justify-content: flex-start;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  color: #344054 !important;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Postbox drag state\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group.is-dragging-metaboxes .metabox-holder .postbox-container .meta-box-sortables {\n  box-sizing: border-box;\n  padding: 2px;\n  outline: none;\n  background-image: repeating-linear-gradient(0deg, #667085, #667085 5px, transparent 5px, transparent 10px, #667085 10px), repeating-linear-gradient(90deg, #667085, #667085 5px, transparent 5px, transparent 10px, #667085 10px), repeating-linear-gradient(180deg, #667085, #667085 5px, transparent 5px, transparent 10px, #667085 10px), repeating-linear-gradient(270deg, #667085, #667085 5px, transparent 5px, transparent 10px, #667085 10px);\n  background-size: 1.5px 100%, 100% 1.5px, 1.5px 100%, 100% 1.5px;\n  background-position: 0 0, 0 0, 100% 0, 0 100%;\n  background-repeat: no-repeat;\n  border-radius: 8px;\n}\n.post-type-acf-field-group .ui-sortable-placeholder {\n  border: none;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Search summary\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .subtitle {\n  display: inline-flex;\n  align-items: center;\n  height: 24px;\n  margin: 0;\n  padding-top: 4px;\n  padding-right: 12px;\n  padding-bottom: 4px;\n  padding-left: 12px;\n  background-color: #EBF5FA;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #A5D2E7;\n  border-radius: 6px;\n}\n.post-type-acf-field-group .subtitle strong {\n  margin-left: 5px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Action strip\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-actions-strip {\n  display: flex;\n}\n.acf-actions-strip .acf-btn {\n  margin-right: 8px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Notices\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .notice,\n.post-type-acf-field-group #lost-connection-notice {\n  position: relative;\n  box-sizing: border-box;\n  min-height: 48px;\n  margin-top: 0 !important;\n  margin-right: 0 !important;\n  margin-bottom: 16px !important;\n  margin-left: 0 !important;\n  padding-top: 13px !important;\n  padding-right: 16px !important;\n  padding-bottom: 12px !important;\n  padding-left: 50px !important;\n  background-color: #E7EFF9;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #9DBAEE;\n  border-radius: 8px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  color: #344054;\n}\n.post-type-acf-field-group .notice.update-nag,\n.post-type-acf-field-group #lost-connection-notice.update-nag {\n  display: block;\n  position: relative;\n  width: calc(100% - 44px);\n  margin-top: 48px !important;\n  margin-right: 44px !important;\n  margin-bottom: -32px !important;\n  margin-left: 12px !important;\n}\n.post-type-acf-field-group .notice .button,\n.post-type-acf-field-group #lost-connection-notice .button {\n  height: auto;\n  margin-left: 8px;\n  padding: 0;\n  border: none;\n}\n.post-type-acf-field-group .notice > div,\n.post-type-acf-field-group #lost-connection-notice > div {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.post-type-acf-field-group .notice p,\n.post-type-acf-field-group #lost-connection-notice p {\n  flex: 1 0 auto;\n  margin: 0;\n  padding: 0;\n}\n.post-type-acf-field-group .notice p.help,\n.post-type-acf-field-group #lost-connection-notice p.help {\n  margin-top: 0;\n  padding-top: 0;\n  color: rgba(52, 64, 84, 0.7);\n}\n.post-type-acf-field-group .notice .notice-dismiss,\n.post-type-acf-field-group #lost-connection-notice .notice-dismiss {\n  position: absolute;\n  top: 4px;\n  right: 8px;\n}\n.post-type-acf-field-group .notice .notice-dismiss:before,\n.post-type-acf-field-group #lost-connection-notice .notice-dismiss:before {\n  content: \"\";\n  display: block;\n  position: relative;\n  z-index: 600;\n  width: 20px;\n  height: 20px;\n  background-color: #667085;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-close.svg\");\n  mask-image: url(\"../../images/icons/icon-close.svg\");\n}\n.post-type-acf-field-group .notice .notice-dismiss:hover::before,\n.post-type-acf-field-group #lost-connection-notice .notice-dismiss:hover::before {\n  background-color: #344054;\n}\n.post-type-acf-field-group .notice:before,\n.post-type-acf-field-group #lost-connection-notice:before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 15px;\n  left: 18px;\n  z-index: 600;\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n  background-color: #fff;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-info-solid.svg\");\n  mask-image: url(\"../../images/icons/icon-info-solid.svg\");\n}\n.post-type-acf-field-group .notice:after,\n.post-type-acf-field-group #lost-connection-notice:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 9px;\n  left: 12px;\n  z-index: 500;\n  width: 28px;\n  height: 28px;\n  background-color: #2D69DA;\n  border-radius: 6px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.post-type-acf-field-group .notice .local-restore,\n.post-type-acf-field-group #lost-connection-notice .local-restore {\n  align-items: center;\n  margin-top: -6px;\n  margin-bottom: 0;\n}\n.post-type-acf-field-group .notice.is-dismissible {\n  padding-right: 56px;\n}\n.post-type-acf-field-group .notice.notice-success {\n  background-color: #EDF7EF;\n  border-color: #B6DEB9;\n}\n.post-type-acf-field-group .notice.notice-success:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n  mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n}\n.post-type-acf-field-group .notice.notice-success:after {\n  background-color: #52AA59;\n}\n.post-type-acf-field-group .notice.notice-error,\n.post-type-acf-field-group #lost-connection-notice {\n  background-color: #F7EEEB;\n  border-color: #F1B6B3;\n}\n.post-type-acf-field-group .notice.notice-error:before,\n.post-type-acf-field-group #lost-connection-notice:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-warning.svg\");\n  mask-image: url(\"../../images/icons/icon-warning.svg\");\n}\n.post-type-acf-field-group .notice.notice-error:after,\n.post-type-acf-field-group #lost-connection-notice:after {\n  background-color: #D13737;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #wpcontent {\n  line-height: 140%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Links\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group a {\n  color: #0783BE;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headings\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-h1, .post-type-acf-field-group h1,\n.acf-headerbar h1 {\n  font-size: 21px;\n  font-weight: 400;\n}\n\n.acf-h2, .acf-no-field-groups-wrapper .acf-no-field-groups-inner h2, .acf-page-title, .post-type-acf-field-group h2,\n.acf-headerbar h2 {\n  font-size: 18px;\n  font-weight: 400;\n}\n\n.acf-h3, .post-type-acf-field-group h3,\n.acf-headerbar h3, .post-type-acf-field-group .postbox .postbox-header h2,\n.post-type-acf-field-group .postbox .postbox-header h3,\n.post-type-acf-field-group .postbox .title h2,\n.post-type-acf-field-group .postbox .title h3,\n.post-type-acf-field-group .acf-box .postbox-header h2,\n.post-type-acf-field-group .acf-box .postbox-header h3,\n.post-type-acf-field-group .acf-box .title h2,\n.post-type-acf-field-group .acf-box .title h3, .acf-postbox-header h2.acf-postbox-title, .post-type-acf-field-group #poststuff .postbox-header h2,\n.post-type-acf-field-group #poststuff .postbox-header h3 {\n  font-size: 16px;\n  font-weight: 400;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Paragraphs\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .p1, .post-type-acf-field-group #acf-field-group-pro-features h1, #acf-field-group-pro-features .post-type-acf-field-group h1 {\n  font-size: 15px;\n}\n.post-type-acf-field-group .p2, .post-type-acf-field-group .acf-no-field-groups-wrapper .acf-no-field-groups-inner p, .acf-no-field-groups-wrapper .acf-no-field-groups-inner .post-type-acf-field-group p, .post-type-acf-field-group #acf-admin-tools .acf-meta-box-wrap .acf-fields .acf-label, #acf-admin-tools .acf-meta-box-wrap .acf-fields .post-type-acf-field-group .acf-label {\n  font-size: 14px;\n}\n.post-type-acf-field-group .p3, .post-type-acf-field-group .acf-admin-field-groups .wp-list-table .post-state, .acf-admin-field-groups .wp-list-table .post-type-acf-field-group .post-state, .post-type-acf-field-group .subtitle {\n  font-size: 13.5px;\n}\n.post-type-acf-field-group .p4, .post-type-acf-field-group .acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn p, .acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn .post-type-acf-field-group p, .post-type-acf-field-group #acf-update-information .form-table th, #acf-update-information .form-table .post-type-acf-field-group th,\n.post-type-acf-field-group #acf-update-information .form-table td,\n#acf-update-information .form-table .post-type-acf-field-group td, .post-type-acf-field-group #acf-admin-tools.tool-export .acf-panel h3, #acf-admin-tools.tool-export .acf-panel .post-type-acf-field-group h3, .post-type-acf-field-group .acf-btn.acf-btn-sm, .post-type-acf-field-group .acf-admin-toolbar .acf-tab, .acf-admin-toolbar .post-type-acf-field-group .acf-tab, .post-type-acf-field-group .acf-admin-field-groups .subsubsub li, .acf-admin-field-groups .subsubsub .post-type-acf-field-group li, .post-type-acf-field-group .acf-admin-field-groups .wp-list-table tbody th, .acf-admin-field-groups .wp-list-table tbody .post-type-acf-field-group th,\n.post-type-acf-field-group .acf-admin-field-groups .wp-list-table tbody td,\n.acf-admin-field-groups .wp-list-table tbody .post-type-acf-field-group td, .post-type-acf-field-group .acf-admin-field-groups .wp-list-table thead th, .acf-admin-field-groups .wp-list-table thead .post-type-acf-field-group th, .post-type-acf-field-group .acf-admin-field-groups .wp-list-table thead td, .acf-admin-field-groups .wp-list-table thead .post-type-acf-field-group td,\n.post-type-acf-field-group .acf-admin-field-groups .wp-list-table tfoot th,\n.acf-admin-field-groups .wp-list-table tfoot .post-type-acf-field-group th, .post-type-acf-field-group .acf-admin-field-groups .wp-list-table tfoot td, .acf-admin-field-groups .wp-list-table tfoot .post-type-acf-field-group td, .post-type-acf-field-group .acf-input .select2-container.-acf .select2-selection__rendered, .post-type-acf-field-group .button, .post-type-acf-field-group input[type=text],\n.post-type-acf-field-group input[type=search],\n.post-type-acf-field-group input[type=number],\n.post-type-acf-field-group textarea,\n.post-type-acf-field-group select {\n  font-size: 13px;\n}\n.post-type-acf-field-group .p5, .post-type-acf-field-group .acf-admin-field-groups .row-actions, .acf-admin-field-groups .post-type-acf-field-group .row-actions, .post-type-acf-field-group .notice .button,\n.post-type-acf-field-group #lost-connection-notice .button {\n  font-size: 12.5px;\n}\n.post-type-acf-field-group .p6, .post-type-acf-field-group #acf-update-information .acf-update-changelog p em, #acf-update-information .acf-update-changelog p .post-type-acf-field-group em, .post-type-acf-field-group .acf-no-field-groups-wrapper .acf-no-field-groups-inner p.acf-small, .acf-no-field-groups-wrapper .acf-no-field-groups-inner .post-type-acf-field-group p.acf-small, .post-type-acf-field-group .acf-admin-field-groups .row-actions, .acf-admin-field-groups .post-type-acf-field-group .row-actions, .post-type-acf-field-group .acf-small {\n  font-size: 12px;\n}\n.post-type-acf-field-group .p7, .post-type-acf-field-group .acf-tooltip, .post-type-acf-field-group .notice p.help,\n.post-type-acf-field-group #lost-connection-notice p.help {\n  font-size: 11.5px;\n}\n.post-type-acf-field-group .p8 {\n  font-size: 11px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Page titles\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-page-title {\n  color: #344054;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide old / native WP titles from pages\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .acf-settings-wrap h1,\n.post-type-acf-field-group #acf-admin-tools h1 {\n  display: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small\n*\n*---------------------------------------------------------------------------------------------*/\n/*---------------------------------------------------------------------------------------------\n*\n*  Link focus style\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group a:focus {\n  box-shadow: none;\n  outline: none;\n}\n\n.post-type-acf-field-group a:focus-visible {\n  box-shadow: 0 0 0 1px #4f94d4, 0 0 2px 1px rgba(79, 148, 212, 0.8);\n  outline: 1px solid transparent;\n}\n\n.post-type-acf-field-group {\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  All Inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Read only text inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Number fields\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Textarea\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Select\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Button & Checkbox base styling\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Buttons\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Checkboxes\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Radio Buttons & Checkbox lists\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  ACF Switch\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  File input button\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Action Buttons\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Edit field group header\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Select2 inputs\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  ACF label\n  *\n  *---------------------------------------------------------------------------------------------*/\n  /*---------------------------------------------------------------------------------------------\n  *\n  *  Tooltip for field name field setting (result of a fix for keyboard navigation)\n  *\n  *---------------------------------------------------------------------------------------------*/\n}\n.post-type-acf-field-group input[type=text],\n.post-type-acf-field-group input[type=search],\n.post-type-acf-field-group input[type=number],\n.post-type-acf-field-group textarea,\n.post-type-acf-field-group select {\n  box-sizing: border-box;\n  height: 40px;\n  padding-right: 12px;\n  padding-left: 12px;\n  background-color: #fff;\n  border-color: #D0D5DD;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  color: #344054;\n}\n.post-type-acf-field-group input[type=text]:focus,\n.post-type-acf-field-group input[type=search]:focus,\n.post-type-acf-field-group input[type=number]:focus,\n.post-type-acf-field-group textarea:focus,\n.post-type-acf-field-group select:focus {\n  outline: 3px solid #EBF5FA;\n  border-color: #399CCB;\n}\n.post-type-acf-field-group input[type=text]:disabled,\n.post-type-acf-field-group input[type=search]:disabled,\n.post-type-acf-field-group input[type=number]:disabled,\n.post-type-acf-field-group textarea:disabled,\n.post-type-acf-field-group select:disabled {\n  background-color: #F9FAFB;\n  color: #808a9e;\n}\n.post-type-acf-field-group input[type=text]::placeholder,\n.post-type-acf-field-group input[type=search]::placeholder,\n.post-type-acf-field-group input[type=number]::placeholder,\n.post-type-acf-field-group textarea::placeholder,\n.post-type-acf-field-group select::placeholder {\n  color: #98A2B3;\n}\n.post-type-acf-field-group input[type=text]:read-only {\n  background-color: #F9FAFB;\n  color: #98A2B3;\n}\n.post-type-acf-field-group .acf-field.acf-field-number .acf-label,\n.post-type-acf-field-group .acf-field.acf-field-number .acf-input input[type=number] {\n  max-width: 180px;\n}\n.post-type-acf-field-group textarea {\n  box-sizing: border-box;\n  padding-top: 10px;\n  padding-bottom: 10px;\n  height: 80px;\n  min-height: 56px;\n}\n.post-type-acf-field-group select {\n  min-width: 160px;\n  max-width: 100%;\n  padding-right: 40px;\n  padding-left: 12px;\n  background-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  background-position: right 10px top 50%;\n  background-size: 20px;\n}\n.post-type-acf-field-group select:hover, .post-type-acf-field-group select:focus {\n  color: #0783BE;\n}\n.post-type-acf-field-group select::before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: 5px;\n  left: 5px;\n  width: 20px;\n  height: 20px;\n  background-color: red;\n}\n.post-type-acf-field-group.rtl select {\n  padding-right: 12px;\n  padding-left: 40px;\n  background-position: left 10px top 50%;\n}\n.post-type-acf-field-group input[type=radio],\n.post-type-acf-field-group input[type=checkbox] {\n  box-sizing: border-box;\n  width: 16px;\n  height: 16px;\n  padding: 0;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #98A2B3;\n  background: #fff;\n  box-shadow: none;\n}\n.post-type-acf-field-group input[type=radio]:hover,\n.post-type-acf-field-group input[type=checkbox]:hover {\n  background-color: #EBF5FA;\n  border-color: #0783BE;\n}\n.post-type-acf-field-group input[type=radio]:checked, .post-type-acf-field-group input[type=radio]:focus-visible,\n.post-type-acf-field-group input[type=checkbox]:checked,\n.post-type-acf-field-group input[type=checkbox]:focus-visible {\n  background-color: #EBF5FA;\n  border-color: #0783BE;\n}\n.post-type-acf-field-group input[type=radio]:checked:before, .post-type-acf-field-group input[type=radio]:focus-visible:before,\n.post-type-acf-field-group input[type=checkbox]:checked:before,\n.post-type-acf-field-group input[type=checkbox]:focus-visible:before {\n  content: \"\";\n  position: relative;\n  top: -1px;\n  left: -1px;\n  width: 16px;\n  height: 16px;\n  margin: 0;\n  padding: 0;\n  background-color: transparent;\n  background-size: cover;\n  background-repeat: no-repeat;\n  background-position: center;\n}\n.post-type-acf-field-group input[type=radio]:active,\n.post-type-acf-field-group input[type=checkbox]:active {\n  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 0px 0px rgba(255, 54, 54, 0.25);\n}\n.post-type-acf-field-group input[type=radio]:disabled,\n.post-type-acf-field-group input[type=checkbox]:disabled {\n  background-color: #F9FAFB;\n  border-color: #D0D5DD;\n}\n.post-type-acf-field-group.rtl input[type=radio]:checked:before, .post-type-acf-field-group.rtl input[type=radio]:focus-visible:before,\n.post-type-acf-field-group.rtl input[type=checkbox]:checked:before,\n.post-type-acf-field-group.rtl input[type=checkbox]:focus-visible:before {\n  left: 1px;\n}\n.post-type-acf-field-group input[type=radio]:checked:before, .post-type-acf-field-group input[type=radio]:focus:before {\n  background-image: url(\"../../images/field-states/radio-active.svg\");\n}\n.post-type-acf-field-group input[type=checkbox]:checked:before, .post-type-acf-field-group input[type=checkbox]:focus:before {\n  background-image: url(\"../../images/field-states/checkbox-active.svg\");\n}\n.post-type-acf-field-group .acf-radio-list li input[type=radio],\n.post-type-acf-field-group .acf-radio-list li input[type=checkbox],\n.post-type-acf-field-group .acf-checkbox-list li input[type=radio],\n.post-type-acf-field-group .acf-checkbox-list li input[type=checkbox] {\n  margin-right: 6px;\n}\n.post-type-acf-field-group .acf-radio-list.acf-bl li,\n.post-type-acf-field-group .acf-checkbox-list.acf-bl li {\n  margin-bottom: 8px;\n}\n.post-type-acf-field-group .acf-radio-list.acf-bl li:last-of-type,\n.post-type-acf-field-group .acf-checkbox-list.acf-bl li:last-of-type {\n  margin-bottom: 0;\n}\n.post-type-acf-field-group .acf-radio-list label,\n.post-type-acf-field-group .acf-checkbox-list label {\n  display: flex;\n  align-items: center;\n  align-content: center;\n}\n.post-type-acf-field-group .acf-switch {\n  width: 42px;\n  height: 24px;\n  border: none;\n  background-color: #D0D5DD;\n  border-radius: 12px;\n}\n.post-type-acf-field-group .acf-switch:hover {\n  background-color: #98A2B3;\n}\n.post-type-acf-field-group .acf-switch:active {\n  box-shadow: 0px 0px 0px 3px #EBF5FA, 0px 0px 0px rgba(255, 54, 54, 0.25);\n}\n.post-type-acf-field-group .acf-switch.-on {\n  background-color: #0783BE;\n}\n.post-type-acf-field-group .acf-switch.-on:hover {\n  background-color: #066998;\n}\n.post-type-acf-field-group .acf-switch.-on .acf-switch-slider {\n  left: 20px;\n}\n.post-type-acf-field-group .acf-switch .acf-switch-off,\n.post-type-acf-field-group .acf-switch .acf-switch-on {\n  visibility: hidden;\n}\n.post-type-acf-field-group .acf-switch .acf-switch-slider {\n  width: 20px;\n  height: 20px;\n  border: none;\n  border-radius: 100px;\n  box-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);\n}\n.post-type-acf-field-group .acf-field-true-false {\n  display: flex;\n  align-items: flex-start;\n}\n.post-type-acf-field-group .acf-field-true-false .acf-label {\n  order: 2;\n  display: block;\n  align-items: center;\n  margin-top: 2px;\n  margin-bottom: 0;\n  margin-left: 12px;\n}\n.post-type-acf-field-group .acf-field-true-false .acf-label label {\n  margin-bottom: 0;\n}\n.post-type-acf-field-group .acf-field-true-false .acf-label .acf-tip {\n  margin-left: 12px;\n}\n.post-type-acf-field-group .acf-field-true-false .acf-label .description {\n  display: block;\n  margin-top: 2px;\n  margin-left: 0;\n}\n.post-type-acf-field-group.rtl .acf-field-true-false .acf-label {\n  margin-right: 12px;\n  margin-left: 0;\n}\n.post-type-acf-field-group.rtl .acf-field-true-false .acf-tip {\n  margin-right: 12px;\n  margin-left: 0;\n}\n.post-type-acf-field-group input::file-selector-button {\n  box-sizing: border-box;\n  min-height: 40px;\n  margin-right: 16px;\n  padding-top: 8px;\n  padding-right: 16px;\n  padding-bottom: 8px;\n  padding-left: 16px;\n  background-color: transparent;\n  color: #0783BE !important;\n  border-radius: 6px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #0783BE;\n  text-decoration: none;\n}\n.post-type-acf-field-group input::file-selector-button:hover {\n  border-color: #066998;\n  cursor: pointer;\n  color: #066998 !important;\n}\n.post-type-acf-field-group .button {\n  display: inline-flex;\n  align-items: center;\n  height: 40px;\n  padding-right: 16px;\n  padding-left: 16px;\n  background-color: transparent;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #0783BE;\n  border-radius: 6px;\n  color: #0783BE;\n}\n.post-type-acf-field-group .button:hover {\n  background-color: #f3f9fc;\n  border-color: #0783BE;\n  color: #0783BE;\n}\n.post-type-acf-field-group .button:focus {\n  background-color: #f3f9fc;\n  outline: 3px solid #EBF5FA;\n  color: #0783BE;\n}\n.post-type-acf-field-group .edit-field-group-header {\n  display: block !important;\n}\n.post-type-acf-field-group .acf-input .select2-container.-acf .select2-selection {\n  border: none;\n  line-height: 1;\n}\n.post-type-acf-field-group .acf-input .select2-container.-acf .select2-selection__rendered {\n  box-sizing: border-box;\n  padding-right: 0;\n  padding-left: 0;\n  background-color: #fff;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #D0D5DD;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  color: #344054;\n}\n.post-type-acf-field-group .acf-input .select2-container--focus {\n  outline: 3px solid #EBF5FA;\n  border-color: #399CCB;\n  border-radius: 6px;\n}\n.post-type-acf-field-group .acf-input .select2-container--focus .select2-selection__rendered {\n  border-color: #399CCB !important;\n}\n.post-type-acf-field-group .acf-input .select2-container--focus.select2-container--below.select2-container--open .select2-selection__rendered {\n  border-bottom-right-radius: 0 !important;\n  border-bottom-left-radius: 0 !important;\n}\n.post-type-acf-field-group .acf-input .select2-container--focus.select2-container--above.select2-container--open .select2-selection__rendered {\n  border-top-right-radius: 0 !important;\n  border-top-left-radius: 0 !important;\n}\n.post-type-acf-field-group .acf-input .select2-container .select2-search--inline .select2-search__field {\n  margin: 0;\n  padding-left: 6px;\n}\n.post-type-acf-field-group .acf-input .select2-container .select2-search--inline .select2-search__field:focus {\n  outline: none;\n  border: none;\n}\n.post-type-acf-field-group .acf-input .select2-container--default .select2-selection--multiple .select2-selection__rendered {\n  padding-top: 0;\n  padding-right: 6px;\n  padding-bottom: 0;\n  padding-left: 6px;\n}\n.post-type-acf-field-group .acf-input .select2-selection__clear {\n  width: 18px;\n  height: 18px;\n  margin-top: 12px;\n  margin-right: 0;\n  text-indent: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.post-type-acf-field-group .acf-input .select2-selection__clear:before {\n  content: \"\";\n  display: block;\n  width: 14px;\n  height: 14px;\n  top: 0;\n  left: 0;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-close.svg\");\n  mask-image: url(\"../../images/icons/icon-close.svg\");\n  background-color: #98A2B3;\n}\n.post-type-acf-field-group .acf-input .select2-selection__clear:hover::before {\n  background-color: #1D2939;\n}\n.post-type-acf-field-group .acf-label {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n.post-type-acf-field-group .acf-label .acf-icon-help {\n  width: 18px;\n  height: 18px;\n  background-color: #98A2B3;\n}\n.post-type-acf-field-group .acf-label label {\n  margin-bottom: 0;\n}\n.post-type-acf-field-group .acf-label .description {\n  margin-top: 2px;\n}\n.post-type-acf-field-group .acf-field-setting-name .acf-tip {\n  position: absolute;\n  top: 0;\n  left: 654px;\n  color: #98A2B3;\n}\n.post-type-acf-field-group .acf-field-setting-name .acf-tip .acf-icon-help {\n  width: 18px;\n  height: 18px;\n}\n\n.rtl.post-type-acf-field-group .acf-field-setting-name .acf-tip {\n  left: auto;\n  right: 654px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Field Groups\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups .tablenav.top {\n  display: none;\n}\n.acf-admin-field-groups .subsubsub {\n  margin-bottom: 3px;\n}\n.acf-admin-field-groups .wp-list-table {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  border-radius: 8px;\n  border: none;\n  overflow: hidden;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.acf-admin-field-groups .wp-list-table strong {\n  color: #98A2B3;\n  margin: 0;\n}\n.acf-admin-field-groups .wp-list-table a.row-title {\n  font-size: 13px !important;\n  font-weight: 500;\n}\n.acf-admin-field-groups .wp-list-table th,\n.acf-admin-field-groups .wp-list-table td {\n  color: #344054;\n}\n.acf-admin-field-groups .wp-list-table th.sortable a,\n.acf-admin-field-groups .wp-list-table td.sortable a {\n  padding: 0;\n}\n.acf-admin-field-groups .wp-list-table th.check-column,\n.acf-admin-field-groups .wp-list-table td.check-column {\n  padding-top: 12px;\n  padding-right: 16px;\n  padding-left: 16px;\n}\n@media screen and (max-width: 880px) {\n  .acf-admin-field-groups .wp-list-table th.check-column,\n  .acf-admin-field-groups .wp-list-table td.check-column {\n    vertical-align: top;\n    padding-right: 2px;\n    padding-left: 10px;\n  }\n}\n.acf-admin-field-groups .wp-list-table th input,\n.acf-admin-field-groups .wp-list-table td input {\n  margin: 0;\n  padding: 0;\n}\n.acf-admin-field-groups .wp-list-table thead th, .acf-admin-field-groups .wp-list-table thead td,\n.acf-admin-field-groups .wp-list-table tfoot th, .acf-admin-field-groups .wp-list-table tfoot td {\n  height: 48px;\n  padding-right: 24px;\n  padding-left: 24px;\n  box-sizing: border-box;\n  background-color: #F9FAFB;\n  border-color: #EAECF0;\n  font-weight: 500;\n}\n@media screen and (max-width: 880px) {\n  .acf-admin-field-groups .wp-list-table thead th, .acf-admin-field-groups .wp-list-table thead td,\n  .acf-admin-field-groups .wp-list-table tfoot th, .acf-admin-field-groups .wp-list-table tfoot td {\n    padding-right: 16px;\n    padding-left: 8px;\n  }\n}\n@media screen and (max-width: 880px) {\n  .acf-admin-field-groups .wp-list-table thead th.check-column, .acf-admin-field-groups .wp-list-table thead td.check-column,\n  .acf-admin-field-groups .wp-list-table tfoot th.check-column, .acf-admin-field-groups .wp-list-table tfoot td.check-column {\n    vertical-align: middle;\n  }\n}\n.acf-admin-field-groups .wp-list-table tbody th,\n.acf-admin-field-groups .wp-list-table tbody td {\n  box-sizing: border-box;\n  height: 60px;\n  padding-top: 10px;\n  padding-right: 24px;\n  padding-bottom: 10px;\n  padding-left: 24px;\n  vertical-align: top;\n  background-color: #fff;\n  border-bottom-width: 1px;\n  border-bottom-color: #EAECF0;\n  border-bottom-style: solid;\n}\n@media screen and (max-width: 880px) {\n  .acf-admin-field-groups .wp-list-table tbody th,\n  .acf-admin-field-groups .wp-list-table tbody td {\n    padding-right: 16px;\n    padding-left: 8px;\n  }\n}\n.acf-admin-field-groups .wp-list-table .column-acf-key {\n  white-space: nowrap;\n}\n.acf-admin-field-groups .wp-list-table .column-acf-key .acf-icon-key-solid {\n  display: inline-block;\n  position: relative;\n  bottom: -2px;\n  width: 15px;\n  height: 15px;\n  margin-right: 4px;\n  color: #98A2B3;\n}\n.acf-admin-field-groups .wp-list-table .acf-location .dashicons {\n  position: relative;\n  bottom: -2px;\n  width: 16px;\n  height: 16px;\n  margin-right: 6px;\n  font-size: 16px;\n  color: #98A2B3;\n}\n.acf-admin-field-groups .wp-list-table .post-state {\n  color: #667085;\n}\n.acf-admin-field-groups .wp-list-table tr:hover,\n.acf-admin-field-groups .wp-list-table tr:focus-within {\n  background: #f7f7f7;\n}\n.acf-admin-field-groups .wp-list-table tr:hover .row-actions,\n.acf-admin-field-groups .wp-list-table tr:focus-within .row-actions {\n  margin-bottom: 0;\n}\n@media screen and (min-width: 782px) {\n  .acf-admin-field-groups .wp-list-table .column-acf-count {\n    width: 10%;\n  }\n}\n.acf-admin-field-groups .wp-list-table .row-actions span.file {\n  display: block;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n.acf-admin-field-groups.rtl .wp-list-table .column-acf-key .acf-icon-key-solid {\n  margin-left: 4px;\n  margin-right: 0;\n}\n.acf-admin-field-groups.rtl .wp-list-table .acf-location .dashicons {\n  margin-left: 6px;\n  margin-right: 0;\n}\n.acf-admin-field-groups .row-actions {\n  margin-top: 2px;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  line-height: 14px;\n  color: #D0D5DD;\n}\n.acf-admin-field-groups .row-actions .trash a {\n  color: #d94f4f;\n}\n.acf-admin-field-groups .widefat thead td.check-column,\n.acf-admin-field-groups .widefat tfoot td.check-column {\n  padding-top: 0;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRow actions\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups .row-actions a:hover {\n  color: #044767;\n}\n.acf-admin-field-groups .row-actions .trash a {\n  color: #a00;\n}\n.acf-admin-field-groups .row-actions .trash a:hover {\n  color: #f00;\n}\n.acf-admin-field-groups .row-actions.visible {\n  margin-bottom: 0;\n  opacity: 1;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRow hover\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups #the-list tr:hover td,\n.acf-admin-field-groups #the-list tr:hover th {\n  background-color: #f7fbfd;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Table Nav\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups .tablenav {\n  margin-top: 24px;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  color: #667085;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSearch box\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups #posts-filter p.search-box {\n  margin-top: 5px;\n  margin-right: 0;\n  margin-bottom: 24px;\n  margin-left: 0;\n}\n.acf-admin-field-groups #posts-filter p.search-box #post-search-input {\n  min-width: 280px;\n  margin-top: 0;\n  margin-right: 8px;\n  margin-bottom: 0;\n  margin-left: 0;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-field-groups #posts-filter p.search-box {\n    display: flex;\n    box-sizing: border-box;\n    padding-right: 24px;\n    margin-right: 16px;\n  }\n  .acf-admin-field-groups #posts-filter p.search-box #post-search-input {\n    min-width: auto;\n  }\n}\n\n.rtl.acf-admin-field-groups #posts-filter p.search-box #post-search-input {\n  margin-right: 0;\n  margin-left: 8px;\n}\n@media screen and (max-width: 768px) {\n  .rtl.acf-admin-field-groups #posts-filter p.search-box {\n    padding-left: 24px;\n    padding-right: 0;\n    margin-left: 16px;\n    margin-right: 0;\n  }\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tStatus tabs\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups .subsubsub {\n  display: flex;\n  align-items: flex-end;\n  height: 40px;\n  margin-bottom: 16px;\n}\n.acf-admin-field-groups .subsubsub li {\n  margin-top: 0;\n  margin-right: 4px;\n  color: #98A2B3;\n}\n.acf-admin-field-groups .subsubsub li .count {\n  color: #667085;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPagination\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups .tablenav-pages {\n  display: flex;\n  align-items: center;\n}\n.acf-admin-field-groups .tablenav-pages .displaying-num {\n  margin-top: 0;\n  margin-right: 16px;\n  margin-bottom: 0;\n  margin-left: 0;\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links {\n  display: flex;\n  align-items: center;\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links #table-paging {\n  margin-top: 0;\n  margin-right: 4px;\n  margin-bottom: 0;\n  margin-left: 8px;\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links #table-paging .total-pages {\n  margin-right: 0;\n}\n.acf-admin-field-groups .tablenav-pages.one-page .pagination-links {\n  display: none;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPagination buttons & icons\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups .tablenav-pages .pagination-links .button {\n  display: inline-flex;\n  align-items: center;\n  align-content: center;\n  justify-content: center;\n  min-width: 40px;\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n  background-color: transparent;\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links .button:nth-child(1), .acf-admin-field-groups .tablenav-pages .pagination-links .button:nth-child(2), .acf-admin-field-groups .tablenav-pages .pagination-links .button:last-child, .acf-admin-field-groups .tablenav-pages .pagination-links .button:nth-last-child(2) {\n  display: inline-block;\n  position: relative;\n  text-indent: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  margin-left: 4px;\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links .button:nth-child(1):before, .acf-admin-field-groups .tablenav-pages .pagination-links .button:nth-child(2):before, .acf-admin-field-groups .tablenav-pages .pagination-links .button:last-child:before, .acf-admin-field-groups .tablenav-pages .pagination-links .button:nth-last-child(2):before {\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n  background-color: #0783BE;\n  border-radius: 0;\n  -webkit-mask-size: 20px;\n  mask-size: 20px;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links .button:nth-child(1):before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-left-double.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-left-double.svg\");\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links .button:nth-child(2):before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links .button:nth-last-child(2):before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links .button:last-child:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-right-double.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-right-double.svg\");\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links .button:hover {\n  border-color: #066998;\n  background-color: rgba(7, 131, 190, 0.05);\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links .button:hover:before {\n  background-color: #066998;\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links .button.disabled {\n  background-color: transparent !important;\n}\n.acf-admin-field-groups .tablenav-pages .pagination-links .button.disabled.disabled:before {\n  background-color: #D0D5DD;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Empty state\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-no-field-groups-wrapper {\n  display: flex;\n  justify-content: center;\n  padding-top: 48px;\n  padding-bottom: 48px;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  align-content: center;\n  align-items: flex-start;\n  text-align: center;\n  max-width: 380px;\n  min-height: 320px;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner img,\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner h2,\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner p {\n  flex: 1 0 100%;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner h2 {\n  margin-top: 32px;\n  margin-bottom: 0;\n  padding: 0;\n  color: #344054;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner p {\n  margin-top: 12px;\n  margin-bottom: 0;\n  padding: 0;\n  color: #667085;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner p.acf-small {\n  display: block;\n  position: relative;\n  margin-top: 32px;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner img {\n  max-width: 284px;\n  margin-bottom: 0;\n}\n.acf-no-field-groups-wrapper .acf-no-field-groups-inner .acf-btn {\n  margin-top: 32px;\n}\n\n.acf-no-field-groups #the-list tr:hover td,\n.acf-no-field-groups #the-list tr:hover th,\n.acf-no-field-groups .acf-admin-field-groups .wp-list-table tr:hover,\n.acf-no-field-groups .striped > tbody > :nth-child(odd), .acf-no-field-groups ul.striped > :nth-child(odd), .acf-no-field-groups .alternate {\n  background-color: transparent !important;\n}\n.acf-no-field-groups .wp-list-table thead,\n.acf-no-field-groups .wp-list-table tfoot {\n  display: none;\n}\n.acf-no-field-groups .no-pages {\n  display: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small screen list table info toggle\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .wp-list-table .toggle-row:before {\n  top: 4px;\n  left: 16px;\n  border-radius: 0;\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: 16px;\n  height: 16px;\n  background-color: #0783BE;\n  border-radius: 0;\n  -webkit-mask-size: 20px;\n  mask-size: 20px;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  text-indent: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.post-type-acf-field-group .wp-list-table .is-expanded .toggle-row:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small screen checkbox\n*\n*---------------------------------------------------------------------------------------------*/\n@media screen and (max-width: 880px) {\n  .post-type-acf-field-group .widefat th input[type=checkbox],\n  .post-type-acf-field-group .widefat thead td input[type=checkbox],\n  .post-type-acf-field-group .widefat tfoot td input[type=checkbox] {\n    margin-bottom: 0;\n  }\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Navigation\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar {\n  display: flex;\n  justify-content: flex-start;\n  align-content: center;\n  align-items: center;\n  position: unset;\n  top: 32px;\n  height: 72px;\n  z-index: 800;\n  background: #344054;\n  color: #98A2B3;\n}\n@media screen and (max-width: 880px) {\n  .acf-admin-toolbar {\n    position: static;\n  }\n}\n.acf-admin-toolbar .acf-logo {\n  margin-right: 32px;\n}\n.acf-admin-toolbar .acf-logo img {\n  display: block;\n  max-width: 55px;\n  line-height: 0%;\n}\n.acf-admin-toolbar h2 {\n  display: none;\n  color: #F9FAFB;\n}\n.acf-admin-toolbar .acf-tab {\n  display: flex;\n  align-items: center;\n  box-sizing: border-box;\n  min-height: 40px;\n  margin-right: 8px;\n  padding-top: 8px;\n  padding-right: 16px;\n  padding-bottom: 8px;\n  padding-left: 16px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: transparent;\n  border-radius: 6px;\n  color: #98A2B3;\n  text-decoration: none;\n}\n.acf-admin-toolbar .acf-tab.is-active {\n  background-color: #475467;\n  color: #fff;\n}\n.acf-admin-toolbar .acf-tab:hover {\n  background-color: #475467;\n  color: #F9FAFB;\n}\n.acf-admin-toolbar .acf-tab:focus-visible {\n  border-width: 1px;\n  border-style: solid;\n  border-color: #667085;\n}\n.acf-admin-toolbar .acf-tab:focus {\n  box-shadow: none;\n}\n#wpcontent .acf-admin-toolbar {\n  box-sizing: border-box;\n  margin-left: -20px;\n  padding-top: 16px;\n  padding-right: 32px;\n  padding-bottom: 16px;\n  padding-left: 32px;\n}\n@media screen and (max-width: 600px) {\n  .acf-admin-toolbar {\n    display: none;\n  }\n}\n\n.rtl #wpcontent .acf-admin-toolbar {\n  margin-left: 0;\n  margin-right: -20px;\n}\n.rtl #wpcontent .acf-admin-toolbar .acf-tab {\n  margin-left: 8px;\n  margin-right: 0;\n}\n.rtl .acf-logo {\n  margin-right: 0;\n  margin-left: 32px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Toolbar Icons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar .acf-tab i.acf-icon {\n  display: none;\n  margin-right: 8px;\n  margin-left: -2px;\n}\n.acf-admin-toolbar .acf-tab.acf-header-tab-acf-field-group i.acf-icon, .acf-admin-toolbar .acf-tab.acf-header-tab-acf-tools i.acf-icon, .acf-admin-toolbar .acf-tab.acf-header-tab-acf-settings-updates i.acf-icon {\n  display: inline-flex;\n}\n.acf-admin-toolbar .acf-tab.is-active i.acf-icon, .acf-admin-toolbar .acf-tab:hover i.acf-icon {\n  background-color: #EAECF0;\n}\n.rtl .acf-admin-toolbar .acf-tab i.acf-icon {\n  margin-right: -2px;\n  margin-left: 8px;\n}\n.acf-admin-toolbar .acf-header-tab-acf-field-group i.acf-icon {\n  -webkit-mask-image: url(\"../../images/icons/icon-field-groups.svg\");\n  mask-image: url(\"../../images/icons/icon-field-groups.svg\");\n}\n.acf-admin-toolbar .acf-header-tab-acf-tools i.acf-icon {\n  -webkit-mask-image: url(\"../../images/icons/icon-tools.svg\");\n  mask-image: url(\"../../images/icons/icon-tools.svg\");\n}\n.acf-admin-toolbar .acf-header-tab-acf-settings-updates i.acf-icon {\n  -webkit-mask-image: url(\"../../images/icons/icon-updates.svg\");\n  mask-image: url(\"../../images/icons/icon-updates.svg\");\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide WP default controls\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group h1.wp-heading-inline {\n  display: none;\n}\n.post-type-acf-field-group .wrap .wp-heading-inline + .page-title-action {\n  display: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headerbar\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-headerbar {\n  display: flex;\n  align-items: center;\n  position: sticky;\n  top: 32px;\n  z-index: 700;\n  box-sizing: border-box;\n  min-height: 72px;\n  margin-left: -20px;\n  padding-top: 8px;\n  padding-right: 32px;\n  padding-bottom: 8px;\n  padding-left: 32px;\n  background-color: #fff;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n.acf-headerbar .acf-headerbar-inner {\n  flex: 1 1 auto;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  max-width: 1440px;\n}\n.acf-headerbar .acf-page-title {\n  margin-top: 0;\n  margin-right: 16px;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n@media screen and (max-width: 880px) {\n  .acf-headerbar {\n    position: static;\n  }\n}\n@media screen and (max-width: 600px) {\n  .acf-headerbar {\n    justify-content: space-between;\n    position: relative;\n    top: 46px;\n    min-height: 64px;\n    padding-right: 12px;\n  }\n}\n.acf-headerbar .acf-headerbar-content {\n  flex: 1 1 auto;\n  display: flex;\n  align-items: center;\n}\n@media screen and (max-width: 880px) {\n  .acf-headerbar .acf-headerbar-content {\n    flex-wrap: wrap;\n  }\n  .acf-headerbar .acf-headerbar-content .acf-headerbar-title,\n  .acf-headerbar .acf-headerbar-content .acf-title-wrap {\n    flex: 1 1 100%;\n  }\n  .acf-headerbar .acf-headerbar-content .acf-title-wrap {\n    margin-top: 8px;\n  }\n}\n.acf-headerbar .acf-input-error {\n  border: 1px rgba(209, 55, 55, 0.5) solid !important;\n  box-shadow: 0px 0px 0px 3px rgba(209, 55, 55, 0.12), 0px 0px 0px rgba(255, 54, 54, 0.25) !important;\n  background-image: url(\"../../images/icons/icon-warning-alt-red.svg\");\n  background-position: right 10px top 50%;\n  background-size: 20px;\n  background-repeat: no-repeat;\n}\n.acf-headerbar .acf-input-error:focus {\n  outline: none !important;\n  border: 1px rgba(209, 55, 55, 0.8) solid !important;\n  box-shadow: 0px 0px 0px 3px rgba(209, 55, 55, 0.16), 0px 0px 0px rgba(255, 54, 54, 0.25) !important;\n}\n.acf-headerbar .acf-headerbar-title-field {\n  min-width: 320px;\n}\n@media screen and (max-width: 880px) {\n  .acf-headerbar .acf-headerbar-title-field {\n    min-width: 100%;\n  }\n}\n.acf-headerbar .acf-headerbar-actions {\n  display: flex;\n}\n.acf-headerbar .acf-headerbar-actions .acf-btn {\n  margin-left: 8px;\n}\n.acf-headerbar .acf-headerbar-actions .disabled {\n  background-color: #F2F4F7;\n  color: #98A2B3 !important;\n  border: 1px #D0D5DD solid;\n  cursor: default;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Edit Field Group Headerbar\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-headerbar-field-editor {\n  position: sticky;\n  top: 32px;\n  z-index: 700;\n  margin-left: -20px;\n  width: calc(100% + 20px);\n}\n@media screen and (max-width: 880px) {\n  .acf-headerbar-field-editor {\n    position: relative;\n    top: 0;\n    width: 100%;\n    margin-left: 0;\n    padding-right: 8px;\n    padding-left: 8px;\n  }\n}\n@media screen and (max-width: 640px) {\n  .acf-headerbar-field-editor {\n    position: relative;\n    top: 46px;\n  }\n}\n@media screen and (max-width: 880px) {\n  .acf-headerbar-field-editor .acf-headerbar-inner {\n    flex-wrap: wrap;\n    justify-content: flex-start;\n    align-content: flex-start;\n    align-items: flex-start;\n    width: 100%;\n  }\n  .acf-headerbar-field-editor .acf-headerbar-inner .acf-page-title {\n    flex: 1 1 auto;\n  }\n  .acf-headerbar-field-editor .acf-headerbar-inner .acf-headerbar-actions {\n    flex: 1 1 100%;\n    margin-top: 8px;\n    gap: 8px;\n  }\n  .acf-headerbar-field-editor .acf-headerbar-inner .acf-headerbar-actions .acf-btn {\n    width: 100%;\n    display: inline-flex;\n    justify-content: center;\n    margin: 0;\n  }\n}\n.acf-headerbar-field-editor .acf-page-title {\n  margin-right: 16px;\n}\n\n.rtl .acf-headerbar,\n.rtl .acf-headerbar-field-editor {\n  margin-left: 0;\n  margin-right: -20px;\n}\n.rtl .acf-headerbar .acf-page-title,\n.rtl .acf-headerbar-field-editor .acf-page-title {\n  margin-left: 16px;\n  margin-right: 0;\n}\n.rtl .acf-headerbar .acf-headerbar-actions .acf-btn,\n.rtl .acf-headerbar-field-editor .acf-headerbar-actions .acf-btn {\n  margin-left: 0;\n  margin-right: 8px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Buttons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn {\n  display: inline-flex;\n  align-items: center;\n  box-sizing: border-box;\n  min-height: 40px;\n  padding-top: 8px;\n  padding-right: 16px;\n  padding-bottom: 8px;\n  padding-left: 16px;\n  background-color: #0783BE;\n  border-radius: 6px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: rgba(16, 24, 40, 0.2);\n  text-decoration: none;\n  color: #fff !important;\n  transition: all 0.2s ease-in-out;\n  transition-property: background, border, box-shadow;\n}\n.acf-btn:disabled {\n  background-color: red;\n}\n.acf-btn:hover {\n  background-color: #066998;\n  color: #fff;\n  cursor: pointer;\n}\n.acf-btn.acf-btn-sm {\n  min-height: 32px;\n  padding-top: 4px;\n  padding-right: 12px;\n  padding-bottom: 4px;\n  padding-left: 12px;\n}\n.acf-btn.acf-btn-secondary {\n  background-color: transparent;\n  color: #0783BE !important;\n  border-color: #0783BE;\n}\n.acf-btn.acf-btn-secondary:hover {\n  background-color: #f3f9fc;\n}\n.acf-btn.acf-btn-tertiary {\n  background-color: transparent;\n  color: #667085 !important;\n  border-color: #D0D5DD;\n}\n.acf-btn.acf-btn-tertiary:hover {\n  color: #667085 !important;\n  border-color: #98A2B3;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Button icons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn i.acf-icon {\n  width: 20px;\n  height: 20px;\n  -webkit-mask-size: 20px;\n  mask-size: 20px;\n  margin-right: 6px;\n  margin-left: -4px;\n}\n.acf-btn.acf-btn-sm i.acf-icon {\n  width: 18px;\n  height: 18px;\n  -webkit-mask-size: 18px;\n  mask-size: 18px;\n  margin-right: 4px;\n  margin-left: -2px;\n}\n\n.rtl .acf-btn i.acf-icon {\n  margin-right: -4px;\n  margin-left: 6px;\n}\n.rtl .acf-btn.acf-btn-sm i.acf-icon {\n  margin-right: -4px;\n  margin-left: 2px;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Delete field group button\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn.acf-delete-field-group:hover {\n  background-color: #fbeded;\n  border-color: #D13737 !important;\n  color: #D13737 !important;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tIcon base styling\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group i.acf-icon {\n  display: inline-flex;\n  width: 20px;\n  height: 20px;\n  background-color: currentColor;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  text-indent: 500%;\n  white-space: nowrap;\n  overflow: hidden;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tIcons\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n  /*--------------------------------------------------------------------------------------------\n  *\n  *\tInactive group icon\n  *\n  *--------------------------------------------------------------------------------------------*/\n}\n.post-type-acf-field-group i.acf-field-setting-fc-delete, .post-type-acf-field-group i.acf-field-setting-fc-duplicate {\n  box-sizing: border-box;\n  /* Auto layout */\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  padding: 8px;\n  cursor: pointer;\n  width: 32px;\n  height: 32px;\n  /* Base / White */\n  background: #FFFFFF;\n  /* Gray/300 */\n  border: 1px solid #D0D5DD;\n  /* Elevation/01 */\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n  border-radius: 6px;\n  /* Inside auto layout */\n  flex: none;\n  order: 0;\n  flex-grow: 0;\n}\n.post-type-acf-field-group i.acf-icon-plus {\n  -webkit-mask-image: url(\"../../images/icons/icon-add.svg\");\n  mask-image: url(\"../../images/icons/icon-add.svg\");\n}\n.post-type-acf-field-group i.acf-icon-stars {\n  -webkit-mask-image: url(\"../../images/icons/icon-stars.svg\");\n  mask-image: url(\"../../images/icons/icon-stars.svg\");\n}\n.post-type-acf-field-group i.acf-icon-help {\n  -webkit-mask-image: url(\"../../images/icons/icon-help.svg\");\n  mask-image: url(\"../../images/icons/icon-help.svg\");\n}\n.post-type-acf-field-group i.acf-icon-key {\n  -webkit-mask-image: url(\"../../images/icons/icon-key.svg\");\n  mask-image: url(\"../../images/icons/icon-key.svg\");\n}\n.post-type-acf-field-group i.acf-icon-trash, .post-type-acf-field-group button.acf-icon-trash {\n  -webkit-mask-image: url(\"../../images/icons/icon-trash.svg\");\n  mask-image: url(\"../../images/icons/icon-trash.svg\");\n}\n.post-type-acf-field-group i.acf-icon.-duplicate, .post-type-acf-field-group button.acf-icon-duplicate {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n}\n.post-type-acf-field-group i.acf-icon.-duplicate:before, .post-type-acf-field-group i.acf-icon.-duplicate:after, .post-type-acf-field-group button.acf-icon-duplicate:before, .post-type-acf-field-group button.acf-icon-duplicate:after {\n  content: none;\n}\n.post-type-acf-field-group i.acf-icon-arrow-right {\n  -webkit-mask-image: url(\"../../images/icons/icon-arrow-right.svg\");\n  mask-image: url(\"../../images/icons/icon-arrow-right.svg\");\n}\n.post-type-acf-field-group i.acf-icon-arrow-left {\n  -webkit-mask-image: url(\"../../images/icons/icon-arrow-left.svg\");\n  mask-image: url(\"../../images/icons/icon-arrow-left.svg\");\n}\n.post-type-acf-field-group i.acf-icon-chevron-right,\n.post-type-acf-field-group .acf-icon.-right {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n}\n.post-type-acf-field-group i.acf-icon-chevron-left,\n.post-type-acf-field-group .acf-icon.-left {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n}\n.post-type-acf-field-group i.acf-icon-key-solid {\n  -webkit-mask-image: url(\"../../images/icons/icon-key-solid.svg\");\n  mask-image: url(\"../../images/icons/icon-key-solid.svg\");\n}\n.post-type-acf-field-group i.acf-icon-globe,\n.post-type-acf-field-group .acf-icon.-globe {\n  -webkit-mask-image: url(\"../../images/icons/icon-globe.svg\");\n  mask-image: url(\"../../images/icons/icon-globe.svg\");\n}\n.post-type-acf-field-group i.acf-icon-image,\n.post-type-acf-field-group .acf-icon.-picture {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n}\n.post-type-acf-field-group i.acf-icon-warning {\n  -webkit-mask-image: url(\"../../images/icons/icon-warning-alt.svg\");\n  mask-image: url(\"../../images/icons/icon-warning-alt.svg\");\n}\n.post-type-acf-field-group i.acf-icon-warning-red {\n  -webkit-mask-image: url(\"../../images/icons/icon-warning-alt-red.svg\");\n  mask-image: url(\"../../images/icons/icon-warning-alt-red.svg\");\n}\n.post-type-acf-field-group .post-type-acf-field-group .post-state {\n  font-weight: normal;\n}\n.post-type-acf-field-group .post-type-acf-field-group .post-state .dashicons.dashicons-hidden {\n  display: inline-flex;\n  width: 18px;\n  height: 18px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: 18px;\n  mask-size: 18px;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-hidden.svg\");\n  mask-image: url(\"../../images/icons/icon-hidden.svg\");\n}\n.post-type-acf-field-group .post-type-acf-field-group .post-state .dashicons.dashicons-hidden:before {\n  display: none;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tEdit field group page postbox header icons\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-field-group-fields .postbox-header h2,\n#acf-field-group-fields .postbox-header h3,\n#acf-field-group-fields .acf-sub-field-list-header h2,\n#acf-field-group-fields .acf-sub-field-list-header h3,\n#acf-field-group-options .postbox-header h2,\n#acf-field-group-options .postbox-header h3,\n#acf-field-group-options .acf-sub-field-list-header h2,\n#acf-field-group-options .acf-sub-field-list-header h3 {\n  display: inline-flex;\n  justify-content: flex-start;\n  align-content: stretch;\n  align-items: center;\n}\n#acf-field-group-fields .postbox-header h2:before,\n#acf-field-group-fields .postbox-header h3:before,\n#acf-field-group-fields .acf-sub-field-list-header h2:before,\n#acf-field-group-fields .acf-sub-field-list-header h3:before,\n#acf-field-group-options .postbox-header h2:before,\n#acf-field-group-options .postbox-header h3:before,\n#acf-field-group-options .acf-sub-field-list-header h2:before,\n#acf-field-group-options .acf-sub-field-list-header h3:before {\n  content: \"\";\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n}\n\n.rtl #acf-field-group-fields .postbox-header h2:before,\n.rtl #acf-field-group-fields .postbox-header h3:before,\n.rtl #acf-field-group-fields .acf-sub-field-list-header h2:before,\n.rtl #acf-field-group-fields .acf-sub-field-list-header h3:before,\n.rtl #acf-field-group-options .postbox-header h2:before,\n.rtl #acf-field-group-options .postbox-header h3:before,\n.rtl #acf-field-group-options .acf-sub-field-list-header h2:before,\n.rtl #acf-field-group-options .acf-sub-field-list-header h3:before {\n  margin-right: 0;\n  margin-left: 8px;\n}\n\n#acf-field-group-fields .postbox-header h2:before,\nh3.acf-sub-field-list-title:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-fields.svg\");\n  mask-image: url(\"../../images/icons/icon-fields.svg\");\n}\n\n#acf-field-group-options .postbox-header h2:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-settings.svg\");\n  mask-image: url(\"../../images/icons/icon-settings.svg\");\n}\n\n.acf-field-setting-fc_layout .acf-field-settings-fc_head label:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-layout.svg\");\n  mask-image: url(\"../../images/icons/icon-layout.svg\");\n}\n\n.acf-field-setting-fc_layout .acf-field-settings-fc_head:hover .reorder-layout:before {\n  width: 20px;\n  height: 11px;\n  background-color: #475467 !important;\n  -webkit-mask-image: url(\"../../images/icons/icon-draggable.svg\");\n  mask-image: url(\"../../images/icons/icon-draggable.svg\");\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPostbox expand / collapse icon\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .postbox-header .handle-actions,\n.post-type-acf-field-group #acf-field-group-fields .postbox-header .handle-actions,\n.post-type-acf-field-group #acf-field-group-options .postbox-header .handle-actions,\n.post-type-acf-field-group .postbox .postbox-header .handle-actions {\n  display: flex;\n}\n.post-type-acf-field-group .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group #acf-field-group-fields .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group #acf-field-group-options .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group .postbox .postbox-header .handle-actions .toggle-indicator:before {\n  content: \"\";\n  display: inline-flex;\n  width: 20px;\n  height: 20px;\n  background-color: currentColor;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n}\n.post-type-acf-field-group.closed .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group #acf-field-group-fields.closed .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group #acf-field-group-options.closed .postbox-header .handle-actions .toggle-indicator:before,\n.post-type-acf-field-group .postbox.closed .postbox-header .handle-actions .toggle-indicator:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n  mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Tools & updates page heading icons\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #acf-admin-tool-export h2,\n.post-type-acf-field-group #acf-admin-tool-export h3,\n.post-type-acf-field-group #acf-admin-tool-import h2,\n.post-type-acf-field-group #acf-admin-tool-import h3,\n.post-type-acf-field-group #acf-license-information h2,\n.post-type-acf-field-group #acf-license-information h3,\n.post-type-acf-field-group #acf-update-information h2,\n.post-type-acf-field-group #acf-update-information h3 {\n  display: inline-flex;\n  justify-content: flex-start;\n  align-content: stretch;\n  align-items: center;\n}\n.post-type-acf-field-group #acf-admin-tool-export h2:before,\n.post-type-acf-field-group #acf-admin-tool-export h3:before,\n.post-type-acf-field-group #acf-admin-tool-import h2:before,\n.post-type-acf-field-group #acf-admin-tool-import h3:before,\n.post-type-acf-field-group #acf-license-information h2:before,\n.post-type-acf-field-group #acf-license-information h3:before,\n.post-type-acf-field-group #acf-update-information h2:before,\n.post-type-acf-field-group #acf-update-information h3:before {\n  content: \"\";\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  margin-right: 8px;\n  background-color: #98A2B3;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n}\n.post-type-acf-field-group.rtl #acf-admin-tool-export h2:before,\n.post-type-acf-field-group.rtl #acf-admin-tool-export h3:before,\n.post-type-acf-field-group.rtl #acf-admin-tool-import h2:before,\n.post-type-acf-field-group.rtl #acf-admin-tool-import h3:before,\n.post-type-acf-field-group.rtl #acf-license-information h2:before,\n.post-type-acf-field-group.rtl #acf-license-information h3:before,\n.post-type-acf-field-group.rtl #acf-update-information h2:before,\n.post-type-acf-field-group.rtl #acf-update-information h3:before {\n  margin-right: 0;\n  margin-left: 8px;\n}\n\n.post-type-acf-field-group #acf-admin-tool-export h2:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-export.svg\");\n  mask-image: url(\"../../images/icons/icon-export.svg\");\n}\n\n.post-type-acf-field-group #acf-admin-tool-import h2:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-import.svg\");\n  mask-image: url(\"../../images/icons/icon-import.svg\");\n}\n\n.post-type-acf-field-group #acf-license-information h3:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-key.svg\");\n  mask-image: url(\"../../images/icons/icon-key.svg\");\n}\n\n.post-type-acf-field-group #acf-update-information h3:before {\n  -webkit-mask-image: url(\"../../images/icons/icon-info.svg\");\n  mask-image: url(\"../../images/icons/icon-info.svg\");\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tAdmin field icons\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-single-field-group .acf-input .acf-icon {\n  width: 18px;\n  height: 18px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tField type icon base styling\n*\n*--------------------------------------------------------------------------------------------*/\n.field-type-icon {\n  box-sizing: border-box;\n  display: inline-flex;\n  align-content: center;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  width: 24px;\n  height: 24px;\n  top: -4px;\n  background-color: #EBF5FA;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #A5D2E7;\n  border-radius: 100%;\n}\n.field-type-icon:before {\n  content: \"\";\n  width: 14px;\n  height: 14px;\n  position: relative;\n  background-color: #0783BE;\n  -webkit-mask-size: cover;\n  mask-size: cover;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-default.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-default.svg\");\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tField type icons\n*\n*--------------------------------------------------------------------------------------------*/\n.field-type-icon.field-type-icon-text:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-text.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-text.svg\");\n}\n\n.field-type-icon.field-type-icon-textarea:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-textarea.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-textarea.svg\");\n}\n\n.field-type-icon.field-type-icon-textarea:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-textarea.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-textarea.svg\");\n}\n\n.field-type-icon.field-type-icon-number:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-number.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-number.svg\");\n}\n\n.field-type-icon.field-type-icon-range:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-range.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-range.svg\");\n}\n\n.field-type-icon.field-type-icon-email:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-email.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-email.svg\");\n}\n\n.field-type-icon.field-type-icon-url:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-url.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-url.svg\");\n}\n\n.field-type-icon.field-type-icon-password:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-password.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-password.svg\");\n}\n\n.field-type-icon.field-type-icon-image:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n}\n\n.field-type-icon.field-type-icon-file:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-file.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-file.svg\");\n}\n\n.field-type-icon.field-type-icon-wysiwyg:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-wysiwyg.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-wysiwyg.svg\");\n}\n\n.field-type-icon.field-type-icon-oembed:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-oembed.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-oembed.svg\");\n}\n\n.field-type-icon.field-type-icon-gallery:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-gallery.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-gallery.svg\");\n}\n\n.field-type-icon.field-type-icon-select:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-select.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-select.svg\");\n}\n\n.field-type-icon.field-type-icon-checkbox:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-checkbox.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-checkbox.svg\");\n}\n\n.field-type-icon.field-type-icon-radio:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-radio.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-radio.svg\");\n}\n\n.field-type-icon.field-type-icon-button-group:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-button-group.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-button-group.svg\");\n}\n\n.field-type-icon.field-type-icon-true-false:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-true-false.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-true-false.svg\");\n}\n\n.field-type-icon.field-type-icon-link:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-link.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-link.svg\");\n}\n\n.field-type-icon.field-type-icon-post-object:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-post-object.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-post-object.svg\");\n}\n\n.field-type-icon.field-type-icon-page-link:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-page-link.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-page-link.svg\");\n}\n\n.field-type-icon.field-type-icon-relationship:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-relationship.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-relationship.svg\");\n}\n\n.field-type-icon.field-type-icon-taxonomy:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-taxonomy.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-taxonomy.svg\");\n}\n\n.field-type-icon.field-type-icon-user:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-user.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-user.svg\");\n}\n\n.field-type-icon.field-type-icon-google-map:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-google-map.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-google-map.svg\");\n}\n\n.field-type-icon.field-type-icon-date-picker:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-date-picker.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-date-picker.svg\");\n}\n\n.field-type-icon.field-type-icon-date-time-picker:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-date-time-picker.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-date-time-picker.svg\");\n}\n\n.field-type-icon.field-type-icon-time-picker:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-time-picker.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-time-picker.svg\");\n}\n\n.field-type-icon.field-type-icon-color-picker:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-color-picker.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-color-picker.svg\");\n}\n\n.field-type-icon.field-type-icon-message:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-message.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-message.svg\");\n}\n\n.field-type-icon.field-type-icon-accordion:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-accordion.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-accordion.svg\");\n}\n\n.field-type-icon.field-type-icon-tab:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-tab.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-tab.svg\");\n}\n\n.field-type-icon.field-type-icon-group:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-group.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-group.svg\");\n}\n\n.field-type-icon.field-type-icon-repeater:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-repeater.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-repeater.svg\");\n}\n\n.field-type-icon.field-type-icon-flexible-content:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-flexible-content.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-flexible-content.svg\");\n}\n\n.field-type-icon.field-type-icon-clone:before {\n  -webkit-mask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n  mask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Tools page layout\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-admin-tools .postbox-header {\n  display: none;\n}\n#acf-admin-tools .acf-meta-box-wrap.-grid {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n}\n#acf-admin-tools .acf-meta-box-wrap.-grid .postbox {\n  width: 100%;\n  clear: none;\n  float: none;\n  margin-bottom: 0;\n}\n@media screen and (max-width: 880px) {\n  #acf-admin-tools .acf-meta-box-wrap.-grid .postbox {\n    flex: 1 1 100%;\n  }\n}\n#acf-admin-tools .acf-meta-box-wrap.-grid .postbox:nth-child(odd) {\n  margin-left: 0;\n}\n#acf-admin-tools .meta-box-sortables {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  grid-template-rows: repeat(1, 1fr);\n  grid-column-gap: 32px;\n  grid-row-gap: 32px;\n}\n@media screen and (max-width: 880px) {\n  #acf-admin-tools .meta-box-sortables {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: flex-start;\n    align-content: flex-start;\n    align-items: center;\n    grid-column-gap: 8px;\n    grid-row-gap: 8px;\n  }\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Tools export pages\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-admin-tools.tool-export .inside {\n  margin: 0;\n}\n#acf-admin-tools.tool-export .acf-postbox-header {\n  margin-bottom: 24px;\n}\n#acf-admin-tools.tool-export .acf-postbox-main {\n  border: none;\n  margin: 0;\n  padding-top: 0;\n  padding-right: 24px;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n#acf-admin-tools.tool-export .acf-postbox-columns {\n  margin-top: 0;\n  margin-right: 280px;\n  margin-bottom: 0;\n  margin-left: 0;\n  padding: 0;\n}\n#acf-admin-tools.tool-export .acf-postbox-columns .acf-postbox-side {\n  padding: 0;\n}\n#acf-admin-tools.tool-export .acf-postbox-columns .acf-postbox-side .acf-panel {\n  margin: 0;\n  padding: 0;\n}\n#acf-admin-tools.tool-export .acf-postbox-columns .acf-postbox-side:before {\n  display: none;\n}\n#acf-admin-tools.tool-export .acf-postbox-columns .acf-postbox-side .acf-btn {\n  display: block;\n  width: 100%;\n  text-align: center;\n}\n#acf-admin-tools.tool-export .meta-box-sortables {\n  display: block;\n}\n#acf-admin-tools.tool-export .acf-panel {\n  border: none;\n}\n#acf-admin-tools.tool-export .acf-panel h3 {\n  margin: 0;\n  padding: 0;\n  color: #344054;\n}\n#acf-admin-tools.tool-export .acf-panel h3:before {\n  display: none;\n}\n#acf-admin-tools.tool-export .acf-checkbox-list {\n  margin-top: 16px;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #D0D5DD;\n  border-radius: 6px;\n}\n#acf-admin-tools.tool-export .acf-checkbox-list li {\n  display: inline-flex;\n  box-sizing: border-box;\n  width: 100%;\n  height: 48px;\n  align-items: center;\n  margin: 0;\n  padding-right: 12px;\n  padding-left: 12px;\n  border-bottom-width: 1px;\n  border-bottom-style: solid;\n  border-bottom-color: #EAECF0;\n}\n#acf-admin-tools.tool-export .acf-checkbox-list li:last-child {\n  border-bottom: none;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Updates layout\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates {\n  display: flex;\n  flex-direction: row;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.custom-fields_page_acf-settings-updates .acf-admin-notice,\n.custom-fields_page_acf-settings-updates .acf-upgrade-notice,\n.custom-fields_page_acf-settings-updates .notice {\n  flex: 1 1 100%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Box\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates .acf-box {\n  margin-top: 0;\n  margin-right: 0;\n  margin-bottom: 0;\n  margin-left: 0;\n}\n.acf-settings-wrap.acf-updates .acf-box .inner {\n  padding-top: 24px;\n  padding-right: 24px;\n  padding-bottom: 24px;\n  padding-left: 24px;\n}\n@media screen and (max-width: 880px) {\n  .acf-settings-wrap.acf-updates .acf-box {\n    flex: 1 1 100%;\n  }\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Notices\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates .acf-admin-notice {\n  flex: 1 1 100%;\n  margin-top: 16px;\n  margin-right: 0;\n  margin-left: 0;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* License information\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-license-information {\n  flex: 1 1 65%;\n  margin-right: 32px;\n}\n@media screen and (max-width: 1024px) {\n  #acf-license-information {\n    margin-right: 0;\n    margin-bottom: 32px;\n  }\n}\n#acf-license-information .acf-activation-form {\n  margin-top: 24px;\n}\n#acf-license-information label {\n  font-weight: 500;\n}\n#acf-license-information .acf-input-wrap {\n  margin-top: 8px;\n  margin-bottom: 24px;\n}\n#acf-license-information #acf_pro_license {\n  width: 100%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Update information table\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-update-information {\n  flex: 1 1 35%;\n  max-width: calc(35% - 32px);\n}\n#acf-update-information .form-table th,\n#acf-update-information .form-table td {\n  padding-top: 0;\n  padding-right: 0;\n  padding-bottom: 24px;\n  padding-left: 0;\n  color: #344054;\n}\n#acf-update-information .acf-update-changelog {\n  margin-top: 8px;\n  margin-bottom: 24px;\n  padding-top: 8px;\n  border-top-width: 1px;\n  border-top-style: solid;\n  border-top-color: #EAECF0;\n  color: #344054;\n}\n#acf-update-information .acf-update-changelog h4 {\n  margin-bottom: 0;\n}\n#acf-update-information .acf-update-changelog p {\n  margin-top: 0;\n  margin-bottom: 16px;\n}\n#acf-update-information .acf-update-changelog p:last-of-type {\n  margin-bottom: 0;\n}\n#acf-update-information .acf-update-changelog p em {\n  color: #667085;\n}\n#acf-update-information .acf-btn {\n  display: inline-flex;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tHeader upsell button\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn {\n  display: inline-flex;\n  align-items: center;\n  align-self: stretch;\n  padding-top: 0;\n  padding-right: 16px;\n  padding-bottom: 0;\n  padding-left: 16px;\n  background: linear-gradient(90.52deg, #2C9FB8 0.44%, #A45CFF 113.3%);\n  background-size: 180% 80%;\n  background-position: 100% 0;\n  transition: background-position 0.5s;\n  border-radius: 6px;\n  text-decoration: none;\n}\n@media screen and (max-width: 768px) {\n  .acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn {\n    display: none;\n  }\n}\n.acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn:hover {\n  background-position: 0 0;\n}\n.acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn:focus {\n  border: none;\n  outline: none;\n  box-shadow: none;\n}\n.acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn p {\n  margin: 0;\n  padding-top: 8px;\n  padding-bottom: 8px;\n  font-weight: normal;\n  text-transform: none;\n  color: #fff;\n}\n.acf-admin-toolbar a.acf-admin-toolbar-upgrade-btn .acf-icon {\n  width: 18px;\n  height: 18px;\n  margin-right: 6px;\n  margin-left: -2px;\n  background-color: #F9FAFB;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Upsell block\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-field-group-pro-features .acf-field-group-pro-features-wrapper {\n  display: flex;\n  justify-content: flex-start;\n  align-content: stretch;\n  align-items: center;\n}\n@media screen and (max-width: 768px) {\n  #acf-field-group-pro-features .acf-field-group-pro-features-wrapper {\n    flex-direction: row;\n    flex-wrap: wrap;\n    justify-content: flex-start;\n    align-content: flex-start;\n    align-items: flex-start;\n  }\n  #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content,\n  #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-actions {\n    flex: 0 1 100%;\n  }\n}\n#acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content {\n  flex: 1 1 auto;\n  margin-right: 40px;\n}\n@media screen and (max-width: 768px) {\n  #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-content {\n    margin-right: 0;\n    margin-bottom: 8px;\n  }\n}\n#acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-actions {\n  display: flex;\n  flex-direction: row;\n  justify-content: flex-end;\n  min-width: 160px;\n}\n@media screen and (max-width: 768px) {\n  #acf-field-group-pro-features .acf-field-group-pro-features-wrapper .acf-field-group-pro-features-actions {\n    justify-content: flex-start;\n  }\n}\n#acf-field-group-pro-features.postbox {\n  display: flex;\n  align-items: center;\n  min-height: 120px;\n  background-image: linear-gradient(to right, #1d4373, #24437e, #304288, #413f8f, #543a95);\n  color: #EAECF0;\n}\n#acf-field-group-pro-features.postbox .postbox-header {\n  display: none;\n}\n#acf-field-group-pro-features.postbox .inside {\n  width: 100%;\n  border: none;\n}\n#acf-field-group-pro-features h1 {\n  margin-top: 0;\n  margin-bottom: 4px;\n  padding-top: 0;\n  padding-bottom: 0;\n  font-weight: bold;\n  color: #F9FAFB;\n}\n#acf-field-group-pro-features h1 .acf-icon {\n  margin-right: 8px;\n}\n#acf-field-group-pro-features .acf-btn {\n  display: inline-flex;\n  background-color: rgba(255, 255, 255, 0.2);\n  border: none;\n}\n#acf-field-group-pro-features .acf-btn:hover {\n  background-color: rgba(255, 255, 255, 0.3);\n}\n#acf-field-group-pro-features .acf-btn .acf-icon {\n  margin-right: -2px;\n  margin-left: 8px;\n}\n#acf-field-group-pro-features .acf-pro-features-list {\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  flex-wrap: wrap;\n  margin-top: 16px;\n  margin-bottom: 0;\n}\n@media screen and (max-width: 768px) {\n  #acf-field-group-pro-features .acf-pro-features-list {\n    flex-direction: row;\n    flex-wrap: wrap;\n    justify-content: flex-start;\n    align-content: flex-start;\n    align-items: flex-start;\n  }\n}\n#acf-field-group-pro-features .acf-pro-features-list li {\n  display: flex;\n  box-sizing: border-box;\n  margin-right: 32px;\n  margin-bottom: 6px;\n}\n@media screen and (max-width: 880px) {\n  #acf-field-group-pro-features .acf-pro-features-list li {\n    flex: 0 1 calc(33.3% - 32px);\n  }\n}\n@media screen and (max-width: 640px) {\n  #acf-field-group-pro-features .acf-pro-features-list li {\n    flex: 0 1 100%;\n  }\n}\n#acf-field-group-pro-features .acf-pro-features-list li:last-child {\n  margin-right: 0;\n}\n#acf-field-group-pro-features .acf-pro-features-list li:before {\n  content: \"\";\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  margin-right: 8px;\n  background-color: #52AA59;\n  border: none;\n  border-radius: 0;\n  -webkit-mask-size: contain;\n  mask-size: contain;\n  -webkit-mask-repeat: no-repeat;\n  mask-repeat: no-repeat;\n  -webkit-mask-position: center;\n  mask-position: center;\n  -webkit-mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n  mask-image: url(\"../../images/icons/icon-check-circle-solid.svg\");\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tVars\n*\n*--------------------------------------------------------------------------------------------*/\n\n/* colors */\n$acf_blue: #2a9bd9;\n$acf_notice: #2a9bd9;\n$acf_error: #d94f4f;\n$acf_success: #49ad52;\n$acf_warning: #fd8d3b;\n\n/* acf-field */\n$field_padding: 15px 12px;\n$field_padding_x: 12px;\n$field_padding_y: 15px;\n$fp: 15px 12px;\n$fy: 15px;\n$fx: 12px;\n\n/* responsive */\n$md: 880px;\n$sm: 640px;\n\n// Admin.\n$wp-card-border: #ccd0d4;\t\t\t// Card border.\n$wp-card-border-1: #d5d9dd;\t\t  // Card inner border 1: Structural (darker).\n$wp-card-border-2: #eeeeee;\t\t  // Card inner border 2: Fields (lighter).\n$wp-input-border: #7e8993;\t\t   // Input border.\n\n// Admin 3.8\n$wp38-card-border: #E5E5E5;\t\t  // Card border.\n$wp38-card-border-1: #dfdfdf;\t\t// Card inner border 1: Structural (darker).\n$wp38-card-border-2: #eeeeee;\t\t// Card inner border 2: Fields (lighter).\n$wp38-input-border: #dddddd;\t\t // Input border.\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tACF 6 ↓\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Grays\n$gray-50:  #F9FAFB;\n$gray-100: #F2F4F7;\n$gray-200: #EAECF0;\n$gray-300: #D0D5DD;\n$gray-400: #98A2B3;\n$gray-500: #667085;\n$gray-600: #475467;\n$gray-700: #344054;\n$gray-800: #1D2939;\n$gray-900: #101828;\n\n// Blues\n$blue-50:  #EBF5FA;\n$blue-100: #D8EBF5;\n$blue-200: #A5D2E7;\n$blue-300: #6BB5D8;\n$blue-400: #399CCB;\n$blue-500: #0783BE;\n$blue-600: #066998;\n$blue-700: #044E71;\n$blue-800: #033F5B;\n$blue-900: #032F45;\n\n// Utility\n$color-info:\t#2D69DA;\n$color-success:\t#52AA59;\n$color-warning:\t#F79009;\n$color-danger:\t#D13737;\n\n$color-primary: $blue-500;\n$color-primary-hover: $blue-600;\n$color-secondary: $gray-500;\n$color-secondary-hover: $gray-400;\n\n// Gradients\n$gradient-pro: linear-gradient(90.52deg, #2C9FB8 0.44%, #A45CFF 113.3%);\n\n// Border radius\n$radius-sm:\t4px;\n$radius-md: 6px;\n$radius-lg: 8px;\n\n// Elevations / Box shadows\n$elevation-01: 0px 1px 2px rgba($gray-900, 0.10);\n\n// Input & button focus outline\n$outline: 3px solid $blue-50;\n\n// Link colours\n$link-color: $blue-500;\n\n// Responsive\n$max-width: 1440px;", "/*--------------------------------------------------------------------------------------------\n*\n*  Mixins\n*\n*--------------------------------------------------------------------------------------------*/\n@mixin clearfix() {\n\t&:after {\n\t\tdisplay: block;\n\t\tclear: both;\n\t\tcontent: \"\";\n\t}\n}\n\n@mixin border-box() {\n\t-webkit-box-sizing: border-box;\n\t-moz-box-sizing: border-box;\n\tbox-sizing: border-box;\n}\n\n@mixin centered() {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n}\n\n@mixin animate( $properties: 'all' ) {\n\t-webkit-transition: $properties 0.3s ease;  // Safari 3.2+, Chrome\n    -moz-transition: $properties 0.3s ease;  \t// Firefox 4-15\n    -o-transition: $properties 0.3s ease;  \t\t// Opera 10.5–12.00\n    transition: $properties 0.3s ease;  \t\t// Firefox 16+, Opera 12.50+\n}\n\n@mixin rtl() {\n\thtml[dir=\"rtl\"] & {\n\t\ttext-align: right;\n\t\t@content;\n\t}\n}\n\n@mixin wp-admin( $version: '3-8' ) {\n\t.acf-admin-#{$version} & {\n\t\t@content;\n\t}\n}", "@use \"sass:math\";\n/*--------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*--------------------------------------------------------------------------------------------*/\n\n/* Horizontal List */\n.acf-hl { padding: 0; margin: 0; list-style: none; display: block; position: relative; }\n.acf-hl > li { float: left; display: block; margin: 0; padding: 0; }\n.acf-hl > li.acf-fr { float: right; }\n\n\n/* Horizontal List: Clearfix */\n.acf-hl:before, .acf-hl:after,\n.acf-bl:before, .acf-bl:after,\n.acf-cf:before, .acf-cf:after {\n    content: \"\";\n    display: block;\n    line-height: 0;\n}\n.acf-hl:after,\n.acf-bl:after,\n.acf-cf:after {\n    clear: both;\n}\n\n\n/* Block List */\n.acf-bl { padding: 0; margin: 0; list-style: none; display: block; position: relative; }\n.acf-bl > li { display: block; margin: 0; padding: 0; float: none; }\n\n\n/* Visibility */\n.acf-hidden {\n\tdisplay: none !important;\n}\n.acf-empty {\n\tdisplay: table-cell !important;\n\t* { display: none !important; }\n}\n\n/* Float */\n.acf-fl { float: left; }\n.acf-fr { float: right; }\n.acf-fn { float: none; }\n\n\n/* Align */\n.acf-al { text-align: left; }\n.acf-ar { text-align: right; }\n.acf-ac { text-align: center; }\n\n\n/* loading */\n.acf-loading,\n.acf-spinner {\n\tdisplay: inline-block;\n\theight: 20px;\n\twidth: 20px;\n\tvertical-align: text-top;\n\tbackground: transparent url(../../images/spinner.gif) no-repeat 50% 50%;\n}\n\n\n/* spinner */\n.acf-spinner {\n\tdisplay: none;\n}\n\n.acf-spinner.is-active {\n\tdisplay: inline-block;\n}\n\n\n/* WP < 4.2 */\n.spinner.is-active {\n\tdisplay: inline-block;\n}\n\n\n/* required */\n.acf-required {\n\tcolor: #f00;\n}\n\n\n/* show on hover */\n.acf-soh .acf-soh-target {\n\t-webkit-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n\t-moz-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n\t-o-transition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n\ttransition: opacity 0.25s 0s ease-in-out, visibility 0s linear 0.25s;\n\n\tvisibility: hidden;\n\topacity: 0;\n}\n\n.acf-soh:hover .acf-soh-target {\n\t-webkit-transition-delay:0s;\n\t-moz-transition-delay:0s;\n\t-o-transition-delay:0s;\n\ttransition-delay:0s;\n\n\tvisibility: visible;\n\topacity: 1;\n}\n\n\n/* show if value */\n.show-if-value { display: none; }\n.hide-if-value { display: block; }\n\n.has-value .show-if-value { display: block; }\n.has-value .hide-if-value { display: none; }\n\n/* select2 WP animation fix */\n.select2-search-choice-close {\n\t-webkit-transition: none;\n\t-moz-transition: none;\n\t-o-transition: none;\n\ttransition: none;\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  tooltip\n*\n*---------------------------------------------------------------------------------------------*/\n\n/* tooltip */\n.acf-tooltip {\n    background: $gray-800;\n    border-radius: $radius-md;\n    color: $gray-300;\n    padding: {\n\t\ttop: 8px;\n\t\tright: 12px;\n\t\tbottom: 10px;\n\t\tleft: 12px;\n\t};\n    position: absolute;\n    @extend .p7;\n    z-index: 900000;\n\tmax-width: 280px;\n\tbox-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);\n\n\n    /* tip */\n    &:before {\n\t    border: solid;\n\t    border-color: transparent;\n\t    border-width: 6px;\n\t    content: \"\";\n\t    position: absolute;\n\t}\n\n\n    /* positions */\n    &.top {\n\t    margin-top: -8px;\n\n\t    &:before {\n\t\t\ttop: 100%;\n\t\t\tleft: 50%;\n\t\t\tmargin-left: -6px;\n\t\t\tborder-top-color: #2F353E;\n\t\t\tborder-bottom-width: 0;\n\t\t}\n    }\n\n    &.right {\n\t    margin-left: 8px;\n\n\t    &:before {\n\t\t\ttop: 50%;\n\t\t\tmargin-top: -6px;\n\t\t\tright: 100%;\n\t\t\tborder-right-color: #2F353E;\n\t\t\tborder-left-width: 0;\n\t\t}\n    }\n\n    &.bottom {\n\t   margin-top: 8px;\n\n\t   &:before {\n\t\t\tbottom: 100%;\n\t\t\tleft: 50%;\n\t\t\tmargin-left: -6px;\n\t\t\tborder-bottom-color: #2F353E;\n\t\t\tborder-top-width: 0;\n\t\t}\n    }\n\n    &.left {\n\t   margin-left: -8px;\n\n\t   &:before {\n\t\t\ttop: 50%;\n\t\t\tmargin-top: -6px;\n\t\t\tleft: 100%;\n\t\t\tborder-left-color: #2F353E;\n\t\t\tborder-right-width: 0;\n\t\t}\n    }\n\n    .acf-overlay {\n\t\tz-index: -1;\n\t}\n\n}\n\n\n/* confirm */\n.acf-tooltip.-confirm {\n\tz-index: 900001; // +1 higher than .acf-tooltip\n\n\ta {\n\t\ttext-decoration: none;\n\t\tcolor: #9ea3a8;\n\n\t\t&:hover {\n\t\t\ttext-decoration: underline;\n\t\t}\n\n\t\t&[data-event=\"confirm\"] {\n\t\t\tcolor: #F55E4F;\n\t\t}\n\t}\n}\n\n.acf-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tcursor: default;\n}\n\n.acf-tooltip-target {\n\tposition: relative;\n\tz-index: 900002; // +1 higher than .acf-tooltip\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  loading\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-loading-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tcursor: default;\n\tz-index: 99;\n\tbackground: rgba(249, 249, 249, 0.5);\n\n\ti {\n\t\t@include centered();\n\t}\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-icon\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-icon {\n\tdisplay: inline-block;\n\theight: 28px;\n\twidth: 28px;\n\tborder: transparent solid 1px;\n\tborder-radius: 100%;\n\tfont-size: 20px;\n\tline-height: 21px;\n\ttext-align: center;\n\ttext-decoration: none;\n\tvertical-align: top;\n\tbox-sizing: border-box;\n\n\t&:before {\n\t\tfont-family: dashicons;\n\t\tdisplay: inline-block;\n\t\tline-height: 1;\n\t\tfont-weight: 400;\n\t\tfont-style: normal;\n\t\tspeak: none;\n\t\ttext-decoration: inherit;\n\t\ttext-transform: none;\n\t\ttext-rendering: auto;\n\t\t-webkit-font-smoothing: antialiased;\n\t\t-moz-osx-font-smoothing: grayscale;\n\t\twidth: 1em;\n\t\theight: 1em;\n\t\tvertical-align: middle;\n\t\ttext-align: center;\n\t}\n}\n\n// Icon types.\n.acf-icon.-plus:before {\n\tcontent: \"\\f543\";\n}\n.acf-icon.-minus:before {\n\tcontent: \"\\f460\";\n}\n.acf-icon.-cancel:before {\n\tcontent: \"\\f335\";\n\tmargin: -1px 0 0 -1px;\n}\n.acf-icon.-pencil:before {\n\tcontent: \"\\f464\";\n}\n.acf-icon.-location:before {\n\tcontent: \"\\f230\";\n}\n.acf-icon.-up:before {\n\tcontent: \"\\f343\";\n\n\t// Fix position relative to font-size.\n\tmargin-top: math.div(-2em, 20);\n}\n.acf-icon.-down:before {\n\tcontent: \"\\f347\";\n\n\t// Fix position relative to font-size.\n\tmargin-top: math.div(2em, 20);\n}\n.acf-icon.-left:before {\n\tcontent: \"\\f341\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n.acf-icon.-right:before {\n\tcontent: \"\\f345\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(2em, 20);\n}\n.acf-icon.-sync:before {\n\tcontent: \"\\f463\";\n}\n.acf-icon.-globe:before {\n\tcontent: \"\\f319\";\n\n\t// Fix position relative to font-size.\n\tmargin-top: math.div(2em, 20);\n\tmargin-left: math.div(2em, 20);\n}\n.acf-icon.-picture:before {\n\tcontent: \"\\f128\";\n}\n.acf-icon.-check:before {\n\tcontent: \"\\f147\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n.acf-icon.-dot-3:before {\n\tcontent: \"\\f533\";\n\n\t// Fix position relative to font-size.\n\tmargin-top: math.div(-2em, 20);\n}\n.acf-icon.-arrow-combo:before {\n\tcontent: \"\\f156\";\n}\n.acf-icon.-arrow-up:before {\n\tcontent: \"\\f142\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n.acf-icon.-arrow-down:before {\n\tcontent: \"\\f140\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n.acf-icon.-search:before {\n\tcontent: \"\\f179\";\n}\n.acf-icon.-link-ext:before {\n\tcontent: \"\\f504\";\n}\n\n// Duplicate is a custom icon made from pseudo elements.\n.acf-icon.-duplicate {\n\tposition: relative;\n\t&:before,\n\t&:after {\n\t\tcontent: \"\";\n\t\tdisplay: block;\n\t\tbox-sizing: border-box;\n\t\twidth: 46%;\n\t\theight: 46%;\n\t\tposition: absolute;\n\t\ttop: 33%;\n\t\tleft: 23%;\n\t}\n\t&:before {\n\t\tmargin: -1px 0 0 1px;\n\t\tbox-shadow: 2px -2px 0px 0px currentColor;\n\t}\n\t&:after {\n\t\tborder: solid 2px currentColor;\n\t}\n}\n\n.acf-icon.-trash {\n\tposition: relative;\n\t&:before,\n\t&:after {\n\t\tcontent: \"\";\n\t\tdisplay: block;\n\t\tbox-sizing: border-box;\n\t\twidth: 46%;\n\t\theight: 46%;\n\t\tposition: absolute;\n\t\ttop: 33%;\n\t\tleft: 23%;\n\t}\n\t&:before {\n\t\tmargin: -1px 0 0 1px;\n\t\tbox-shadow: 2px -2px 0px 0px currentColor;\n\t}\n\t&:after {\n\t\tborder: solid 2px currentColor;\n\t}\n}\n\n// Collapse icon toggles automatically.\n.acf-icon.-collapse:before {\n\tcontent: \"\\f142\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n.-collapsed .acf-icon.-collapse:before {\n\tcontent: \"\\f140\";\n\n\t// Fix position relative to font-size.\n\tmargin-left: math.div(-2em, 20);\n}\n\n// <span> displays with grey border.\nspan.acf-icon {\n\tcolor: #555d66;\n\tborder-color: #b5bcc2;\n\tbackground-color: #fff;\n}\n\n// <a> also displays with grey border.\na.acf-icon {\n\tcolor: #555d66;\n\tborder-color: #b5bcc2;\n\tbackground-color: #fff;\n\tposition: relative;\n\ttransition: none;\n\tcursor: pointer;\n\n\t// State \"hover\".\n\t&:hover {\n\t\tbackground: #f3f5f6;\n\t\tborder-color: #0071a1;\n\t\tcolor: #0071a1;\n\t}\n\t&.-minus:hover,\n\t&.-cancel:hover {\n\t\tbackground: #f7efef;\n\t\tborder-color: #a10000;\n\t\tcolor: #dc3232;\n\t}\n\n\t// Fix: Remove WP outline box-shadow.\n\t&:active,\n\t&:focus {\n\t\toutline: none;\n\t\tbox-shadow: none;\n\t}\n}\n\n// Style \"clear\".\n.acf-icon.-clear {\n\tborder-color: transparent;\n\tbackground: transparent;\n\tcolor: #444;\n}\n\n// Style \"light\".\n.acf-icon.light {\n\tborder-color: transparent;\n\tbackground: #F5F5F5;\n\tcolor: #23282d;\n}\n\n// Style \"dark\".\n.acf-icon.dark {\n\tborder-color: transparent !important;\n\tbackground: #23282D;\n\tcolor: #eee;\n}\na.acf-icon.dark {\n\t&:hover {\n\t\tbackground: #191E23;\n\t\tcolor: #00b9eb;\n\t}\n\t&.-minus:hover,\n\t&.-cancel:hover {\n\t\tcolor: #D54E21;\n\t}\n}\n\n// Style \"grey\".\n.acf-icon.grey {\n\tborder-color: transparent !important;\n\tbackground: #b4b9be;\n\tcolor: #fff !important;\n\n\t&:hover {\n\t\tbackground: #00A0D2;\n\t\tcolor: #fff;\n\t}\n\t&.-minus:hover,\n\t&.-cancel:hover {\n\t\tbackground: #32373C;\n\t}\n}\n\n// Size \"small\".\n.acf-icon.small,\n.acf-icon.-small {\n\twidth: 20px;\n\theight: 20px;\n\tline-height: 14px;\n\tfont-size: 14px;\n\n\t// Apply minor transforms to reduce clarirty of \"duplicate\" icon.\n\t// Helps to unify rendering with dashicons.\n\t&.-duplicate {\n\t\t&:before, &:after {\n\t\t\t//transform: rotate(0.1deg) scale(0.9) translate(-5%, 5%);\n\t\t\topacity: 0.8;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-box\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-box {\n    background: #FFFFFF;\n    border: 1px solid $wp-card-border;\n    position: relative;\n    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);\n\n    /* title */\n    .title {\n\t\tborder-bottom: 1px solid $wp-card-border;\n\t    margin: 0;\n\t    padding: 15px;\n\n\t\th3 {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tfont-size: 14px;\n\t\t    line-height: 1em;\n\t\t    margin: 0;\n\t\t    padding: 0;\n\t\t}\n\t}\n\n\n\t.inner {\n\t    padding: 15px;\n\t}\n\n\th2 {\n\t\tcolor: #333333;\n\t    font-size: 26px;\n\t    line-height: 1.25em;\n\t    margin: 0.25em 0 0.75em;\n\t    padding: 0;\n\t}\n\n\th3 {\n\t\tmargin: 1.5em 0 0;\n\t}\n\n\tp {\n\t\tmargin-top: 0.5em;\n\t}\n\n\ta {\n\t\ttext-decoration: none;\n\t}\n\n\ti {\n\t\t&.dashicons-external {\n\t\t\tmargin-top: -1px;\n\t\t}\n\t}\n\n\t/* footer */\n\t.footer {\n\t    border-top: 1px solid $wp-card-border;\n\t    padding: 12px;\n\t    font-size: 13px;\n\t    line-height: 1.5;\n\n\t    p {\n\t\t    margin: 0;\n\t    }\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin('3-8') {\n\t\tborder-color: $wp38-card-border;\n\t\t.title,\n\t\t.footer {\n\t\t\tborder-color: $wp38-card-border;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-notice\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-notice {\n\tposition: relative;\n\tdisplay: block;\n\tcolor: #fff;\n\tmargin: 5px 0 15px;\n\tpadding: 3px 12px;\n\tbackground: $acf_notice;\n\tborder-left: darken($acf_notice, 10%) solid 3px;\n\n\tp {\n\t\tfont-size: 13px;\n\t\tline-height: 1.5;\n\t\tmargin: 0.5em 0;\n\t\ttext-shadow: none;\n\t\tcolor: inherit;\n\t}\n\n\t.acf-notice-dismiss {\n\t\tposition: absolute;\n\t\ttop: 9px;\n\t\tright: 12px;\n\t\tbackground: transparent !important;\n\t\tcolor: inherit !important;\n\t\tborder-color: #fff !important;\n\t\topacity: 0.75;\n\t\t&:hover {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t// dismiss\n\t&.-dismiss {\n\t\tpadding-right: 40px;\n\t}\n\n\t// error\n\t&.-error {\n\t\tbackground: $acf_error;\n\t\tborder-color: darken($acf_error, 10%);\n\t}\n\n\t// success\n\t&.-success {\n\t\tbackground: $acf_success;\n\t\tborder-color: darken($acf_success, 10%);\n\t}\n\n\t// warning\n\t&.-warning {\n\t\tbackground: $acf_warning;\n\t\tborder-color: darken($acf_warning, 10%);\n\t}\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-table\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-table {\n\tborder: $wp-card-border solid 1px;\n\tbackground: #fff;\n\tborder-spacing: 0;\n\tborder-radius: 0;\n\ttable-layout: auto;\n\tpadding: 0;\n\tmargin: 0;\n    width: 100%;\n    clear: both;\n    box-sizing: content-box;\n\n    /* defaults */\n    > tbody > tr,\n\t> thead > tr {\n\n    \t> th, > td {\n\t\t\tpadding: 8px;\n\t\t\tvertical-align: top;\n\t\t\tbackground: #fff;\n\t\t\ttext-align: left;\n\t\t    border-style: solid;\n\t\t    font-weight: normal;\n\t\t}\n\n\t\t> th {\n\t\t\tposition: relative;\n\t\t\tcolor: #333333;\n\t\t}\n\n    }\n\n\n    /* thead */\n    > thead {\n\n\t    > tr {\n\n\t\t\t> th {\n\t\t\t    border-color: $wp-card-border-1;\n\t\t\t\tborder-width: 0 0 1px 1px;\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tborder-left-width: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t    }\n\n    }\n\n\n    /* tbody */\n    > tbody {\n\n\t    > tr {\n\t\t\tz-index: 1;\n\n\t\t\t> td {\n\t\t\t\tborder-color: $wp-card-border-2;\n\t\t\t\tborder-width: 1px 0 0 1px;\n\n\t\t\t\t&:first-child {\n\t\t\t\t\tborder-left-width: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t&:first-child > td {\n\t\t\t\tborder-top-width: 0;\n\t\t\t}\n\t\t}\n\n    }\n\n\n    /* -clear */\n    &.-clear {\n\t    border: 0 none;\n\n\t    > tbody > tr,\n\t    > thead > tr {\n\n\t\t    > td, >th {\n\t\t\t    border: 0 none;\n\t\t\t\tpadding: 4px;\n\t\t    }\n\t    }\n    }\n}\n\n\n/* remove tr */\n.acf-remove-element {\n\t-webkit-transition: all 0.25s ease-out;\n\t-moz-transition: all 0.25s ease-out;\n\t-o-transition: all 0.25s ease-out;\n\ttransition: all 0.25s ease-out;\n\n\ttransform: translate(50px, 0);\n\topacity: 0;\n}\n\n\n/* fade-up */\n.acf-fade-up {\n\t-webkit-transition: all 0.25s ease-out;\n\t-moz-transition: all 0.25s ease-out;\n\t-o-transition: all 0.25s ease-out;\n\ttransition: all 0.25s ease-out;\n\n\ttransform: translate(0, -10px);\n\topacity: 0;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Fake table\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-thead,\n.acf-tbody,\n.acf-tfoot {\n\twidth: 100%;\n\tpadding: 0;\n\tmargin: 0;\n\n\t> li {\n\t\tbox-sizing: border-box;\n\t\tpadding: {\n\t\t\ttop: 14px;\n\t\t};\n\t\tfont-size: 12px;\n\t\tline-height: 14px;\n\t}\n}\n\n.acf-thead {\n\tborder-bottom: $wp-card-border solid 1px;\n\tcolor: #23282d;\n\n\t> li {\n\t\tfont-size: 14px;\n\t\tline-height: 1.4;\n\t\tfont-weight: bold;\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin('3-8') {\n\t\tborder-color: $wp38-card-border-1;\n\t}\n}\n\n.acf-tfoot {\n\tbackground: #f5f5f5;\n\tborder-top: $wp-card-border-1 solid 1px;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSettings\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-settings-wrap {\n\n\t#poststuff {\n\t\tpadding-top: 15px;\n\t}\n\n\t.acf-box {\n\t\tmargin: 20px 0;\n\t}\n\n\ttable {\n\t\tmargin: 0;\n\n\t\t.button {\n\t\t\tvertical-align: middle;\n\t\t}\n\t}\n}\n\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-popup\n*\n*--------------------------------------------------------------------------------------------*/\n\n#acf-popup {\n\tposition: fixed;\n\tz-index: 900000;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\ttext-align: center;\n\n\t// bg\n\t.bg {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tz-index: 0;\n\t\tbackground: rgba(0,0,0,0.25);\n\t}\n\n\t&:before {\n\t\tcontent: '';\n\t\tdisplay: inline-block;\n\t\theight: 100%;\n\t\tvertical-align: middle;\n\t}\n\n\t// box\n\t.acf-popup-box {\n\t\tdisplay: inline-block;\n\t\tvertical-align: middle;\n\t\tz-index: 1;\n\t\tmin-width: 300px;\n\t\tmin-height: 160px;\n\t\tborder-color: #aaaaaa;\n\t\tbox-shadow: 0 5px 30px -5px rgba(0, 0, 0, 0.25);\n\t\ttext-align: left;\n\t\t@include rtl();\n\n\t\t// title\n\t\t.title {\n\t\t\tmin-height: 15px;\n\t\t\tline-height: 15px;\n\n\t\t\t// icon\n\t\t\t.acf-icon {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 10px;\n\t\t\t\tright: 10px;\n\n\t\t\t\t// rtl\n\t\t\t\thtml[dir=\"rtl\"] & {\n\t\t\t\t\tright: auto;\n\t\t\t\t\tleft: 10px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.inner {\n\t\t\tmin-height: 50px;\n\n\t\t\t// use margin instead of padding to allow inner elements marin to overlap and avoid large hitespace at top/bottom\n\t\t\tpadding: 0;\n\t\t\tmargin: 15px;\n\t\t}\n\n\t\t// loading\n\t\t.loading {\n\t\t\tposition: absolute;\n\t\t\ttop: 45px;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tz-index: 2;\n\t\t\tbackground: rgba(0,0,0,0.1);\n\t\t\tdisplay: none;\n\n\t\t\ti {\n\t\t\t\t@include centered();\n\t\t\t}\n\t\t}\n\n\t}\n}\n\n\n// acf-submit\n.acf-submit {\n\tmargin-bottom: 0;\n\tline-height: 28px; // .button height\n\n\t// message\n\tspan {\n\t\tfloat: right;\n\t\tcolor: #999;\n\n\t\t&.-error {\n\t\t\tcolor: #dd4232;\n\t\t}\n\t}\n\n\t// button (allow margin between loading)\n\t.button {\n\t\tmargin-right: 5px;\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tupgrade notice\n*\n*--------------------------------------------------------------------------------------------*/\n\n#acf-upgrade-notice {\n\tposition: relative;\n\tbackground: #fff;\n\tpadding: 20px;\n\t@include clearfix();\n\n\t.col-content {\n\t\tfloat: left;\n\t\twidth: 55%;\n\t\tpadding-left: 90px;\n\t}\n\n\t.notice-container {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: flex-start;\n\t\talign-content: flex-start;\n\t}\n\n\t.col-actions {\n\t\tfloat: right;\n\t\ttext-align: center;\n\t}\n\n\timg {\n\t\tfloat: left;\n\t\twidth: 64px;\n\t\theight: 64px;\n\t\tmargin: 0 0 0 -90px;\n\t}\n\n\th2 {\n\t\tdisplay: inline-block;\n\t\tfont-size: 16px;\n\t\tmargin: 2px 0 6.5px;\n\t}\n\n\tp {\n\t\tpadding: 0;\n\t\tmargin: 0;\n\t}\n\n\t.button:before {\n\t\tmargin-top: 11px;\n\t}\n\n\t// mobile\n\t@media screen and (max-width: $sm) {\n\n\t\t.col-content,\n\t\t.col-actions {\n\t\t\tfloat: none;\n\t\t\tpadding-left: 90px;\n\t\t\twidth: auto;\n\t\t\ttext-align: left;\n\t\t}\n\t}\n}\n\n// Hide icons for upgade notice.\n#acf-upgrade-notice:has(.notice-container)::before, #acf-upgrade-notice:has(.notice-container)::after {\n\tdisplay: none;\n}\n\n// Match padding of other non-icon notices.\n#acf-upgrade-notice:has(.notice-container) {\n\tpadding-left: 20px !important;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tWelcome\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-wrap {\n\n\th1 {\n\t\tmargin-top: 0;\n\t\tpadding-top: 20px;\n\t}\n\n\t.about-text {\n\t\tmargin-top: 0.5em;\n\t\tmin-height: 50px;\n\t}\n\n\t.about-headline-callout {\n\t    font-size: 2.4em;\n\t    font-weight: 300;\n\t    line-height: 1.3;\n\t    margin: 1.1em 0 0.2em;\n\t    text-align: center;\n\t}\n\n\t.feature-section {\n\t    padding: 40px 0;\n\n\t    h2 {\n\t\t    margin-top: 20px;\n\t    }\n\t}\n\n\t.changelog {\n\t\tlist-style: disc;\n\t\tpadding-left: 15px;\n\n\t\tli {\n\t\t\tmargin: 0 0 0.75em;\n\t\t}\n\t}\n\n\t.acf-three-col {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: space-between;\n\n\t\t> div {\n\t\t\tflex: 1;\n\t\t\talign-self: flex-start;\n\t\t\tmin-width: 31%;\n\t\t\tmax-width: 31%;\n\n\t\t\t@media screen and (max-width: $md) {\n\t\t\t\tmin-width: 48%;\n\t\t\t}\n\n\t\t\t@media screen and (max-width: $sm) {\n\t\t\t\tmin-width: 100%;\n\t\t\t}\n\t\t}\n\n\t\th3 .badge {\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: top;\n\t\t\tborder-radius: 5px;\n\t\t\tbackground: #fc9700;\n\t\t\tcolor: #fff;\n\t\t\tfont-weight: normal;\n\t\t\tfont-size: 12px;\n\t\t\tpadding: 2px 5px;\n\t\t}\n\n\t\timg + h3 {\n\t\t\tmargin-top: 0.5em;\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tacf-hl cols\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-hl[data-cols] {\n\tmargin-left: -10px;\n\tmargin-right: -10px;\n\n\t> li {\n\t\tpadding: 0 6px 0 10px;\n\n\t\t-webkit-box-sizing: border-box;\n\t\t-moz-box-sizing: border-box;\n\t\tbox-sizing: border-box;\n\t}\n\n}\n\n\n/* sizes */\n.acf-hl[data-cols=\"2\"] > li { width: 50%; }\n.acf-hl[data-cols=\"3\"] > li { width: 33.333%; }\n.acf-hl[data-cols=\"4\"] > li { width: 25%; }\n\n\n/* mobile */\n@media screen and (max-width: $sm) {\n\n\t.acf-hl[data-cols] {\n\t\tflex-wrap: wrap;\n\t\tjustify-content: flex-start;\n\t\talign-content: flex-start;\n\t\talign-items: flex-start;\n\t\tmargin-left: 0;\n\t\tmargin-right: 0;\n\t\tmargin-top: -10px;\n\n\t\t> li {\n\t\t\tflex: 1 1 100%;\n\t\t\twidth: 100% !important;\n\t\t\tpadding: 10px 0 0;\n\t\t}\n\n\t}\n\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tmisc\n*\n*--------------------------------------------------------------------------------------------*/\n\n.acf-actions {\n\ttext-align: right;\n\tz-index: 1;\n\n\t/* hover */\n\t&.-hover {\n\t\tposition: absolute;\n\t\tdisplay: none;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tpadding: 5px;\n\t}\n\n\n\t/* rtl */\n\thtml[dir=\"rtl\"] & {\n\t\t&.-hover {\n\t\t\tright: auto;\n\t\t\tleft: 0;\n\t\t}\n\t}\n}\n\n\n/* ul compatibility */\nul.acf-actions {\n\tli { float: right; margin-left: 4px; }\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRTL\n*\n*--------------------------------------------------------------------------------------------*/\n\nhtml[dir=\"rtl\"] .acf-fl { float: right; }\nhtml[dir=\"rtl\"] .acf-fr { float: left; }\n\nhtml[dir=\"rtl\"] .acf-hl > li {\n\tfloat: right;\n}\n\nhtml[dir=\"rtl\"] .acf-hl > li.acf-fr {\n    float: left;\n}\n\nhtml[dir=\"rtl\"] .acf-icon.logo {\n\tleft: 0;\n\tright: auto;\n}\n\n\nhtml[dir=\"rtl\"] .acf-table thead th {\n\ttext-align: right;\n\tborder-right-width: 1px;\n\tborder-left-width: 0px;\n}\n\nhtml[dir=\"rtl\"] .acf-table > tbody > tr > td {\n\ttext-align: right;\n\tborder-right-width: 1px;\n\tborder-left-width: 0px;\n}\n\nhtml[dir=\"rtl\"] .acf-table > thead > tr > th:first-child,\nhtml[dir=\"rtl\"] .acf-table > tbody > tr > td:first-child {\n\tborder-right-width: 0;\n}\n\nhtml[dir=\"rtl\"] .acf-table > tbody > tr > td.order + td {\n\tborder-right-color: #e1e1e1;\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  acf-postbox-columns\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-postbox-columns {\n\t@include clearfix();\n\tposition: relative;\n\tmargin-top: -11px;\n\tmargin-bottom: -12px;\n\tmargin-left: -12px;\n\tmargin-right: (280px - 12px);\n\n\t.acf-postbox-main,\n\t.acf-postbox-side {\n\t\t@include border-box();\n\t\tpadding: 0 12px 12px;\n\t}\n\n\t.acf-postbox-main {\n\t\tfloat: left;\n\t\twidth: 100%;\n\t}\n\n\t.acf-postbox-side {\n\t\tfloat: right;\n\t\twidth: 280px;\n\t\tmargin-right: -280px;\n\n\t\t&:before {\n\t\t\tcontent: \"\";\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\twidth: 1px;\n\t\t\theight: 100%;\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbackground: $wp-card-border-1;\n\t\t}\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin('3-8') {\n\t\t.acf-postbox-side:before {\n\t\t\tbackground: $wp38-card-border-1;\n\t\t}\n\t}\n}\n\n/* mobile */\n@media only screen and (max-width: 850px) {\n\n\t.acf-postbox-columns {\n\t\tmargin: 0;\n\n\t\t.acf-postbox-main,\n\t\t.acf-postbox-side {\n\t\t\tfloat: none;\n\t\t\twidth: auto;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\t\t}\n\n\t\t.acf-postbox-side {\n\t\t\tmargin-top: 1em;\n\n\t\t\t&:before {\n\t\t\t\tdisplay: none;\n\t\t\t}\n\t\t}\n\t}\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  acf-panel\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-panel {\n\tmargin-top: -1px;\n\tborder-top: 1px solid $wp-card-border-1;\n\tborder-bottom: 1px solid $wp-card-border-1;\n\n\t.acf-panel-title {\n\t\tmargin: 0;\n\t\tpadding: 12px;\n\t\tfont-weight: bold;\n\t\tcursor: pointer;\n\t\tfont-size: inherit;\n\n\t\ti {\n\t\t\tfloat: right;\n\t\t}\n\t}\n\n\t.acf-panel-inside {\n\t\tmargin: 0;\n\t\tpadding: 0 12px 12px;\n\t\tdisplay: none;\n\t}\n\n\t/* open */\n\t&.-open {\n\n\t\t.acf-panel-inside {\n\t\t\tdisplay: block;\n\t\t}\n\n\t}\n\n\n\t/* inside postbox */\n\t.postbox & {\n\t\tmargin-left: -12px;\n\t\tmargin-right: -12px;\n\t}\n\n\n\t/* fields */\n\t.acf-field {\n\t\tmargin: 20px 0 0;\n\n\t\t.acf-label label {\n\t\t\tcolor: #555d66;\n\t\t\tfont-weight: normal;\n\t\t}\n\n\t\t&:first-child {\n\t\t\tmargin-top: 0;\n\t\t}\n\t}\n\n\t// WP Admin 3.8\n\t@include wp-admin('3-8') {\n\t\tborder-color: $wp38-card-border-1;\n\t}\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Tools\n*\n*---------------------------------------------------------------------------------------------*/\n\n#acf-admin-tools {\n\n\t.notice {\n\t\tmargin-top: 10px;\n\t}\n\n\t.acf-meta-box-wrap {\n\n\t\t.inside {\n\t\t\tborder-top: none;\n\t\t}\n\n\t\t/* acf-fields */\n\t\t.acf-fields {\n\t\t\tmargin: {\n\t\t\t\tbottom: 24px;\n\t\t\t};\n\t\t\tborder: none;\n\t\t\tbackground: #fff;\n\t\t\tborder-radius: 0;\n\n\t\t\t.acf-field {\n\t\t\t\tpadding: 0;\n\t\t\t}\n\n\t\t\t.acf-label {\n\t\t\t\t@extend .p2;\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 16px;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-input {\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 16px;\n\t\t\t\t\tright: 16px;\n\t\t\t\t\tbottom: 16px;\n\t\t\t\t\tleft: 16px;\n\t\t\t\t};\n\t\t\t\tborder: {\n\t\t\t\t\twidth: 1px;\n\t\t\t\t\tstyle: solid;\n\t\t\t\t\tcolor: $gray-300;\n\t\t\t\t};\n\t\t\t\tborder-radius: $radius-md;\n\t\t\t}\n\n\t\t}\n\t}\n\n}\n\n.acf-meta-box-wrap {\n\n\t.postbox {\n\t@include border-box();\n\n\t\t.inside {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\n\t\t.hndle {\n\t\t\tfont-size: 14px;\n\t\t\tpadding: 8px 12px;\n\t\t\tmargin: 0;\n\t\t\tline-height: 1.4;\n\n\t\t\t// Prevent .acf-panel border overlapping.\n\t\t\tposition: relative;\n\t\t\tz-index: 1;\n\t\t\tcursor: default;\n\t\t}\n\n\t\t.handlediv,\n\t\t.handle-order-higher,\n\t\t.handle-order-lower {\n\t\t\tdisplay: none;\n\t\t}\n\n\t}\n\n}\n\n/* grid */\n.acf-meta-box-wrap.-grid {\n\tmargin-left: 8px;\n\tmargin-right: 8px;\n\n\t.postbox {\n\t\tfloat: left;\n\t\tclear: left;\n\t\twidth: 50%;\n\t\tmargin: 0 0 16px;\n\n\t\t&:nth-child(odd) {\n\t\t\tmargin-left: -8px;\n\t\t}\n\n\t\t&:nth-child(even) {\n\t\t\tfloat: right;\n\t\t\tclear: right;\n\t\t\tmargin-right: -8px;\n\t\t}\n\t}\n}\n\n\n/* mobile */\n@media only screen and (max-width: 850px) {\n\n\t.acf-meta-box-wrap.-grid {\n\t\tmargin-left: 0;\n\t\tmargin-right: 0;\n\n\t\t.postbox {\n\t\t\tmargin-left: 0 !important;\n\t\t\tmargin-right: 0 !important;\n\t\t\twidth: 100%;\n\t\t}\n\t}\n}\n\n\n/* export tool */\n#acf-admin-tool-export {\n\n\tp {\n\t\tmax-width: 800px;\n\t}\n\n\tul {\n\t\tcolumn-width: 200px;\n\t}\n\n\t.acf-postbox-side .button {\n\t\tmargin: 0;\n\t\twidth: 100%;\n\t}\n\n\ttextarea {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t\tmin-height: 500px;\n\t\tbackground: $gray-50;\n\t\tborder-color: $gray-300;\n\t\tbox-shadow: none;\n\t\tpadding: 7px;\n\t\tborder-radius: $radius-md;\n\t}\n\n\t/* panel: selection */\n\t.acf-panel-selection {\n\t\t.acf-label {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}\n\n// CSS only Tooltip.\n.acf-css-tooltip {\n\tposition: relative;\n\t&:before {\n\t\tcontent: attr(aria-label);\n\t\tdisplay: none;\n\t\tposition: absolute;\n\t\tz-index: 999;\n\n\t\tbottom: 100%;\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, -8px);\n\n\t\tbackground: #191e23;\n\t\tborder-radius: 2px;\n\t\tpadding: 5px 10px;\n\n\t\tcolor: #fff;\n\t    font-size: 12px;\n\t    line-height: 1.4em;\n\t    white-space: pre;\n\t}\n    &:after {\n\t    content: \"\";\n\t    display: none;\n\t    position: absolute;\n\t    z-index: 998;\n\n\t    bottom: 100%;\n\t\tleft: 50%;\n\t\ttransform: translate(-50%, 4px);\n\n\t    border: solid 6px transparent;\n\t    border-top-color: #191e23;\n\t}\n\n\t&:hover, &:focus {\n\t\t&:before, &:after {\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\n\n// Diff modal.\n.acf-diff {\n\n\t.acf-diff-title {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 40px;\n\t\tpadding: 14px 16px;\n\t\tbackground: #f3f3f3;\n\t\tborder-bottom: #dddddd solid 1px;\n\n\t\tstrong {\n\t\t\tfont-size: 14px;\n\t\t\tdisplay: block;\n\t\t}\n\n\t\t.acf-diff-title-left,\n\t\t.acf-diff-title-right {\n\t\t\twidth: 50%;\n\t\t\tfloat: left;\n\t\t}\n\t}\n\n\t.acf-diff-content {\n\t\tposition: absolute;\n\t\ttop: 70px;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\toverflow: auto;\n\t}\n\n\ttable.diff {\n\t\tborder-spacing: 0;\n\n\t\tcol.diffsplit.middle {\n\t\t\twidth: 0;\n\t\t}\n\n\t\ttd, th {\n\t\t\tpadding-top: 0.25em;\n\t\t\tpadding-bottom: 0.25em;\n\t\t}\n\n\t\t// Fix WP 5.7 conflicting CSS.\n\t\ttr td:nth-child(2) {\n\t\t\twidth: auto;\n\t\t}\n\n\t\ttd:nth-child(3) {\n\t\t\tborder-left: #dddddd solid 1px;\n\t\t}\n\t}\n\n\t// Mobile\n\t@media screen and (max-width: 600px) {\n\t\t.acf-diff-title {\n\t\t\theight: 70px;\n\t\t}\n\t\t.acf-diff-content {\n\t\t\ttop: 100px;\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Modal\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-modal {\n\tposition: fixed;\n\ttop: 30px;\n\tleft: 30px;\n\tright: 30px;\n\tbottom: 30px;\n\tz-index: 160000;\n\tbox-shadow: 0 5px 15px rgba(0, 0, 0, 0.7);\n\tbackground: #fcfcfc;\n\n\t.acf-modal-title,\n\t.acf-modal-content,\n\t.acf-modal-toolbar {\n\t\tbox-sizing: border-box;\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\tright: 0;\n\t}\n\n\t.acf-modal-title {\n\t\theight: 50px;\n\t\ttop: 0;\n\t\tborder-bottom: 1px solid #ddd;\n\n\t\th2 {\n\t\t\tmargin: 0;\n\t\t\tpadding: 0 16px;\n\t\t\tline-height: 50px;\n\t\t}\n\t\t.acf-modal-close {\n\t\t\tposition: absolute;\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\theight: 50px;\n\t\t\twidth: 50px;\n\t\t\tborder: none;\n\t\t\tborder-left: 1px solid #ddd;\n\t\t\tbackground: transparent;\n\t\t\tcursor: pointer;\n\t\t\tcolor: #666;\n\t\t\t&:hover {\n\t\t\t\tcolor: #00a0d2;\n\t\t\t}\n\t\t}\n\t}\n\n\t.acf-modal-content {\n\t\ttop: 50px;\n\t\tbottom: 60px;\n\t\tbackground: #fff;\n\t\toverflow: auto;\n\t\tpadding: 16px;\n\t}\n\n\t.acf-modal-feedback {\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\tmargin: -10px 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\ttext-align: center;\n\t\topacity: 0.75;\n\n\t\t&.error {\n\t\t\topacity: 1;\n\t\t\tcolor: #b52727;\n\t\t}\n\t}\n\n\t.acf-modal-toolbar {\n\t\theight: 60px;\n\t\tbottom: 0;\n\t\tpadding: 15px 16px;\n\t\tborder-top: 1px solid #ddd;\n\n\t\t.button {\n\t\t\tfloat: right;\n\t\t}\n\t}\n\n\t// Responsive.\n\t@media only screen and (max-width: 640px) {\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t}\n\n}\n.acf-modal-backdrop {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: #000;\n\topacity: 0.7;\n\tz-index: 159900;\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Retina\n*\n*---------------------------------------------------------------------------------------------*/\n\n@media\nonly screen and (-webkit-min-device-pixel-ratio: 2),\nonly screen and (   min--moz-device-pixel-ratio: 2),\nonly screen and (     -o-min-device-pixel-ratio: 2/1),\nonly screen and (        min-device-pixel-ratio: 2),\nonly screen and (                min-resolution: 192dpi),\nonly screen and (                min-resolution: 2dppx) {\n\n\t.acf-loading,\n\t.acf-spinner {\n\t\tbackground-image: url(../../images/<EMAIL>);\n\t\tbackground-size: 20px 20px;\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Wrap\n*\n*--------------------------------------------------------------------------------------------*/\n\n.post-type-acf-field-group {\n\n\t.wrap {\n\t\tmargin: {\n\t\t\ttop: 48px;\n\t\t\tright: 32px;\n\t\t\tbottom: 0;\n\t\t\tleft: 12px;\n\t\t};\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t\tleft: 8px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t&.rtl .wrap {\n\t\tmargin: {\n\t\t\tright: 12px;\n\t\t\tleft: 32px;\n\t\t};\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t\tleft: 8px;\n\t\t\t};\n\t\t}\n\t}\n\n\t#wpcontent {\n\t\t@media screen and (max-width: 768px) {\n\t\t\tpadding: {\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t}\n\t}\n\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Postbox & ACF Postbox\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group .postbox,\n.post-type-acf-field-group .acf-box {\n\tborder: none;\n\tborder-radius: $radius-lg;\n\tbox-shadow: $elevation-01;\n\n\t.inside {\n\t\tpadding: {\n\t\t\ttop: 24px;\n\t\t\tright: 24px;\n\t\t\tbottom: 24px;\n\t\t\tleft: 24px;\n\t\t};\n\t}\n\n\t.acf-postbox-inner {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 24px;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t}\n\n\t.inner,\n\t.inside {\n\t\tmargin: {\n\t\t\ttop: 0 !important;\n\t\t\tright: 0 !important;\n\t\t\tbottom: 0 !important;\n\t\t\tleft: 0 !important;\n\t\t};\n\t\tborder-top: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t};\n\t}\n\n\t.postbox-header,\n\t.title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbox-sizing: border-box;\n\t\tmin-height: 64px;\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 24px;\n\t\t\tbottom: 0;\n\t\t\tleft: 24px;\n\t\t};\n\t\tborder-bottom: {\n\t\t\twidth: 0;\n\t\t\tstyle: none;\n\t\t};\n\n\t\th2,\n\t\th3 {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\t@extend .acf-h3;\n\t\t\tcolor: $gray-700;\n\t\t}\n\n\t}\n\n\t.hndle {\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 24px;\n\t\t\tbottom: 0;\n\t\t\tleft: 24px;\n\t\t};\n\t}\n\n}\n\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Custom ACF postbox header\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-postbox-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tbox-sizing: border-box;\n\tmin-height: 64px;\n\tmargin: {\n\t\ttop: -24px;\n\t\tright: -24px;\n\t\tbottom: 0;\n\t\tleft: -24px;\n\t};\n\tpadding: {\n\t\ttop: 0;\n\t\tright: 24px;\n\t\tbottom: 0;\n\t\tleft: 24px;\n\t};\n\tborder-bottom: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $gray-200;\n\t};\n\n\th2.acf-postbox-title {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 24px;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\t@extend .acf-h3;\n\t\tcolor: $gray-700;\n\t}\n\n\t.rtl & h2.acf-postbox-title {\n\t\tpadding: {\n\t\t\tright: 0;\n\t\t\tleft: 24px;\n\t\t};\n\t}\n\n\t.acf-icon {\n\t\tbackground-color: $gray-400;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Screen options button & screen meta container\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t#screen-meta-links {\n\t\tmargin: {\n\t\t\tright: 32px;\n\t\t};\n\n\t\t.show-settings {\n\t\t\tborder-color: $gray-300;\n\t\t}\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tmargin: {\n\t\t\t\tright: 16px;\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t&.rtl #screen-meta-links {\n\t\tmargin: {\n\t\t\tright: 0;\n\t\t\tleft: 32px;\n\t\t};\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tmargin: {\n\t\t\t\tright: 0;\n\t\t\t\tleft: 16px;\n\t\t\t};\n\t\t}\n\t}\n\n\t#screen-meta {\n\t\tborder-color: $gray-300;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Postbox headings\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t#poststuff {\n\n\t\t.postbox-header {\n\n\t\t\th2,\n\t\t\th3 {\n\t\t\t\tjustify-content: flex-start;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t\t@extend .acf-h3;\n\t\t\t\tcolor: $gray-700 !important;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Postbox drag state\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t&.is-dragging-metaboxes .metabox-holder .postbox-container .meta-box-sortables {\n\t\tbox-sizing: border-box;\n\t\tpadding: 2px;\n\t\toutline: none;\n\t\tbackground-image: repeating-linear-gradient(0deg, $gray-500, $gray-500 5px, transparent 5px, transparent 10px, $gray-500 10px), repeating-linear-gradient(90deg, $gray-500, $gray-500 5px, transparent 5px, transparent 10px, $gray-500 10px), repeating-linear-gradient(180deg, $gray-500, $gray-500 5px, transparent 5px, transparent 10px, $gray-500 10px), repeating-linear-gradient(270deg, $gray-500, $gray-500 5px, transparent 5px, transparent 10px, $gray-500 10px);\n\t\tbackground-size: 1.5px 100%, 100% 1.5px, 1.5px 100% , 100% 1.5px;\n\t\tbackground-position: 0 0, 0 0, 100% 0, 0 100%;\n\t\tbackground-repeat: no-repeat;\n\t\tborder-radius: $radius-lg;\n\t}\n\n\t.ui-sortable-placeholder {\n\t\tborder: none;\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Search summary\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t.subtitle {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\theight: 24px;\n\t\tmargin: 0;\n\t\tpadding: {\n\t\t\ttop: 4px;\n\t\t\tright: 12px;\n\t\t\tbottom: 4px;\n\t\t\tleft: 12px;\n\t\t};\n\t\tbackground-color: $blue-50;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $blue-200;\n\t\t};\n\t\tborder-radius: $radius-md;\n\t\t@extend .p3;\n\n\t\tstrong {\n\t\t\tmargin: {\n\t\t\t\tleft: 5px;\n\t\t\t};\n\t\t}\n\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Action strip\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-actions-strip {\n\tdisplay: flex;\n\n\t.acf-btn {\n\t\tmargin: {\n\t\t\tright: 8px;\n\t\t};\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Notices\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t.notice,\n\t#lost-connection-notice {\n\t\tposition: relative;\n\t\tbox-sizing: border-box;\n\t\tmin-height: 48px;\n\t\tmargin: {\n\t\t\ttop: 0 !important;\n\t\t\tright: 0 !important;\n\t\t\tbottom: 16px !important;\n\t\t\tleft: 0 !important;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 13px !important;\n\t\t\tright: 16px !important;\n\t\t\tbottom: 12px !important;\n\t\t\tleft: 50px !important;\n\t\t};\n\t\tbackground-color: #E7EFF9;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: #9DBAEE;\n\t\t};\n\t\tborder-radius: $radius-lg;\n\t\tbox-shadow: $elevation-01;\n\t\tcolor: $gray-700;\n\n\t\t&.update-nag {\n\t\t\tdisplay: block;\n\t\t\tposition: relative;\n\t\t\twidth: calc(100% - 44px);\n\t\t\tmargin: {\n\t\t\t\ttop: 48px !important;\n\t\t\t\tright: 44px !important;\n\t\t\t\tbottom: -32px !important;\n\t\t\t\tleft: 12px !important;\n\t\t\t};\n\t\t}\n\n\t\t.button {\n\t\t\theight: auto;\n\t\t\tmargin: {\n\t\t\t\tleft: 8px;\n\t\t\t};\n\t\t\tpadding: 0;\n\t\t\tborder: none;\n\t\t\t@extend .p5;\n\t\t}\n\n\t\t>div {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t}\n\n\t\tp {\n\t\t\tflex: 1 0 auto;\n\t\t\tmargin: 0;\n\t\t\tpadding: 0;\n\n\t\t\t&.help {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t};\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t};\n\t\t\t\t@extend .p7;\n\t\t\t\tcolor: rgba($gray-700,.7);\n\t\t\t}\n\n\t\t}\n\n\t\t// Dismiss button\n\t\t.notice-dismiss {\n\t\t\tposition: absolute;\n\t\t\ttop: 4px;\n\t\t\tright: 8px;\n\n\t\t\t&:before {\n\t\t\t\tcontent: '';\n\t\t\t\t$icon-size: 20px;\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: relative;\n\t\t\t\tz-index: 600;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tbackground-color: $gray-500;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-close.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-close.svg');\n\t\t\t}\n\n\t\t\t&:hover::before {\n\t\t\t\tbackground-color: $gray-700;\n\t\t\t}\n\n\t\t}\n\n\t\t// Icon base styling\n\t\t&:before {\n\t\t\tcontent: '';\n\t\t\t$icon-size: 16px;\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\ttop: 15px;\n\t\t\tleft: 18px;\n\t\t\tz-index: 600;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t};\n\t\t\tbackground-color: #fff;\n\t\t\tborder: none;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: contain;\n\t\t\tmask-size: contain;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t\t-webkit-mask-image: url('../../images/icons/icon-info-solid.svg');\n\t\t\tmask-image: url('../../images/icons/icon-info-solid.svg');\n\t\t}\n\n\t\t&:after {\n\t\t\tcontent: '';\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\ttop: 9px;\n\t\t\tleft: 12px;\n\t\t\tz-index: 500;\n\t\t\twidth: 28px;\n\t\t\theight: 28px;\n\t\t\tbackground-color: $color-info;\n\t\t\tborder-radius: $radius-md;\n\t\t\tbox-shadow: $elevation-01;\n\t\t}\n\n\t\t.local-restore {\n\t\t\talign-items: center;\n\t\t\tmargin: {\n\t\t\t\ttop: -6px;\n\t\t\t\tbottom: 0;\n\t\t\t};\n\n\t\t}\n\n\t}\n\n\t.notice.is-dismissible {\n\t\tpadding: {\n\t\t\tright: 56px;\n\t\t};\n\t}\n\n\t// Success notice\n\t.notice.notice-success {\n\t\tbackground-color: #EDF7EF;\n\t\tborder-color: #B6DEB9;\n\n\t\t&:before {\n\t\t\t-webkit-mask-image: url('../../images/icons/icon-check-circle-solid.svg');\n\t\t\tmask-image: url('../../images/icons/icon-check-circle-solid.svg');\n\t\t}\n\n\t\t&:after {\n\t\t\tbackground-color: $color-success;\n\t\t}\n\n\t}\n\n\t// Error notice\n\t.notice.notice-error,\n\t#lost-connection-notice {\n\t\tbackground-color: #F7EEEB;\n\t\tborder-color: #F1B6B3;\n\n\t\t&:before {\n\t\t\t-webkit-mask-image: url('../../images/icons/icon-warning.svg');\n\t\t\tmask-image: url('../../images/icons/icon-warning.svg');\n\t\t}\n\n\t\t&:after {\n\t\t\tbackground-color: $color-danger;\n\t\t}\n\n\t}\n\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Global\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group #wpcontent {\n\tline-height: 140%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Links\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\ta {\n\t\tcolor: $blue-500;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headings\n*\n*---------------------------------------------------------------------------------------------*/\n\n.acf-h1 {\n\tfont-size: 21px;\n\tfont-weight: 400;\n}\n\n.acf-h2 {\n\tfont-size: 18px;\n\tfont-weight: 400;\n}\n\n.acf-h3 {\n\tfont-size: 16px;\n\tfont-weight: 400;\n}\n\n.post-type-acf-field-group,\n.acf-headerbar {\n\n\th1 {\n\t\t@extend .acf-h1;\n\t}\n\n\th2 {\n\t\t@extend .acf-h2;\n\t}\n\n\th3 {\n\t\t@extend .acf-h3;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Paragraphs\n*\n*---------------------------------------------------------------------------------------------*/\n\n.post-type-acf-field-group {\n\n\t.p1 {\n\t\tfont-size: 15px;\n\t}\n\t\n\t.p2 {\n\t\tfont-size: 14px;\n\t}\n\t\n\t.p3 {\n\t\tfont-size: 13.5px;\n\t}\n\t\n\t.p4 {\n\t\tfont-size: 13px;\n\t}\n\t\n\t.p5 {\n\t\tfont-size: 12.5px;\n\t}\n\t\n\t.p6 {\n\t\tfont-size: 12px;\n\t}\n\t\n\t.p7 {\n\t\tfont-size: 11.5px;\n\t}\n\t\n\t.p8 {\n\t\tfont-size: 11px;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Page titles\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-page-title {\n\t@extend .acf-h2;\n\tcolor: $gray-700;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Hide old / native WP titles from pages\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t.acf-settings-wrap h1,\n\t#acf-admin-tools h1 {\n\t\tdisplay: none;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-small {\n\t@extend .p6;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Link focus style\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group a:focus {\n\tbox-shadow: none;\n\toutline: none;\n}\n.post-type-acf-field-group a:focus-visible {\n\tbox-shadow: 0 0 0 1px #4f94d4, 0 0 2px 1px rgb(79 148 212 / 80%);\n\toutline: 1px solid transparent;\n}", ".post-type-acf-field-group {\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  All Inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"text\"],\n\tinput[type=\"search\"],\n\tinput[type=\"number\"],\n\ttextarea,\n\tselect {\n\t\tbox-sizing: border-box;\n\t\theight: 40px;\n\t\tpadding: {\n\t\t\tright: 12px;\n\t\t\tleft: 12px;\n\t\t};\n\t\tbackground-color: #fff;\n\t\tborder-color: $gray-300;\n\t\tbox-shadow: $elevation-01;\n\t\tborder-radius: $radius-md;\n\t\t@extend .p4;\n\t\tcolor: $gray-700;\n\n\t\t&:focus {\n\t\t\toutline: $outline;\n\t\t\tborder-color: $blue-400;\n\t\t}\n\n\t\t&:disabled {\n\t\t\tbackground-color: $gray-50;\n\t\t\tcolor: lighten($gray-500, 10%);\n\t\t}\n\n\t\t&::placeholder {\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Read only text inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"text\"] {\n\n\t\t&:read-only {\n\t\t\tbackground-color: $gray-50;\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Number fields\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-field.acf-field-number {\n\n\t\t.acf-label,\n\t\t.acf-input input[type=\"number\"] {\n\t\t\tmax-width: 180px;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Textarea\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\ttextarea {\n\t\tbox-sizing: border-box;\n\t\tpadding: {\n\t\t\ttop: 10px;\n\t\t\tbottom: 10px;\n\t\t};\n\t\theight: 80px;\n\t\tmin-height: 56px;\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Select\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tselect {\n\t\tmin-width: 160px;\n\t\tmax-width: 100%;\n\t\tpadding: {\n\t\t\tright: 40px;\n\t\t\tleft: 12px;\n\t\t};\n\t\tbackground-image: url('../../images/icons/icon-chevron-down.svg');\n\t\tbackground-position: right 10px top 50%;\n\t\tbackground-size: 20px;\n\t\t@extend .p4;\n\n\t\t&:hover,\n\t\t&:focus {\n\t\t\tcolor: $blue-500;\n\t\t}\n\n\t\t&::before {\n\t\t\tcontent: '';\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\ttop: 5px;\n\t\t\tleft: 5px;\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t\tbackground-color: red;\n\t\t}\n\n\t}\n\n\t&.rtl {\n\t\tselect {\n\t\t\tpadding: {\n\t\t\t\tright: 12px;\n\t\t\t\tleft: 40px;\n\t\t\t};\n\t\t\tbackground-position: left 10px top 50%;\n\t\t}\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Button & Checkbox base styling\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"radio\"],\n\tinput[type=\"checkbox\"] {\n\t\tbox-sizing: border-box;\n\t\twidth: 16px;\n\t\theight: 16px;\n\t\tpadding: 0;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-400;\n\t\t};\n\t\tbackground: #fff;\n\t\tbox-shadow: none;\n\n\t\t&:hover {\n\t\t\tbackground-color: $blue-50;\n\t\t\tborder-color: $blue-500;\n\t\t}\n\n\t\t&:checked,\n\t\t&:focus-visible {\n\t\t\tbackground-color: $blue-50;\n\t\t\tborder-color: $blue-500;\n\n\t\t\t&:before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: relative;\n\t\t\t\ttop: -1px;\n\t\t\t\tleft: -1px;\n\t\t\t\twidth: 16px;\n\t\t\t\theight: 16px;\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0;\n\t\t\t\tbackground-color: transparent;\n\t\t\t\tbackground-size: cover;\n\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\tbackground-position: center;\n\t\t\t}\n\n\t\t}\n\n\t\t&:active {\n\t\t\tbox-shadow: 0px 0px 0px 3px $blue-50, 0px 0px 0px rgba(255, 54, 54, 0.25);\n\t\t}\n\n\t\t&:disabled {\n\t\t\tbackground-color: $gray-50;\n\t\t\tborder-color: $gray-300;\n\t\t}\n\n\t}\n\n\t&.rtl {\n\t\tinput[type=\"radio\"],\n\t\tinput[type=\"checkbox\"] {\n\t\t\t&:checked,\n\t\t\t&:focus-visible {\n\t\t\t\t&:before {\n\t\t\t\t\tleft: 1px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Buttons\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"radio\"] {\n\n\t\t&:checked,\n\t\t&:focus {\n\n\t\t\t&:before {\n\t\t\t\tbackground-image: url('../../images/field-states/radio-active.svg');\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Checkboxes\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\tinput[type=\"checkbox\"] {\n\n\t\t&:checked,\n\t\t&:focus {\n\n\t\t\t&:before {\n\t\t\t\tbackground-image: url('../../images/field-states/checkbox-active.svg');\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Radio Buttons & Checkbox lists\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-radio-list,\n\t.acf-checkbox-list {\n\n\t\tli input[type=\"radio\"],\n\t\tli input[type=\"checkbox\"] {\n\t\t\tmargin: {\n\t\t\t\tright: 6px;\n\t\t\t};\n\t\t}\n\n\t\t&.acf-bl li {\n\t\t\tmargin: {\n\t\t\t\tbottom: 8px;\n\t\t\t};\n\n\t\t\t&:last-of-type {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\n\t\t}\n\n\t\tlabel {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\talign-content: center;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  ACF Switch\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-switch {\n\t\twidth: 42px;\n\t\theight: 24px;\n\t\tborder: none;\n\t\tbackground-color: $gray-300;\n\t\tborder-radius: 12px;\n\n\t\t&:hover {\n\t\t\tbackground-color: $gray-400;\n\t\t}\n\n\t\t&:active {\n\t\t\tbox-shadow: 0px 0px 0px 3px $blue-50, 0px 0px 0px rgba(255, 54, 54, 0.25);\n\t\t}\n\n\t\t&.-on {\n\t\t\tbackground-color: $color-primary;\n\n\t\t\t&:hover {\n\t\t\t\tbackground-color: $color-primary-hover;\n\t\t\t}\n\n\t\t\t.acf-switch-slider {\n\t\t\t\tleft: 20px;\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-switch-off,\n\t\t.acf-switch-on {\n\t\t\tvisibility: hidden;\n\t\t}\n\n\t\t.acf-switch-slider {\n\t\t\twidth: 20px;\n\t\t\theight: 20px;\n\t\t\tborder: none;\n\t\t\tborder-radius: 100px;\n\t\t\tbox-shadow: 0px 1px 3px rgba(16, 24, 40, 0.1), 0px 1px 2px rgba(16, 24, 40, 0.06);\n\t\t}\n\n\t}\n\n\t.acf-field-true-false {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\n\t\t.acf-label {\n\t\t\torder: 2;\n\t\t\tdisplay: block;\n\t\t\talign-items: center;\n\t\t\tmargin: {\n\t\t\t\ttop: 2px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 12px;\n\t\t\t};\n\n\t\t\tlabel {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-tip {\n\t\t\t\tmargin: {\n\t\t\t\t\tleft: 12px;\n\t\t\t\t};\n\t\t\t}\n\t\t\t\n\t\t\t.description {\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 2px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\t\t\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t&.rtl {\n\t\t.acf-field-true-false {\n\t\t\t.acf-label {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-tip {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tleft: 0;\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  File input button\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\n\tinput::file-selector-button {\n\t\tbox-sizing: border-box;\n\t\tmin-height: 40px;\n\t\tmargin: {\n\t\t\tright: 16px;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 8px;\n\t\t\tright: 16px;\n\t\t\tbottom: 8px;\n\t\t\tleft: 16px;\n\t\t};\n\t\tbackground-color: transparent;\n\t\tcolor: $color-primary !important;\n\t\tborder-radius: $radius-md;\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $color-primary;\n\t\t};\n\t\ttext-decoration: none;\n\n\t\t&:hover {\n\t\t\tborder-color: $color-primary-hover;\n\t\t\tcursor: pointer;\n\t\t\tcolor: $color-primary-hover !important;\n\t\t}\n\n\t}\n\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Action Buttons\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.button {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\theight: 40px;\n\t\tpadding: {\n\t\t\tright: 16px;\n\t\t\tleft: 16px;\n\t\t};\n\t\tbackground-color: transparent;\n\t\tborder-width: 1px;\n\t\tborder-style: solid;\n\t\tborder-color: $blue-500;\n\t\tborder-radius: $radius-md;\n\t\t@extend .p4;\n\t\tcolor: $blue-500;\n\n\t\t&:hover {\n\t\t\tbackground-color: lighten($blue-50, 2%);\n\t\t\tborder-color: $color-primary;\n\t\t\tcolor: $color-primary;\n\t\t}\n\t\t&:focus {\n\t\t\tbackground-color: lighten($blue-50, 2%);\n\t\t\toutline: $outline;\n\t\t\tcolor: $color-primary;\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Edit field group header\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.edit-field-group-header {\n\t\tdisplay: block !important;\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Select2 inputs\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-input {\n\n\t\t.select2-container.-acf .select2-selection {\n\t\t\tborder: none;\n\t\t\tline-height: 1;\n\t\t}\n\n\t\t.select2-container.-acf .select2-selection__rendered {\n\t\t\tbox-sizing: border-box;\n\t\t\tpadding: {\n\t\t\t\tright: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\tbackground-color: #fff;\n\t\t\tborder: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-300;\n\t\t\t};\n\t\t\tbox-shadow: $elevation-01;\n\t\t\tborder-radius: $radius-md;\n\t\t\t@extend .p4;\n\t\t\tcolor: $gray-700;\n\t\t}\n\n\t\t.select2-container--focus {\n\t\t\toutline: $outline;\n\t\t\tborder-color: $blue-400;\n\t\t\tborder-radius: $radius-md;\n\n\t\t\t.select2-selection__rendered {\n\t\t\t\tborder-color: $blue-400 !important;\n\t\t\t}\n\n\t\t\t&.select2-container--below.select2-container--open {\n\n\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\tborder-bottom-right-radius: 0 !important;\n\t\t\t\t\tborder-bottom-left-radius: 0 !important;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t&.select2-container--above.select2-container--open {\n\n\t\t\t\t.select2-selection__rendered {\n\t\t\t\t\tborder-top-right-radius: 0 !important;\n\t\t\t\t\tborder-top-left-radius: 0 !important;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t.select2-container .select2-search--inline .select2-search__field {\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\tleft: 6px;\n\t\t\t};\n\n\t\t\t&:focus {\n\t\t\t\toutline: none;\n\t\t\t\tborder: none;\n\t\t\t}\n\n\t\t}\n\n\t\t.select2-container--default .select2-selection--multiple .select2-selection__rendered {\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 6px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 6px;\n\t\t\t};\n\t\t}\n\n\t\t.select2-selection__clear {\n\t\t\twidth: 18px;\n\t\t\theight: 18px;\n\t\t\tmargin: {\n\t\t\t\ttop: 12px;\n\t\t\t\tright: 0;\n\t\t\t};\n\t\t\ttext-indent: 100%;\n\t\t\twhite-space: nowrap;\n\t\t\toverflow: hidden;\n\n\t\t\t&:before {\n\t\t\t\tcontent: '';\n\t\t\t\t$icon-size: 14px;\n\t\t\t\tdisplay: block;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\ttop: 0;\n\t\t\t\tleft: 0;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-close.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-close.svg');\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t}\n\n\t\t\t&:hover::before {\n\t\t\t\tbackground-color: $gray-800;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  ACF label\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\n\t\t.acf-icon-help {\n\t\t\t$icon-size: 18px;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tbackground-color: $gray-400;\n\t\t}\n\n\t\tlabel {\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t}\n\t\t\n\t\t.description {\n\t\t\tmargin: {\n\t\t\t\ttop: 2px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t/*---------------------------------------------------------------------------------------------\n\t*\n\t*  Tooltip for field name field setting (result of a fix for keyboard navigation)\n\t*\n\t*---------------------------------------------------------------------------------------------*/\n\t.acf-field-setting-name .acf-tip {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 654px;\n\t\tcolor: #98A2B3;\n\n\t\t.acf-icon-help {\n\t\t\twidth: 18px;\n\t\t\theight: 18px;\n\t\t}\n\t}\n\n}\n\n.rtl.post-type-acf-field-group {\n\t.acf-field-setting-name .acf-tip {\n\t\tleft: auto;\n\t\tright: 654px;\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n*\n*  Field Groups\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups {\n\n\t// Hide tablenav top.\n\t.tablenav.top {\n\t\tdisplay: none;\n\t}\n\n\t// Fix margin due to hidden tablenav.\n\t.subsubsub {\n\t\tmargin-bottom: 3px;\n\t}\n\n\t// table.\n\t.wp-list-table {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\t\tborder-radius: $radius-lg;\n\t\tborder: none;\n\t\toverflow: hidden;\n\t\tbox-shadow: $elevation-01;\n\n\t\tstrong {\n\t\t\tcolor: $gray-400;\n\t\t\tmargin: 0;\n\t\t}\n\n\t\ta.row-title {\n\t\t\tfont-size: 13px !important;\n\t\t\tfont-weight: 500;\n\t\t}\n\n\t\tth,\n\t\ttd {\n\t\t\tcolor: $gray-700;\n\n\t\t\t&.sortable a {\n\t\t\t\tpadding: 0;\n\t\t\t}\n\n\t\t\t&.check-column {\n\t\t\t\tpadding: {\n\t\t\t\t\ttop: 12px;\n\t\t\t\t\tright: 16px;\n\t\t\t\t\tleft: 16px;\n\t\t\t\t};\n\n\t\t\t\t@media screen and (max-width: $md) {\n\t\t\t\t\tvertical-align: top;\n\t\t\t\t\tpadding: {\n\t\t\t\t\t\tright: 2px;\n\t\t\t\t\t\tleft: 10px;\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tinput {\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0;\n\t\t\t}\n\n\t\t}\n\n\t\t// Table headers\n\t\tthead th, thead td,\n\t\ttfoot th, tfoot td {\n\t\t\theight: 48px;\n\t\t\tpadding: {\n\t\t\t\tright: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t};\n\t\t\tbox-sizing: border-box;\n\t\t\tbackground-color: $gray-50;\n\t\t\tborder-color: $gray-200;\n\t\t\t@extend .p4;\n\t\t\tfont-weight: 500;\n\n\t\t\t@media screen and (max-width: $md) {\n\t\t\t\tpadding: {\n\t\t\t\t\tright: 16px;\n\t\t\t\t\tleft: 8px;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t&.check-column {\n\t\t\t\t@media screen and (max-width: $md) {\n\t\t\t\t\tvertical-align: middle;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t// Table body\n\t\ttbody th,\n\t\ttbody td {\n\t\t\tbox-sizing: border-box;\n\t\t\theight: 60px;\n\t\t\tpadding: {\n\t\t\t\ttop: 10px;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 10px;\n\t\t\t\tleft: 24px;\n\t\t\t};\n\t\t\tvertical-align: top;\n\t\t\tbackground-color: #fff;\n\t\t\tborder-bottom: {\n\t\t\t\twidth: 1px;\n\t\t\t\tcolor: $gray-200;\n\t\t\t\tstyle: solid;\n\t\t\t};\n\t\t\t@extend .p4;\n\n\t\t\t@media screen and (max-width: $md) {\n\t\t\t\tpadding: {\n\t\t\t\t\tright: 16px;\n\t\t\t\t\tleft: 8px;\n\t\t\t\t};\n\t\t\t}\n\n\t\t}\n\n\t\t.column-acf-key {\n\t\t\twhite-space: nowrap;\n\t\t}\n\n\t\t// SVG icons\n\t\t.column-acf-key .acf-icon-key-solid {\n\t\t\tdisplay: inline-block;\n\t\t\tposition: relative;\n\t\t\tbottom: -2px;\n\t\t\twidth: 15px;\n\t\t\theight: 15px;\n\t\t\tmargin: {\n\t\t\t\tright: 4px;\n\t\t\t};\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t\t// Post location icon\n\t\t.acf-location .dashicons {\n\t\t\tposition: relative;\n\t\t\tbottom: -2px;\n\t\t\twidth: 16px;\n\t\t\theight: 16px;\n\t\t\tmargin: {\n\t\t\t\tright: 6px;\n\t\t\t};\n\t\t\tfont-size: 16px;\n\t\t\tcolor: $gray-400;\n\t\t}\n\n\t\t.post-state {\n\t\t\t@extend .p3;\n\t\t\tcolor: $gray-500;\n\t\t}\n\n\t\t// Add subtle hover background to define row.\n\t\ttr:hover,\n\t\ttr:focus-within {\n\t\t\tbackground: #f7f7f7;\n\n\t\t\t.row-actions {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t};\n\n\t\t}\n\n\t\t// Use less specific identifier to inherit mobile styling.\n\t\t@media screen and ( min-width: 782px ) {\n\t\t\t.column-acf-count { width: 10%; }\n\t\t}\n\n\t\t.row-actions {\n\t\t\tspan.file {\n\t\t\t\tdisplay: block;\n\t\t\t\toverflow: hidden;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t}\n\t\t}\n\t}\n\n\t&.rtl {\n\t\t.wp-list-table {\n\t\t\t.column-acf-key .acf-icon-key-solid {\n\t\t\t\tmargin: {\n\t\t\t\t\tleft: 4px;\n\t\t\t\t\tright: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t.acf-location .dashicons {\n\t\t\t\tmargin: {\n\t\t\t\t\tleft: 6px;\n\t\t\t\t\tright: 0;\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t}\n\n\t// Actions\n\t.row-actions {\n\t\tmargin: {\n\t\t\ttop: 2px;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t}\n\t\t@extend .p5;\n\t\tline-height: 14px;\n\t\tcolor: $gray-300;\n\n\t\t.trash a {\n\t\t\tcolor: $acf_error;\n\t\t}\n\n\t}\n\n\n\t// Remove padding from checkbox column\n\t.widefat thead td.check-column,\n\t.widefat tfoot td.check-column {\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t};\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRow actions\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups {\n\n\t.row-actions {\n\t\t@extend .p6;\n\n\t\ta:hover {\n\t\t\tcolor: darken($color-primary-hover, 10%);\n\t\t}\n\n\t\t.trash a {\n\t\t\tcolor: #a00;\n\t\t\t&:hover { color: #f00; }\n\t\t}\n\n\t\t&.visible {\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t\topacity: 1;\n\t\t}\n\n\t}\n\n}\n\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tRow hover\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups {\n\n\t#the-list tr:hover td,\n\t#the-list tr:hover th {\n\t\tbackground-color: lighten($blue-50, 3%);\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Table Nav\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups {\n\n\t.tablenav {\n\t\tmargin: {\n\t\t\ttop: 24px;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tcolor: $gray-500;\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tSearch box\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups #posts-filter p.search-box {\n\tmargin: {\n\t\ttop: 5px;\n\t\tright: 0;\n\t\tbottom: 24px;\n\t\tleft: 0;\n\t};\n\n\t#post-search-input {\n\t\tmin-width: 280px;\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 8px;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t}\n\n\t@media screen and (max-width: 768px) {\n\t\tdisplay: flex;\n\t\tbox-sizing: border-box;\n\t\tpadding-right: 24px;\n\t\tmargin-right: 16px;\n\n\t\t#post-search-input {\n\t\t\tmin-width: auto;\n\t\t}\n\n\t}\n\n}\n\n.rtl.acf-admin-field-groups #posts-filter p.search-box {\n\t#post-search-input {\n\t\tmargin: {\n\t\t\tright: 0;\n\t\t\tleft: 8px;\n\t\t};\n\t}\n\n\t@media screen and (max-width: 768px) {\n\t\tpadding-left: 24px;\n\t\tpadding-right: 0;\n\t\tmargin-left: 16px;\n\t\tmargin-right: 0;\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tStatus tabs\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups .subsubsub {\n\tdisplay: flex;\n\talign-items: flex-end;\n\theight: 40px;\n\tmargin: {\n\t\tbottom: 16px;\n\t};\n\n\tli {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 4px;\n\t\t};\n\t\tcolor: $gray-400;\n\t\t@extend .p4;\n\n\t\t.count {\n\t\t\tcolor: $gray-500;\n\t\t}\n\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPagination\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups {\n\n\t.tablenav-pages {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t.displaying-num {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 16px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t}\n\n\t\t.pagination-links {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t#table-paging {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 0;\n\t\t\t\t\tright: 4px;\n\t\t\t\t\tbottom: 0;\n\t\t\t\t\tleft: 8px;\n\t\t\t\t};\n\n\t\t\t\t.total-pages {\n\t\t\t\t\tmargin: {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Hide pagination if there's only 1 page\n\t\t&.one-page .pagination-links {\n\t\t\tdisplay: none;\n\t\t}\n\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPagination buttons & icons\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-field-groups .tablenav-pages .pagination-links .button {\n\tdisplay: inline-flex;\n\talign-items: center;\n\talign-content: center;\n\tjustify-content: center;\n\tmin-width: 40px;\n\tmargin: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t};\n\tpadding: {\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t};\n\tbackground-color: transparent;\n\n\t// Pagination Buttons\n\t&:nth-child(1),\n\t&:nth-child(2),\n\t&:last-child,\n\t&:nth-last-child(2) {\n\t\tdisplay: inline-block;\n\t\tposition: relative;\n\t\ttext-indent: 100%;\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t\tmargin: {\n\t\t\tleft: 4px;\n\t\t}\n\n\t\t// Pagination Button Icons\n\t\t&:before {\n\t\t\t$icon-size: 20px;\n\t\t\tcontent: \"\";\n\t\t\tdisplay: block;\n\t\t\tposition: absolute;\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\t$icon-size: $icon-size;\n\t\t\tbackground-color: $link-color;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: $icon-size;\n\t\t\tmask-size: $icon-size;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t}\n\n\t}\n\n\t// First Page Icon\n\t&:nth-child(1):before {\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-left-double.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-left-double.svg');\n\t}\n\n\t// Previous Page Icon\n\t&:nth-child(2):before {\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-left.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-left.svg');\n\t}\n\n\t// Next Page Icon\n\t&:nth-last-child(2):before {\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-right.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-right.svg');\n\t}\n\n\t// Last Page Icon\n\t&:last-child:before {\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-right-double.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-right-double.svg');\n\t}\n\n\t// Pagination Button Hover State\n\t&:hover {\n\t\tborder-color: $blue-600;\n\t\tbackground-color: rgba($link-color, .05);\n\n\t\t&:before {\n\t\t\tbackground-color: $blue-600;\n\t\t}\n\n\t}\n\n\t// Pagination Button Disabled State\n\t&.disabled {\n\t\tbackground-color: transparent !important;\n\n\t\t&.disabled:before {\n\t\t\tbackground-color: $gray-300;\n\t\t}\n\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Empty state\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-no-field-groups-wrapper {\n\tdisplay: flex;\n\tjustify-content: center;\n\tpadding: {\n\t\ttop: 48px;\n\t\tbottom: 48px;\n\t};\n\n\t.acf-no-field-groups-inner {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: center;\n\t\talign-content: center;\n\t\talign-items: flex-start;\n\t\ttext-align: center;\n\t\tmax-width: 380px;\n\t\tmin-height: 320px;\n\n\t\timg,\n\t\th2,\n\t\tp {\n\t\t\tflex: 1 0 100%;\n\t\t}\n\n\t\th2 {\n\t\t\t@extend .acf-h2;\n\t\t\tmargin: {\n\t\t\t\ttop: 32px;\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t\tpadding: 0;\n\t\t\tcolor: $gray-700;\n\t\t}\n\n\t\tp {\n\t\t\t@extend .p2;\n\t\t\tmargin: {\n\t\t\t\ttop: 12px;\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t\tpadding: 0;\n\t\t\tcolor: $gray-500;\n\n\t\t\t&.acf-small {\n\t\t\t\tdisplay: block;\n\t\t\t\tposition: relative;\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 32px;\n\t\t\t\t};\n\t\t\t\t@extend .p6;\n\t\t\t}\n\n\t\t}\n\n\n\t\timg {\n\t\t\tmax-width: 284px;\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t}\n\n\t\t.acf-btn {\n\t\t\tmargin: {\n\t\t\t\ttop: 32px;\n\t\t\t};\n\t\t}\n\n\t}\n\n};\n\n.acf-no-field-groups {\n\n\t#the-list tr:hover td,\n\t#the-list tr:hover th,\n\t.acf-admin-field-groups .wp-list-table tr:hover,\n\t.striped > tbody > :nth-child(odd), ul.striped > :nth-child(odd), .alternate {\n\t\tbackground-color: transparent !important;\n\t}\n\n\t.wp-list-table {\n\n\t\tthead,\n\t\ttfoot {\n\t\t\tdisplay: none;\n\t\t}\n\n\t}\n\n\t.no-pages {\n\t\tdisplay: none;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small screen list table info toggle\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t.wp-list-table .toggle-row:before {\n\t\ttop: 4px;\n\t\tleft: 16px;\n\t\tborder-radius: 0;\n\t\t$icon-size: 20px;\n\t\tcontent: \"\";\n\t\tdisplay: block;\n\t\tposition: absolute;\n\t\twidth: 16px;\n\t\theight: 16px;\n\t\t$icon-size: $icon-size;\n\t\tbackground-color: $link-color;\n\t\tborder-radius: 0;\n\t\t-webkit-mask-size: $icon-size;\n\t\tmask-size: $icon-size;\n\t\t-webkit-mask-repeat: no-repeat;\n\t\tmask-repeat: no-repeat;\n\t\t-webkit-mask-position: center;\n\t\tmask-position: center;\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-down.svg');\n\t\ttext-indent: 100%;\n\t\twhite-space: nowrap;\n\t\toverflow: hidden;\n\t}\n\n\t.wp-list-table .is-expanded .toggle-row:before {\n\t\t-webkit-mask-image: url('../../images/icons/icon-chevron-up.svg');\n\t\tmask-image: url('../../images/icons/icon-chevron-up.svg');\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Small screen checkbox\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t@media screen and (max-width: $md) {\n\n\t\t.widefat th input[type=\"checkbox\"],\n\t\t.widefat thead td input[type=\"checkbox\"],\n\t\t.widefat tfoot td input[type=\"checkbox\"] {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\n\t}\n\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Admin Navigation\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar {\n\tdisplay: flex;\n\tjustify-content: flex-start;\n\talign-content: center;\n\talign-items: center;\n\tposition: unset;\n\ttop: 32px;\n\theight: 72px;\n\tz-index: 800;\n\tbackground: $gray-700;\n\tcolor: $gray-400;\n\n\t@media screen and (max-width: $md) {\n\t\tposition: static;\n\t}\n\n\t.acf-logo {\n\t\tmargin: {\n\t\t\tright: 32px;\n\t\t};\n\n\t\timg {\n\t\t\tdisplay: block;\n\t\t\tmax-width: 55px;\n\t\t\tline-height: 0%;\n\t\t}\n\n\t}\n\n\th2 {\n\t\tdisplay: none;\n\t\tcolor: $gray-50;\n\t}\n\n\t.acf-tab {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbox-sizing: border-box;\n\t\tmin-height: 40px;\n\t\tmargin: {\n\t\t\tright: 8px;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 8px;\n\t\t\tright: 16px;\n\t\t\tbottom: 8px;\n\t\t\tleft: 16px;\n\t\t};\n\t\tborder: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: transparent;\n\t\t};\n\t\tborder-radius: $radius-md;\n\t\t@extend .p4;\n\t\tcolor: $gray-400;\n\t\ttext-decoration: none;\n\n\t\t&.is-active {\n\t\t\tbackground-color: $gray-600;\n\t\t\tcolor: #fff;\n\t\t}\n\t\t&:hover {\n\t\t\tbackground-color: $gray-600;\n\t\t\tcolor: $gray-50;\n\t\t}\n\t\t&:focus-visible {\n\t\t\tborder: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-500;\n\t\t\t};\n\t\t}\n\t\t&:focus {\n\t\t\tbox-shadow: none;\n\t\t}\n\t}\n\n\t// Within wpcontent.\n\t#wpcontent & {\n\t\tbox-sizing: border-box;\n\t\tmargin-left: -20px;\n\t\tpadding: {\n\t\t\ttop: 16px;\n\t\t\tright: 32px;\n\t\t\tbottom: 16px;\n\t\t\tleft: 32px;\n\t\t};\n\t}\n\n\t// Mobile\n\t@media screen and (max-width: 600px) {\n\t\t& {\n\t\t\tdisplay: none;\n\t\t}\n\t}\n}\n\n.rtl {\n\t#wpcontent .acf-admin-toolbar {\n\t\tmargin-left: 0;\n\t\tmargin-right: -20px;\n\n\t\t.acf-tab {\n\t\t\tmargin: {\n\t\t\t\tleft: 8px;\n\t\t\t\tright: 0;\n\t\t\t};\n\t\t}\n\t}\n\n\t.acf-logo {\n\t\tmargin: {\n\t\t\tright: 0;\n\t\t\tleft: 32px;\n\t\t};\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Admin Toolbar Icons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar {\n\n\t.acf-tab {\n\n\t\ti.acf-icon {\n\t\t\tdisplay: none; // Icons only shown for specified nav items below\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t\tleft: -2px;\n\t\t\t};\n\t\t}\n\n\t\t// Only show icons for specified nav items, stops third party plugin items with no icon appearing broken\n\t\t&.acf-header-tab-acf-field-group,\n\t\t&.acf-header-tab-acf-tools,\n\t\t&.acf-header-tab-acf-settings-updates {\n\t\t\ti.acf-icon {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t}\n\t\t}\n\n\t\t&.is-active,\n\t\t&:hover {\n\n\t\t\ti.acf-icon {\n\t\t\t\tbackground-color: $gray-200;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t.rtl & .acf-tab {\n\t\ti.acf-icon {\n\t\t\tmargin: {\n\t\t\t\tright: -2px;\n\t\t\t\tleft: 8px;\n\t\t\t};\n\t\t}\n\t}\n\n\t// Field groups tab\n\t.acf-header-tab-acf-field-group {\n\n\t\ti.acf-icon {\n\t\t\t$icon-url: url('../../images/icons/icon-field-groups.svg');\n\t\t\t-webkit-mask-image: $icon-url;\n\t\t\tmask-image: $icon-url;\n\t\t}\n\n\t}\n\n\t// Field groups tab\n\t.acf-header-tab-acf-tools {\n\n\t\ti.acf-icon {\n\t\t\t$icon-url: url('../../images/icons/icon-tools.svg');\n\t\t\t-webkit-mask-image: $icon-url;\n\t\t\tmask-image: $icon-url;\n\t\t}\n\n\t}\n\n\t// Field groups tab\n\t.acf-header-tab-acf-settings-updates {\n\n\t\ti.acf-icon {\n\t\t\t$icon-url: url('../../images/icons/icon-updates.svg');\n\t\t\t-webkit-mask-image: $icon-url;\n\t\t\tmask-image: $icon-url;\n\t\t}\n\n\t}\n\n\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Hide WP default controls\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\th1.wp-heading-inline {\n\t\tdisplay: none;\n\t}\n\n\t.wrap .wp-heading-inline + .page-title-action {\n\t\tdisplay: none;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Headerbar\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-headerbar {\n\tdisplay: flex;\n\talign-items: center;\n\tposition: sticky;\n\ttop: 32px;\n\tz-index: 700;\n\tbox-sizing: border-box;\n\tmin-height: 72px;\n\tmargin: {\n\t\tleft: -20px;\n\t};\n\tpadding: {\n\t\ttop: 8px;\n\t\tright: 32px;\n\t\tbottom: 8px;\n\t\tleft: 32px;\n\t};\n\tbackground-color: #fff;\n\tbox-shadow: $elevation-01;\n\n\t.acf-headerbar-inner {\n\t\tflex: 1 1 auto;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tmax-width: $max-width;\n\t}\n\n\t.acf-page-title {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 16px;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\t}\n\n\t@media screen and (max-width: $md) {\n\t\tposition: static;\n\t}\n\n\t@media screen and (max-width: 600px) {\n\t\tjustify-content: space-between;\n\t\tposition: relative;\n\t\ttop: 46px;\n\t\tmin-height: 64px;\n\t\tpadding: {\n\t\t\tright: 12px;\n\t\t};\n\t}\n\n\t.acf-headerbar-content {\n\t\tflex: 1 1 auto;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t@media screen and (max-width: $md) {\n\t\t\tflex-wrap: wrap;\n\n\t\t\t.acf-headerbar-title,\n\t\t\t.acf-title-wrap {\n\t\t\t\tflex: 1 1 100%;\n\t\t\t}\n\n\t\t\t.acf-title-wrap {\n\t\t\t\tmargin: {\n\t\t\t\t\ttop: 8px;\n\t\t\t\t};\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t.acf-input-error {\n\t\tborder: 1px rgba($color-danger, .5) solid !important;\n\t\tbox-shadow: 0px 0px 0px 3px rgba(209, 55, 55, 0.12), 0px 0px 0px rgba(255, 54, 54, 0.25) !important;\n\t\tbackground-image: url('../../images/icons/icon-warning-alt-red.svg');\n\t\tbackground-position: right 10px top 50%;\n\t\tbackground-size: 20px;\n\t\tbackground-repeat: no-repeat;\n\n\t\t&:focus {\n\t\t\toutline: none !important;\n\t\t\tborder: 1px rgba($color-danger, .8) solid !important;\n\t\t\tbox-shadow: 0px 0px 0px 3px rgba(209, 55, 55, 0.16), 0px 0px 0px rgba(255, 54, 54, 0.25) !important;\n\t\t}\n\t}\n\n\t.acf-headerbar-title-field {\n\t\tmin-width: 320px;\n\n\t\t@media screen and (max-width: $md) {\n\t\t\tmin-width: 100%;\n\t\t}\n\t}\n\n\t.acf-headerbar-actions {\n\t\tdisplay: flex;\n\n\t\t.acf-btn {\n\t\t\tmargin: {\n\t\t\t\tleft: 8px;\n\t\t\t};\n\t\t};\n\n\t\t.disabled {\n\t\t\tbackground-color: $gray-100;\n\t\t\tcolor: $gray-400 !important;\n\t\t\tborder: 1px $gray-300 solid;\n\t\t\tcursor: default;\n\t\t}\n\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Edit Field Group Headerbar\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-headerbar-field-editor {\n\tposition: sticky;\n\ttop: 32px;\n\tz-index: 700;\n\tmargin: {\n\t\tleft: -20px;\n\t};\n\twidth: calc(100% + 20px);\n\n\t@media screen and (max-width: $md) {\n\t\tposition: relative;\n\t\ttop: 0;\n\t\twidth: 100%;\n\t\tmargin: {\n\t\t\tleft: 0;\n\t\t};\n\t\tpadding: {\n\t\t\tright: 8px;\n\t\t\tleft: 8px;\n\t\t};\n\t}\n\n\t@media screen and (max-width: $sm) {\n\t\tposition: relative;\n\t\ttop: 46px;\n\t}\n\n\n\t.acf-headerbar-inner {\n\n\t\t@media screen and (max-width: $md) {\n\t\t\tflex-wrap: wrap;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: flex-start;\n\t\t\talign-items: flex-start;\n\t\t\twidth: 100%;\n\n\t\t\t.acf-page-title {\n\t\t\t\tflex: 1 1 auto;\n\t\t\t}\n\n\t\t\t.acf-headerbar-actions {\n\t\t\t\tflex: 1 1 100%;\n\t\t\t\tmargin-top: 8px;\n\t\t\t\tgap: 8px;\n\n\t\t\t\t.acf-btn {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\tdisplay: inline-flex;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tmargin: 0;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t.acf-page-title {\n\t\tmargin: {\n\t\t\tright: 16px;\n\t\t};\n\t}\n\n}\n\n.rtl .acf-headerbar,\n.rtl .acf-headerbar-field-editor {\n\tmargin-left: 0;\n\tmargin-right: -20px;\n\n\t.acf-page-title {\n\t\tmargin: {\n\t\t\tleft: 16px;\n\t\t\tright: 0;\n\t\t};\n\t}\n\n\t.acf-headerbar-actions {\n\t\t.acf-btn {\n\t\t\tmargin: {\n\t\t\t\tleft: 0;\n\t\t\t\tright: 8px;\n\t\t\t};\n\t\t};\n\n\t}\n}\n", "/*---------------------------------------------------------------------------------------------\n*\n*  ACF Buttons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn {\n\tdisplay: inline-flex;\n\talign-items: center;\n\tbox-sizing: border-box;\n\tmin-height: 40px;\n\tpadding: {\n\t\ttop: 8px;\n\t\tright: 16px;\n\t\tbottom: 8px;\n\t\tleft: 16px;\n\t};\n\tbackground-color: $color-primary;\n\tborder-radius: $radius-md;\n\tborder: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: rgba($gray-900, 20%);\n\t};\n\ttext-decoration: none;\n\tcolor: #fff !important;\n\ttransition: all .2s ease-in-out;\n\ttransition-property: background, border, box-shadow;\n\n\t&:disabled {\n\t\tbackground-color: red;\n\t}\n\n\t&:hover {\n\t\tbackground-color: $color-primary-hover;\n\t\tcolor: #fff;\n\t\tcursor: pointer;\n\t}\n\n\t&.acf-btn-sm {\n\t\tmin-height: 32px;\n\t\tpadding: {\n\t\t\ttop: 4px;\n\t\t\tright: 12px;\n\t\t\tbottom: 4px;\n\t\t\tleft: 12px;\n\t\t};\n\t\t@extend .p4;\n\t}\n\n\t&.acf-btn-secondary {\n\t\tbackground-color: transparent;\n\t\tcolor: $color-primary !important;\n\t\tborder-color: $color-primary;\n\n\t\t&:hover {\n\t\t\tbackground-color: lighten($blue-50, 2%);\n\t\t}\n\n\t}\n\n\t&.acf-btn-tertiary {\n\t\tbackground-color: transparent;\n\t\tcolor: $gray-500 !important;\n\t\tborder-color: $gray-300;\n\n\t\t&:hover {\n\t\t\tcolor: $gray-500 !important;\n\t\t\tborder-color: $gray-400;\n\t\t}\n\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Button icons\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn {\n\n\ti.acf-icon {\n\t\t$icon-size: 20px;\n\t\twidth: $icon-size;\n\t\theight: $icon-size;\n\t\t-webkit-mask-size: $icon-size;\n\t\tmask-size: $icon-size;\n\t\tmargin: {\n\t\t\tright: 6px;\n\t\t\tleft: -4px;\n\t\t};\n\t}\n\n\t&.acf-btn-sm {\n\n\t\ti.acf-icon {\n\t\t\t$icon-size: 18px;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\t-webkit-mask-size: $icon-size;\n\t\t\tmask-size: $icon-size;\n\t\t\tmargin: {\n\t\t\t\tright: 4px;\n\t\t\t\tleft: -2px;\n\t\t\t};\n\t\t}\n\n\t}\n\n}\n\n.rtl .acf-btn {\n\ti.acf-icon {\n\t\tmargin: {\n\t\t\tright: -4px;\n\t\t\tleft: 6px;\n\t\t};\n\t}\n\n\t&.acf-btn-sm {\n\t\ti.acf-icon {\n\t\t\tmargin: {\n\t\t\t\tright: -4px;\n\t\t\t\tleft: 2px;\n\t\t\t};\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Delete field group button\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-btn.acf-delete-field-group {\n\n\t&:hover {\n\t\tbackground-color: lighten($color-danger, 44%);\n\t\tborder-color: $color-danger !important;\n\t\tcolor: $color-danger !important;\n\t}\n\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tIcon base styling\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group i.acf-icon {\n\t$icon-size: 20px;\n\tdisplay: inline-flex;\n\twidth: $icon-size;\n\theight: $icon-size;\n\tbackground-color: currentColor;\n\tborder: none;\n\tborder-radius: 0;\n\t-webkit-mask-size: contain;\n\tmask-size: contain;\n\t-webkit-mask-repeat: no-repeat;\n\tmask-repeat: no-repeat;\n\t-webkit-mask-position: center;\n\tmask-position: center;\n\ttext-indent: 500%;\n\twhite-space: nowrap;\n\toverflow: hidden;\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tIcons\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\n\t// Action icons for Flexible Content Field\n\ti.acf-field-setting-fc-delete, i.acf-field-setting-fc-duplicate {\n\t\tbox-sizing: border-box;\n\n\t\t/* Auto layout */\n\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 8px;\n\t\tcursor: pointer;\n\n\t\twidth: 32px;\n\t\theight: 32px;\n\n\t\t/* Base / White */\n\n\t\tbackground: #FFFFFF;\n\t\t/* Gray/300 */\n\n\t\tborder: 1px solid $gray-300;\n\t\t/* Elevation/01 */\n\n\t\tbox-shadow: $elevation-01;\n\t\tborder-radius: 6px;\n\n\t\t/* Inside auto layout */\n\n\t\tflex: none;\n\t\torder: 0;\n\t\tflex-grow: 0;\n\t}\n\n\ti.acf-icon-plus {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-add.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-add.svg\");\n\t}\n\n\ti.acf-icon-stars {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-stars.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-stars.svg\");\n\t}\n\n\ti.acf-icon-help {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-help.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-help.svg\");\n\t}\n\n\ti.acf-icon-key {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-key.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-key.svg\");\n\t}\n\n\ti.acf-icon-trash, button.acf-icon-trash {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-trash.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-trash.svg\");\n\t}\n\n\ti.acf-icon.-duplicate, button.acf-icon-duplicate {\n\t\t-webkit-mask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n\t\tmask-image: url(\"../../images/field-type-icons/icon-field-clone.svg\");\n\n\t\t&:before,\n\t\t&:after {\n\t\t\tcontent: none;\n\t\t}\n\t}\n\n\ti.acf-icon-arrow-right {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-arrow-right.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-arrow-right.svg\");\n\t}\n\n\ti.acf-icon-arrow-left {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-arrow-left.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-arrow-left.svg\");\n\t}\n\n\ti.acf-icon-chevron-right,\n\t.acf-icon.-right {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-chevron-right.svg\");\n\t}\n\n\ti.acf-icon-chevron-left,\n\t.acf-icon.-left {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-chevron-left.svg\");\n\t}\n\n\ti.acf-icon-key-solid {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-key-solid.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-key-solid.svg\");\n\t}\n\n\ti.acf-icon-globe,\n\t.acf-icon.-globe {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-globe.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-globe.svg\");\n\t}\n\n\ti.acf-icon-image,\n\t.acf-icon.-picture {\n\t\t-webkit-mask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n\t\tmask-image: url(\"../../images/field-type-icons/icon-field-image.svg\");\n\t}\n\t\n\ti.acf-icon-warning {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-warning-alt.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-warning-alt.svg\");\n\t}\n\t\n\ti.acf-icon-warning-red {\n\t\t-webkit-mask-image: url(\"../../images/icons/icon-warning-alt-red.svg\");\n\t\tmask-image: url(\"../../images/icons/icon-warning-alt-red.svg\");\n\t}\n\n\t/*--------------------------------------------------------------------------------------------\n\t*\n\t*\tInactive group icon\n\t*\n\t*--------------------------------------------------------------------------------------------*/\n\t.post-type-acf-field-group {\n\t\t.post-state {\n\t\t\tfont-weight: normal;\n\n\t\t\t.dashicons.dashicons-hidden {\n\t\t\t\t$icon-size: 18px;\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: $icon-size;\n\t\t\t\tmask-size: $icon-size;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-hidden.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-hidden.svg\");\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tEdit field group page postbox header icons\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-field-group-fields,\n#acf-field-group-options {\n\t.postbox-header,\n\t.acf-sub-field-list-header {\n\t\th2,\n\t\th3 {\n\t\t\tdisplay: inline-flex;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: stretch;\n\t\t\talign-items: center;\n\n\t\t\t&:before {\n\t\t\t\tcontent: \"\";\n\t\t\t\t$icon-size: 20px;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 8px;\n\t\t\t\t}\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t}\n\t\t}\n\t}\n}\n\n.rtl #acf-field-group-fields,\n.rtl #acf-field-group-options {\n\t.postbox-header,\n\t.acf-sub-field-list-header {\n\t\th2,\n\t\th3 {\n\t\t\t&:before {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 0;\n\t\t\t\t\tleft: 8px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// Field icon\n#acf-field-group-fields .postbox-header h2:before,\nh3.acf-sub-field-list-title:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-fields.svg\");\n\tmask-image: url(\"../../images/icons/icon-fields.svg\");\n}\n\n// Settings icon\n#acf-field-group-options .postbox-header h2:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-settings.svg\");\n\tmask-image: url(\"../../images/icons/icon-settings.svg\");\n}\n\n// Layout icon\n.acf-field-setting-fc_layout .acf-field-settings-fc_head label:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-layout.svg\");\n\tmask-image: url(\"../../images/icons/icon-layout.svg\");\n}\n\n// Flexible Content reorder\n.acf-field-setting-fc_layout .acf-field-settings-fc_head:hover .reorder-layout:before {\n\twidth: 20px;\n\theight: 11px;\n\tbackground-color: $gray-600 !important;\n\t-webkit-mask-image: url(\"../../images/icons/icon-draggable.svg\");\n\tmask-image: url(\"../../images/icons/icon-draggable.svg\");\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tPostbox expand / collapse icon\n*\n*--------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group, \n.post-type-acf-field-group #acf-field-group-fields,\n.post-type-acf-field-group #acf-field-group-options,\n.post-type-acf-field-group .postbox {\n\t.postbox-header .handle-actions {\n\t\tdisplay: flex;\n\n\t\t.toggle-indicator:before {\n\t\t\tcontent: \"\";\n\t\t\t$icon-size: 20px;\n\t\t\tdisplay: inline-flex;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tbackground-color: currentColor;\n\t\t\tborder: none;\n\t\t\tborder-radius: 0;\n\t\t\t-webkit-mask-size: contain;\n\t\t\tmask-size: contain;\n\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\tmask-repeat: no-repeat;\n\t\t\t-webkit-mask-position: center;\n\t\t\tmask-position: center;\n\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n\t\t\tmask-image: url(\"../../images/icons/icon-chevron-up.svg\");\n\t\t}\n\t}\n\n\t// Closed state\n\t&.closed {\n\t\t.postbox-header .handle-actions {\n\t\t\t.toggle-indicator:before {\n\t\t\t\t-webkit-mask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t\tmask-image: url(\"../../images/icons/icon-chevron-down.svg\");\n\t\t\t}\n\t\t}\n\t}\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Tools & updates page heading icons\n*\n*---------------------------------------------------------------------------------------------*/\n.post-type-acf-field-group {\n\t#acf-admin-tool-export,\n\t#acf-admin-tool-import,\n\t#acf-license-information,\n\t#acf-update-information {\n\t\th2,\n\t\th3 {\n\t\t\tdisplay: inline-flex;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: stretch;\n\t\t\talign-items: center;\n\n\t\t\t&:before {\n\t\t\t\tcontent: \"\";\n\t\t\t\t$icon-size: 20px;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 8px;\n\t\t\t\t}\n\t\t\t\tbackground-color: $gray-400;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t}\n\t\t}\n\t}\n\n\t&.rtl {\n\t\t#acf-admin-tool-export,\n\t\t#acf-admin-tool-import,\n\t\t#acf-license-information,\n\t\t#acf-update-information {\n\t\t\th2,\n\t\t\th3 {\n\t\t\t\t&:before {\n\t\t\t\t\tmargin: {\n\t\t\t\t\t\tright: 0;\n\t\t\t\t\t\tleft: 8px;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n// Export icon\n.post-type-acf-field-group #acf-admin-tool-export h2:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-export.svg\");\n\tmask-image: url(\"../../images/icons/icon-export.svg\");\n}\n\n// Import icon\n.post-type-acf-field-group #acf-admin-tool-import h2:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-import.svg\");\n\tmask-image: url(\"../../images/icons/icon-import.svg\");\n}\n\n// License information icon\n.post-type-acf-field-group #acf-license-information h3:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-key.svg\");\n\tmask-image: url(\"../../images/icons/icon-key.svg\");\n}\n\n// Update information icon\n.post-type-acf-field-group #acf-update-information h3:before {\n\t-webkit-mask-image: url(\"../../images/icons/icon-info.svg\");\n\tmask-image: url(\"../../images/icons/icon-info.svg\");\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tAdmin field icons\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-single-field-group .acf-input {\n\t.acf-icon {\n\t\t$icon-size: 18px;\n\t\twidth: $icon-size;\n\t\theight: $icon-size;\n\t}\n}\n", "/*--------------------------------------------------------------------------------------------\n*\n*\tField type icon base styling\n*\n*--------------------------------------------------------------------------------------------*/\n.field-type-icon {\n\tbox-sizing: border-box;\n\tdisplay: inline-flex;\n\talign-content: center;\n\talign-items: center;\n\tjustify-content: center;\n\tposition: relative;\n\twidth: 24px;\n\theight: 24px;\n\ttop: -4px;\n\tbackground-color: $blue-50;\n\tborder: {\n\t\twidth: 1px;\n\t\tstyle: solid;\n\t\tcolor: $blue-200;\n\t};\n\tborder-radius: 100%;\n\n\t&:before {\n\t\t$icon-size: 14px;\n\t\tcontent: \"\";\n\t\twidth: $icon-size;\n\t\theight: $icon-size;\n\t\tposition: relative;\n\t\tbackground-color: $blue-500;\n\t\t-webkit-mask-size: cover;\n\t\tmask-size: cover;\n\t\t-webkit-mask-repeat: no-repeat;\n\t\tmask-repeat: no-repeat;\n\t\t-webkit-mask-position: center;\n\t\tmask-position: center;\n\t\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-default.svg');\n\t\tmask-image: url('../../images/field-type-icons/icon-field-default.svg');\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*\tField type icons\n*\n*--------------------------------------------------------------------------------------------*/\n\n// Text field\n.field-type-icon.field-type-icon-text:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-text.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-text.svg');\n}\n\n// Textarea\n.field-type-icon.field-type-icon-textarea:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-textarea.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-textarea.svg');\n}\n\n// Textarea\n.field-type-icon.field-type-icon-textarea:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-textarea.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-textarea.svg');\n}\n\n// Number\n.field-type-icon.field-type-icon-number:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-number.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-number.svg');\n}\n\n// Range\n.field-type-icon.field-type-icon-range:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-range.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-range.svg');\n}\n\n// Email\n.field-type-icon.field-type-icon-email:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-email.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-email.svg');\n}\n\n// URL\n.field-type-icon.field-type-icon-url:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-url.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-url.svg');\n}\n\n// Password\n.field-type-icon.field-type-icon-password:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-password.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-password.svg');\n}\n\n// Image\n.field-type-icon.field-type-icon-image:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-image.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-image.svg');\n}\n\n// File\n.field-type-icon.field-type-icon-file:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-file.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-file.svg');\n}\n\n// WYSIWYG\n.field-type-icon.field-type-icon-wysiwyg:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-wysiwyg.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-wysiwyg.svg');\n}\n\n// oEmbed\n.field-type-icon.field-type-icon-oembed:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-oembed.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-oembed.svg');\n}\n\n// Gallery\n.field-type-icon.field-type-icon-gallery:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-gallery.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-gallery.svg');\n}\n\n// Select\n.field-type-icon.field-type-icon-select:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-select.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-select.svg');\n}\n\n// Checkbox\n.field-type-icon.field-type-icon-checkbox:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-checkbox.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-checkbox.svg');\n}\n\n// Radio Button\n.field-type-icon.field-type-icon-radio:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-radio.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-radio.svg');\n}\n\n// Button Group\n.field-type-icon.field-type-icon-button-group:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-button-group.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-button-group.svg');\n}\n\n// True / False\n.field-type-icon.field-type-icon-true-false:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-true-false.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-true-false.svg');\n}\n\n// Link\n.field-type-icon.field-type-icon-link:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-link.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-link.svg');\n}\n\n// Post Object\n.field-type-icon.field-type-icon-post-object:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-post-object.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-post-object.svg');\n}\n\n// Page Link\n.field-type-icon.field-type-icon-page-link:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-page-link.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-page-link.svg');\n}\n\n// Relationship\n.field-type-icon.field-type-icon-relationship:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-relationship.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-relationship.svg');\n}\n\n// Taxonomy\n.field-type-icon.field-type-icon-taxonomy:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-taxonomy.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-taxonomy.svg');\n}\n\n// User\n.field-type-icon.field-type-icon-user:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-user.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-user.svg');\n}\n\n// Google Map\n.field-type-icon.field-type-icon-google-map:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-google-map.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-google-map.svg');\n}\n\n// Date Picker\n.field-type-icon.field-type-icon-date-picker:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-date-picker.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-date-picker.svg');\n}\n\n// Date / Time Picker\n.field-type-icon.field-type-icon-date-time-picker:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-date-time-picker.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-date-time-picker.svg');\n}\n\n// Time Picker\n.field-type-icon.field-type-icon-time-picker:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-time-picker.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-time-picker.svg');\n}\n\n// Color Picker\n.field-type-icon.field-type-icon-color-picker:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-color-picker.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-color-picker.svg');\n}\n\n// Message\n.field-type-icon.field-type-icon-message:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-message.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-message.svg');\n}\n\n// Accordion\n.field-type-icon.field-type-icon-accordion:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-accordion.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-accordion.svg');\n}\n\n// Tab\n.field-type-icon.field-type-icon-tab:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-tab.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-tab.svg');\n}\n\n// Group\n.field-type-icon.field-type-icon-group:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-group.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-group.svg');\n}\n\n// Repeater\n.field-type-icon.field-type-icon-repeater:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-repeater.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-repeater.svg');\n}\n\n\n// Flexible Content\n.field-type-icon.field-type-icon-flexible-content:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-flexible-content.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-flexible-content.svg');\n}\n\n// Clone\n.field-type-icon.field-type-icon-clone:before {\n\t-webkit-mask-image: url('../../images/field-type-icons/icon-field-clone.svg');\n\tmask-image: url('../../images/field-type-icons/icon-field-clone.svg');\n}", "/*---------------------------------------------------------------------------------------------\n*\n* Tools page layout\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-admin-tools {\n\n\t.postbox-header {\n\t\tdisplay: none; // Hide native WP postbox headers\n\t}\n\n\t.acf-meta-box-wrap.-grid {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\n\t\t.postbox {\n\t\t\twidth: 100%;\n\t\t\tclear: none;\n\t\t\tfloat: none;\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\n\t\t\t@media screen and (max-width: $md) {\n\t\t\t\tflex: 1 1 100%;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t.acf-meta-box-wrap.-grid .postbox:nth-child(odd) {\n\t\tmargin: {\n\t\t\tleft: 0;\n\t\t};\n\t}\n\n\t.meta-box-sortables {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(2, 1fr);\n\t\tgrid-template-rows: repeat(1, 1fr);\n\t\tgrid-column-gap: 32px;\n\t\tgrid-row-gap: 32px;\n\n\t\t@media screen and (max-width: $md) {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: flex-start;\n\t\t\talign-items: center;\n\t\t\tgrid-column-gap: 8px;\n\t\t\tgrid-row-gap: 8px;\n\t\t}\n\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* Tools export pages\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-admin-tools {\n\n\t&.tool-export {\n\n\t\t.inside {\n\t\t\tmargin: 0;\n\t\t}\n\n\t\t// ACF custom postbox header\n\t\t.acf-postbox-header {\n\t\t\tmargin: {\n\t\t\t\tbottom: 24px;\n\t\t\t};\n\t\t}\n\n\t\t// Main postbox area\n\t\t.acf-postbox-main {\n\t\t\tborder: none;\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t}\n\n\t\t.acf-postbox-columns {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 280px;\n\t\t\t\tbottom: 0;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\tpadding: 0;\n\n\t\t\t.acf-postbox-side {\n\t\t\t\tpadding: 0;\n\n\t\t\t\t.acf-panel {\n\t\t\t\t\tmargin: 0;\n\t\t\t\t\tpadding: 0;\n\t\t\t\t}\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t\t.acf-btn {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t.meta-box-sortables {\n\t\t\tdisplay: block;\n\t\t}\n\n\t\t.acf-panel {\n\t\t\tborder: none;\n\n\t\t\th3 {\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: 0;\n\t\t\t\tcolor: $gray-700;\n\t\t\t\t@extend .p4;\n\n\t\t\t\t&:before {\n\t\t\t\t\tdisplay: none;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-checkbox-list {\n\t\t\tmargin: {\n\t\t\t\ttop: 16px;\n\t\t\t};\n\t\t\tborder: {\n\t\t\t\twidth: 1px;\n\t\t\t\tstyle: solid;\n\t\t\t\tcolor: $gray-300;\n\t\t\t};\n\t\t\tborder-radius: $radius-md;\n\n\t\t\tli {\n\t\t\t\tdisplay: inline-flex;\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 48px;\n\t\t\t\talign-items: center;\n\t\t\t\tmargin: 0;\n\t\t\t\tpadding: {\n\t\t\t\t\tright: 12px;\n\t\t\t\t\tleft: 12px;\n\t\t\t\t};\n\t\t\t\tborder-bottom: {\n\t\t\t\t\twidth: 1px;\n\t\t\t\t\tstyle: solid;\n\t\t\t\t\tcolor: $gray-200;\n\t\t\t\t};\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tborder-bottom: none;\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}", "/*---------------------------------------------------------------------------------------------\n*\n*  Updates layout\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates {\n\tdisplay: flex;\n\tflex-direction: row;\n\tflex-wrap: wrap;\n\tjustify-content: flex-start;\n\talign-content: flex-start;\n\talign-items: flex-start;\n}\n\n.custom-fields_page_acf-settings-updates .acf-admin-notice,\n.custom-fields_page_acf-settings-updates .acf-upgrade-notice,\n.custom-fields_page_acf-settings-updates .notice {\n\tflex: 1 1 100%;\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  ACF Box\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates {\n\n\t.acf-box {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t};\n\n\t\t.inner {\n\t\t\tpadding: {\n\t\t\t\ttop: 24px;\n\t\t\t\tright: 24px;\n\t\t\t\tbottom: 24px;\n\t\t\t\tleft: 24px;\n\t\t\t};\n\t\t}\n\n\t\t@media screen and (max-width: $md) {\n\t\t\tflex: 1 1 100%;\n\t\t}\n\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Notices\n*\n*---------------------------------------------------------------------------------------------*/\n.acf-settings-wrap.acf-updates {\n\n\t.acf-admin-notice {\n\t\tflex: 1 1 100%;\n\t\tmargin: {\n\t\t\ttop: 16px;\n\t\t\tright: 0;\n\t\t\tleft: 0;\n\t\t};\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n* License information\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-license-information {\n\tflex: 1 1 65%;\n\tmargin: {\n\t\tright: 32px;\n\t};\n\t\n\t@media screen and (max-width: 1024px) {\n\t\tmargin: {\n\t\t\tright: 0;\n\t\t\tbottom: 32px;\n\t\t};\n\t}\n\n\t.acf-activation-form {\n\t\tmargin: {\n\t\t\ttop: 24px;\n\t\t};\n\t}\n\n\tlabel {\n\t\tfont-weight: 500;\n\t}\n\n\t.acf-input-wrap {\n\t\tmargin: {\n\t\t\ttop: 8px;\n\t\t\tbottom: 24px;\n\t\t};\n\t}\n\n\t#acf_pro_license {\n\t\twidth: 100%;\n\t}\n\n}\n\n/*---------------------------------------------------------------------------------------------\n*\n*  Update information table\n*\n*---------------------------------------------------------------------------------------------*/\n#acf-update-information {\n\tflex: 1 1 35%;\n\tmax-width: calc(35% - 32px);\n\n\t.form-table {\n\n\t\tth,\n\t\ttd {\n\t\t\tpadding: {\n\t\t\t\ttop: 0;\n\t\t\t\tright: 0;\n\t\t\t\tbottom: 24px;\n\t\t\t\tleft: 0;\n\t\t\t};\n\t\t\t@extend .p4;\n\t\t\tcolor: $gray-700;\n\t\t}\n\n\t}\n\n\t.acf-update-changelog {\n\t\tmargin: {\n\t\t\ttop: 8px;\n\t\t\tbottom: 24px;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 8px;\n\t\t};\n\t\tborder-top: {\n\t\t\twidth: 1px;\n\t\t\tstyle: solid;\n\t\t\tcolor: $gray-200;\n\t\t};\n\t\tcolor: $gray-700;\n\n\t\th4 {\n\t\t\tmargin: {\n\t\t\t\tbottom: 0;\n\t\t\t};\n\t\t}\n\n\t\tp {\n\t\t\tmargin: {\n\t\t\t\ttop: 0;\n\t\t\t\tbottom: 16px;\n\t\t\t};\n\n\t\t\t&:last-of-type {\n\t\t\t\tmargin: {\n\t\t\t\t\tbottom: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tem {\n\t\t\t\t@extend .p6;\n\t\t\t\tcolor: $gray-500;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t.acf-btn {\n\t\tdisplay: inline-flex;\n\t}\n\n}", "/*--------------------------------------------------------------------------------------------\n*\n*\tHeader upsell button\n*\n*--------------------------------------------------------------------------------------------*/\n.acf-admin-toolbar {\n\n\ta.acf-admin-toolbar-upgrade-btn {\n\t\tdisplay: inline-flex;\n\t\talign-items: center;\n\t\talign-self: stretch;\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tright: 16px;\n\t\t\tbottom: 0;\n\t\t\tleft: 16px;\n\t\t};\n\t\tbackground: $gradient-pro;\n\t\tbackground-size: 180% 80%;\n\t\tbackground-position: 100% 0;\n\t\ttransition: background-position .5s;\n\t\tborder-radius: $radius-md;\n\t\ttext-decoration: none;\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t&:hover {\n\t\t\tbackground-position: 0 0;\n\t\t}\n\n\t\t&:focus {\n\t\t\tborder: none;\n\t\t\toutline: none;\n\t\t\tbox-shadow: none;\n\t\t}\n\n\t\tp {\n\t\t\tmargin: 0;\n\t\t\tpadding: {\n\t\t\t\ttop: 8px;\n\t\t\t\tbottom: 8px;\n\t\t\t}\n\t\t\t@extend .p4;\n\t\t\tfont-weight: normal;\n\t\t\ttext-transform: none;\n\t\t\tcolor: #fff;\n\t\t}\n\n\t\t.acf-icon {\n\t\t\t$icon-size: 18px;\n\t\t\twidth: $icon-size;\n\t\t\theight: $icon-size;\n\t\t\tmargin: {\n\t\t\t\tright: 6px;\n\t\t\t\tleft: -2px;\n\t\t\t};\n\t\t\tbackground-color: $gray-50;\n\t\t}\n\n\t}\n\n}\n\n/*--------------------------------------------------------------------------------------------\n*\n*  Upsell block\n*\n*--------------------------------------------------------------------------------------------*/\n#acf-field-group-pro-features {\n\n\t.acf-field-group-pro-features-wrapper {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\talign-content: stretch;\n\t\talign-items: center;\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tflex-direction: row;\n\t\t\tflex-wrap: wrap;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: flex-start;\n\t\t\talign-items: flex-start;\n\n\t\t\t.acf-field-group-pro-features-content,\n\t\t\t.acf-field-group-pro-features-actions {\n\t\t\t\tflex: 0 1 100%;\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-field-group-pro-features-content {\n\t\t\tflex: 1 1 auto;\n\t\t\tmargin: {\n\t\t\t\tright: 40px;\n\t\t\t};\n\n\t\t\t@media screen and (max-width: 768px) {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 0;\n\t\t\t\t\tbottom: 8px;\n\t\t\t\t};\n\t\t\t}\n\n\t\t}\n\n\t\t.acf-field-group-pro-features-actions {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\tjustify-content: flex-end;\n\t\t\tmin-width: 160px;\n\n\t\t\t@media screen and (max-width: 768px) {\n\t\t\t\tjustify-content: flex-start;\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\t&.postbox {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmin-height: 120px;\n\t\tbackground-image: linear-gradient(to right, #1d4373, #24437e, #304288, #413f8f, #543a95);\n\t\tcolor: $gray-200;\n\n\t\t.postbox-header {\n\t\t\tdisplay: none;\n\t\t}\n\n\t\t.inside {\n\t\t\twidth: 100%;\n\t\t\tborder: none;\n\t\t}\n\n\t}\n\n\th1 {\n\t\tmargin: {\n\t\t\ttop: 0;\n\t\t\tbottom: 4px;\n\t\t};\n\t\tpadding: {\n\t\t\ttop: 0;\n\t\t\tbottom: 0;\n\t\t};\n\t\t@extend .p1;\n\t\tfont-weight: bold;\n\t\tcolor: $gray-50;\n\n\t\t.acf-icon {\n\t\t\tmargin: {\n\t\t\t\tright: 8px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t// Upsell block btn\n\t.acf-btn {\n\t\tdisplay: inline-flex;\n\t\tbackground-color: rgba(#fff,.2);\n\t\tborder: none;\n\n\t\t&:hover {\n\t\t\tbackground-color: rgba(#fff,.3);\n\t\t}\n\n\t\t.acf-icon {\n\t\t\tmargin: {\n\t\t\t\tright: -2px;\n\t\t\t\tleft: 8px;\n\t\t\t};\n\t\t}\n\n\t}\n\n\t// Features list\n\t.acf-pro-features-list  {\n\t\tdisplay: flex;\n\t\tjustify-content: flex-start;\n\t\talign-items: center;\n\t\tflex-wrap: wrap;\n\t\tmargin: {\n\t\t\ttop: 16px;\n\t\t\tbottom: 0;\n\t\t};\n\n\t\t@media screen and (max-width: 768px) {\n\t\t\tflex-direction: row;\n\t\t\tflex-wrap: wrap;\n\t\t\tjustify-content: flex-start;\n\t\t\talign-content: flex-start;\n\t\t\talign-items: flex-start;\n\t\t}\n\n\t\tli {\n\t\t\tdisplay: flex;\n\t\t\tbox-sizing: border-box;\n\t\t\tmargin: {\n\t\t\t\tright: 32px;\n\t\t\t\tbottom: 6px;\n\t\t\t};\n\n\t\t\t@media screen and (max-width: 880px) {\n\t\t\t\tflex: 0 1 calc(33.3% - 32px);\n\t\t\t}\n\n\t\t\t@media screen and (max-width: $sm) {\n\t\t\t\tflex: 0 1 100%;\n\t\t\t}\n\n\t\t\t&:last-child {\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 0;\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t&:before {\n\t\t\t\tcontent: '';\n\t\t\t\t$icon-size: 16px;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\twidth: $icon-size;\n\t\t\t\theight: $icon-size;\n\t\t\t\tmargin: {\n\t\t\t\t\tright: 8px;\n\t\t\t\t};\n\t\t\t\tbackground-color: $color-success;\n\t\t\t\tborder: none;\n\t\t\t\tborder-radius: 0;\n\t\t\t\t-webkit-mask-size: contain;\n\t\t\t\tmask-size: contain;\n\t\t\t\t-webkit-mask-repeat: no-repeat;\n\t\t\t\tmask-repeat: no-repeat;\n\t\t\t\t-webkit-mask-position: center;\n\t\t\t\tmask-position: center;\n\t\t\t\t-webkit-mask-image: url('../../images/icons/icon-check-circle-solid.svg');\n\t\t\t\tmask-image: url('../../images/icons/icon-check-circle-solid.svg');\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\n"], "names": [], "sourceRoot": ""}