(()=>{var e={436:()=>{!function(e){var t=acf.FieldSetting.extend({type:"clone",name:"display",render:function(){var e=this.field.val();this.$fieldObject.attr("data-display",e)}});acf.registerFieldSetting(t);var i=acf.FieldSetting.extend({type:"clone",name:"prefix_label",render:function(){var e="";this.field.val()&&(e=this.fieldObject.prop("label")+" "),this.$("code").html(e+"%field_label%")}});acf.registerFieldSetting(i);var a=acf.FieldSetting.extend({type:"clone",name:"prefix_name",render:function(){var e="";this.field.val()&&(e=this.fieldObject.prop("name")+"_"),this.$("code").html(e+"%field_name%")}});acf.registerFieldSetting(a),new acf.Model({filters:{select2_args:"select2Args"},select2Args:function(e,t,i,a,l){return"acf/fields/clone/query"==i.ajaxAction&&(e.closeOnSelect=!1,l.data.ajaxData=this.ajaxData),e},ajaxData:function(t){return t.fields={},acf.getFieldObjects().map((function(e){t.fields[e.prop("key")]={key:e.prop("key"),type:e.prop("type"),label:e.prop("label"),ancestors:e.getParents().length}})),t.title=e("#title").val(),t}})}(jQuery)},309:()=>{var e,t;e=jQuery,t=acf.FieldSetting.extend({type:"flexible_content",name:"fc_layout",events:{"blur .layout-label":"onChangeLabel","click .add-layout":"onClickAdd","click .acf-field-settings-fc_head":"onClickEdit","click .acf-field-setting-fc-duplicate":"onClickDuplicate","click .acf-field-setting-fc-delete":"onClickDelete","changed:layoutLabel":"updateLayoutTitles"},$input:function(t){return e("#"+this.getInputId()+"-"+t)},$list:function(){return this.$(".acf-field-list:first")},getInputId:function(){return this.fieldObject.getInputId()+"-layouts-"+this.field.get("id")},getFields:function(){return acf.getFieldObjects({parent:this.$el})},getChildren:function(){return acf.getFieldObjects({list:this.$list()})},initialize:function(){var e=this.$el.parent();e.hasClass("ui-sortable")||e.sortable({items:"> .acf-field-setting-fc_layout",handle:".acf-fc_draggable",forceHelperSize:!0,forcePlaceholderSize:!0,scroll:!0,stop:this.proxy((function(e,t){this.fieldObject.save()}))}),this.updateFieldLayouts(),this.updateLayoutTitles()},updateFieldLayouts:function(){this.getChildren().map(this.updateFieldLayout,this)},updateFieldLayout:function(e){e.prop("parent_layout",this.get("id"))},updateLayoutTitles:function(){const e=this.get("layoutLabel"),t=this.$el.find("> .acf-label .acf-fc-layout-name");e&&t.html(e)},onClickEdit:function(t){const i=e(t.target);i.hasClass("acf-btn")||i.parent().hasClass("acf-btn")||(this.isOpen()?this.close():this.open())},isOpen:function(e){return this.$el.children(".acf-field-layout-settings").hasClass("open")},open:function(e,t){const i=e?e.children(".acf-field-layout-settings"):this.$el.children(".acf-field-layout-settings"),a=e?e.find(".toggle-indicator").first():this.$el.find(".toggle-indicator").first();acf.doAction("show",i),t?i.slideDown({complete:function(){i.find(".layout-label").trigger("focus")}}):i.slideDown(),a.addClass("open"),a.hasClass("closed")&&a.removeClass("closed"),i.addClass("open")},close:function(){const e=this.$el.children(".acf-field-layout-settings"),t=this.$el.find(".toggle-indicator").first();e.slideUp(),e.removeClass("open"),t.removeClass("open"),t.hasClass("closed")||t.addClass("closed"),acf.doAction("hide",e)},onChangeLabel:function(e,t){var i=t.val();this.set("layoutLabel",i),this.$el.attr("data-layout-label",i);var a=this.$input("name");""==a.val()&&acf.val(a,acf.strSanitize(i))},onClickAdd:function(e,t){e.preventDefault();var i=this.get("id"),a=acf.uniqid("layout_");$layout=acf.duplicate({$el:this.$el,search:i,replace:a,after:function(e,t){var i=t.find(".acf-field-list:first");i.children(".acf-field-object").remove(),i.addClass("-empty"),t.attr("data-layout-label",""),t.find(".acf-fc-meta input").val(""),t.find(".acf-fc-layout-name").html(acf.__("Layout"))}});var l=acf.getFieldSetting($layout);l.$input("key").val(a),this.isOpen()?l.$el.find(".layout-label").trigger("focus"):this.open(l.$el,!0),this.fieldObject.save()},onClickDuplicate:function(e,t){e.preventDefault();var i=this.get("id"),a=acf.uniqid("layout_");$layout=acf.duplicate({$el:this.$el,search:i,replace:a});var l=acf.getFieldObjects({parent:$layout});l.length&&(l.map((function(e){e.wipe(),e.updateParent()})),acf.doAction("duplicate_field_objects",l,this.fieldObject,this.fieldObject));var n=acf.getFieldSetting($layout);n.$input("key").val(a),this.isOpen()?n.$el.find(".layout-label").trigger("focus"):this.open(n.$el,!0),this.fieldObject.save()},onClickDelete:function(e,t){if(e.preventDefault(),e.shiftKey)return this.delete();this.$el.addClass("-hover"),acf.newTooltip({confirmRemove:!0,target:t,context:this,confirm:function(){this.delete()},cancel:function(){this.$el.removeClass("-hover")}})},delete:function(){if(!this.$el.siblings(".acf-field-setting-fc_layout").length)return alert(acf.__("Flexible Content requires at least 1 layout")),!1;this.getFields().map((function(e){e.delete({animate:!1})})),acf.remove(this.$el),this.fieldObject.save()}}),acf.registerFieldSetting(t),new acf.Model({actions:{sortstop_field_object:"updateParentLayout",change_field_object_parent:"updateParentLayout"},updateParentLayout:function(e){var t=e.getParent();if(t&&"flexible_content"===t.prop("type")){var i=e.$el.closest(".acf-field-setting-fc_layout"),a=acf.getFieldSetting(i);e.has("parent_layout")||e.prop("parent_layout",0),e.prop("parent_layout",a.get("id"))}else e.prop("parent_layout",null)}})},166:()=>{var e;jQuery,e=acf.FieldSetting.extend({type:"repeater",name:"collapsed",events:{"focus select":"onFocus"},onFocus:function(e,t){var i=t,a=[];a.push({label:i.find('option[value=""]').text(),value:""});var l=this.fieldObject.$(".acf-field-list:first");acf.getFieldObjects({list:l}).map((function(e){a.push({label:e.prop("label"),value:e.prop("key")})})),acf.renderSelect(i,a)}}),acf.registerFieldSetting(e)}},t={};function i(a){var l=t[a];if(void 0!==l)return l.exports;var n=t[a]={exports:{}};return e[a](n,n.exports,i),n.exports}i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},i.d=(e,t)=>{for(var a in t)i.o(t,a)&&!i.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";i(166),i(309),i(436)})()})();