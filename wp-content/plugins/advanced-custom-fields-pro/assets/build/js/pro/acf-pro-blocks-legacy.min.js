(()=>{var e={905:()=>{jQuery,acf.jsxNameReplacements={"accent-height":"accentHeight",accentheight:"accentHeight","accept-charset":"acceptCharset",acceptcharset:"acceptCharset",accesskey:"accessKey","alignment-baseline":"alignmentBaseline",alignmentbaseline:"alignmentBaseline",allowedblocks:"allowedBlocks",allowfullscreen:"allowFullScreen",allowreorder:"allowReorder","arabic-form":"arabicForm",arabicform:"arabicForm",attributename:"attributeName",attributetype:"attributeType",autocapitalize:"autoCapitalize",autocomplete:"autoComplete",autocorrect:"autoCorrect",autofocus:"autoFocus",autoplay:"autoPlay",autoreverse:"autoReverse",autosave:"autoSave",basefrequency:"baseFrequency","baseline-shift":"baselineShift",baselineshift:"baselineShift",baseprofile:"baseProfile",calcmode:"calcMode","cap-height":"capHeight",capheight:"capHeight",cellpadding:"cellPadding",cellspacing:"cellSpacing",charset:"charSet",class:"className",classid:"classID",classname:"className","clip-path":"clipPath","clip-rule":"clipRule",clippath:"clipPath",clippathunits:"clipPathUnits",cliprule:"clipRule","color-interpolation":"colorInterpolation","color-interpolation-filters":"colorInterpolationFilters","color-profile":"colorProfile","color-rendering":"colorRendering",colorinterpolation:"colorInterpolation",colorinterpolationfilters:"colorInterpolationFilters",colorprofile:"colorProfile",colorrendering:"colorRendering",colspan:"colSpan",contenteditable:"contentEditable",contentscripttype:"contentScriptType",contentstyletype:"contentStyleType",contextmenu:"contextMenu",controlslist:"controlsList",crossorigin:"crossOrigin",dangerouslysetinnerhtml:"dangerouslySetInnerHTML",datetime:"dateTime",defaultchecked:"defaultChecked",defaultvalue:"defaultValue",diffuseconstant:"diffuseConstant",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback","dominant-baseline":"dominantBaseline",dominantbaseline:"dominantBaseline",edgemode:"edgeMode","enable-background":"enableBackground",enablebackground:"enableBackground",enctype:"encType",enterkeyhint:"enterKeyHint",externalresourcesrequired:"externalResourcesRequired","fill-opacity":"fillOpacity","fill-rule":"fillRule",fillopacity:"fillOpacity",fillrule:"fillRule",filterres:"filterRes",filterunits:"filterUnits","flood-color":"floodColor","flood-opacity":"floodOpacity",floodcolor:"floodColor",floodopacity:"floodOpacity","font-family":"fontFamily","font-size":"fontSize","font-size-adjust":"fontSizeAdjust","font-stretch":"fontStretch","font-style":"fontStyle","font-variant":"fontVariant","font-weight":"fontWeight",fontfamily:"fontFamily",fontsize:"fontSize",fontsizeadjust:"fontSizeAdjust",fontstretch:"fontStretch",fontstyle:"fontStyle",fontvariant:"fontVariant",fontweight:"fontWeight",for:"htmlFor",foreignobject:"foreignObject",formaction:"formAction",formenctype:"formEncType",formmethod:"formMethod",formnovalidate:"formNoValidate",formtarget:"formTarget",frameborder:"frameBorder","glyph-name":"glyphName","glyph-orientation-horizontal":"glyphOrientationHorizontal","glyph-orientation-vertical":"glyphOrientationVertical",glyphname:"glyphName",glyphorientationhorizontal:"glyphOrientationHorizontal",glyphorientationvertical:"glyphOrientationVertical",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits","horiz-adv-x":"horizAdvX","horiz-origin-x":"horizOriginX",horizadvx:"horizAdvX",horizoriginx:"horizOriginX",hreflang:"hrefLang",htmlfor:"htmlFor","http-equiv":"httpEquiv",httpequiv:"httpEquiv","image-rendering":"imageRendering",imagerendering:"imageRendering",innerhtml:"innerHTML",inputmode:"inputMode",itemid:"itemID",itemprop:"itemProp",itemref:"itemRef",itemscope:"itemScope",itemtype:"itemType",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keyparams:"keyParams",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",keytype:"keyType",lengthadjust:"lengthAdjust","letter-spacing":"letterSpacing",letterspacing:"letterSpacing","lighting-color":"lightingColor",lightingcolor:"lightingColor",limitingconeangle:"limitingConeAngle",marginheight:"marginHeight",marginwidth:"marginWidth","marker-end":"markerEnd","marker-mid":"markerMid","marker-start":"markerStart",markerend:"markerEnd",markerheight:"markerHeight",markermid:"markerMid",markerstart:"markerStart",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",maxlength:"maxLength",mediagroup:"mediaGroup",minlength:"minLength",nomodule:"noModule",novalidate:"noValidate",numoctaves:"numOctaves","overline-position":"overlinePosition","overline-thickness":"overlineThickness",overlineposition:"overlinePosition",overlinethickness:"overlineThickness","paint-order":"paintOrder",paintorder:"paintOrder","panose-1":"panose1",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",playsinline:"playsInline","pointer-events":"pointerEvents",pointerevents:"pointerEvents",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",radiogroup:"radioGroup",readonly:"readOnly",referrerpolicy:"referrerPolicy",refx:"refX",refy:"refY","rendering-intent":"renderingIntent",renderingintent:"renderingIntent",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",rowspan:"rowSpan","shape-rendering":"shapeRendering",shaperendering:"shapeRendering",specularconstant:"specularConstant",specularexponent:"specularExponent",spellcheck:"spellCheck",spreadmethod:"spreadMethod",srcdoc:"srcDoc",srclang:"srcLang",srcset:"srcSet",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles","stop-color":"stopColor","stop-opacity":"stopOpacity",stopcolor:"stopColor",stopopacity:"stopOpacity","strikethrough-position":"strikethroughPosition","strikethrough-thickness":"strikethroughThickness",strikethroughposition:"strikethroughPosition",strikethroughthickness:"strikethroughThickness","stroke-dasharray":"strokeDasharray","stroke-dashoffset":"strokeDashoffset","stroke-linecap":"strokeLinecap","stroke-linejoin":"strokeLinejoin","stroke-miterlimit":"strokeMiterlimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth",strokedasharray:"strokeDasharray",strokedashoffset:"strokeDashoffset",strokelinecap:"strokeLinecap",strokelinejoin:"strokeLinejoin",strokemiterlimit:"strokeMiterlimit",strokeopacity:"strokeOpacity",strokewidth:"strokeWidth",suppresscontenteditablewarning:"suppressContentEditableWarning",suppresshydrationwarning:"suppressHydrationWarning",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tabindex:"tabIndex",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",templatelock:"templateLock","text-anchor":"textAnchor","text-decoration":"textDecoration","text-rendering":"textRendering",textanchor:"textAnchor",textdecoration:"textDecoration",textlength:"textLength",textrendering:"textRendering","underline-position":"underlinePosition","underline-thickness":"underlineThickness",underlineposition:"underlinePosition",underlinethickness:"underlineThickness","unicode-bidi":"unicodeBidi","unicode-range":"unicodeRange",unicodebidi:"unicodeBidi",unicoderange:"unicodeRange","units-per-em":"unitsPerEm",unitsperem:"unitsPerEm",usemap:"useMap","v-alphabetic":"vAlphabetic","v-hanging":"vHanging","v-ideographic":"vIdeographic","v-mathematical":"vMathematical",valphabetic:"vAlphabetic","vector-effect":"vectorEffect",vectoreffect:"vectorEffect","vert-adv-y":"vertAdvY","vert-origin-x":"vertOriginX","vert-origin-y":"vertOriginY",vertadvy:"vertAdvY",vertoriginx:"vertOriginX",vertoriginy:"vertOriginY",vhanging:"vHanging",videographic:"vIdeographic",viewbox:"viewBox",viewtarget:"viewTarget",vmathematical:"vMathematical","word-spacing":"wordSpacing",wordspacing:"wordSpacing","writing-mode":"writingMode",writingmode:"writingMode","x-height":"xHeight",xchannelselector:"xChannelSelector",xheight:"xHeight","xlink:actuate":"xlinkActuate","xlink:arcrole":"xlinkArcrole","xlink:href":"xlinkHref","xlink:role":"xlinkRole","xlink:show":"xlinkShow","xlink:title":"xlinkTitle","xlink:type":"xlinkType",xlinkactuate:"xlinkActuate",xlinkarcrole:"xlinkArcrole",xlinkhref:"xlinkHref",xlinkrole:"xlinkRole",xlinkshow:"xlinkShow",xlinktitle:"xlinkTitle",xlinktype:"xlinkType","xml:base":"xmlBase","xml:lang":"xmlLang","xml:space":"xmlSpace",xmlbase:"xmlBase",xmllang:"xmlLang","xmlns:xlink":"xmlnsXlink",xmlnsxlink:"xmlnsXlink",xmlspace:"xmlSpace",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"}},418:e=>{"use strict";var t=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable;function i(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},r=0;r<10;r++)t["_"+String.fromCharCode(r)]=r;if("0123456789"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var n={};return"abcdefghijklmnopqrst".split("").forEach((function(e){n[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},n)).join("")}catch(e){return!1}}()?Object.assign:function(e,o){for(var a,s,l=i(e),c=1;c<arguments.length;c++){for(var p in a=Object(arguments[c]))r.call(a,p)&&(l[p]=a[p]);if(t){s=t(a);for(var u=0;u<s.length;u++)n.call(a,s[u])&&(l[s[u]]=a[s[u]])}}return l}},408:(e,t,r)=>{"use strict";var n=r(418),i=60103;if("function"==typeof Symbol&&Symbol.for){var o=Symbol.for;i=o("react.element"),o("react.portal"),o("react.fragment"),o("react.strict_mode"),o("react.profiler"),o("react.provider"),o("react.context"),o("react.forward_ref"),o("react.suspense"),o("react.memo"),o("react.lazy")}"function"==typeof Symbol&&Symbol.iterator;function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var s={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},l={};function c(e,t,r){this.props=e,this.context=t,this.refs=l,this.updater=r||s}function p(){}function u(e,t,r){this.props=e,this.context=t,this.refs=l,this.updater=r||s}c.prototype.isReactComponent={},c.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error(a(85));this.updater.enqueueSetState(this,e,t,"setState")},c.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},p.prototype=c.prototype;var d=u.prototype=new p;d.constructor=u,n(d,c.prototype),d.isPureReactComponent=!0;var h=null,f=Object.prototype.hasOwnProperty,m={key:!0,ref:!0,__self:!0,__source:!0};t.createElement=function(e,t,r){var n,o={},a=null,s=null;if(null!=t)for(n in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)f.call(t,n)&&!m.hasOwnProperty(n)&&(o[n]=t[n]);var l=arguments.length-2;if(1===l)o.children=r;else if(1<l){for(var c=Array(l),p=0;p<l;p++)c[p]=arguments[p+2];o.children=c}if(e&&e.defaultProps)for(n in l=e.defaultProps)void 0===o[n]&&(o[n]=l[n]);return{$$typeof:i,type:e,key:a,ref:s,props:o,_owner:h}}},294:(e,t,r)=>{"use strict";e.exports=r(408)}},t={};function r(n){var i=t[n];if(void 0!==i)return i.exports;var o=t[n]={exports:{}};return e[n](o,o.exports,r),o.exports}(()=>{"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(t,r,n){return(r=function(t){var r=function(t,r){if("object"!==e(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var i=n.call(t,"string");if("object"!==e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"===e(r)?r:String(r)}(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,t}r(905);var n=r(294);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function o(e){for(var r=1;r<arguments.length;r++){var n=null!=arguments[r]?arguments[r]:{};r%2?i(Object(n),!0).forEach((function(r){t(e,r,n[r])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}!function(e,t){const{BlockControls:r,InspectorControls:i,InnerBlocks:a}=wp.blockEditor,{Toolbar:s,IconButton:l,Placeholder:c,Spinner:p}=wp.components,{Fragment:u}=wp.element,{Component:d}=React,{withSelect:h}=wp.data,{createHigherOrderComponent:f}=wp.compose,m={};function g(e){return m[e]||!1}function y(e){var i=e.post_types||[];if(i.length){i.push("wp_block");var o=acf.get("postType");if(-1===i.indexOf(o))return!1}if("string"==typeof e.icon&&"<svg"===e.icon.substr(0,4)){const t=e.icon;e.icon=(0,n.createElement)(T,null,t)}e.icon||delete e.icon,wp.blocks.getCategories().filter((t=>t.slug===e.category)).pop()||(e.category="common");let a={id:{type:"string"},name:{type:"string"},data:{type:"object"},align:{type:"string"},mode:{type:"string"}},s=j,l=E;e.supports.align_text&&(a=function(e){return e.align_text={type:"string"},e}(a),s=function(e,t){const i=B;return t.align_text=i(t.align_text),class extends d{render(){const{attributes:t,setAttributes:o}=this.props,{align_text:a}=t;return(0,n.createElement)(u,null,(0,n.createElement)(r,null,(0,n.createElement)(z,{value:i(a),onChange:function(e){o({align_text:i(e)})}})),(0,n.createElement)(e,this.props))}}}(s,e)),e.supports.align_content&&(a=function(e){return e.align_content={type:"string"},e}(a),s=function(e,i){let o,a,s=i.supports.align_content;return"matrix"===s?(o=L||I,a=q):(o=H,a=D),o===t?(console.warn(`The "${s}" alignment component was not found.`),e):(i.align_content=a(i.align_content),class extends d{render(){const{attributes:t,setAttributes:i}=this.props,{align_content:s}=t;return(0,n.createElement)(u,null,(0,n.createElement)(r,{group:"block"},(0,n.createElement)(o,{label:acf.__("Change content alignment"),value:a(s),onChange:function(e){i({align_content:a(e)})}})),(0,n.createElement)(e,this.props))}})}(s,e)),e=acf.parseArgs(e,{title:"",name:"",category:"",attributes:a,edit:function(e){return(0,n.createElement)(s,e)},save:function(e){return(0,n.createElement)(l,e)}});for(const t in e.attributes)delete e.attributes[t].default;m[e.name]=e;var c=wp.blocks.registerBlockType(e.name,e);return c.attributes.anchor&&(c.attributes.anchor={type:"string"}),c}const b={};function k(t){const{attributes:r={},query:n={},delay:i=0}=t,{id:a}=r,s=b[a]||{query:{},timeout:!1,promise:e.Deferred()};return s.query=o(o({},s.query),n),clearTimeout(s.timeout),s.timeout=setTimeout((function(){e.ajax({url:acf.get("ajaxurl"),dataType:"json",type:"post",cache:!1,data:acf.prepareForAjax({action:"acf/ajax/fetch-block",block:JSON.stringify(r),query:s.query})}).always((function(){b[a]=null})).done((function(){s.promise.resolve.apply(this,arguments)})).fail((function(){s.promise.reject.apply(this,arguments)}))}),i),b[a]=s,s.promise}function v(e,t){return JSON.stringify(e)===JSON.stringify(t)}function x(e){var t=function(e){switch(e){case"innerblocks":return a;case"script":return C;case"#comment":return null;default:e=w(e)}return e}(e.nodeName.toLowerCase());if(!t)return null;var r={};acf.arrayArgs(e.attributes).map(S).forEach((function(e){r[e.name]=e.value}));var n=[t,r];return acf.arrayArgs(e.childNodes).forEach((function(e){if(e instanceof Text){var t=e.textContent;t&&n.push(t)}else n.push(x(e))})),React.createElement.apply(this,n)}function w(e){return acf.isget(acf,"jsxNameReplacements",e)||e}function S(e){var t=e.name,r=e.value;switch(t){case"class":t="className";break;case"style":var n={};r.split(";").forEach((function(e){var t=e.indexOf(":");if(t>0){var r=e.substr(0,t).trim(),i=e.substr(t+1).trim();"-"!==r.charAt(0)&&(r=acf.strCamelCase(r)),n[r]=i}})),r=n;break;default:if(0===t.indexOf("data-"))break;t=w(t);var i=r.charAt(0);"["!==i&&"{"!==i||(r=JSON.parse(r)),"true"!==r&&"false"!==r||(r="true"===r)}return{name:t,value:r}}acf.parseJSX=function(t){return x(e(t)[0])};var O=f((function(e){return class extends d{constructor(e){super(e);const{name:r,attributes:n}=this.props,i=g(r);if(i)if(function(e){return!e.attributes.id}(e)){n.id=acf.uniqid("block_");for(let e in i.attributes)n[e]===t&&i[e]!==t&&(n[e]=i[e])}else(function(e){return function(e){let t=(wp.data.select("core/block-editor")||wp.data.select("core/editor")).getBlocks(),r=0;for(;r<t.length;)t=t.concat(t[r].innerBlocks),r++;for(var n in e)t=t.filter((t=>t.attributes[n]===e[n]));return t}().filter((t=>t.attributes.id===e.attributes.id)).filter((t=>t.clientId!==e.clientId)).length})(e)&&(n.id=acf.uniqid("block_"))}render(){return(0,n.createElement)(e,this.props)}}}),"withDefaultAttributes");function E(){return(0,n.createElement)(a.Content,null)}wp.hooks.addFilter("editor.BlockListBlock","acf/with-default-attributes",O);class j extends d{constructor(e){super(e),this.setup()}setup(){const{name:e,attributes:t}=this.props;function r(e){-1===e.indexOf(t.mode)&&(t.mode=e[0])}switch(g(e).mode){case"edit":r(["edit","preview"]);break;case"preview":r(["preview","edit"]);break;default:r(["auto"])}}render(){const{name:e,attributes:t,setAttributes:o}=this.props,{mode:a}=t;let c=g(e).supports.mode;"auto"===a&&(c=!1);const p="preview"===a?acf.__("Switch to Edit"):acf.__("Switch to Preview"),d="preview"===a?"edit":"welcome-view-site";return(0,n.createElement)(u,null,(0,n.createElement)(r,null,c&&(0,n.createElement)(s,null,(0,n.createElement)(l,{className:"components-icon-button components-toolbar__control",label:p,icon:d,onClick:function(){o({mode:"preview"===a?"edit":"preview"})}}))),(0,n.createElement)(i,null,"preview"===a&&(0,n.createElement)("div",{className:"acf-block-component acf-block-panel"},(0,n.createElement)(_,this.props))),(0,n.createElement)(A,this.props))}}const A=h((function(e,t){const{clientId:r}=t,n=e("core/block-editor").getBlockRootClientId(r);return{index:e("core/block-editor").getBlockIndex(r,n)}}))(class extends d{render(){const{attributes:e,isSelected:t}=this.props,{mode:r}=e;return(0,n.createElement)("div",{className:"acf-block-component acf-block-body"},"auto"===r&&t?(0,n.createElement)(_,this.props):"auto"!==r||t?"preview"===r?(0,n.createElement)(M,this.props):(0,n.createElement)(_,this.props):(0,n.createElement)(M,this.props))}});class T extends d{render(){return(0,n.createElement)("div",{dangerouslySetInnerHTML:{__html:this.props.children}})}}class C extends d{render(){return(0,n.createElement)("div",{ref:e=>this.el=e})}setHTML(t){e(this.el).html(`<script>${t}<\/script>`)}componentDidUpdate(){this.setHTML(this.props.children)}componentDidMount(){this.setHTML(this.props.children)}}const P={};class R extends d{constructor(e){super(e),this.setRef=this.setRef.bind(this),this.id="",this.el=!1,this.subscribed=!0,this.renderMethod="jQuery",this.setup(e),this.loadState()}setup(e){}fetch(){}loadState(){this.state=P[this.id]||{}}setState(e){P[this.id]=o(o({},this.state),e),this.subscribed&&super.setState(e)}setHtml(t){if((t=t?t.trim():"")!==this.state.html){var r={html:t};"jsx"===this.renderMethod?(r.jsx=acf.parseJSX(t),r.$el=e(this.el)):r.$el=e(t),this.setState(r)}}setRef(e){this.el=e}render(){return this.state.jsx?(0,n.createElement)("div",{ref:this.setRef},this.state.jsx):(0,n.createElement)("div",{ref:this.setRef},(0,n.createElement)(c,null,(0,n.createElement)(p,null)))}shouldComponentUpdate(e,t){return e.index!==this.props.index&&this.componentWillMove(),t.html!==this.state.html}display(t){if("jQuery"===this.renderMethod){var r=this.state.$el,n=r.parent(),i=e(this.el);i.html(r),n.length&&n[0]!==i[0]&&n.html(r.clone())}switch(t){case"append":this.componentDidAppend();break;case"remount":this.componentDidRemount()}}componentDidMount(){this.state.html===t?this.fetch():this.display("remount")}componentDidUpdate(e,t){this.display("append")}componentDidAppend(){acf.doAction("append",this.state.$el)}componentWillUnmount(){acf.doAction("unmount",this.state.$el),this.subscribed=!1}componentDidRemount(){this.subscribed=!0,setTimeout((()=>{acf.doAction("remount",this.state.$el)}))}componentWillMove(){acf.doAction("unmount",this.state.$el),setTimeout((()=>{acf.doAction("remount",this.state.$el)}))}}class _ extends R{setup(e){this.id=`BlockForm-${e.attributes.id}`}fetch(){const{attributes:e}=this.props;k({attributes:e,query:{form:!0}}).done((e=>{this.setHtml(e.data.form)}))}componentDidAppend(){super.componentDidAppend();const{attributes:e,setAttributes:r}=this.props,{$el:n}=this.state;function i(){let i=arguments.length>0&&arguments[0]!==t&&arguments[0];const o=acf.serialize(n,`acf-${e.id}`);i?e.data=o:r({data:o})}var o=!1;n.on("change keyup",(function(){clearTimeout(o),o=setTimeout(i,300)})),e.data||i(!0)}}class M extends R{setup(e){this.id=`BlockPreview-${e.attributes.id}`,g(e.name).supports.jsx&&(this.renderMethod="jsx")}fetch(){let e=arguments.length>0&&arguments[0]!==t?arguments[0]:{};const{attributes:r=this.props.attributes,delay:n=0}=e;this.setState({prevAttributes:r}),k({attributes:r,query:{preview:!0},delay:n}).done((e=>{this.setHtml('<div class="acf-block-preview">'+e.data.preview+"</div>")}))}componentDidAppend(){super.componentDidAppend();const{attributes:e}=this.props,{$el:t}=this.state,r=e.name.replace("acf/","");acf.doAction("render_block_preview",t,e),acf.doAction(`render_block_preview/type=${r}`,t,e)}shouldComponentUpdate(e,t){const r=e.attributes,n=this.props.attributes;if(!v(r,n)){let e=0;r.className!==n.className&&(e=300),r.anchor!==n.anchor&&(e=300),this.fetch({attributes:r,delay:e})}return super.shouldComponentUpdate(e,t)}componentDidRemount(){super.componentDidRemount(),v(this.state.prevAttributes,this.props.attributes)||this.fetch()}}function D(e){return["top","center","bottom"].includes(e)?e:"top"}function B(e){const t=acf.get("rtl")?"right":"left";return["left","center","right"].includes(e)?e:t}function q(e){if(e){const[t,r]=e.split(" ");return D(t)+" "+B(r)}return"center center"}acf.addAction("prepare",(function(){wp.blockEditor||(wp.blockEditor=wp.editor);var e=acf.get("blockTypes");e&&e.map(y)}));const{AlignmentToolbar:z,BlockVerticalAlignmentToolbar:H}=wp.blockEditor,I=wp.blockEditor.__experimentalBlockAlignmentMatrixToolbar||wp.blockEditor.BlockAlignmentMatrixToolbar,L=wp.blockEditor.__experimentalBlockAlignmentMatrixControl||wp.blockEditor.BlockAlignmentMatrixControl}(jQuery)})()})();