{"version": 3, "file": "acf-field-group.js", "mappings": ";;;;;;;;;AAAA,CAAE,UAAWA,CAAC,EAAEC,SAAS,EAAG;EAC3B,IAAIC,IAAI,GAAGC,GAAG,CAACC,gBAAgB,CAAED,GAAG,CAAE;;EAEtC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECD,IAAI,CAACG,WAAW,GAAG;IAClBC,UAAU,EAAE,UAAWC,MAAM,EAAEC,IAAI,EAAG;MACrCA,IAAI,GAAGA,IAAI,KAAKP,SAAS,GAAGO,IAAI,GAAG,UAAU;MAC7CL,GAAG,CAACM,cAAc,CAAEF,MAAM,CAAE,CAACG,IAAI,CAAEF,IAAI,CAAE;IAC1C,CAAC;IAEDG,YAAY,EAAE,UAAWJ,MAAM,EAAEK,OAAO,EAAG;MAC1CA,OAAO,GAAGA,OAAO,KAAKX,SAAS,GAAGW,OAAO,GAAG,IAAI;MAChDT,GAAG,CAACM,cAAc,CAAEF,MAAM,CAAE,CAACM,MAAM,CAAE;QACpCD,OAAO,EAAEA;MACV,CAAC,CAAE;IACJ,CAAC;IAEDE,iBAAiB,EAAE,UAAWP,MAAM,EAAEQ,IAAI,EAAEC,KAAK,EAAG;MACnDb,GAAG,CAACM,cAAc,CAAEF,MAAM,CAAE,CAACU,IAAI,CAAEF,IAAI,EAAEC,KAAK,CAAE;IACjD,CAAC;IAEDE,iBAAiB,EAAE,UAAWX,MAAM,EAAEQ,IAAI,EAAG;MAC5CZ,GAAG,CAACM,cAAc,CAAEF,MAAM,CAAE,CAACU,IAAI,CAAEF,IAAI,EAAE,IAAI,CAAE;IAChD;EACD,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECb,IAAI,CAACG,WAAW,CAACc,YAAY,GAAGhB,GAAG,CAACiB,KAAK,CAACC,MAAM,CAAE;IACjD;IACAb,IAAI,EAAE,EAAE;IACRc,CAAC,EAAE,CAAC,CAAC;IACLf,MAAM,EAAE,IAAI;IACZgB,SAAS,EAAE,IAAI;IAEfC,GAAG,EAAE,UAAWA,GAAG,EAAG;MACrB;MACA,IAAIhB,IAAI,GAAG,IAAI,CAACA,IAAI;;MAEpB;MACA;MACA;MACA,IAAIiB,IAAI,GAAGD,GAAG,CAACE,KAAK,CAAE,GAAG,CAAE;MAC3BD,IAAI,CAACE,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE,OAAO,CAAE;MAC5BH,GAAG,GAAGC,IAAI,CAACG,IAAI,CAAE,GAAG,CAAE;;MAEtB;MACA,IAAKpB,IAAI,EAAG;QACXgB,GAAG,IAAI,QAAQ,GAAGhB,IAAI;MACvB;;MAEA;MACA,OAAOgB,GAAG;IACX,CAAC;IAEDK,QAAQ,EAAE,YAAY;MACrB;MACA,IAAIA,QAAQ,GAAG,mBAAmB;MAClC,IAAIrB,IAAI,GAAG,IAAI,CAACA,IAAI;;MAEpB;MACA,IAAKA,IAAI,EAAG;QACXqB,QAAQ,IAAI,GAAG,GAAGrB,IAAI;QACtBqB,QAAQ,GAAG1B,GAAG,CAAC2B,WAAW,CAAE,GAAG,EAAE,GAAG,EAAED,QAAQ,CAAE;MACjD;;MAEA;MACA,OAAOA,QAAQ;IAChB,CAAC;IAEDE,WAAW,EAAE,UAAWhB,IAAI,EAAEiB,QAAQ,EAAG;MACxC;MACA,IAAIZ,KAAK,GAAG,IAAI;;MAEhB;MACAjB,GAAG,CAAC8B,UAAU,CAAE,IAAI,CAACT,GAAG,CAAET,IAAI,CAAE,EAAE,UAAWR,MAAM,EAAG;QACrD;QACAa,KAAK,CAACc,GAAG,CAAE,QAAQ,EAAE3B,MAAM,CAAE;;QAE7B;QACAa,KAAK,CAAEY,QAAQ,CAAE,CAACG,KAAK,CAAEf,KAAK,EAAEgB,SAAS,CAAE;MAC5C,CAAC,CAAE;IACJ,CAAC;IAEDC,WAAW,EAAE,UAAWtB,IAAI,EAAEiB,QAAQ,EAAG;MACxC;MACA,IAAIZ,KAAK,GAAG,IAAI;;MAEhB;MACAjB,GAAG,CAACmC,UAAU,CAAE,IAAI,CAACd,GAAG,CAAET,IAAI,CAAE,EAAE,UAAWR,MAAM,EAAG;QACrD;QACAa,KAAK,CAACc,GAAG,CAAE,QAAQ,EAAE3B,MAAM,CAAE;;QAE7B;QACAa,KAAK,CAAEY,QAAQ,CAAE,CAACG,KAAK,CAAEf,KAAK,EAAEgB,SAAS,CAAE;MAC5C,CAAC,CAAE;IACJ,CAAC;IAEDG,UAAU,EAAE,UAAWxB,IAAI,EAAEiB,QAAQ,EAAG;MACvC;MACA,IAAIZ,KAAK,GAAG,IAAI;MAChB,IAAIoB,KAAK,GAAGzB,IAAI,CAAC0B,MAAM,CAAE,CAAC,EAAE1B,IAAI,CAAC2B,OAAO,CAAE,GAAG,CAAE,CAAE;MACjD,IAAIb,QAAQ,GAAGd,IAAI,CAAC0B,MAAM,CAAE1B,IAAI,CAAC2B,OAAO,CAAE,GAAG,CAAE,GAAG,CAAC,CAAE;MACrD,IAAIC,OAAO,GAAG,IAAI,CAACd,QAAQ,EAAE;;MAE7B;MACA7B,CAAC,CAAE4C,QAAQ,CAAE,CAACC,EAAE,CAAEL,KAAK,EAAEG,OAAO,GAAG,GAAG,GAAGd,QAAQ,EAAE,UAAWiB,CAAC,EAAG;QACjE;QACAA,CAAC,CAACC,GAAG,GAAG/C,CAAC,CAAE,IAAI,CAAE;QACjB8C,CAAC,CAACvC,MAAM,GAAGuC,CAAC,CAACC,GAAG,CAACC,OAAO,CAAE,mBAAmB,CAAE;;QAE/C;QACA5B,KAAK,CAACc,GAAG,CAAE,QAAQ,EAAEY,CAAC,CAACvC,MAAM,CAAE;;QAE/B;QACAa,KAAK,CAAEY,QAAQ,CAAE,CAACG,KAAK,CAAEf,KAAK,EAAE,CAAE0B,CAAC,CAAE,CAAE;MACxC,CAAC,CAAE;IACJ,CAAC;IAEDG,WAAW,EAAE,YAAY;MACxB;MACA,IAAI,CAAC3B,CAAC,GAAG,IAAI,CAACf,MAAM,CAAC2C,IAAI,EAAE;;MAE3B;MACA,IAAI,CAAC3B,SAAS,GAAG,IAAI,CAAChB,MAAM,CAAC4C,IAAI,CAAE,6BAA6B,CAAE;;MAElE;MACA,IAAI,CAACC,KAAK,EAAE;IACb,CAAC;IAEDA,KAAK,EAAE,YAAY;MAClB;IAAA,CACA;IAEDC,OAAO,EAAE,UAAWtC,IAAI,EAAG;MAC1B,OAAO,IAAI,CAACQ,SAAS,CAAC4B,IAAI,CAAE,uBAAuB,GAAGpC,IAAI,CAAE;IAC7D;EACD,CAAC,CAAE;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIuC,aAAa,GAAG,IAAInD,GAAG,CAACoD,KAAK,CAAE;IAClCC,OAAO,EAAE;MACRC,iBAAiB,EAAE,mBAAmB;MACtCC,kBAAkB,EAAE,oBAAoB;MACxCC,gBAAgB,EAAE,kBAAkB;MACpCC,sBAAsB,EAAE,wBAAwB;MAChDC,mBAAmB,EAAE,qBAAqB;MAC1CC,wBAAwB,EAAE,yBAAyB;MACnDC,yBAAyB,EAAE,0BAA0B;MACrDC,wBAAwB,EAAE,yBAAyB;MACnDC,0BAA0B,EAAE,2BAA2B;MACvDC,qBAAqB,EAAE;IACxB,CAAC;IAEDC,iBAAiB,EAAE,UAAWC,KAAK,EAAG;MACrCjE,GAAG,CAACkE,QAAQ,CAAE,YAAY,EAAED,KAAK,CAACrB,GAAG,CAAE;MACvC5C,GAAG,CAACkE,QAAQ,CAAE,kBAAkB,GAAGD,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,EAAEF,KAAK,CAACrB,GAAG,CAAE;MAEnE5C,GAAG,CAACkE,QAAQ,CAAE,uBAAuB,EAAED,KAAK,CAACrB,GAAG,CAAE;MAClD5C,GAAG,CAACkE,QAAQ,CACX,6BAA6B,GAAGD,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,EACnDF,KAAK,CAACrB,GAAG,CACT;IACF,CAAC;IAEDwB,kBAAkB,EAAE,UAAWH,KAAK,EAAG;MACtCjE,GAAG,CAACkE,QAAQ,CAAE,aAAa,EAAED,KAAK,CAACrB,GAAG,CAAE;MACxC5C,GAAG,CAACkE,QAAQ,CACX,mBAAmB,GAAGD,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,EACzCF,KAAK,CAACrB,GAAG,CACT;IACF,CAAC;IAEDyB,gBAAgB,EAAE,UAAWJ,KAAK,EAAG;MACpCjE,GAAG,CAACkE,QAAQ,CAAE,WAAW,EAAED,KAAK,CAACrB,GAAG,CAAE;MACtC5C,GAAG,CAACkE,QAAQ,CAAE,iBAAiB,GAAGD,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,EAAEF,KAAK,CAACrB,GAAG,CAAE;IACnE,CAAC;IAED0B,sBAAsB,EAAE,UAAWL,KAAK,EAAG;MAC1CjE,GAAG,CAACkE,QAAQ,CAAE,iBAAiB,EAAED,KAAK,CAACrB,GAAG,CAAE;MAC5C5C,GAAG,CAACkE,QAAQ,CACX,uBAAuB,GAAGD,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,EAC7CF,KAAK,CAACrB,GAAG,CACT;IACF,CAAC;IAED2B,mBAAmB,EAAE,UAAWN,KAAK,EAAG;MACvCjE,GAAG,CAACkE,QAAQ,CAAE,cAAc,EAAED,KAAK,CAACrB,GAAG,CAAE;MACzC5C,GAAG,CAACkE,QAAQ,CACX,oBAAoB,GAAGD,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,EAC1CF,KAAK,CAACrB,GAAG,CACT;IACF,CAAC;IAED4B,uBAAuB,EAAE,UAAWP,KAAK,EAAG;MAC3CjE,GAAG,CAACkE,QAAQ,CAAE,mBAAmB,EAAED,KAAK,CAACrB,GAAG,CAAE;MAC9C5C,GAAG,CAACkE,QAAQ,CACX,yBAAyB,GAAGD,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,EAC/CF,KAAK,CAACrB,GAAG,CACT;MAED5C,GAAG,CAACkE,QAAQ,CAAE,uBAAuB,EAAED,KAAK,CAACrB,GAAG,CAAE;MAClD5C,GAAG,CAACkE,QAAQ,CACX,6BAA6B,GAAGD,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,EACnDF,KAAK,CAACrB,GAAG,CACT;IACF,CAAC;IAED6B,wBAAwB,EAAE,UAAWR,KAAK,EAAG;MAC5CjE,GAAG,CAACkE,QAAQ,CAAE,oBAAoB,EAAED,KAAK,CAACrB,GAAG,CAAE;MAC/C5C,GAAG,CAACkE,QAAQ,CACX,0BAA0B,GAAGD,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,EAChDF,KAAK,CAACrB,GAAG,CACT;IACF,CAAC;IAED8B,uBAAuB,EAAE,UAAWT,KAAK,EAAG;MAC3CjE,GAAG,CAACkE,QAAQ,CAAE,mBAAmB,EAAED,KAAK,CAACrB,GAAG,CAAE;MAC9C5C,GAAG,CAACkE,QAAQ,CACX,yBAAyB,GAAGD,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,EAC/CF,KAAK,CAACrB,GAAG,CACT;IACF,CAAC;IAED+B,yBAAyB,EAAE,UAAWV,KAAK,EAAG;MAC7CjE,GAAG,CAACkE,QAAQ,CAAE,qBAAqB,EAAED,KAAK,CAACrB,GAAG,CAAE;IACjD;EACD,CAAC,CAAE;AACJ,CAAC,EAAIgC,MAAM,CAAE;;;;;;;;;;ACrQb,CAAE,UAAW/E,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAI+E,4BAA4B,GAAG7E,GAAG,CAAC8E,YAAY,CAAC5D,MAAM,CAAE;IAC3Db,IAAI,EAAE,EAAE;IACRO,IAAI,EAAE,mBAAmB;IACzBmE,MAAM,EAAE;MACP,2BAA2B,EAAE,gBAAgB;MAC7C,8BAA8B,EAAE,iBAAiB;MACjD,6BAA6B,EAAE,cAAc;MAC7C,8BAA8B,EAAE,eAAe;MAC/C,iCAAiC,EAAE,kBAAkB;MACrD,6BAA6B,EAAE,YAAY;MAC3C,gCAAgC,EAAE;IACnC,CAAC;IAEDC,KAAK,EAAE,KAAK;IAEZC,KAAK,EAAE,UAAWD,KAAK,EAAG;MACzB,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,OAAO,IAAI;IACZ,CAAC;IAEDE,QAAQ,EAAE,UAAWtE,IAAI,EAAEC,KAAK,EAAG;MAClC,OAAO,IAAI,CAACmE,KAAK,CAACjC,IAAI,CAACf,KAAK,CAAE,IAAI,CAACgD,KAAK,EAAE/C,SAAS,CAAE;IACtD,CAAC;IAEDkD,MAAM,EAAE,UAAWvE,IAAI,EAAG;MACzB,OAAO,IAAI,CAACoE,KAAK,CAAChC,IAAI,CAAE,kBAAkB,GAAGpC,IAAI,CAAE;IACpD,CAAC;IAEDwE,GAAG,EAAE,UAAWxE,IAAI,EAAG;MACtB,OAAO,IAAI,CAACoE,KAAK,CAAChC,IAAI,CAAE,KAAK,GAAGpC,IAAI,CAAE;IACvC,CAAC;IAEDyE,OAAO,EAAE,YAAY;MACpB,OAAO,IAAI,CAACxF,CAAC,CAAE,oBAAoB,CAAE;IACtC,CAAC;IAEDyF,QAAQ,EAAE,YAAY;MACrB,OAAO,IAAI,CAACzF,CAAC,CAAE,cAAc,CAAE;IAChC,CAAC;IAED0F,OAAO,EAAE,YAAY;MACpB,OAAO,IAAI,CAAC1F,CAAC,CAAE,aAAa,CAAE;IAC/B,CAAC;IAED2F,MAAM,EAAE,YAAY;MACnB,OAAO,IAAI,CAAC3F,CAAC,CAAE,OAAO,CAAE;IACzB,CAAC;IAED4F,SAAS,EAAE,YAAY;MACtB,OAAO,IAAI,CAACC,WAAW,CAAC9C,GAAG,CAACI,IAAI,CAAC,0BAA0B,CAAC;IAC7D,CAAC;IAED2C,IAAI,EAAE,YAAY;MACjB,IAAIC,IAAI,GAAG,IAAI,CAACN,QAAQ,EAAE;MAC1BM,IAAI,CAACC,IAAI,EAAE;MACX7F,GAAG,CAAC8F,MAAM,CAAEF,IAAI,CAAE;IACnB,CAAC;IAEDG,KAAK,EAAE,YAAY;MAClB,IAAIH,IAAI,GAAG,IAAI,CAACN,QAAQ,EAAE;MAC1BM,IAAI,CAACI,IAAI,EAAE;MACXhG,GAAG,CAACiG,OAAO,CAAEL,IAAI,CAAE;IACpB,CAAC;IAEDM,MAAM,EAAE,YAAY;MACnB;MACA,IAAK,IAAI,CAACb,OAAO,EAAE,CAACvE,IAAI,CAAE,SAAS,CAAE,EAAG;QACvC,IAAI,CAAC2E,SAAS,EAAE,CAACU,QAAQ,CAAC,YAAY,CAAC;QACvC,IAAI,CAACC,WAAW,EAAE;QAClB,IAAI,CAACT,IAAI,EAAE;;QAEX;MACD,CAAC,MAAM;QACN,IAAI,CAACF,SAAS,EAAE,CAACY,WAAW,CAAC,YAAY,CAAC;QAC1C,IAAI,CAACN,KAAK,EAAE;MACb;IACD,CAAC;IAEDK,WAAW,EAAE,YAAY;MACxB;MACA,IAAIE,IAAI,GAAG,IAAI;;MAEf;MACA,IAAI,CAACd,MAAM,EAAE,CAACe,IAAI,CAAE,YAAY;QAC/BD,IAAI,CAACE,UAAU,CAAE3G,CAAC,CAAE,IAAI,CAAE,CAAE;MAC7B,CAAC,CAAE;IACJ,CAAC;IAED2G,UAAU,EAAE,UAAWxB,KAAK,EAAG;MAC9B,IAAI,CAACC,KAAK,CAAED,KAAK,CAAE;MACnB,IAAI,CAACyB,WAAW,EAAE;MAClB,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,WAAW,EAAE;IACnB,CAAC;IAEDF,WAAW,EAAE,YAAY;MACxB;MACA,IAAIG,OAAO,GAAG,EAAE;MAChB,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAIC,GAAG,GAAG,IAAI,CAACpB,WAAW,CAACoB,GAAG;MAC9B,IAAIC,OAAO,GAAG,IAAI,CAAC5B,MAAM,CAAE,OAAO,CAAE;;MAEpC;MACAnF,GAAG,CAACgH,eAAe,EAAE,CAACC,GAAG,CAAE,UAAWvB,WAAW,EAAG;QACnD;QACA,IAAIwB,MAAM,GAAG;UACZC,EAAE,EAAEzB,WAAW,CAAC0B,MAAM,EAAE;UACxBC,IAAI,EAAE3B,WAAW,CAAC4B,QAAQ;QAC3B,CAAC;;QAED;QACA,IAAK5B,WAAW,CAACoB,GAAG,KAAKA,GAAG,EAAG;UAC9BI,MAAM,CAACG,IAAI,IAAIrH,GAAG,CAACuH,EAAE,CAAE,cAAc,CAAE;UACvCL,MAAM,CAACM,QAAQ,GAAG,IAAI;QACvB;;QAEA;QACA,IAAIC,cAAc,GAAGzH,GAAG,CAAC0H,iBAAiB,CAAE;UAC3CC,SAAS,EAAEjC,WAAW,CAACkC,OAAO;QAC/B,CAAC,CAAE;;QAEH;QACA,IAAK,CAAEH,cAAc,CAACI,MAAM,EAAG;UAC9BX,MAAM,CAACM,QAAQ,GAAG,IAAI;QACvB;;QAEA;QACA,IAAIM,OAAO,GAAGpC,WAAW,CAACqC,UAAU,EAAE,CAACF,MAAM;QAC7CX,MAAM,CAACG,IAAI,GAAG,IAAI,CAACW,MAAM,CAAEF,OAAO,CAAE,GAAGZ,MAAM,CAACG,IAAI;;QAElD;QACAT,OAAO,CAACqB,IAAI,CAAEf,MAAM,CAAE;MACvB,CAAC,CAAE;;MAEH;MACA,IAAK,CAAEN,OAAO,CAACiB,MAAM,EAAG;QACvBjB,OAAO,CAACqB,IAAI,CAAE;UACbd,EAAE,EAAE,EAAE;UACNE,IAAI,EAAErH,GAAG,CAACuH,EAAE,CAAE,4BAA4B;QAC3C,CAAC,CAAE;MACJ;;MAEA;MACAvH,GAAG,CAACkI,YAAY,CAAEnB,OAAO,EAAEH,OAAO,CAAE;;MAEpC;MACA,IAAI,CAAC1B,QAAQ,CAAE,OAAO,EAAE6B,OAAO,CAACoB,GAAG,EAAE,CAAE;IACxC,CAAC;IAEDzB,cAAc,EAAE,YAAY;MAC3B;MACA,IAAK,CAAE,IAAI,CAACxB,QAAQ,CAAE,OAAO,CAAE,EAAG;QACjC;MACD;;MAEA;MACA,IAAI6B,OAAO,GAAG,IAAI,CAAC5B,MAAM,CAAE,UAAU,CAAE;MACvC,IAAIgD,GAAG,GAAGpB,OAAO,CAACoB,GAAG,EAAE;MACvB,IAAIvB,OAAO,GAAG,EAAE;;MAEhB;MACA;MACA,IAAKG,OAAO,CAACoB,GAAG,EAAE,KAAK,IAAI,EAAG;QAC7BnI,GAAG,CAACkI,YAAY,CAAEnB,OAAO,EAAE,CAC1B;UACCI,EAAE,EAAE,IAAI,CAACjC,QAAQ,CAAE,UAAU,CAAE;UAC/BmC,IAAI,EAAE;QACP,CAAC,CACD,CAAE;MACJ;;MAEA;MACA,IAAIjH,MAAM,GAAGJ,GAAG,CAACoI,eAAe,CAAE,IAAI,CAAClD,QAAQ,CAAE,OAAO,CAAE,CAAE;MAC5D,IAAIjB,KAAK,GAAGjE,GAAG,CAACM,cAAc,CAAEF,MAAM,CAAE;;MAExC;MACA,IAAIqH,cAAc,GAAGzH,GAAG,CAAC0H,iBAAiB,CAAE;QAC3CC,SAAS,EAAE1D,KAAK,CAAC2D,OAAO;MACzB,CAAC,CAAE;;MAEH;MACAH,cAAc,CAACR,GAAG,CAAE,UAAWhG,KAAK,EAAG;QACtC2F,OAAO,CAACqB,IAAI,CAAE;UACbd,EAAE,EAAElG,KAAK,CAACoH,SAAS,CAACC,QAAQ;UAC5BjB,IAAI,EAAEpG,KAAK,CAACoH,SAAS,CAACE;QACvB,CAAC,CAAE;MACJ,CAAC,CAAE;;MAEH;MACAvI,GAAG,CAACkI,YAAY,CAAEnB,OAAO,EAAEH,OAAO,CAAE;;MAEpC;MACA,IAAI,CAAC1B,QAAQ,CAAE,UAAU,EAAE6B,OAAO,CAACoB,GAAG,EAAE,CAAE;IAC3C,CAAC;IAEDxB,WAAW,EAAE,YAAY;MACxB;MACA,IAAK,CAAE,IAAI,CAACzB,QAAQ,CAAE,OAAO,CAAE,IAAI,CAAE,IAAI,CAACA,QAAQ,CAAE,UAAU,CAAE,EAAG;QAClE;MACD;;MAEA;MACA,IAAI6B,OAAO,GAAG,IAAI,CAAC5B,MAAM,CAAE,OAAO,CAAE;MACpC,IAAIC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAE,OAAO,CAAE;MAC7B,IAAI+C,GAAG,GAAGpB,OAAO,CAACoB,GAAG,EAAE;;MAEvB;MACA,IAAI/H,MAAM,GAAGJ,GAAG,CAACoI,eAAe,CAAE,IAAI,CAAClD,QAAQ,CAAE,OAAO,CAAE,CAAE;MAC5D,IAAIjB,KAAK,GAAGjE,GAAG,CAACM,cAAc,CAAEF,MAAM,CAAE;;MAExC;MACA,IAAIqH,cAAc,GAAGzH,GAAG,CAAC0H,iBAAiB,CAAE;QAC3CC,SAAS,EAAE1D,KAAK,CAAC2D,OAAO,EAAE;QAC1BU,QAAQ,EAAE,IAAI,CAACpD,QAAQ,CAAE,UAAU;MACpC,CAAC,CAAE;;MAEH;MACA,IAAIsD,aAAa,GAAGf,cAAc,CAAE,CAAC,CAAE,CAACY,SAAS;MACjD,IAAIzB,OAAO,GAAG4B,aAAa,CAAC5B,OAAO,CAAE3C,KAAK,CAAE;;MAE5C;MACA,IAAK2C,OAAO,YAAY6B,KAAK,EAAG;QAC/B,IAAIC,UAAU,GAAG7I,CAAC,CAAE,mBAAmB,CAAE;QACzCG,GAAG,CAACkI,YAAY,CAAEQ,UAAU,EAAE9B,OAAO,CAAE;;QAEvC;MACD,CAAC,MAAM;QACN,IAAI8B,UAAU,GAAG7I,CAAC,CAAE+G,OAAO,CAAE;MAC9B;;MAEA;MACAG,OAAO,CAAC4B,MAAM,EAAE;MAChBvD,GAAG,CAACwD,IAAI,CAAEF,UAAU,CAAE;;MAEtB;MACA;MACAG,UAAU,CAAE,YAAY;QACvB,CAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAE,CAAC5B,GAAG,CAAE,UAAW6B,IAAI,EAAG;UAChDJ,UAAU,CAACI,IAAI,CAAEA,IAAI,EAAE/B,OAAO,CAAC+B,IAAI,CAAEA,IAAI,CAAE,CAAE;QAC9C,CAAC,CAAE;MACJ,CAAC,EAAE,CAAC,CAAE;;MAEN;MACA,IAAK,CAAEJ,UAAU,CAAC5H,IAAI,CAAE,UAAU,CAAE,EAAG;QACtCd,GAAG,CAACmI,GAAG,CAAEO,UAAU,EAAEP,GAAG,EAAE,IAAI,CAAE;MACjC;;MAEA;MACA,IAAI,CAACjD,QAAQ,CAAE,OAAO,EAAEwD,UAAU,CAACP,GAAG,EAAE,CAAE;IAC3C,CAAC;IAEDY,cAAc,EAAE,YAAY;MAC3B,IAAI,CAAC7C,MAAM,EAAE;IACd,CAAC;IAED8C,eAAe,EAAE,UAAWrG,CAAC,EAAEC,GAAG,EAAG;MACpC,IAAI,CAACqG,QAAQ,EAAE;IAChB,CAAC;IAEDA,QAAQ,EAAE,YAAY;MACrB;MACA,IAAIC,MAAM,GAAG,IAAI,CAACrJ,CAAC,CAAE,kBAAkB,CAAE;;MAEzC;MACA,IAAIsJ,OAAO,GAAGnJ,GAAG,CAACoJ,SAAS,CAAEF,MAAM,CAAE;;MAErC;MACAC,OAAO,CAACnG,IAAI,CAAE,IAAI,CAAE,CAACqE,IAAI,CAAErH,GAAG,CAACuH,EAAE,CAAE,IAAI,CAAE,CAAE;;MAE3C;MACA4B,OAAO,CAACnG,IAAI,CAAE,IAAI,CAAE,CAACqG,GAAG,CAAE,QAAQ,CAAE,CAACC,MAAM,EAAE;;MAE7C;MACA,IAAI,CAAC5D,WAAW,CAACnF,IAAI,EAAE;IACxB,CAAC;IAEDgJ,YAAY,EAAE,UAAW5G,CAAC,EAAEC,GAAG,EAAG;MACjC,IAAI,CAAC6D,WAAW,EAAE;IACnB,CAAC;IAED+C,aAAa,EAAE,UAAW7G,CAAC,EAAEC,GAAG,EAAG;MAClC;MACA,IAAI,CAACqC,KAAK,CAAErC,GAAG,CAACC,OAAO,CAAE,OAAO,CAAE,CAAE;;MAEpC;MACA,IAAI,CAACqC,QAAQ,CAAE,OAAO,EAAEtC,GAAG,CAACuF,GAAG,EAAE,CAAE;;MAEnC;MACA,IAAI,CAACzB,cAAc,EAAE;MACrB,IAAI,CAACC,WAAW,EAAE;IACnB,CAAC;IAED8C,gBAAgB,EAAE,UAAW9G,CAAC,EAAEC,GAAG,EAAG;MACrC;MACA,IAAI,CAACqC,KAAK,CAAErC,GAAG,CAACC,OAAO,CAAE,OAAO,CAAE,CAAE;;MAEpC;MACA,IAAI,CAACqC,QAAQ,CAAE,UAAU,EAAEtC,GAAG,CAACuF,GAAG,EAAE,CAAE;;MAEtC;MACA,IAAI,CAACxB,WAAW,EAAE;IACnB,CAAC;IAED+C,UAAU,EAAE,UAAW/G,CAAC,EAAEC,GAAG,EAAG;MAC/B;MACA,IAAIoC,KAAK,GAAGhF,GAAG,CAACoJ,SAAS,CAAExG,GAAG,CAACC,OAAO,CAAE,OAAO,CAAE,CAAE;;MAEnD;MACA,IAAI,CAAC2D,UAAU,CAAExB,KAAK,CAAE;IACzB,CAAC;IAED2E,aAAa,EAAE,UAAWhH,CAAC,EAAEC,GAAG,EAAG;MAClC;MACA,IAAIoC,KAAK,GAAGpC,GAAG,CAACC,OAAO,CAAE,OAAO,CAAE;;MAElC;MACA,IAAI,CAAC6C,WAAW,CAACnF,IAAI,EAAE;;MAEvB;MACA,IAAKyE,KAAK,CAAC4E,QAAQ,CAAE,OAAO,CAAE,CAAC/B,MAAM,IAAI,CAAC,EAAG;QAC5C7C,KAAK,CAACnC,OAAO,CAAE,aAAa,CAAE,CAACyG,MAAM,EAAE;MACxC;;MAEA;MACAtE,KAAK,CAACsE,MAAM,EAAE;IACf;EACD,CAAC,CAAE;EAEHtJ,GAAG,CAAC6J,oBAAoB,CAAEhF,4BAA4B,CAAE;;EAExD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIiF,sBAAsB,GAAG,IAAI9J,GAAG,CAACoD,KAAK,CAAE;IAC3CC,OAAO,EAAE;MACR0G,uBAAuB,EAAE;IAC1B,CAAC;IAEDC,uBAAuB,EAAE,UAAWC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAG;MACnE;MACA,IAAIpH,IAAI,GAAG,CAAC,CAAC;MACb,IAAIqH,QAAQ,GAAGvK,CAAC,EAAE;;MAElB;MACAoK,QAAQ,CAAChD,GAAG,CAAE,UAAWoD,KAAK,EAAG;QAChC;QACAtH,IAAI,CAAEsH,KAAK,CAAClG,GAAG,CAAE,SAAS,CAAE,CAAE,GAAGkG,KAAK,CAAClG,GAAG,CAAE,KAAK,CAAE;;QAEnD;QACAiG,QAAQ,GAAGA,QAAQ,CAACE,GAAG,CAAED,KAAK,CAACxK,CAAC,CAAE,uBAAuB,CAAE,CAAE;MAC9D,CAAC,CAAE;;MAEH;MACAuK,QAAQ,CAAC7D,IAAI,CAAE,YAAY;QAC1B;QACA,IAAIQ,OAAO,GAAGlH,CAAC,CAAE,IAAI,CAAE;QACvB,IAAIsI,GAAG,GAAGpB,OAAO,CAACoB,GAAG,EAAE;;QAEvB;QACA,IAAK,CAAEA,GAAG,IAAI,CAAEpF,IAAI,CAAEoF,GAAG,CAAE,EAAG;UAC7B;QACD;;QAEA;QACApB,OAAO,CAAC/D,IAAI,CAAE,iBAAiB,CAAE,CAAC8F,IAAI,CAAE,OAAO,EAAE/F,IAAI,CAAEoF,GAAG,CAAE,CAAE;;QAE9D;QACApB,OAAO,CAACoB,GAAG,CAAEpF,IAAI,CAAEoF,GAAG,CAAE,CAAE;MAC3B,CAAC,CAAE;IACJ;EACD,CAAC,CAAE;AACJ,CAAC,EAAIvD,MAAM,CAAE;;;;;;;;;;ACzYb,CAAE,UAAW/E,CAAC,EAAEC,SAAS,EAAG;EAC3BE,GAAG,CAACuK,WAAW,GAAGvK,GAAG,CAACoD,KAAK,CAAClC,MAAM,CAAE;IACnC;IACAsJ,UAAU,EAAE,mBAAmB;IAE/B;IACAzF,MAAM,EAAE;MACP,iBAAiB,EAAE,aAAa;MAChC,eAAe,EAAE,aAAa;MAC9B,oBAAoB,EAAE,aAAa;MACnC,6CAA6C,EAC5C,qBAAqB;MACtB,qBAAqB,EAAE,eAAe;MACtC,wBAAwB,EAAE,WAAW;MACrC,mBAAmB,EAAE,MAAM;MAE3B,mBAAmB,EAAE,aAAa;MAClC,kCAAkC,EAAE,YAAY;MAEhD,oBAAoB,EAAE,cAAc;MACpC,wBAAwB,EAAE,kBAAkB;MAC5C,mBAAmB,EAAE,eAAe;MACpC,kBAAkB,EAAE,cAAc;MAElC0F,MAAM,EAAE,UAAU;MAClBC,OAAO,EAAE;IACV,CAAC;IAED;IACA3H,IAAI,EAAE;MACL;MACA;MACAoE,EAAE,EAAE,CAAC;MAEL;MACAwD,GAAG,EAAE,EAAE;MAEP;MACAtK,IAAI,EAAE;;MAEN;MACA;;MAEA;MACA;;MAEA;MACA;IACD,CAAC;;IAEDuK,KAAK,EAAE,UAAWxK,MAAM,EAAG;MAC1B;MACA,IAAI,CAACwC,GAAG,GAAGxC,MAAM;;MAEjB;MACA,IAAI,CAACyK,OAAO,CAAEzK,MAAM,CAAE;;MAEtB;MACA;MACA,IAAI,CAACU,IAAI,CAAE,IAAI,CAAE;MACjB,IAAI,CAACA,IAAI,CAAE,QAAQ,CAAE;MACrB,IAAI,CAACA,IAAI,CAAE,YAAY,CAAE;IAC1B,CAAC;IAEDqE,MAAM,EAAE,UAAWvE,IAAI,EAAG;MACzB,OAAOf,CAAC,CAAE,GAAG,GAAG,IAAI,CAACiL,UAAU,EAAE,GAAG,GAAG,GAAGlK,IAAI,CAAE;IACjD,CAAC;IAEDmK,KAAK,EAAE,YAAY;MAClB,OAAO,IAAI,CAAClL,CAAC,CAAE,aAAa,CAAE;IAC/B,CAAC;IAEDmL,OAAO,EAAE,YAAY;MACpB,OAAO,IAAI,CAACnL,CAAC,CAAE,eAAe,CAAE;IACjC,CAAC;IAEDuB,SAAS,EAAE,YAAY;MACtB,OAAO,IAAI,CAACvB,CAAC,CAAE,iBAAiB,CAAE;IACnC,CAAC;IAEDoL,QAAQ,EAAE,UAAWrK,IAAI,EAAG;MAC3B,OAAO,IAAI,CAACf,CAAC,CACZ,+CAA+C,GAAGe,IAAI,CACtD;IACF,CAAC;IAEDsK,SAAS,EAAE,YAAY;MACtB,OAAOlL,GAAG,CAACgH,eAAe,CAAE;QAAEqD,KAAK,EAAE,IAAI,CAACzH,GAAG;QAAEuI,KAAK,EAAE;MAAE,CAAC,CAAE,CAACC,GAAG,EAAE;IAClE,CAAC;IAEDrD,UAAU,EAAE,YAAY;MACvB,OAAO/H,GAAG,CAACgH,eAAe,CAAE;QAAEqD,KAAK,EAAE,IAAI,CAACzH;MAAI,CAAC,CAAE;IAClD,CAAC;IAEDyI,SAAS,EAAE,YAAY;MACtB,OAAOrL,GAAG,CAACgH,eAAe,CAAE;QAAEsE,MAAM,EAAE,IAAI,CAAC1I;MAAI,CAAC,CAAE;IACnD,CAAC;IAED2I,YAAY,EAAE,YAAY;MACzB,OAAO,aAAa,GAAG,IAAI,CAACpH,GAAG,CAAE,IAAI,CAAE,GAAG,GAAG;IAC9C,CAAC;IAED2G,UAAU,EAAE,YAAY;MACvB,OAAO,aAAa,GAAG,IAAI,CAAC3G,GAAG,CAAE,IAAI,CAAE;IACxC,CAAC;IAEDqH,QAAQ,EAAE,UAAW5K,IAAI,EAAEC,KAAK,EAAG;MAClC;MACA,IAAI4K,OAAO,GAAG,IAAI,CAACX,UAAU,EAAE;MAC/B,IAAIY,SAAS,GAAG,IAAI,CAACH,YAAY,EAAE;;MAEnC;MACA,IAAK3K,IAAI,EAAG;QACX6K,OAAO,IAAI,GAAG,GAAG7K,IAAI;QACrB8K,SAAS,IAAI,GAAG,GAAG9K,IAAI,GAAG,GAAG;MAC9B;;MAEA;MACA,IAAIuE,MAAM,GAAGtF,CAAC,CAAE,WAAW,CAAE,CAACiJ,IAAI,CAAE;QACnC3B,EAAE,EAAEsE,OAAO;QACX7K,IAAI,EAAE8K,SAAS;QACf7K,KAAK,EAAEA;MACR,CAAC,CAAE;MACH,IAAI,CAAChB,CAAC,CAAE,SAAS,CAAE,CAAC8L,MAAM,CAAExG,MAAM,CAAE;;MAEpC;MACA,OAAOA,MAAM;IACd,CAAC;IAEDyG,OAAO,EAAE,UAAWhL,IAAI,EAAG;MAC1B;MACA,IAAK,IAAI,CAACiL,GAAG,CAAEjL,IAAI,CAAE,EAAG;QACvB,OAAO,IAAI,CAACuD,GAAG,CAAEvD,IAAI,CAAE;MACxB;;MAEA;MACA,IAAIuE,MAAM,GAAG,IAAI,CAACA,MAAM,CAAEvE,IAAI,CAAE;MAChC,IAAIC,KAAK,GAAGsE,MAAM,CAAC0C,MAAM,GAAG1C,MAAM,CAACgD,GAAG,EAAE,GAAG,IAAI;;MAE/C;MACA,IAAI,CAACpG,GAAG,CAAEnB,IAAI,EAAEC,KAAK,EAAE,IAAI,CAAE;;MAE7B;MACA,OAAOA,KAAK;IACb,CAAC;IAEDiL,OAAO,EAAE,UAAWlL,IAAI,EAAEC,KAAK,EAAG;MACjC;MACA,IAAIsE,MAAM,GAAG,IAAI,CAACA,MAAM,CAAEvE,IAAI,CAAE;MAChC,IAAImL,OAAO,GAAG5G,MAAM,CAACgD,GAAG,EAAE;;MAE1B;MACA,IAAK,CAAEhD,MAAM,CAAC0C,MAAM,EAAG;QACtB1C,MAAM,GAAG,IAAI,CAACqG,QAAQ,CAAE5K,IAAI,EAAEC,KAAK,CAAE;MACtC;;MAEA;MACA,IAAKA,KAAK,KAAK,IAAI,EAAG;QACrBsE,MAAM,CAACmE,MAAM,EAAE;;QAEf;MACD,CAAC,MAAM;QACNnE,MAAM,CAACgD,GAAG,CAAEtH,KAAK,CAAE;MACpB;;MAEA;;MAEA;MACA,IAAK,CAAE,IAAI,CAACgL,GAAG,CAAEjL,IAAI,CAAE,EAAG;QACzB;QACA,IAAI,CAACmB,GAAG,CAAEnB,IAAI,EAAEC,KAAK,EAAE,IAAI,CAAE;;QAE7B;MACD,CAAC,MAAM;QACN;QACA,IAAI,CAACkB,GAAG,CAAEnB,IAAI,EAAEC,KAAK,CAAE;MACxB;;MAEA;MACA,OAAO,IAAI;IACZ,CAAC;IAEDC,IAAI,EAAE,UAAWF,IAAI,EAAEC,KAAK,EAAG;MAC9B,IAAKA,KAAK,KAAKf,SAAS,EAAG;QAC1B,OAAO,IAAI,CAACgM,OAAO,CAAElL,IAAI,EAAEC,KAAK,CAAE;MACnC,CAAC,MAAM;QACN,OAAO,IAAI,CAAC+K,OAAO,CAAEhL,IAAI,CAAE;MAC5B;IACD,CAAC;IAEDoL,KAAK,EAAE,UAAWA,KAAK,EAAG;MACzBC,MAAM,CAACC,IAAI,CAAEF,KAAK,CAAE,CAAC/E,GAAG,CAAE,UAAW0D,GAAG,EAAG;QAC1C,IAAI,CAACmB,OAAO,CAAEnB,GAAG,EAAEqB,KAAK,CAAErB,GAAG,CAAE,CAAE;MAClC,CAAC,EAAE,IAAI,CAAE;IACV,CAAC;IAEDrD,QAAQ,EAAE,YAAY;MACrB;MACA,IAAIiB,KAAK,GAAG,IAAI,CAACzH,IAAI,CAAE,OAAO,CAAE;MAChC,IAAKyH,KAAK,KAAK,EAAE,EAAG;QACnBA,KAAK,GAAGvI,GAAG,CAACuH,EAAE,CAAE,YAAY,CAAE;MAC/B;;MAEA;MACA,OAAOgB,KAAK;IACb,CAAC;IAED4D,OAAO,EAAE,YAAY;MACpB,OAAO,IAAI,CAACrL,IAAI,CAAE,MAAM,CAAE;IAC3B,CAAC;IAED8G,OAAO,EAAE,YAAY;MACpB,OAAO,IAAI,CAAC9G,IAAI,CAAE,MAAM,CAAE;IAC3B,CAAC;IAEDsL,YAAY,EAAE,YAAY;MACzB,IAAI/L,IAAI,GAAG,IAAI,CAACS,IAAI,CAAE,MAAM,CAAE;MAC9B,IAAIuL,KAAK,GAAGrM,GAAG,CAACmE,GAAG,CAAE,YAAY,CAAE;MACnC,OAAOkI,KAAK,CAAEhM,IAAI,CAAE,GAAGgM,KAAK,CAAEhM,IAAI,CAAE,CAACkI,KAAK,GAAGlI,IAAI;IAClD,CAAC;IAED+G,MAAM,EAAE,YAAY;MACnB,OAAO,IAAI,CAACtG,IAAI,CAAE,KAAK,CAAE;IAC1B,CAAC;IAEDwL,UAAU,EAAE,YAAY;MACvB,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACC,aAAa,EAAE;IACrB,CAAC;IAEDC,YAAY,EAAE,UAAWpF,IAAI,EAAG;MAC/B,IAAK,CAAEqF,SAAS,CAACC,SAAS,EAAG,OAAO,0CAA0C,GAAGtF,IAAI,GAAG,SAAS;MACjG,OAAO,yBAAyB,GAAGA,IAAI,GAAG,SAAS;IACpD,CAAC;IAEDmF,aAAa,EAAE,YAAY;MAC1B,IAAK,CAAEE,SAAS,CAACC,SAAS,EAAG;QAC5B,IAAI,CAAC/J,GAAG,CAACI,IAAI,CAAE,WAAW,CAAE,CAACmD,QAAQ,CAAE,kBAAkB,CAAE;MAC5D;IACD,CAAC;IAEDoG,YAAY,EAAE,YAAY;MACzB;MACA,IAAKvM,GAAG,CAAC+C,IAAI,CAAC6J,UAAU,CAACC,cAAc,CAAE,OAAO,CAAE,EAAG;QACpD;MACD;;MAEA;MACA,IAAIC,gBAAgB,GAAGjN,CAAC,CAAE,aAAa,CAAE,CAACwJ,GAAG,CAC5C,sBAAsB,CACtB;;MAED;MACA,IAAI0D,YAAY,GAAGD,gBAAgB,CACjC9J,IAAI,CAAE,gCAAgC,CAAE,CACxCsI,MAAM,EAAE;MACVyB,YAAY,CAACpB,MAAM,CAClB,2CAA2C,GAC1C3L,GAAG,CAACuH,EAAE,CAAE,qBAAqB,CAAE,GAC/B,WAAW,GACX,2CAA2C,GAC3CvH,GAAG,CAACuH,EAAE,CAAE,6BAA6B,CAAE,GACvC,WAAW,GACX,2CAA2C,GAC3CvH,GAAG,CAACuH,EAAE,CAAE,kBAAkB,CAAE,GAC5B,WAAW,CACZ;;MAED;MACA,IAAIyF,aAAa,GAAGF,gBAAgB,CAClC9J,IAAI,CAAE,gCAAgC,CAAE,CACxCsI,MAAM,EAAE;MACV0B,aAAa,CAACrB,MAAM,CACnB,2CAA2C,GAC1C3L,GAAG,CAACuH,EAAE,CAAE,oBAAoB,CAAE,GAC9B,WAAW,CACZ;MAEDuF,gBAAgB,CAAC3G,QAAQ,CAAE,qBAAqB,CAAE;IACnD,CAAC;IAEDD,MAAM,EAAE,YAAY;MACnB;MACA,IAAI8E,OAAO,GAAG,IAAI,CAACnL,CAAC,CAAE,eAAe,CAAE;MACvC,IAAIoN,UAAU,GAAG,IAAI,CAACnM,IAAI,CAAE,YAAY,CAAE;MAC1C,IAAIyH,KAAK,GAAG,IAAI,CAACjB,QAAQ,EAAE;MAC3B,IAAI1G,IAAI,GAAG,IAAI,CAACE,IAAI,CAAE,MAAM,CAAE;MAC9B,IAAIT,IAAI,GAAG,IAAI,CAAC+L,YAAY,EAAE;MAC9B,IAAIzB,GAAG,GAAG,IAAI,CAAC7J,IAAI,CAAE,KAAK,CAAE;MAC5B,IAAIoM,QAAQ,GAAG,IAAI,CAAC/H,MAAM,CAAE,UAAU,CAAE,CAACrE,IAAI,CAAE,SAAS,CAAE;;MAE1D;MACAkK,OAAO,CAAChI,IAAI,CAAE,WAAW,CAAE,CAAC4F,IAAI,CAAEuE,QAAQ,CAAEF,UAAU,CAAE,GAAG,CAAC,CAAE;;MAE9D;MACA,IAAKC,QAAQ,EAAG;QACf3E,KAAK,IAAI,sCAAsC;MAChD;;MAEA;MACAyC,OAAO,CAAChI,IAAI,CAAE,0BAA0B,CAAE,CAAC4F,IAAI,CAAEL,KAAK,CAAE;;MAExD;MACAyC,OAAO,CAAChI,IAAI,CAAE,gBAAgB,CAAE,CAAC4F,IAAI,CAAE,IAAI,CAAC6D,YAAY,CAAE7L,IAAI,CAAE,CAAE;;MAElE;MACA,MAAMwM,QAAQ,GAAGpN,GAAG,CAACqN,UAAU,CAAE,IAAI,CAACzF,OAAO,EAAE,CAAE;MACjDoD,OAAO,CAAChI,IAAI,CAAE,mBAAmB,CAAE,CAACqE,IAAI,CAAE,GAAG,GAAGhH,IAAI,CAAE;MACtD2K,OAAO,CACLhI,IAAI,CAAE,kBAAkB,CAAE,CAC1BqD,WAAW,EAAE,CACbF,QAAQ,CAAE,kCAAkC,GAAGiH,QAAQ,CAAE;;MAE3D;MACApC,OAAO,CAAChI,IAAI,CAAE,eAAe,CAAE,CAAC4F,IAAI,CAAE,IAAI,CAAC6D,YAAY,CAAE9B,GAAG,CAAE,CAAE;;MAEhE;MACA3K,GAAG,CAACkE,QAAQ,CAAE,qBAAqB,EAAE,IAAI,CAAE;IAC5C,CAAC;IAEDoJ,OAAO,EAAE,YAAY;MACpBtN,GAAG,CAACkE,QAAQ,CAAE,sBAAsB,EAAE,IAAI,CAAE;IAC7C,CAAC;IAEDqJ,MAAM,EAAE,YAAY;MACnB,OAAO,IAAI,CAAC3K,GAAG,CAAC4K,QAAQ,CAAE,MAAM,CAAE;IACnC,CAAC;IAEDC,WAAW,EAAE,UAAW9K,CAAC,EAAG;MAC3BA,CAAC,CAAC+K,eAAe,EAAE;MACnB,IAAK,CAAEhB,SAAS,CAACC,SAAS,EAAG;MAC7BD,SAAS,CAACC,SAAS,CAACgB,SAAS,CAAE9N,CAAC,CAAE8C,CAAC,CAACiL,MAAM,CAAE,CAACvG,IAAI,EAAE,CAAE,CAACwG,IAAI,CAAE,MAAM;QACjEhO,CAAC,CAAE8C,CAAC,CAACiL,MAAM,CAAE,CAACzH,QAAQ,CAAE,QAAQ,CAAE;QAClC0C,UAAU,CAAE,YAAY;UACvBhJ,CAAC,CAAE8C,CAAC,CAACiL,MAAM,CAAE,CAACvH,WAAW,CAAE,QAAQ,CAAE;QACtC,CAAC,EAAE,IAAI,CAAE;MACV,CAAC,CAAE;IACJ,CAAC;IAEDyH,WAAW,EAAE,UAAWnL,CAAC,EAAG;MAC3BoL,OAAO,GAAGlO,CAAC,CAAE8C,CAAC,CAACiL,MAAM,CAAE;MACvB,IACCG,OAAO,CAACzC,MAAM,EAAE,CAACkC,QAAQ,CAAE,aAAa,CAAE,IAC1C,CAAEO,OAAO,CAACP,QAAQ,CAAE,YAAY,CAAE,EAElC;MACD,IAAI,CAACD,MAAM,EAAE,GAAG,IAAI,CAACxH,KAAK,EAAE,GAAG,IAAI,CAACJ,IAAI,EAAE;IAC3C,CAAC;IAEDqI,mBAAmB,EAAE,YAAY;MAChC,MAAM5M,SAAS,GAAG,IAAI,CAACwB,GAAG,CAACqH,QAAQ,CAAE,WAAW,CAAE;MAClDjK,GAAG,CAACkE,QAAQ,CAAE,MAAM,EAAE9C,SAAS,CAAE;IAClC,CAAC;IAED;AACF;AACA;IACE6M,WAAW,EAAE,UAAWtL,CAAC,EAAG;MAC3B,IAAIuL,WAAW,GAAGrO,CAAC,CAAE8C,CAAC,CAACiL,MAAM,CAAE,CAC7B/K,OAAO,CAAE,IAAI,CAAE,CACfG,IAAI,CAAE,cAAc,CAAE;MACxBkL,WAAW,CAAC/H,QAAQ,CAAE,QAAQ,CAAE;IACjC,CAAC;IAED;AACF;AACA;IACEgI,UAAU,EAAE,UAAWxL,CAAC,EAAG;MAC1B,IAAIyL,sBAAsB,GAAG,EAAE;MAC/B,IAAIC,sBAAsB,GAAGxO,CAAC,CAAE8C,CAAC,CAACiL,MAAM,CAAE,CACxC/K,OAAO,CAAE,IAAI,CAAE,CACfG,IAAI,CAAE,cAAc,CAAE;;MAExB;MACA6F,UAAU,CAAE,YAAY;QACvB,IAAIyF,uBAAuB,GAAGzO,CAAC,CAAE4C,QAAQ,CAAC8L,aAAa,CAAE,CACvD1L,OAAO,CAAE,IAAI,CAAE,CACfG,IAAI,CAAE,cAAc,CAAE;QACxB,IAAK,CAAEqL,sBAAsB,CAACG,EAAE,CAAEF,uBAAuB,CAAE,EAAG;UAC7DD,sBAAsB,CAAChI,WAAW,CAAE,QAAQ,CAAE;QAC/C;MACD,CAAC,EAAE+H,sBAAsB,CAAE;IAC5B,CAAC;IAEDzI,IAAI,EAAE,YAAY;MACjB;MACA,IAAIvE,SAAS,GAAG,IAAI,CAACwB,GAAG,CAACqH,QAAQ,CAAE,WAAW,CAAE;;MAEhD;MACAjK,GAAG,CAACkE,QAAQ,CAAE,mBAAmB,EAAE,IAAI,CAAE;MACzC,IAAI,CAACuK,OAAO,CAAE,iBAAiB,CAAE;;MAEjC;MACAzO,GAAG,CAACkE,QAAQ,CAAE,MAAM,EAAE9C,SAAS,CAAE;;MAEjC;MACAA,SAAS,CAACsN,SAAS,EAAE;MACrB,IAAI,CAAC9L,GAAG,CAACuD,QAAQ,CAAE,MAAM,CAAE;IAC5B,CAAC;IAEDJ,KAAK,EAAE,YAAY;MAClB;MACA,IAAI3E,SAAS,GAAG,IAAI,CAACwB,GAAG,CAACqH,QAAQ,CAAE,WAAW,CAAE;;MAEhD;MACA7I,SAAS,CAACuN,OAAO,EAAE;MACnB,IAAI,CAAC/L,GAAG,CAACyD,WAAW,CAAE,MAAM,CAAE;;MAE9B;MACArG,GAAG,CAACkE,QAAQ,CAAE,oBAAoB,EAAE,IAAI,CAAE;MAC1C,IAAI,CAACuK,OAAO,CAAE,kBAAkB,CAAE;;MAElC;MACAzO,GAAG,CAACkE,QAAQ,CAAE,MAAM,EAAE9C,SAAS,CAAE;IAClC,CAAC;IAEDwN,SAAS,EAAE,YAAY;MACtB,OAAO5O,GAAG,CAAC4O,SAAS,CAAE,IAAI,CAAChM,GAAG,EAAE,IAAI,CAAC2I,YAAY,EAAE,CAAE;IACtD,CAAC;IAEDhL,IAAI,EAAE,UAAWF,IAAI,EAAG;MACvB;MACAA,IAAI,GAAGA,IAAI,IAAI,UAAU,CAAC,CAAC;;MAE3B;MACA,IAAIE,IAAI,GAAG,IAAI,CAACqL,OAAO,CAAE,MAAM,CAAE;;MAEjC;MACA,IAAKrL,IAAI,KAAK,UAAU,EAAG;QAC1B;MACD;;MAEA;MACA,IAAI,CAACuL,OAAO,CAAE,MAAM,EAAEzL,IAAI,CAAE;;MAE5B;MACA,IAAI,CAACuC,GAAG,CAACkG,IAAI,CAAE,WAAW,EAAEzI,IAAI,CAAE;;MAElC;MACAL,GAAG,CAACkE,QAAQ,CAAE,mBAAmB,EAAE,IAAI,EAAE7D,IAAI,CAAE;IAChD,CAAC;IAEDwO,MAAM,EAAE,YAAY;MACnB;MACA,IAAInD,SAAS,GAAG,IAAI,CAACH,YAAY,EAAE;MACnC,IAAIhL,IAAI,GAAG,IAAI,CAAC4D,GAAG,CAAE,MAAM,CAAE;;MAE7B;MACA,IAAK,IAAI,CAACoJ,MAAM,EAAE,EAAG;QACpB,IAAI,CAACxH,KAAK,EAAE;MACb;;MAEA;MACA,IAAKxF,IAAI,IAAI,UAAU,EAAG;QACzB;QACA;MAAA,CACA,MAAM,IAAKA,IAAI,IAAI,MAAM,EAAG;QAC5B,IAAI,CAACV,CAAC,CAAE,sBAAsB,GAAG6L,SAAS,GAAG,IAAI,CAAE,CAACpC,MAAM,EAAE;;QAE5D;MACD,CAAC,MAAM;QACN,IAAI,CAACzJ,CAAC,CAAE,UAAU,GAAG6L,SAAS,GAAG,IAAI,CAAE,CAACpC,MAAM,EAAE;MACjD;;MAEA;MACAtJ,GAAG,CAACkE,QAAQ,CAAE,qBAAqB,EAAE,IAAI,CAAE;IAC5C,CAAC;IAED4K,QAAQ,EAAE,UAAWnM,CAAC,EAAEC,GAAG,EAAG;MAC7B;MACA,IAAI,CAACrC,IAAI,EAAE;;MAEX;MACAP,GAAG,CAACkE,QAAQ,CAAE,qBAAqB,EAAE,IAAI,CAAE;IAC5C,CAAC;IAED6K,SAAS,EAAE,UAAWpM,CAAC,EAAEC,GAAG,EAAEhC,IAAI,EAAEC,KAAK,EAAG;MAC3C;MACA,IAAKD,IAAI,IAAI,MAAM,EAAG;QACrB;MACD;;MAEA;MACA,IAAK,CAAE,YAAY,EAAE,QAAQ,CAAE,CAAC2B,OAAO,CAAE3B,IAAI,CAAE,GAAG,CAAC,CAAC,EAAG;QACtD,IAAI,CAACL,IAAI,CAAE,MAAM,CAAE;;QAEnB;MACD,CAAC,MAAM;QACN,IAAI,CAACA,IAAI,EAAE;MACZ;;MAEA;MACA,IACC,CACC,YAAY,EACZ,OAAO,EACP,UAAU,EACV,MAAM,EACN,MAAM,EACN,KAAK,CACL,CAACgC,OAAO,CAAE3B,IAAI,CAAE,GAAG,CAAC,CAAC,EACrB;QACD,IAAI,CAACsF,MAAM,EAAE;MACd;;MAEA;MACAlG,GAAG,CAACkE,QAAQ,CAAE,sBAAsB,GAAGtD,IAAI,EAAE,IAAI,EAAEC,KAAK,CAAE;IAC3D,CAAC;IAEDmO,aAAa,EAAE,UAAWrM,CAAC,EAAEC,GAAG,EAAG;MAClC;MACA,IAAI2F,KAAK,GAAG3F,GAAG,CAACuF,GAAG,EAAE;MACrB,IAAI,CAACpG,GAAG,CAAE,OAAO,EAAEwG,KAAK,CAAE;;MAE1B;MACA,IAAK,IAAI,CAACzH,IAAI,CAAE,MAAM,CAAE,IAAI,EAAE,EAAG;QAChC,IAAIF,IAAI,GAAGZ,GAAG,CAACiP,YAAY,CAC1B,4BAA4B,EAC5BjP,GAAG,CAACkP,WAAW,CAAE3G,KAAK,CAAE,EACxB,IAAI,CACJ;QACD,IAAI,CAACzH,IAAI,CAAE,MAAM,EAAEF,IAAI,CAAE;MAC1B;IACD,CAAC;IAEDuO,YAAY,EAAE,UAAWxM,CAAC,EAAEC,GAAG,EAAG;MACjC;MACA,IAAIhC,IAAI,GAAGgC,GAAG,CAACuF,GAAG,EAAE;MACpB,IAAI,CAACpG,GAAG,CAAE,MAAM,EAAEnB,IAAI,CAAE;;MAExB;MACA,IAAKA,IAAI,CAAC0B,MAAM,CAAE,CAAC,EAAE,CAAC,CAAE,KAAK,QAAQ,EAAG;QACvC8M,KAAK,CACJpP,GAAG,CAACuH,EAAE,CACL,kEAAkE,CAClE,CACD;MACF;IACD,CAAC;IAED8H,gBAAgB,EAAE,UAAW1M,CAAC,EAAEC,GAAG,EAAG;MACrC;MACA,IAAIsK,QAAQ,GAAGtK,GAAG,CAAC9B,IAAI,CAAE,SAAS,CAAE,GAAG,CAAC,GAAG,CAAC;MAC5C,IAAI,CAACiB,GAAG,CAAE,UAAU,EAAEmL,QAAQ,CAAE;IACjC,CAAC;IAEDxM,MAAM,EAAE,UAAW4O,IAAI,EAAG;MACzB;MACAA,IAAI,GAAGtP,GAAG,CAACuP,SAAS,CAAED,IAAI,EAAE;QAC3B7O,OAAO,EAAE;MACV,CAAC,CAAE;;MAEH;MACA,IAAI0G,EAAE,GAAG,IAAI,CAACrG,IAAI,CAAE,IAAI,CAAE;MAE1B,IAAKqG,EAAE,EAAG;QACT,IAAIhC,MAAM,GAAGtF,CAAC,CAAE,qBAAqB,CAAE;QACvC,IAAI2P,MAAM,GAAGrK,MAAM,CAACgD,GAAG,EAAE,GAAG,GAAG,GAAGhB,EAAE;QACpChC,MAAM,CAACgD,GAAG,CAAEqH,MAAM,CAAE;MACrB;;MAEA;MACAxP,GAAG,CAACkE,QAAQ,CAAE,qBAAqB,EAAE,IAAI,CAAE;;MAE3C;MACA,IAAKoL,IAAI,CAAC7O,OAAO,EAAG;QACnB,IAAI,CAACgP,aAAa,EAAE;MACrB,CAAC,MAAM;QACN,IAAI,CAACnG,MAAM,EAAE;MACd;IACD,CAAC;IAEDoG,aAAa,EAAE,UAAW/M,CAAC,EAAEC,GAAG,EAAG;MAClC;MACA,IAAKD,CAAC,CAACgN,QAAQ,EAAG;QACjB,OAAO,IAAI,CAACjP,MAAM,EAAE;MACrB;;MAEA;MACA,IAAI,CAACkC,GAAG,CAACuD,QAAQ,CAAE,QAAQ,CAAE;;MAE7B;MACA,IAAIyJ,OAAO,GAAG5P,GAAG,CAAC6P,UAAU,CAAE;QAC7BC,aAAa,EAAE,IAAI;QACnBlC,MAAM,EAAEhL,GAAG;QACXJ,OAAO,EAAE,IAAI;QACbuN,OAAO,EAAE,YAAY;UACpB,IAAI,CAACrP,MAAM,EAAE;QACd,CAAC;QACDsP,MAAM,EAAE,YAAY;UACnB,IAAI,CAACpN,GAAG,CAACyD,WAAW,CAAE,QAAQ,CAAE;QACjC;MACD,CAAC,CAAE;IACJ,CAAC;IAEDoJ,aAAa,EAAE,YAAY;MAC1B;MACA,IAAIxL,KAAK,GAAG,IAAI;MAChB,IAAIgM,KAAK,GAAG,IAAI,CAACrN,GAAG,CAAC0I,MAAM,EAAE;MAC7B,IAAI4E,OAAO,GAAGlQ,GAAG,CAACmQ,gBAAgB,CAAE;QACnCC,OAAO,EAAE,IAAI,CAACxN;MACf,CAAC,CAAE;;MAEH;MACA5C,GAAG,CAACsJ,MAAM,CAAE;QACXsE,MAAM,EAAE,IAAI,CAAChL,GAAG;QAChByN,SAAS,EAAEH,OAAO,CAACrI,MAAM,GAAG,CAAC,GAAG,EAAE;QAClCyI,QAAQ,EAAE,YAAY;UACrBrM,KAAK,CAACqF,MAAM,EAAE;UACdtJ,GAAG,CAACkE,QAAQ,CAAE,sBAAsB,EAAED,KAAK,EAAEgM,KAAK,CAAE;QACrD;MACD,CAAC,CAAE;;MAEH;MACAjQ,GAAG,CAACkE,QAAQ,CAAE,qBAAqB,EAAED,KAAK,EAAEgM,KAAK,CAAE;IACpD,CAAC;IAED7G,SAAS,EAAE,YAAY;MACtB;MACA,IAAImH,MAAM,GAAGvQ,GAAG,CAACwQ,MAAM,CAAE,QAAQ,CAAE;;MAEnC;MACA,IAAIC,SAAS,GAAGzQ,GAAG,CAACoJ,SAAS,CAAE;QAC9BwE,MAAM,EAAE,IAAI,CAAChL,GAAG;QAChB8N,MAAM,EAAE,IAAI,CAACvM,GAAG,CAAE,IAAI,CAAE;QACxBwM,OAAO,EAAEJ;MACV,CAAC,CAAE;;MAEH;MACAE,SAAS,CAAC3H,IAAI,CAAE,UAAU,EAAEyH,MAAM,CAAE;;MAEpC;MACA,IAAIrG,QAAQ,GAAGlK,GAAG,CAACM,cAAc,CAAEmQ,SAAS,CAAE;;MAE9C;MACA,IAAK,IAAI,CAAClD,MAAM,EAAE,EAAG;QACpB,IAAI,CAACxH,KAAK,EAAE;MACb,CAAC,MAAM;QACNmE,QAAQ,CAACvE,IAAI,EAAE;MAChB;;MAEA;MACA,IAAIiL,MAAM,GAAG1G,QAAQ,CAACe,QAAQ,CAAE,aAAa,CAAE;MAC/CpC,UAAU,CAAE,YAAY;QACvB+H,MAAM,CAACnC,OAAO,CAAE,OAAO,CAAE;MAC1B,CAAC,EAAE,GAAG,CAAE;;MAER;MACA,IAAIlG,KAAK,GAAG2B,QAAQ,CAACpJ,IAAI,CAAE,OAAO,CAAE;MACpC,IAAIF,IAAI,GAAGsJ,QAAQ,CAACpJ,IAAI,CAAE,MAAM,CAAE;MAClC,IAAI+P,GAAG,GAAGjQ,IAAI,CAACW,KAAK,CAAE,GAAG,CAAE,CAAC6J,GAAG,EAAE;MACjC,IAAI0F,IAAI,GAAG9Q,GAAG,CAACuH,EAAE,CAAE,MAAM,CAAE;;MAE3B;MACA,IAAKvH,GAAG,CAAC+Q,SAAS,CAAEF,GAAG,CAAE,EAAG;QAC3B,IAAIG,CAAC,GAAGH,GAAG,GAAG,CAAC,GAAG,CAAC;QACnBtI,KAAK,GAAGA,KAAK,CAACoI,OAAO,CAAEE,GAAG,EAAEG,CAAC,CAAE;QAC/BpQ,IAAI,GAAGA,IAAI,CAAC+P,OAAO,CAAEE,GAAG,EAAEG,CAAC,CAAE;;QAE7B;MACD,CAAC,MAAM,IAAKH,GAAG,CAACtO,OAAO,CAAEuO,IAAI,CAAE,KAAK,CAAC,EAAG;QACvC,IAAIE,CAAC,GAAGH,GAAG,CAACF,OAAO,CAAEG,IAAI,EAAE,EAAE,CAAE,GAAG,CAAC;QACnCE,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC;;QAEjB;QACAzI,KAAK,GAAGA,KAAK,CAACoI,OAAO,CAAEE,GAAG,EAAEC,IAAI,GAAGE,CAAC,CAAE;QACtCpQ,IAAI,GAAGA,IAAI,CAAC+P,OAAO,CAAEE,GAAG,EAAEC,IAAI,GAAGE,CAAC,CAAE;;QAEpC;MACD,CAAC,MAAM;QACNzI,KAAK,IAAI,IAAI,GAAGuI,IAAI,GAAG,GAAG;QAC1BlQ,IAAI,IAAI,GAAG,GAAGkQ,IAAI;MACnB;MAEA5G,QAAQ,CAACpJ,IAAI,CAAE,IAAI,EAAE,CAAC,CAAE;MACxBoJ,QAAQ,CAACpJ,IAAI,CAAE,OAAO,EAAEyH,KAAK,CAAE;MAC/B2B,QAAQ,CAACpJ,IAAI,CAAE,MAAM,EAAEF,IAAI,CAAE;MAC7BsJ,QAAQ,CAACpJ,IAAI,CAAE,KAAK,EAAEyP,MAAM,CAAE;;MAE9B;MACAvQ,GAAG,CAACkE,QAAQ,CAAE,wBAAwB,EAAE,IAAI,EAAEgG,QAAQ,CAAE;MACxDlK,GAAG,CAACkE,QAAQ,CAAE,qBAAqB,EAAEgG,QAAQ,CAAE;IAChD,CAAC;IAED+G,IAAI,EAAE,YAAY;MACjB;MACA,IAAIC,MAAM,GAAG,IAAI,CAAC/M,GAAG,CAAE,IAAI,CAAE;MAC7B,IAAIgN,OAAO,GAAG,IAAI,CAAChN,GAAG,CAAE,KAAK,CAAE;MAC/B,IAAIoM,MAAM,GAAGvQ,GAAG,CAACwQ,MAAM,CAAE,QAAQ,CAAE;;MAEnC;MACAxQ,GAAG,CAACoR,MAAM,CAAE;QACXxD,MAAM,EAAE,IAAI,CAAChL,GAAG;QAChB8N,MAAM,EAAEQ,MAAM;QACdP,OAAO,EAAEJ;MACV,CAAC,CAAE;;MAEH;MACA,IAAI,CAACxO,GAAG,CAAE,IAAI,EAAEwO,MAAM,CAAE;MACxB,IAAI,CAACxO,GAAG,CAAE,QAAQ,EAAEmP,MAAM,CAAE;MAC5B,IAAI,CAACnP,GAAG,CAAE,SAAS,EAAEoP,OAAO,CAAE;;MAE9B;MACA,IAAI,CAACrQ,IAAI,CAAE,KAAK,EAAEyP,MAAM,CAAE;MAC1B,IAAI,CAACzP,IAAI,CAAE,IAAI,EAAE,CAAC,CAAE;;MAEpB;MACA,IAAI,CAAC8B,GAAG,CAACkG,IAAI,CAAE,UAAU,EAAEyH,MAAM,CAAE;MACnC,IAAI,CAAC3N,GAAG,CAACkG,IAAI,CAAE,SAAS,EAAEyH,MAAM,CAAE;;MAElC;MACAvQ,GAAG,CAACkE,QAAQ,CAAE,mBAAmB,EAAE,IAAI,CAAE;IAC1C,CAAC;IAEDmN,IAAI,EAAE,YAAY;MACjB;MACA,IAAIC,UAAU,GAAG,UAAWrN,KAAK,EAAG;QACnC,OAAOA,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,IAAI,UAAU;MACzC,CAAC;;MAED;MACA,IAAIuG,OAAO,GAAG4G,UAAU,CAAE,IAAI,CAAE;;MAEhC;MACA,IAAK,CAAE5G,OAAO,EAAG;QAChB1K,GAAG,CAACgH,eAAe,CAAE;UACpBsE,MAAM,EAAE,IAAI,CAAC1I;QACd,CAAC,CAAE,CAACqE,GAAG,CAAE,UAAWhD,KAAK,EAAG;UAC3ByG,OAAO,GAAG4G,UAAU,CAAErN,KAAK,CAAE,IAAIA,KAAK,CAACyG,OAAO;QAC/C,CAAC,CAAE;MACJ;;MAEA;MACA,IAAKA,OAAO,EAAG;QACd0E,KAAK,CACJpP,GAAG,CAACuH,EAAE,CACL,8DAA8D,CAC9D,CACD;QACD;MACD;;MAEA;MACA,IAAIJ,EAAE,GAAG,IAAI,CAACrG,IAAI,CAAE,IAAI,CAAE;MAC1B,IAAImD,KAAK,GAAG,IAAI;MAChB,IAAIsN,KAAK,GAAG,KAAK;MACjB,IAAIC,KAAK,GAAG,YAAY;QACvB;QACAD,KAAK,GAAGvR,GAAG,CAACyR,QAAQ,CAAE;UACrBC,KAAK,EAAE1R,GAAG,CAACuH,EAAE,CAAE,mBAAmB,CAAE;UACpCoK,OAAO,EAAE,IAAI;UACbC,KAAK,EAAE,OAAO;UACdC,QAAQ,EAAE5N,KAAK,CAACrB,GAAG,CAACI,IAAI,CAAE,aAAa;QACxC,CAAC,CAAE;;QAEH;QACA,IAAI8O,QAAQ,GAAG;UACdC,MAAM,EAAE,4BAA4B;UACpCC,QAAQ,EAAE7K;QACX,CAAC;;QAED;QACAtH,CAAC,CAACoS,IAAI,CAAE;UACPC,GAAG,EAAElS,GAAG,CAACmE,GAAG,CAAE,SAAS,CAAE;UACzBpB,IAAI,EAAE/C,GAAG,CAACmS,cAAc,CAAEL,QAAQ,CAAE;UACpCzR,IAAI,EAAE,MAAM;UACZ+R,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAEC;QACV,CAAC,CAAE;MACJ,CAAC;MAED,IAAIA,KAAK,GAAG,UAAW1J,IAAI,EAAG;QAC7B;QACA2I,KAAK,CAACI,OAAO,CAAE,KAAK,CAAE;QACtBJ,KAAK,CAACgB,OAAO,CAAE3J,IAAI,CAAE;;QAErB;QACA2I,KAAK,CAAC7O,EAAE,CAAE,QAAQ,EAAE,MAAM,EAAE8P,KAAK,CAAE;MACpC,CAAC;MAED,IAAIA,KAAK,GAAG,UAAW7P,CAAC,EAAEC,GAAG,EAAG;QAC/B;QACAD,CAAC,CAAC8P,cAAc,EAAE;;QAElB;QACAzS,GAAG,CAAC0S,kBAAkB,CAAEnB,KAAK,CAAC1R,CAAC,CAAE,SAAS,CAAE,CAAE;;QAE9C;QACA,IAAIiS,QAAQ,GAAG;UACdC,MAAM,EAAE,4BAA4B;UACpCC,QAAQ,EAAE7K,EAAE;UACZwL,cAAc,EAAEpB,KAAK,CAAC1R,CAAC,CAAE,QAAQ,CAAE,CAACsI,GAAG;QACxC,CAAC;;QAED;QACAtI,CAAC,CAACoS,IAAI,CAAE;UACPC,GAAG,EAAElS,GAAG,CAACmE,GAAG,CAAE,SAAS,CAAE;UACzBpB,IAAI,EAAE/C,GAAG,CAACmS,cAAc,CAAEL,QAAQ,CAAE;UACpCzR,IAAI,EAAE,MAAM;UACZ+R,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAEO;QACV,CAAC,CAAE;MACJ,CAAC;MAED,IAAIA,KAAK,GAAG,UAAWhK,IAAI,EAAG;QAC7B2I,KAAK,CAACgB,OAAO,CAAE3J,IAAI,CAAE;QAErB,IAAKiK,EAAE,CAACC,IAAI,IAAID,EAAE,CAACC,IAAI,CAACC,KAAK,IAAI/S,GAAG,CAACuH,EAAE,EAAG;UACzCsL,EAAE,CAACC,IAAI,CAACC,KAAK,CACZ/S,GAAG,CAACuH,EAAE,CAAE,4BAA4B,CAAE,EACtC,QAAQ,CACR;QACF;QAEAgK,KAAK,CAAC1R,CAAC,CAAE,kBAAkB,CAAE,CAACoD,KAAK,EAAE;QAErCgB,KAAK,CAACwL,aAAa,EAAE;MACtB,CAAC;;MAED;MACA+B,KAAK,EAAE;IACR,CAAC;IAEDwB,YAAY,EAAE,UAAWrQ,CAAC,EAAEC,GAAG,EAAG;MACjC;MACA,IAAK,IAAI,CAACqQ,aAAa,EAAG;QACzBC,YAAY,CAAE,IAAI,CAACD,aAAa,CAAE;MACnC;;MAEA;MACA;MACA,IAAI,CAACA,aAAa,GAAG,IAAI,CAACpK,UAAU,CAAE,YAAY;QACjD,IAAI,CAACsK,UAAU,CAAEvQ,GAAG,CAACuF,GAAG,EAAE,CAAE;MAC7B,CAAC,EAAE,GAAG,CAAE;IACT,CAAC;IAEDgL,UAAU,EAAE,UAAWC,OAAO,EAAG;MAChC,IAAIC,QAAQ,GAAG,IAAI,CAACvS,IAAI,CAAE,MAAM,CAAE;MAClC,IAAIwS,SAAS,GAAGtT,GAAG,CAACqN,UAAU,CAAE,mBAAmB,GAAGgG,QAAQ,CAAE;MAChE,IAAIE,QAAQ,GAAGvT,GAAG,CAACqN,UAAU,CAAE,mBAAmB,GAAG+F,OAAO,CAAE;;MAE9D;MACA,IAAI,CAACxQ,GAAG,CAACyD,WAAW,CAAEiN,SAAS,CAAE,CAACnN,QAAQ,CAAEoN,QAAQ,CAAE;MACtD,IAAI,CAAC3Q,GAAG,CAACkG,IAAI,CAAE,WAAW,EAAEsK,OAAO,CAAE;MACrC,IAAI,CAACxQ,GAAG,CAACG,IAAI,CAAE,MAAM,EAAEqQ,OAAO,CAAE;;MAEhC;MACA,IAAK,IAAI,CAACvH,GAAG,CAAE,KAAK,CAAE,EAAG;QACxB,IAAI,CAAC1H,GAAG,CAAE,KAAK,CAAE,CAACqP,KAAK,EAAE;MAC1B;;MAEA;MACA,MAAMC,YAAY,GAAG,CAAC,CAAC;MAEvB,IAAI,CAAC7Q,GAAG,CACNI,IAAI,CACJ,iFAAiF,CACjF,CACAuD,IAAI,CAAE,YAAY;QAClB,IAAImN,GAAG,GAAG7T,CAAC,CAAE,IAAI,CAAE,CAACkD,IAAI,CAAE,YAAY,CAAE;QACxC,IAAI4Q,YAAY,GAAG9T,CAAC,CAAE,IAAI,CAAE,CAACoK,QAAQ,EAAE,CAAC2J,UAAU,EAAE;QAEpDH,YAAY,CAAEC,GAAG,CAAE,GAAGC,YAAY;QAElCA,YAAY,CAAChL,MAAM,EAAE;MACtB,CAAC,CAAE;MAEJ,IAAI,CAAC5G,GAAG,CAAE,WAAW,GAAGsR,QAAQ,EAAEI,YAAY,CAAE;;MAEhD;MACA,IAAK,IAAI,CAAC5H,GAAG,CAAE,WAAW,GAAGuH,OAAO,CAAE,EAAG;QACxC,IAAIS,YAAY,GAAG,IAAI,CAAC1P,GAAG,CAAE,WAAW,GAAGiP,OAAO,CAAE;QAEpD,IAAI,CAACU,qBAAqB,CAAED,YAAY,CAAE;QAC1C,IAAI,CAAC9R,GAAG,CAAE,MAAM,EAAEqR,OAAO,CAAE;QAC3B;MACD;;MAEA;MACA,MAAMW,QAAQ,GAAGlU,CAAC,CACjB,2FAA2F,CAC3F;MACD,IAAI,CAAC+C,GAAG,CACNI,IAAI,CACJ,2DAA2D,CAC3D,CACAgR,MAAM,CAAED,QAAQ,CAAE;MAEpB,MAAMjC,QAAQ,GAAG;QAChBC,MAAM,EAAE,uCAAuC;QAC/C9N,KAAK,EAAE,IAAI,CAAC2K,SAAS,EAAE;QACvBqF,MAAM,EAAE,IAAI,CAAC1I,YAAY;MAC1B,CAAC;;MAED;MACA,IAAI2I,GAAG,GAAGrU,CAAC,CAACoS,IAAI,CAAE;QACjBC,GAAG,EAAElS,GAAG,CAACmE,GAAG,CAAE,SAAS,CAAE;QACzBpB,IAAI,EAAE/C,GAAG,CAACmS,cAAc,CAAEL,QAAQ,CAAE;QACpCzR,IAAI,EAAE,MAAM;QACZ+R,QAAQ,EAAE,MAAM;QAChB5P,OAAO,EAAE,IAAI;QACb6P,OAAO,EAAE,UAAW8B,QAAQ,EAAG;UAC9B,IAAK,CAAEnU,GAAG,CAACoU,aAAa,CAAED,QAAQ,CAAE,EAAG;YACtC;UACD;UAEA,IAAI,CAACL,qBAAqB,CAAEK,QAAQ,CAACpR,IAAI,CAAE;QAC5C,CAAC;QACDuN,QAAQ,EAAE,YAAY;UACrB;UACAyD,QAAQ,CAACzK,MAAM,EAAE;UACjB,IAAI,CAACvH,GAAG,CAAE,MAAM,EAAEqR,OAAO,CAAE;UAC3B;QACD;MACD,CAAC,CAAE;;MAEH;MACA,IAAI,CAACrR,GAAG,CAAE,KAAK,EAAEmS,GAAG,CAAE;IACvB,CAAC;IAEDJ,qBAAqB,EAAE,UAAWO,QAAQ,EAAG;MAC5C,IAAK,QAAQ,KAAK,OAAOA,QAAQ,EAAG;QACnC;MACD;MAEA,MAAM/N,IAAI,GAAG,IAAI;MACjB,MAAMgO,IAAI,GAAGrI,MAAM,CAACC,IAAI,CAAEmI,QAAQ,CAAE;MAEpCC,IAAI,CAACC,OAAO,CAAIb,GAAG,IAAM;QACxB,MAAMc,IAAI,GAAGlO,IAAI,CAAC1D,GAAG,CAACI,IAAI,CACzB,2BAA2B,GAC1B0Q,GAAG,GACH,2BAA2B,CAC5B;QACD,IAAIe,UAAU,GAAG,EAAE;QAEnB,IACC,CAAE,QAAQ,EAAE,QAAQ,CAAE,CAACC,QAAQ,CAAE,OAAOL,QAAQ,CAAEX,GAAG,CAAE,CAAE,EACxD;UACDe,UAAU,GAAGJ,QAAQ,CAAEX,GAAG,CAAE;QAC7B;QAEAc,IAAI,CAACG,OAAO,CAAEF,UAAU,CAAE;QAC1BzU,GAAG,CAACkE,QAAQ,CAAE,QAAQ,EAAEsQ,IAAI,CAAE;MAC/B,CAAC,CAAE;IACJ,CAAC;IAEDI,YAAY,EAAE,YAAY;MACzB;MACA,IAAIC,EAAE,GAAG7U,GAAG,CAACmE,GAAG,CAAE,SAAS,CAAE;;MAE7B;MACA,IAAImH,MAAM,GAAG,IAAI,CAACJ,SAAS,EAAE;MAC7B,IAAKI,MAAM,EAAG;QACbuJ,EAAE,GAAG1H,QAAQ,CAAE7B,MAAM,CAACxK,IAAI,CAAE,IAAI,CAAE,CAAE,IAAIwK,MAAM,CAACxK,IAAI,CAAE,KAAK,CAAE;MAC7D;;MAEA;MACA,IAAI,CAACA,IAAI,CAAE,QAAQ,EAAE+T,EAAE,CAAE;IAC1B;EACD,CAAC,CAAE;AACJ,CAAC,EAAIjQ,MAAM,CAAE;;;;;;;;;;ACj8Bb,CAAE,UAAW/E,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECE,GAAG,CAACoI,eAAe,GAAG,UAAWuC,GAAG,EAAG;IACtC,OAAO3K,GAAG,CAACmQ,gBAAgB,CAAE;MAC5BxF,GAAG,EAAEA,GAAG;MACRQ,KAAK,EAAE;IACR,CAAC,CAAE;EACJ,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECnL,GAAG,CAACmQ,gBAAgB,GAAG,UAAWb,IAAI,EAAG;IACxC;IACAA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,IAAI5N,QAAQ,GAAG,mBAAmB;IAClC,IAAIwO,OAAO,GAAG,KAAK;;IAEnB;IACAZ,IAAI,GAAGtP,GAAG,CAACuP,SAAS,CAAED,IAAI,EAAE;MAC3BnI,EAAE,EAAE,EAAE;MACNwD,GAAG,EAAE,EAAE;MACPtK,IAAI,EAAE,EAAE;MACR8K,KAAK,EAAE,KAAK;MACZ2J,IAAI,EAAE,IAAI;MACVxJ,MAAM,EAAE,KAAK;MACb8E,OAAO,EAAE,KAAK;MACd/F,KAAK,EAAE;IACR,CAAC,CAAE;;IAEH;IACA,IAAKiF,IAAI,CAACnI,EAAE,EAAG;MACdzF,QAAQ,IAAI,YAAY,GAAG4N,IAAI,CAACnI,EAAE,GAAG,IAAI;IAC1C;;IAEA;IACA,IAAKmI,IAAI,CAAC3E,GAAG,EAAG;MACfjJ,QAAQ,IAAI,aAAa,GAAG4N,IAAI,CAAC3E,GAAG,GAAG,IAAI;IAC5C;;IAEA;IACA,IAAK2E,IAAI,CAACjP,IAAI,EAAG;MAChBqB,QAAQ,IAAI,cAAc,GAAG4N,IAAI,CAACjP,IAAI,GAAG,IAAI;IAC9C;;IAEA;IACA,IAAKiP,IAAI,CAACwF,IAAI,EAAG;MAChB5E,OAAO,GAAGZ,IAAI,CAACwF,IAAI,CAAC7K,QAAQ,CAAEvI,QAAQ,CAAE;IACzC,CAAC,MAAM,IAAK4N,IAAI,CAAChE,MAAM,EAAG;MACzB4E,OAAO,GAAGZ,IAAI,CAAChE,MAAM,CAACtI,IAAI,CAAEtB,QAAQ,CAAE;IACvC,CAAC,MAAM,IAAK4N,IAAI,CAACc,OAAO,EAAG;MAC1BF,OAAO,GAAGZ,IAAI,CAACc,OAAO,CAACxG,QAAQ,CAAElI,QAAQ,CAAE;IAC5C,CAAC,MAAM,IAAK4N,IAAI,CAACjF,KAAK,EAAG;MACxB6F,OAAO,GAAGZ,IAAI,CAACjF,KAAK,CAAC0K,OAAO,CAAErT,QAAQ,CAAE;IACzC,CAAC,MAAM;MACNwO,OAAO,GAAGrQ,CAAC,CAAE6B,QAAQ,CAAE;IACxB;;IAEA;IACA,IAAK4N,IAAI,CAACnE,KAAK,EAAG;MACjB+E,OAAO,GAAGA,OAAO,CAAC8E,KAAK,CAAE,CAAC,EAAE1F,IAAI,CAACnE,KAAK,CAAE;IACzC;;IAEA;IACA,OAAO+E,OAAO;EACf,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEClQ,GAAG,CAACM,cAAc,GAAG,UAAWF,MAAM,EAAG;IACxC;IACA,IAAK,OAAOA,MAAM,KAAK,QAAQ,EAAG;MACjCA,MAAM,GAAGJ,GAAG,CAACoI,eAAe,CAAEhI,MAAM,CAAE;IACvC;;IAEA;IACA,IAAI6D,KAAK,GAAG7D,MAAM,CAAC2C,IAAI,CAAE,KAAK,CAAE;IAChC,IAAK,CAAEkB,KAAK,EAAG;MACdA,KAAK,GAAGjE,GAAG,CAACiV,cAAc,CAAE7U,MAAM,CAAE;IACrC;;IAEA;IACA,OAAO6D,KAAK;EACb,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECjE,GAAG,CAACgH,eAAe,GAAG,UAAWsI,IAAI,EAAG;IACvC;IACA,IAAIY,OAAO,GAAGlQ,GAAG,CAACmQ,gBAAgB,CAAEb,IAAI,CAAE;;IAE1C;IACA,IAAI4F,MAAM,GAAG,EAAE;IACfhF,OAAO,CAAC3J,IAAI,CAAE,YAAY;MACzB,IAAItC,KAAK,GAAGjE,GAAG,CAACM,cAAc,CAAET,CAAC,CAAE,IAAI,CAAE,CAAE;MAC3CqV,MAAM,CAACjN,IAAI,CAAEhE,KAAK,CAAE;IACrB,CAAC,CAAE;;IAEH;IACA,OAAOiR,MAAM;EACd,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEClV,GAAG,CAACiV,cAAc,GAAG,UAAW7U,MAAM,EAAG;IACxC;IACA,IAAI6D,KAAK,GAAG,IAAIjE,GAAG,CAACuK,WAAW,CAAEnK,MAAM,CAAE;;IAEzC;IACAJ,GAAG,CAACkE,QAAQ,CAAE,kBAAkB,EAAED,KAAK,CAAE;;IAEzC;IACA,OAAOA,KAAK;EACb,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIkR,YAAY,GAAG,IAAInV,GAAG,CAACoD,KAAK,CAAE;IACjCgS,QAAQ,EAAE,CAAC;IAEX9I,UAAU,EAAE,YAAY;MACvB;MACA,IAAIjJ,OAAO,GAAG,CAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAE;;MAExD;MACAA,OAAO,CAAC4D,GAAG,CAAE,UAAW8K,MAAM,EAAG;QAChC,IAAI,CAACsD,eAAe,CAAEtD,MAAM,CAAE;MAC/B,CAAC,EAAE,IAAI,CAAE;IACV,CAAC;IAEDsD,eAAe,EAAE,UAAWtD,MAAM,EAAG;MACpC;MACA,IAAIuD,YAAY,GAAGvD,MAAM,GAAG,gBAAgB,CAAC,CAAC;MAC9C,IAAIwD,YAAY,GAAGxD,MAAM,GAAG,eAAe,CAAC,CAAC;MAC7C,IAAIyD,WAAW,GAAGzD,MAAM,GAAG,aAAa,CAAC,CAAC;;MAE1C;MACA,IAAIlQ,QAAQ,GAAG,UAAWe,GAAG,CAAC,uBAAwB;QACrD;QACA,IAAI6S,YAAY,GAAGzV,GAAG,CAACgH,eAAe,CAAE;UAAEsE,MAAM,EAAE1I;QAAI,CAAC,CAAE;;QAEzD;QACA,IAAK6S,YAAY,CAAC5N,MAAM,EAAG;UAC1B;UACA,IAAIyH,IAAI,GAAGtP,GAAG,CAAC0V,SAAS,CAAEzT,SAAS,CAAE;;UAErC;UACAqN,IAAI,CAAC9N,MAAM,CAAE,CAAC,EAAE,CAAC,EAAE8T,YAAY,EAAEG,YAAY,CAAE;UAC/CzV,GAAG,CAACkE,QAAQ,CAAClC,KAAK,CAAE,IAAI,EAAEsN,IAAI,CAAE;QACjC;MACD,CAAC;;MAED;MACA,IAAIqG,cAAc,GAAG,UACpBF,YAAY,CAAC,uBACZ;QACD;QACA,IAAInG,IAAI,GAAGtP,GAAG,CAAC0V,SAAS,CAAEzT,SAAS,CAAE;;QAErC;QACAqN,IAAI,CAACsG,OAAO,CAAEL,YAAY,CAAE;;QAE5B;QACAE,YAAY,CAACxO,GAAG,CAAE,UAAWvB,WAAW,EAAG;UAC1C;UACA4J,IAAI,CAAE,CAAC,CAAE,GAAG5J,WAAW;UACvB1F,GAAG,CAACkE,QAAQ,CAAClC,KAAK,CAAE,IAAI,EAAEsN,IAAI,CAAE;QACjC,CAAC,CAAE;MACJ,CAAC;;MAED;MACA,IAAIuG,cAAc,GAAG,UACpBnQ,WAAW,CAAC,uBACX;QACD;QACA,IAAI4J,IAAI,GAAGtP,GAAG,CAAC0V,SAAS,CAAEzT,SAAS,CAAE;;QAErC;QACAqN,IAAI,CAACsG,OAAO,CAAEL,YAAY,CAAE;;QAE5B;QACA,IAAIO,UAAU,GAAG,CAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAE;QAC1CA,UAAU,CAAC7O,GAAG,CAAE,UAAW8O,SAAS,EAAG;UACtCzG,IAAI,CAAE,CAAC,CAAE,GACRiG,YAAY,GACZ,GAAG,GACHQ,SAAS,GACT,GAAG,GACHrQ,WAAW,CAACvB,GAAG,CAAE4R,SAAS,CAAE;UAC7B/V,GAAG,CAACkE,QAAQ,CAAClC,KAAK,CAAE,IAAI,EAAEsN,IAAI,CAAE;QACjC,CAAC,CAAE;;QAEH;QACAA,IAAI,CAAC9N,MAAM,CAAE,CAAC,EAAE,CAAC,CAAE;;QAEnB;QACAkE,WAAW,CAAC+I,OAAO,CAAE+G,WAAW,EAAElG,IAAI,CAAE;MACzC,CAAC;;MAED;MACAtP,GAAG,CAACgW,SAAS,CAAEjE,MAAM,EAAElQ,QAAQ,EAAE,CAAC,CAAE;MACpC7B,GAAG,CAACgW,SAAS,CAAEV,YAAY,EAAEK,cAAc,EAAE,CAAC,CAAE;MAChD3V,GAAG,CAACgW,SAAS,CAAET,YAAY,EAAEM,cAAc,EAAE,CAAC,CAAE;IACjD;EACD,CAAC,CAAE;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAII,YAAY,GAAG,IAAIjW,GAAG,CAACoD,KAAK,CAAE;IACjC+D,EAAE,EAAE,cAAc;IAElBpC,MAAM,EAAE;MACP,cAAc,EAAE,UAAU;MAC1B,4BAA4B,EAAE,iBAAiB;MAC/C,kBAAkB,EAAE;IACrB,CAAC;IAED1B,OAAO,EAAE;MACR6S,oBAAoB,EAAE,gBAAgB;MACtCnS,qBAAqB,EAAE,gBAAgB;MACvCL,mBAAmB,EAAE,eAAe;MACpCC,wBAAwB,EAAE,mBAAmB;MAC7CF,sBAAsB,EAAE;IACzB,CAAC;IAED0S,QAAQ,EAAE,UAAWxT,CAAC,EAAEC,GAAG,EAAG;MAC7B;MACA,IAAIsS,MAAM,GAAGlV,GAAG,CAACgH,eAAe,EAAE;;MAElC;MACAkO,MAAM,CAACjO,GAAG,CAAE,UAAWhD,KAAK,EAAG;QAC9BA,KAAK,CAAC4K,MAAM,EAAE;MACf,CAAC,CAAE;IACJ,CAAC;IAEDuH,iBAAiB,EAAE,UAAWnS,KAAK,EAAG;MACrC,IAAI,CAACoS,YAAY,CAAEpS,KAAK,CAACrB,GAAG,CAAC0I,MAAM,EAAE,CAAE;IACxC,CAAC;IAEDgL,eAAe,EAAE,UAAW3T,CAAC,EAAEC,GAAG,EAAG;MACpC;MACA,IAAKA,GAAG,CAAC4K,QAAQ,CAAE,aAAa,CAAE,EAAG;;MAErC;MACA5K,GAAG,CAAC2T,QAAQ,CAAE;QACbC,MAAM,EAAE,UAAUnU,KAAK,EAAEoU,OAAO,EAAG;UAClC;UACA,OAAOA,OAAO,CAACC,KAAK,EAAE,CACpB1T,IAAI,CAAE,QAAQ,CAAE,CACf8F,IAAI,CAAE,MAAM,EAAE,UAAUkI,CAAC,EAAE2F,WAAW,EAAG;YACxC,OAAO,OAAO,GAAGxJ,QAAQ,CAAEyJ,IAAI,CAACC,MAAM,EAAE,GAAG,MAAM,EAAE,EAAE,CAAE,CAACC,QAAQ,EAAE,GAAG,GAAG,GAAGH,WAAW;UACxF,CAAC,CAAE,CACH9F,GAAG,EAAE;QACR,CAAC;QACDkG,MAAM,EAAE,sBAAsB;QAC9BC,WAAW,EAAE,iBAAiB;QAC9BC,KAAK,EAAE,UAAWtU,CAAC,EAAEuU,EAAE,EAAG;UACzB,IAAIjT,KAAK,GAAGjE,GAAG,CAACM,cAAc,CAAE4W,EAAE,CAACC,IAAI,CAAE;UACzCD,EAAE,CAACE,WAAW,CAACC,MAAM,CAAEH,EAAE,CAACC,IAAI,CAACE,MAAM,EAAE,CAAE;UACzCrX,GAAG,CAACkE,QAAQ,CAAE,wBAAwB,EAAED,KAAK,EAAErB,GAAG,CAAE;QACrD,CAAC;QACD0U,MAAM,EAAE,UAAW3U,CAAC,EAAEuU,EAAE,EAAG;UAC1B,IAAIjT,KAAK,GAAGjE,GAAG,CAACM,cAAc,CAAE4W,EAAE,CAACC,IAAI,CAAE;UACzCnX,GAAG,CAACkE,QAAQ,CAAE,uBAAuB,EAAED,KAAK,EAAErB,GAAG,CAAE;QACpD;MACD,CAAC,CAAE;IACJ,CAAC;IAED2U,cAAc,EAAE,UAAWtT,KAAK,EAAEgM,KAAK,EAAG;MACzC,IAAI,CAACoG,YAAY,CAAEpG,KAAK,CAAE;IAC3B,CAAC;IAEDuH,cAAc,EAAE,UAAWvT,KAAK,EAAEgM,KAAK,EAAG;MACzChM,KAAK,CAAC2Q,YAAY,EAAE;MACpB,IAAI,CAACyB,YAAY,CAAEpG,KAAK,CAAE;IAC3B,CAAC;IAEDwH,aAAa,EAAE,UAAWxT,KAAK,EAAG;MACjC;MACAA,KAAK,CAACoH,SAAS,EAAE,CAACpE,GAAG,CAAE,UAAWoD,KAAK,EAAG;QACzCA,KAAK,CAAC3J,MAAM,CAAE;UAAED,OAAO,EAAE;QAAM,CAAC,CAAE;MACnC,CAAC,CAAE;IACJ,CAAC;IAEDiX,iBAAiB,EAAE,UAAWzT,KAAK,EAAG;MACrC;MACA;IAAA,CACA;IAED0T,gBAAgB,EAAE,UAAW1T,KAAK,EAAEiG,QAAQ,EAAG;MAC9C;MACA,IAAID,QAAQ,GAAGC,QAAQ,CAACmB,SAAS,EAAE;MACnC,IAAKpB,QAAQ,CAACpC,MAAM,EAAG;QACtB;QACAoC,QAAQ,CAAChD,GAAG,CAAE,UAAWoD,KAAK,EAAG;UAChC;UACAA,KAAK,CAAC4G,IAAI,EAAE;;UAEZ;UACA5G,KAAK,CAACuK,YAAY,EAAE;QACrB,CAAC,CAAE;;QAEH;QACA5U,GAAG,CAACkE,QAAQ,CACX,yBAAyB,EACzB+F,QAAQ,EACRC,QAAQ,EACRjG,KAAK,CACL;MACF;;MAEA;MACA,IAAI,CAACmS,iBAAiB,CAAElM,QAAQ,CAAE;IACnC,CAAC;IAEDmM,YAAY,EAAE,UAAWpG,KAAK,EAAG;MAChC;MACA,IAAIiF,MAAM,GAAGlV,GAAG,CAACgH,eAAe,CAAE;QACjC8N,IAAI,EAAE7E;MACP,CAAC,CAAE;;MAEH;MACA,IAAK,CAAEiF,MAAM,CAACrN,MAAM,EAAG;QACtBoI,KAAK,CAAC9J,QAAQ,CAAE,QAAQ,CAAE;QAC1B8J,KAAK,CACH8E,OAAO,CAAE,sBAAsB,CAAE,CACjC6C,KAAK,EAAE,CACPzR,QAAQ,CAAE,QAAQ,CAAE;QACtB;MACD;;MAEA;MACA8J,KAAK,CAAC5J,WAAW,CAAE,QAAQ,CAAE;MAC7B4J,KAAK,CACH8E,OAAO,CAAE,sBAAsB,CAAE,CACjC6C,KAAK,EAAE,CACPvR,WAAW,CAAE,QAAQ,CAAE;;MAEzB;MACA6O,MAAM,CAACjO,GAAG,CAAE,UAAWhD,KAAK,EAAE+M,CAAC,EAAG;QACjC/M,KAAK,CAACnD,IAAI,CAAE,YAAY,EAAEkQ,CAAC,CAAE;MAC9B,CAAC,CAAE;IACJ,CAAC;IAEDtH,UAAU,EAAE,UAAW/G,CAAC,EAAEC,GAAG,EAAG;MAC/B,IAAIqN,KAAK;MAET,IAAKrN,GAAG,CAAC4K,QAAQ,CAAE,iBAAiB,CAAE,EAAG;QACxCyC,KAAK,GAAGrN,GAAG,CAACmS,OAAO,CAAE,iBAAiB,CAAE,CAAC8C,EAAE,CAAE,CAAC,CAAE;MACjD,CAAC,MAAM,IACNjV,GAAG,CAAC0I,MAAM,EAAE,CAACkC,QAAQ,CAAE,uBAAuB,CAAE,IAChD5K,GAAG,CAAC0I,MAAM,EAAE,CAACkC,QAAQ,CAAE,yBAAyB,CAAE,EACjD;QACDyC,KAAK,GAAGpQ,CAAC,CAAE,uBAAuB,CAAE;MACrC,CAAC,MAAM,IAAK+C,GAAG,CAAC0I,MAAM,EAAE,CAACkC,QAAQ,CAAE,2BAA2B,CAAE,EAAG;QAClEyC,KAAK,GAAGrN,GAAG,CACTmS,OAAO,CAAE,kBAAkB,CAAE,CAC7B/R,IAAI,CAAE,uBAAuB,CAAE;MAClC,CAAC,MAAM;QACNiN,KAAK,GAAGrN,GAAG,CACTC,OAAO,CAAE,YAAY,CAAE,CACvB+G,QAAQ,CAAE,iBAAiB,CAAE;MAChC;MAEA,IAAI,CAACkO,QAAQ,CAAE7H,KAAK,CAAE;IACvB,CAAC;IAED6H,QAAQ,EAAE,UAAW7H,KAAK,EAAG;MAC5B;MACA,IAAIrH,IAAI,GAAG/I,CAAC,CAAE,iBAAiB,CAAE,CAAC+I,IAAI,EAAE;MACxC,IAAIhG,GAAG,GAAG/C,CAAC,CAAE+I,IAAI,CAAE;MACnB,IAAIsI,MAAM,GAAGtO,GAAG,CAACG,IAAI,CAAE,IAAI,CAAE;MAC7B,IAAIwN,MAAM,GAAGvQ,GAAG,CAACwQ,MAAM,CAAE,QAAQ,CAAE;;MAEnC;MACA,IAAIC,SAAS,GAAGzQ,GAAG,CAACoJ,SAAS,CAAE;QAC9BwE,MAAM,EAAEhL,GAAG;QACX8N,MAAM,EAAEQ,MAAM;QACdP,OAAO,EAAEJ,MAAM;QACf5E,MAAM,EAAE,UAAW/I,GAAG,EAAEmV,IAAI,EAAG;UAC9B9H,KAAK,CAACtE,MAAM,CAAEoM,IAAI,CAAE;QACrB;MACD,CAAC,CAAE;;MAEH;MACA,IAAI7N,QAAQ,GAAGlK,GAAG,CAACM,cAAc,CAAEmQ,SAAS,CAAE;;MAE9C;MACAvG,QAAQ,CAACpJ,IAAI,CAAE,KAAK,EAAEyP,MAAM,CAAE;MAC9BrG,QAAQ,CAACpJ,IAAI,CAAE,IAAI,EAAE,CAAC,CAAE;MACxBoJ,QAAQ,CAACpJ,IAAI,CAAE,OAAO,EAAE,EAAE,CAAE;MAC5BoJ,QAAQ,CAACpJ,IAAI,CAAE,MAAM,EAAE,EAAE,CAAE;;MAE3B;MACA2P,SAAS,CAAC3H,IAAI,CAAE,UAAU,EAAEyH,MAAM,CAAE;MACpCE,SAAS,CAAC3H,IAAI,CAAE,SAAS,EAAEyH,MAAM,CAAE;;MAEnC;MACArG,QAAQ,CAAC0K,YAAY,EAAE;;MAEvB;MACA,IAAIoD,KAAK,GAAG9N,QAAQ,CAAC/E,MAAM,CAAE,MAAM,CAAE;MACrC0D,UAAU,CAAE,YAAY;QACvB,IAAKoH,KAAK,CAACzC,QAAQ,CAAE,oBAAoB,CAAE,EAAG;UAC7CyC,KAAK,CAAC5J,WAAW,CAAE,oBAAoB,CAAE;QAC1C,CAAC,MAAM;UACN2R,KAAK,CAACvJ,OAAO,CAAE,OAAO,CAAE;QACzB;MACD,CAAC,EAAE,GAAG,CAAE;;MAER;MACAvE,QAAQ,CAACvE,IAAI,EAAE;;MAEf;MACA,IAAI,CAAC0Q,YAAY,CAAEpG,KAAK,CAAE;;MAE1B;MACAjQ,GAAG,CAACkE,QAAQ,CAAE,kBAAkB,EAAEgG,QAAQ,CAAE;MAC5ClK,GAAG,CAACkE,QAAQ,CAAE,qBAAqB,EAAEgG,QAAQ,CAAE;IAChD;EACD,CAAC,CAAE;AACJ,CAAC,EAAItF,MAAM,CAAE;;;;;;;;;;AC3eb,CAAE,UAAW/E,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAImY,eAAe,GAAG,IAAIjY,GAAG,CAACoD,KAAK,CAAE;IACpC+D,EAAE,EAAE,iBAAiB;IACrB+Q,IAAI,EAAE,OAAO;IAEbnT,MAAM,EAAE;MACP,0BAA0B,EAAE,gBAAgB;MAC5C,2BAA2B,EAAE,iBAAiB;MAC9C,6BAA6B,EAAE,mBAAmB;MAClD,+BAA+B,EAAE;IAClC,CAAC;IAEDuH,UAAU,EAAE,YAAY;MACvB,IAAI,CAAC1J,GAAG,GAAG/C,CAAC,CAAE,0BAA0B,CAAE;MAC1C,IAAI,CAACsY,iBAAiB,EAAE;IACzB,CAAC;IAEDC,cAAc,EAAE,UAAWzV,CAAC,EAAEC,GAAG,EAAG;MACnC,IAAI,CAACyV,OAAO,CAAEzV,GAAG,CAACC,OAAO,CAAE,IAAI,CAAE,CAAE;IACpC,CAAC;IAEDyV,iBAAiB,EAAE,UAAW3V,CAAC,EAAEC,GAAG,EAAG;MACtC,IAAI,CAAC2V,UAAU,CAAE3V,GAAG,CAACC,OAAO,CAAE,IAAI,CAAE,CAAE;IACvC,CAAC;IAED2V,kBAAkB,EAAE,UAAW7V,CAAC,EAAEC,GAAG,EAAG;MACvC,IAAI,CAAC6V,UAAU,CAAE7V,GAAG,CAACC,OAAO,CAAE,IAAI,CAAE,CAAE;IACvC,CAAC;IAEDmG,eAAe,EAAE,UAAWrG,CAAC,EAAEC,GAAG,EAAG;MACpC,IAAI,CAACqG,QAAQ,EAAE;IAChB,CAAC;IAEDoP,OAAO,EAAE,UAAWK,GAAG,EAAG;MACzB1Y,GAAG,CAACoJ,SAAS,CAAEsP,GAAG,CAAE;MACpB,IAAI,CAACP,iBAAiB,EAAE;IACzB,CAAC;IAEDI,UAAU,EAAE,UAAWG,GAAG,EAAG;MAC5B,IAAKA,GAAG,CAAC9O,QAAQ,CAAE,IAAI,CAAE,CAAC/B,MAAM,IAAI,CAAC,EAAG;QACvC6Q,GAAG,CAAC7V,OAAO,CAAE,aAAa,CAAE,CAACyG,MAAM,EAAE;MACtC,CAAC,MAAM;QACNoP,GAAG,CAACpP,MAAM,EAAE;MACb;;MAEA;MACA,IAAIJ,MAAM,GAAG,IAAI,CAACrJ,CAAC,CAAE,mBAAmB,CAAE;MAC1CqJ,MAAM,CAAClG,IAAI,CAAE,IAAI,CAAE,CAACqE,IAAI,CAAErH,GAAG,CAACuH,EAAE,CAAE,0BAA0B,CAAE,CAAE;MAEhE,IAAI,CAAC4Q,iBAAiB,EAAE;IACzB,CAAC;IAEDM,UAAU,EAAE,UAAWzT,KAAK,EAAG;MAC9B;MACA,IAAIkE,MAAM,GAAGlE,KAAK,CAACnC,OAAO,CAAE,aAAa,CAAE;MAC3C,IAAIoR,MAAM,GAAGjP,KAAK,CAChBhC,IAAI,CAAE,iBAAiB,CAAE,CACzB8F,IAAI,CAAE,MAAM,CAAE,CACd6H,OAAO,CAAE,SAAS,EAAE,EAAE,CAAE;;MAE1B;MACA,IAAIgI,QAAQ,GAAG,CAAC,CAAC;MACjBA,QAAQ,CAAC5G,MAAM,GAAG,sCAAsC;MACxD4G,QAAQ,CAACC,IAAI,GAAG5Y,GAAG,CAAC4O,SAAS,CAAE5J,KAAK,EAAEiP,MAAM,CAAE;MAC9C0E,QAAQ,CAACC,IAAI,CAACzR,EAAE,GAAGnC,KAAK,CAACjC,IAAI,CAAE,IAAI,CAAE;MACrC4V,QAAQ,CAACC,IAAI,CAACC,KAAK,GAAG3P,MAAM,CAACnG,IAAI,CAAE,IAAI,CAAE;;MAEzC;MACA/C,GAAG,CAACiG,OAAO,CAAEjB,KAAK,CAAChC,IAAI,CAAE,UAAU,CAAE,CAAE;;MAEvC;MACAnD,CAAC,CAACoS,IAAI,CAAE;QACPC,GAAG,EAAElS,GAAG,CAACmE,GAAG,CAAE,SAAS,CAAE;QACzBpB,IAAI,EAAE/C,GAAG,CAACmS,cAAc,CAAEwG,QAAQ,CAAE;QACpCtY,IAAI,EAAE,MAAM;QACZ+R,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,UAAWzJ,IAAI,EAAG;UAC1B,IAAK,CAAEA,IAAI,EAAG;UACd5D,KAAK,CAAC8T,WAAW,CAAElQ,IAAI,CAAE;QAC1B;MACD,CAAC,CAAE;IACJ,CAAC;IAEDK,QAAQ,EAAE,YAAY;MACrB;MACA,IAAIC,MAAM,GAAG,IAAI,CAACrJ,CAAC,CAAE,kBAAkB,CAAE;;MAEzC;MACAsJ,OAAO,GAAGnJ,GAAG,CAACoJ,SAAS,CAAEF,MAAM,CAAE;;MAEjC;MACAC,OAAO,CAACnG,IAAI,CAAE,IAAI,CAAE,CAACqE,IAAI,CAAErH,GAAG,CAACuH,EAAE,CAAE,IAAI,CAAE,CAAE;;MAE3C;MACA4B,OAAO,CAACnG,IAAI,CAAE,IAAI,CAAE,CAACqG,GAAG,CAAE,QAAQ,CAAE,CAACC,MAAM,EAAE;;MAE7C;MACA,IAAI,CAAC6O,iBAAiB,EAAE;IACzB,CAAC;IAEDA,iBAAiB,EAAE,YAAY;MAC9B,IAAIjP,MAAM,GAAG,IAAI,CAACrJ,CAAC,CAAE,kBAAkB,CAAE;MAEzC,IAAIkZ,WAAW,GAAG7P,MAAM,CAACrG,OAAO,CAAE,cAAc,CAAE;MAElD,IAAImW,UAAU,GAAGD,WAAW,CAAC/V,IAAI,CAAE,eAAe,CAAE,CAAC6E,MAAM;MAE3D,IAAKmR,UAAU,GAAG,CAAC,EAAG;QACrBD,WAAW,CAAC5S,QAAQ,CAAE,sBAAsB,CAAE;MAC/C,CAAC,MAAM;QACN4S,WAAW,CAAC1S,WAAW,CAAE,sBAAsB,CAAE;MAClD;IACD;EACD,CAAC,CAAE;AACJ,CAAC,EAAIzB,MAAM,CAAE;;;;;;;;;;AC9Hb,CAAE,UAAW/E,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAImZ,OAAO,GAAG,UAAW5Y,IAAI,EAAG;IAC/B,OAAOL,GAAG,CAACkZ,aAAa,CAAE7Y,IAAI,IAAI,EAAE,CAAE,GAAG,cAAc;EACxD,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECL,GAAG,CAAC6J,oBAAoB,GAAG,UAAW5I,KAAK,EAAG;IAC7C,IAAIkY,KAAK,GAAGlY,KAAK,CAACoH,SAAS;IAC3B,IAAI+Q,GAAG,GAAGH,OAAO,CAAEE,KAAK,CAAC9Y,IAAI,GAAG,GAAG,GAAG8Y,KAAK,CAACvY,IAAI,CAAE;IAClD,IAAI,CAACyY,MAAM,CAAED,GAAG,CAAE,GAAGnY,KAAK;EAC3B,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECjB,GAAG,CAACsZ,eAAe,GAAG,UAAWrV,KAAK,EAAG;IACxC;IACA,IAAI5D,IAAI,GAAG4D,KAAK,CAACE,GAAG,CAAE,SAAS,CAAE,IAAI,EAAE;IACvC,IAAIvD,IAAI,GAAGqD,KAAK,CAACE,GAAG,CAAE,MAAM,CAAE,IAAI,EAAE;IACpC,IAAIiV,GAAG,GAAGH,OAAO,CAAE5Y,IAAI,GAAG,GAAG,GAAGO,IAAI,CAAE;IACtC,IAAIK,KAAK,GAAGjB,GAAG,CAACqZ,MAAM,CAAED,GAAG,CAAE,IAAI,IAAI;;IAErC;IACA,IAAKnY,KAAK,KAAK,IAAI,EAAG,OAAO,KAAK;;IAElC;IACA,IAAIiC,OAAO,GAAG,IAAIjC,KAAK,CAAEgD,KAAK,CAAE;;IAEhC;IACA,OAAOf,OAAO;EACf,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEClD,GAAG,CAACuZ,eAAe,GAAG,UAAWtV,KAAK,EAAG;IACxC;IACA,IAAKA,KAAK,YAAYW,MAAM,EAAG;MAC9BX,KAAK,GAAGjE,GAAG,CAACwZ,QAAQ,CAAEvV,KAAK,CAAE;IAC9B;;IAEA;IACA,OAAOA,KAAK,CAACf,OAAO;EACrB,CAAC;;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIuW,eAAe,GAAG,IAAIzZ,GAAG,CAACoD,KAAK,CAAE;IACpCC,OAAO,EAAE;MACRqW,SAAS,EAAE;IACZ,CAAC;IACDC,UAAU,EAAE,UAAW1V,KAAK,EAAG;MAC9BA,KAAK,CAACf,OAAO,GAAGlD,GAAG,CAACsZ,eAAe,CAAErV,KAAK,CAAE;IAC7C;EACD,CAAC,CAAE;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACCjE,GAAG,CAAC8E,YAAY,GAAG9E,GAAG,CAACoD,KAAK,CAAClC,MAAM,CAAE;IACpC+C,KAAK,EAAE,KAAK;IACZ5D,IAAI,EAAE,EAAE;IACRO,IAAI,EAAE,EAAE;IACRsX,IAAI,EAAE,OAAO;IACb1N,UAAU,EAAE,YAAY;IAExBzF,MAAM,EAAE;MACP0F,MAAM,EAAE;IACT,CAAC;IAEDG,KAAK,EAAE,UAAW3G,KAAK,EAAG;MACzB;MACA,IAAI7D,MAAM,GAAG6D,KAAK,CAACrB,GAAG;;MAEtB;MACA,IAAI,CAACA,GAAG,GAAGxC,MAAM;MACjB,IAAI,CAAC6D,KAAK,GAAGA,KAAK;MAClB,IAAI,CAAC2V,YAAY,GAAGxZ,MAAM,CAACyC,OAAO,CAAE,mBAAmB,CAAE;MACzD,IAAI,CAAC6C,WAAW,GAAG1F,GAAG,CAACM,cAAc,CAAE,IAAI,CAACsZ,YAAY,CAAE;;MAE1D;MACA/Z,CAAC,CAACqB,MAAM,CAAE,IAAI,CAAC6B,IAAI,EAAEkB,KAAK,CAAClB,IAAI,CAAE;IAClC,CAAC;IAEDuJ,UAAU,EAAE,YAAY;MACvB,IAAI,CAACpG,MAAM,EAAE;IACd,CAAC;IAEDA,MAAM,EAAE,YAAY;MACnB;IAAA;EAEF,CAAC,CAAE;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAI2T,oBAAoB,GAAG7Z,GAAG,CAAC8E,YAAY,CAAC5D,MAAM,CAAE;IACnDb,IAAI,EAAE,EAAE;IACRO,IAAI,EAAE,EAAE;IACRsF,MAAM,EAAE,YAAY;MACnB,IAAI4T,iBAAiB,GAAG,IAAI,CAACpU,WAAW,CAACuF,QAAQ,CAAE,UAAU,CAAE;MAC/D,IAAI8O,eAAe,GAAGD,iBAAiB,CAAC9W,IAAI,CAC3C,8BAA8B,CAC9B;MACD,IAAK+W,eAAe,CAACvL,EAAE,CAAE,UAAU,CAAE,EAAG;QACvC,IAAI,CAAC9I,WAAW,CAAC9C,GAAG,CAACuD,QAAQ,CAAE,uBAAuB,CAAE;MACzD,CAAC,MAAM;QACN,IAAI,CAACT,WAAW,CAAC9C,GAAG,CAACyD,WAAW,CAAE,uBAAuB,CAAE;MAC5D;IACD;EACD,CAAC,CAAE;EAEH,IAAI2T,6BAA6B,GAAGH,oBAAoB,CAAC3Y,MAAM,CAAE;IAChEb,IAAI,EAAE,WAAW;IACjBO,IAAI,EAAE;EACP,CAAC,CAAE;EAEH,IAAIqZ,uBAAuB,GAAGJ,oBAAoB,CAAC3Y,MAAM,CAAE;IAC1Db,IAAI,EAAE,KAAK;IACXO,IAAI,EAAE;EACP,CAAC,CAAE;EAEHZ,GAAG,CAAC6J,oBAAoB,CAAEmQ,6BAA6B,CAAE;EACzDha,GAAG,CAAC6J,oBAAoB,CAAEoQ,uBAAuB,CAAE;;EAEnD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,yBAAyB,GAAGla,GAAG,CAAC8E,YAAY,CAAC5D,MAAM,CAAE;IACxDb,IAAI,EAAE,EAAE;IACRO,IAAI,EAAE,EAAE;IACRsF,MAAM,EAAE,YAAY;MACnB,IAAIf,MAAM,GAAG,IAAI,CAACtF,CAAC,CAAE,6BAA6B,CAAE;MACpD,IAAKsF,MAAM,CAACgD,GAAG,EAAE,IAAI,OAAO,EAAG;QAC9B,IAAI,CAACtI,CAAC,CAAE,oBAAoB,CAAE,CAACsI,GAAG,CAAEhD,MAAM,CAACgD,GAAG,EAAE,CAAE;MACnD;IACD;EACD,CAAC,CAAE;EAEH,IAAIgS,mCAAmC,GAAGD,yBAAyB,CAAChZ,MAAM,CACzE;IACCb,IAAI,EAAE,aAAa;IACnBO,IAAI,EAAE;EACP,CAAC,CACD;EAED,IAAIwZ,kCAAkC,GAAGF,yBAAyB,CAAChZ,MAAM,CAAE;IAC1Eb,IAAI,EAAE,aAAa;IACnBO,IAAI,EAAE;EACP,CAAC,CAAE;EAEHZ,GAAG,CAAC6J,oBAAoB,CAAEsQ,mCAAmC,CAAE;EAC/Dna,GAAG,CAAC6J,oBAAoB,CAAEuQ,kCAAkC,CAAE;;EAE9D;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,uCAAuC,GAC1CH,yBAAyB,CAAChZ,MAAM,CAAE;IACjCb,IAAI,EAAE,kBAAkB;IACxBO,IAAI,EAAE;EACP,CAAC,CAAE;EAEJ,IAAI0Z,sCAAsC,GACzCJ,yBAAyB,CAAChZ,MAAM,CAAE;IACjCb,IAAI,EAAE,kBAAkB;IACxBO,IAAI,EAAE;EACP,CAAC,CAAE;EAEJZ,GAAG,CAAC6J,oBAAoB,CAAEwQ,uCAAuC,CAAE;EACnEra,GAAG,CAAC6J,oBAAoB,CAAEyQ,sCAAsC,CAAE;;EAElE;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,mCAAmC,GAAGL,yBAAyB,CAAChZ,MAAM,CACzE;IACCb,IAAI,EAAE,aAAa;IACnBO,IAAI,EAAE;EACP,CAAC,CACD;EAED,IAAI4Z,kCAAkC,GAAGN,yBAAyB,CAAChZ,MAAM,CAAE;IAC1Eb,IAAI,EAAE,aAAa;IACnBO,IAAI,EAAE;EACP,CAAC,CAAE;EAEHZ,GAAG,CAAC6J,oBAAoB,CAAE0Q,mCAAmC,CAAE;EAC/Dva,GAAG,CAAC6J,oBAAoB,CAAE2Q,kCAAkC,CAAE;;EAE9D;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,IAAIC,uBAAuB,GAAGza,GAAG,CAAC8E,YAAY,CAAC5D,MAAM,CAAE;IACtDb,IAAI,EAAE,cAAc;IACpBO,IAAI,EAAE,gBAAgB;IACtBsF,MAAM,EAAE,YAAY;MACnB,IAAIwU,sBAAsB,GACzB,IAAI,CAAChV,WAAW,CAACuF,QAAQ,CAAE,eAAe,CAAE;MAC7C,IAAI0P,sBAAsB,GACzB,IAAI,CAACjV,WAAW,CAACuF,QAAQ,CAAE,eAAe,CAAE;MAC7C,IAAI2P,UAAU,GAAGF,sBAAsB,CACrC1X,IAAI,CAAE,qCAAqC,CAAE,CAC7CsI,MAAM,CAAE,OAAO,CAAE,CACjBuP,QAAQ,EAAE,CACVC,IAAI,EAAE;MACR,IAAIC,mBAAmB,GACtBJ,sBAAsB,CAAC3X,IAAI,CAAE,oBAAoB,CAAE;MACpD,IAAIgY,IAAI,GAAGhb,GAAG,CAACmE,GAAG,CAAE,iBAAiB,CAAE;MAEvC,IAAK,IAAI,CAACF,KAAK,CAACkE,GAAG,EAAE,EAAG;QACvByS,UAAU,CAAC9B,WAAW,CAAEkC,IAAI,CAACC,WAAW,CAAE;QAC1CF,mBAAmB,CAACjS,IAAI,CACvB,aAAa,EACb,uBAAuB,CACvB;MACF,CAAC,MAAM;QACN8R,UAAU,CAAC9B,WAAW,CAAEkC,IAAI,CAACE,UAAU,CAAE;QACzCH,mBAAmB,CAACjS,IAAI,CAAE,aAAa,EAAE,SAAS,CAAE;MACrD;IACD;EACD,CAAC,CAAE;EACH9I,GAAG,CAAC6J,oBAAoB,CAAE4Q,uBAAuB,CAAE;AACpD,CAAC,EAAI7V,MAAM,CAAE;;;;;;;;;;ACtTb,CAAE,UAAW/E,CAAC,EAAEC,SAAS,EAAG;EAC3B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAIqb,iBAAiB,GAAG,IAAInb,GAAG,CAACoD,KAAK,CAAE;IACtC+D,EAAE,EAAE,mBAAmB;IAEvBpC,MAAM,EAAE;MACP,cAAc,EAAE,UAAU;MAC1B,mBAAmB,EAAE,SAAS;MAC9B,+BAA+B,EAAE,yBAAyB;MAC1D,kBAAkB,EAAE;IACrB,CAAC;IAEDqW,OAAO,EAAE;MACRC,gBAAgB,EAAE,qBAAqB;MACvCC,oBAAoB,EAAE;IACvB,CAAC;IAEDhP,UAAU,EAAE,YAAY;MACvBtM,GAAG,CAACgW,SAAS,CAAE,SAAS,EAAE,IAAI,CAACuF,sBAAsB,CAAE;IACxD,CAAC;IAEDA,sBAAsB,EAAE,YAAW;MAClC,IAAIC,mBAAmB,GAAG3b,CAAC,CAC1B,6EAA6E,CAC7E;MAED,IAAK2b,mBAAmB,CAAC3T,MAAM,EAAG;QACjChI,CAAC,CAAE,mCAAmC,CAAE,CAAC4O,OAAO,CAAE,OAAO,CAAE;QAC3D5O,CAAC,CAAE,wBAAwB,CAAE,CAAC4O,OAAO,CAAE,OAAO,CAAE;MACjD;;MAEA;MACA;MACA;MACA,IAAIgN,aAAa,GAAG5b,CAAC,CAAE,cAAc,CAAE;MACvCA,CAAC,CAAE,aAAa,CAAE,CAAC6C,EAAE,CAAE,OAAO,EAAE,YAAW;QAC1C,MAAMgZ,UAAU,GAAG7b,CAAC,CAAC,IAAI,CAAC,CAACsI,GAAG,EAAE;QAEhC,IAAK,CAAEuT,UAAU,EAAG;UACnBD,aAAa,CAACtV,QAAQ,CAAE,UAAU,CAAE;QACrC,CAAC,MAAM;UACNsV,aAAa,CAACpV,WAAW,CAAE,UAAU,CAAE;UACvCxG,CAAC,CAAC,IAAI,CAAC,CAACwG,WAAW,CAAE,iBAAiB,CAAE;QACzC;MACA,CAAC,CAAC;IACJ,CAAC;IAED8P,QAAQ,EAAE,UAAWxT,CAAC,EAAEC,GAAG,EAAG;MAC7B;MACA,IAAI+Y,MAAM,GAAG9b,CAAC,CAAE,wBAAwB,CAAE;;MAE1C;MACA,IAAK,CAAE8b,MAAM,CAACxT,GAAG,EAAE,EAAG;QACrB;QACAxF,CAAC,CAAC8P,cAAc,EAAE;;QAElB;QACAzS,GAAG,CAAC4b,UAAU,CAAEhZ,GAAG,CAAE;;QAErB;QACA+Y,MAAM,CAAClN,OAAO,CAAE,OAAO,CAAE;MAC1B;IACD,CAAC;IAEDoN,OAAO,EAAE,UAAWlZ,CAAC,EAAG;MACvBA,CAAC,CAAC8P,cAAc,EAAE;IACnB,CAAC;IAEDqJ,uBAAuB,EAAE,UAAWnZ,CAAC,EAAEC,GAAG,EAAG;MAC5CD,CAAC,CAAC8P,cAAc,EAAE;MAClB7P,GAAG,CAACuD,QAAQ,CAAE,QAAQ,CAAE;;MAExB;MACAnG,GAAG,CAAC6P,UAAU,CAAE;QACfE,OAAO,EAAE,IAAI;QACbnC,MAAM,EAAEhL,GAAG;QACXJ,OAAO,EAAE,IAAI;QACb6E,IAAI,EAAErH,GAAG,CAACuH,EAAE,CAAE,4BAA4B,CAAE;QAC5CwI,OAAO,EAAE,YAAY;UACpBgM,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAGrZ,GAAG,CAACkG,IAAI,CAAE,MAAM,CAAE;QAC1C,CAAC;QACDkH,MAAM,EAAE,YAAY;UACnBpN,GAAG,CAACyD,WAAW,CAAE,QAAQ,CAAE;QAC5B;MACD,CAAC,CAAE;IACJ,CAAC;IAED6V,mBAAmB,EAAE,UAAWvZ,CAAC,EAAEC,GAAG,EAAG;MACxC,IAAK,CAAEA,GAAG,CAACuF,GAAG,EAAE,EAAG;QAClBvF,GAAG,CAACuD,QAAQ,CAAE,iBAAiB,CAAE;QACjCtG,CAAC,CAAE,cAAc,CAAE,CAACsG,QAAQ,CAAE,UAAU,CAAE;MAC3C,CAAC,MAAM;QACNvD,GAAG,CAACyD,WAAW,CAAE,iBAAiB,CAAE;QACpCxG,CAAC,CAAE,cAAc,CAAE,CAACwG,WAAW,CAAE,UAAU,CAAE;MAC9C;IACD,CAAC;IAED8V,mBAAmB,EAAE,UAAW7M,IAAI,EAAG;MACtCA,IAAI,CAAC8M,OAAO,GAAG,IAAI;MAEnB,IAAK9M,IAAI,CAAChE,MAAM,KAAMgE,IAAI,CAAChE,MAAM,CAACkC,QAAQ,CAAE,kBAAkB,CAAE,IAAI8B,IAAI,CAAChE,MAAM,CAACyJ,OAAO,CAAE,mBAAmB,CAAE,CAAClN,MAAM,CAAE,EAAG;QACzHyH,IAAI,CAAC8M,OAAO,GAAG,KAAK;QACpB9M,IAAI,CAAC+M,gBAAgB,GAAG,IAAI;MAC7B;MAEA,OAAO/M,IAAI;IACZ,CAAC;IAEDgN,wBAAwB,EAAE,UAAW5a,QAAQ,EAAG;MAC/C,OAAOA,QAAQ,GAAG,4CAA4C;IAC/D;EACD,CAAC,CAAE;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAI6a,oBAAoB,GAAG,IAAIvc,GAAG,CAACoD,KAAK,CAAE;IACzC+D,EAAE,EAAE,sBAAsB;IAC1B+Q,IAAI,EAAE,SAAS;IAEfnT,MAAM,EAAE;MACP,4BAA4B,EAAE,mBAAmB;MACjD,iCAAiC,EAAE,2BAA2B;MAC9D,gCAAgC,EAAE;IACnC,CAAC;IAEDuH,UAAU,EAAE,YAAY;MACvB;MACA,IAAI1G,IAAI,GAAG/F,CAAC,CAAE,eAAe,CAAE;MAC/B,IAAI2c,OAAO,GAAG3c,CAAC,CAAE,4BAA4B,CAAE;;MAE/C;MACA+F,IAAI,CAAC5C,IAAI,CAAE,gBAAgB,CAAE,CAAC2I,MAAM,CAAE6Q,OAAO,CAAC5T,IAAI,EAAE,CAAE;MACtDhD,IAAI,CAAC5C,IAAI,CAAE,mBAAmB,CAAE,CAACsG,MAAM,EAAE;;MAEzC;MACAkT,OAAO,CAAClT,MAAM,EAAE;;MAEhB;MACA,IAAI,CAAC1G,GAAG,GAAG/C,CAAC,CAAE,sBAAsB,CAAE;;MAEtC;MACA,IAAI,CAACqG,MAAM,EAAE;IACd,CAAC;IAEDuW,kBAAkB,EAAE,YAAY;MAC/B,OAAO,IAAI,CAAC7Z,GAAG,CAACI,IAAI,CAAE,qBAAqB,CAAE,CAAClC,IAAI,CAAE,SAAS,CAAE;IAChE,CAAC;IAED4b,0BAA0B,EAAE,YAAY;MACvC,MAAMvX,MAAM,GAAG,IAAI,CAACvC,GAAG,CAACI,IAAI,CAAE,0BAA0B,CAAE;;MAE1D;MACA,IAAK,CAAEmC,MAAM,CAAC0C,MAAM,EAAG;QACtB,OAAO,KAAK;MACb;MAEA,OAAO1C,MAAM,CAACrE,IAAI,CAAE,SAAS,CAAE;IAChC,CAAC;IAED6b,sBAAsB,EAAE,YAAY;MACnC,OAAO,IAAI,CAAC/Z,GAAG,CACbI,IAAI,CAAE,sCAAsC,CAAE,CAC9CmF,GAAG,EAAE;IACR,CAAC;IAEDyU,iBAAiB,EAAE,UAAWja,CAAC,EAAEC,GAAG,EAAG;MACtC,IAAIuF,GAAG,GAAG,IAAI,CAACsU,kBAAkB,EAAE,GAAG,CAAC,GAAG,CAAC;MAC3Czc,GAAG,CAAC6c,iBAAiB,CAAE,iBAAiB,EAAE1U,GAAG,CAAE;MAC/C,IAAI,CAACjC,MAAM,EAAE;IACd,CAAC;IAED4W,yBAAyB,EAAE,YAAY;MACtC,MAAM3U,GAAG,GAAG,IAAI,CAACuU,0BAA0B,EAAE,GAAG,CAAC,GAAG,CAAC;MACrD1c,GAAG,CAAC6c,iBAAiB,CAAE,0BAA0B,EAAE1U,GAAG,CAAE;MACxD,IAAI,CAACjC,MAAM,EAAE;IACd,CAAC;IAEDA,MAAM,EAAE,YAAY;MACnB,IAAK,IAAI,CAACuW,kBAAkB,EAAE,EAAG;QAChC5c,CAAC,CAAE,yBAAyB,CAAE,CAACsG,QAAQ,CAAE,iBAAiB,CAAE;MAC7D,CAAC,MAAM;QACNtG,CAAC,CAAE,yBAAyB,CAAE,CAACwG,WAAW,CAAE,iBAAiB,CAAE;MAChE;MAEA,IAAK,CAAE,IAAI,CAACqW,0BAA0B,EAAE,EAAG;QAC1C7c,CAAC,CAAE,yBAAyB,CAAE,CAACsG,QAAQ,CAAE,WAAW,CAAE;QACtDtG,CAAC,CAAE,0BAA0B,CAAE,CAC7BwG,WAAW,CAAE,YAAY,CAAE,CAC3BvF,IAAI,CAAE,QAAQ,EAAE,KAAK,CAAE;MAC1B,CAAC,MAAM;QACNjB,CAAC,CAAE,yBAAyB,CAAE,CAACwG,WAAW,CAAE,WAAW,CAAE;QAEzDxG,CAAC,CAAE,wBAAwB,CAAE,CAAC0G,IAAI,CAAE,YAAY;UAC/C,MAAMwW,SAAS,GAAG/c,GAAG,CAACqL,SAAS,CAAE;YAChChL,IAAI,EAAE,KAAK;YACXiL,MAAM,EAAEzL,CAAC,CAAE,IAAI,CAAE;YACjBwc,gBAAgB,EAAE,IAAI;YACtBlR,KAAK,EAAE;UACR,CAAC,CAAE;UAEH,IAAK4R,SAAS,CAAClV,MAAM,EAAG;YACvBkV,SAAS,CAAE,CAAC,CAAE,CAACzI,IAAI,CAACvS,GAAG,CAAE,aAAa,EAAE,KAAK,CAAE;UAChD;UAEA/B,GAAG,CAACkE,QAAQ,CAAE,MAAM,EAAErE,CAAC,CAAE,IAAI,CAAE,CAAE;QAClC,CAAC,CAAE;MACJ;MAEA,IAAK,IAAI,CAAC8c,sBAAsB,EAAE,IAAI,CAAC,EAAG;QACzC9c,CAAC,CAAE,MAAM,CAAE,CAACwG,WAAW,CAAE,WAAW,CAAE;QACtCxG,CAAC,CAAE,MAAM,CAAE,CAACsG,QAAQ,CAAE,WAAW,CAAE;MACpC,CAAC,MAAM;QACNtG,CAAC,CAAE,MAAM,CAAE,CAACwG,WAAW,CAAE,WAAW,CAAE;QACtCxG,CAAC,CAAE,MAAM,CAAE,CAACsG,QAAQ,CAAE,WAAW,CAAE;MACpC;IACD;EACD,CAAC,CAAE;;EAEH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEC,IAAI6W,kBAAkB,GAAG,IAAIhd,GAAG,CAACoD,KAAK,CAAE;IACvCC,OAAO,EAAE;MACRqW,SAAS,EAAE;IACZ,CAAC;IAEDC,UAAU,EAAE,UAAW1V,KAAK,EAAG;MAC9B;MACA,IAAK,CAAEA,KAAK,CAAC4H,GAAG,CAAE,QAAQ,CAAE,EAAG;;MAE/B;MACA,IAAIF,MAAM,GAAG1H,KAAK,CAACE,GAAG,CAAE,QAAQ,CAAE;MAClC,IAAI8Y,QAAQ,GAAGhZ,KAAK,CAACrB,GAAG,CACtBgH,QAAQ,CAAE,cAAc,GAAG+B,MAAM,GAAG,IAAI,CAAE,CAC1CiM,KAAK,EAAE;;MAET;MACA,IAAK,CAAEqF,QAAQ,CAACpV,MAAM,EAAG;;MAEzB;MACA,IAAIjC,IAAI,GAAGqX,QAAQ,CAAChT,QAAQ,CAAE,YAAY,CAAE;MAC5C,IAAIiT,GAAG,GAAGtX,IAAI,CAACqE,QAAQ,CAAE,IAAI,CAAE;;MAE/B;MACA,IAAK,CAAEiT,GAAG,CAACrV,MAAM,EAAG;QACnBjC,IAAI,CAACuX,SAAS,CAAE,mCAAmC,CAAE;QACrDD,GAAG,GAAGtX,IAAI,CAACqE,QAAQ,CAAE,IAAI,CAAE;MAC5B;;MAEA;MACA,IAAIrB,IAAI,GAAG3E,KAAK,CAACpE,CAAC,CAAE,YAAY,CAAE,CAAC+I,IAAI,EAAE;MACzC,IAAIwU,GAAG,GAAGvd,CAAC,CAAE,MAAM,GAAG+I,IAAI,GAAG,OAAO,CAAE;MACtCsU,GAAG,CAACvR,MAAM,CAAEyR,GAAG,CAAE;MACjBF,GAAG,CAACpU,IAAI,CAAE,WAAW,EAAEoU,GAAG,CAACjT,QAAQ,EAAE,CAACpC,MAAM,CAAE;;MAE9C;MACA5D,KAAK,CAACqF,MAAM,EAAE;IACf;EACD,CAAC,CAAE;AACJ,CAAC,EAAI1E,MAAM,CAAE;;;;;;UCjSb;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;ACN2B;AACM;AACG;AACE;AACJ;AACG", "sources": ["webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-compatibility.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-conditions.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-field.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-fields.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-locations.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group-settings.js", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/_field-group.js", "webpack://advanced-custom-fields-pro/webpack/bootstrap", "webpack://advanced-custom-fields-pro/webpack/runtime/compat get default export", "webpack://advanced-custom-fields-pro/webpack/runtime/define property getters", "webpack://advanced-custom-fields-pro/webpack/runtime/hasOwnProperty shorthand", "webpack://advanced-custom-fields-pro/webpack/runtime/make namespace object", "webpack://advanced-custom-fields-pro/./src/advanced-custom-fields-pro/assets/src/js/acf-field-group.js"], "sourcesContent": ["( function ( $, undefined ) {\n\tvar _acf = acf.getCompatibility( acf );\n\n\t/**\n\t *  fieldGroupCompatibility\n\t *\n\t *  Compatibility layer for extinct acf.field_group\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\t_acf.field_group = {\n\t\tsave_field: function ( $field, type ) {\n\t\t\ttype = type !== undefined ? type : 'settings';\n\t\t\tacf.getFieldObject( $field ).save( type );\n\t\t},\n\n\t\tdelete_field: function ( $field, animate ) {\n\t\t\tanimate = animate !== undefined ? animate : true;\n\t\t\tacf.getFieldObject( $field ).delete( {\n\t\t\t\tanimate: animate,\n\t\t\t} );\n\t\t},\n\n\t\tupdate_field_meta: function ( $field, name, value ) {\n\t\t\tacf.getFieldObject( $field ).prop( name, value );\n\t\t},\n\n\t\tdelete_field_meta: function ( $field, name ) {\n\t\t\tacf.getFieldObject( $field ).prop( name, null );\n\t\t},\n\t};\n\n\t/**\n\t *  fieldGroupCompatibility.field_object\n\t *\n\t *  Compatibility layer for extinct acf.field_group.field_object\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\t_acf.field_group.field_object = acf.model.extend( {\n\t\t// vars\n\t\ttype: '',\n\t\to: {},\n\t\t$field: null,\n\t\t$settings: null,\n\n\t\ttag: function ( tag ) {\n\t\t\t// vars\n\t\t\tvar type = this.type;\n\n\t\t\t// explode, add 'field' and implode\n\t\t\t// - open \t\t\t=> open_field\n\t\t\t// - change_type\t=> change_field_type\n\t\t\tvar tags = tag.split( '_' );\n\t\t\ttags.splice( 1, 0, 'field' );\n\t\t\ttag = tags.join( '_' );\n\n\t\t\t// add type\n\t\t\tif ( type ) {\n\t\t\t\ttag += '/type=' + type;\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn tag;\n\t\t},\n\n\t\tselector: function () {\n\t\t\t// vars\n\t\t\tvar selector = '.acf-field-object';\n\t\t\tvar type = this.type;\n\n\t\t\t// add type\n\t\t\tif ( type ) {\n\t\t\t\tselector += '-' + type;\n\t\t\t\tselector = acf.str_replace( '_', '-', selector );\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn selector;\n\t\t},\n\n\t\t_add_action: function ( name, callback ) {\n\t\t\t// vars\n\t\t\tvar model = this;\n\n\t\t\t// add action\n\t\t\tacf.add_action( this.tag( name ), function ( $field ) {\n\t\t\t\t// focus\n\t\t\t\tmodel.set( '$field', $field );\n\n\t\t\t\t// callback\n\t\t\t\tmodel[ callback ].apply( model, arguments );\n\t\t\t} );\n\t\t},\n\n\t\t_add_filter: function ( name, callback ) {\n\t\t\t// vars\n\t\t\tvar model = this;\n\n\t\t\t// add action\n\t\t\tacf.add_filter( this.tag( name ), function ( $field ) {\n\t\t\t\t// focus\n\t\t\t\tmodel.set( '$field', $field );\n\n\t\t\t\t// callback\n\t\t\t\tmodel[ callback ].apply( model, arguments );\n\t\t\t} );\n\t\t},\n\n\t\t_add_event: function ( name, callback ) {\n\t\t\t// vars\n\t\t\tvar model = this;\n\t\t\tvar event = name.substr( 0, name.indexOf( ' ' ) );\n\t\t\tvar selector = name.substr( name.indexOf( ' ' ) + 1 );\n\t\t\tvar context = this.selector();\n\n\t\t\t// add event\n\t\t\t$( document ).on( event, context + ' ' + selector, function ( e ) {\n\t\t\t\t// append $el to event object\n\t\t\t\te.$el = $( this );\n\t\t\t\te.$field = e.$el.closest( '.acf-field-object' );\n\n\t\t\t\t// focus\n\t\t\t\tmodel.set( '$field', e.$field );\n\n\t\t\t\t// callback\n\t\t\t\tmodel[ callback ].apply( model, [ e ] );\n\t\t\t} );\n\t\t},\n\n\t\t_set_$field: function () {\n\t\t\t// vars\n\t\t\tthis.o = this.$field.data();\n\n\t\t\t// els\n\t\t\tthis.$settings = this.$field.find( '> .settings > table > tbody' );\n\n\t\t\t// focus\n\t\t\tthis.focus();\n\t\t},\n\n\t\tfocus: function () {\n\t\t\t// do nothing\n\t\t},\n\n\t\tsetting: function ( name ) {\n\t\t\treturn this.$settings.find( '> .acf-field-setting-' + name );\n\t\t},\n\t} );\n\n\t/*\n\t *  field\n\t *\n\t *  This model fires actions and filters for registered fields\n\t *\n\t *  @type\tfunction\n\t *  @date\t21/02/2014\n\t *  @since\t3.5.1\n\t *\n\t *  @param\tn/a\n\t *  @return\tn/a\n\t */\n\n\tvar actionManager = new acf.Model( {\n\t\tactions: {\n\t\t\topen_field_object: 'onOpenFieldObject',\n\t\t\tclose_field_object: 'onCloseFieldObject',\n\t\t\tadd_field_object: 'onAddFieldObject',\n\t\t\tduplicate_field_object: 'onDuplicateFieldObject',\n\t\t\tdelete_field_object: 'onDeleteFieldObject',\n\t\t\tchange_field_object_type: 'onChangeFieldObjectType',\n\t\t\tchange_field_object_label: 'onChangeFieldObjectLabel',\n\t\t\tchange_field_object_name: 'onChangeFieldObjectName',\n\t\t\tchange_field_object_parent: 'onChangeFieldObjectParent',\n\t\t\tsortstop_field_object: 'onChangeFieldObjectParent',\n\t\t},\n\n\t\tonOpenFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'open_field', field.$el );\n\t\t\tacf.doAction( 'open_field/type=' + field.get( 'type' ), field.$el );\n\n\t\t\tacf.doAction( 'render_field_settings', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'render_field_settings/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonCloseFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'close_field', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'close_field/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonAddFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'add_field', field.$el );\n\t\t\tacf.doAction( 'add_field/type=' + field.get( 'type' ), field.$el );\n\t\t},\n\n\t\tonDuplicateFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'duplicate_field', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'duplicate_field/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonDeleteFieldObject: function ( field ) {\n\t\t\tacf.doAction( 'delete_field', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'delete_field/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectType: function ( field ) {\n\t\t\tacf.doAction( 'change_field_type', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'change_field_type/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\n\t\t\tacf.doAction( 'render_field_settings', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'render_field_settings/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectLabel: function ( field ) {\n\t\t\tacf.doAction( 'change_field_label', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'change_field_label/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectName: function ( field ) {\n\t\t\tacf.doAction( 'change_field_name', field.$el );\n\t\t\tacf.doAction(\n\t\t\t\t'change_field_name/type=' + field.get( 'type' ),\n\t\t\t\tfield.$el\n\t\t\t);\n\t\t},\n\n\t\tonChangeFieldObjectParent: function ( field ) {\n\t\t\tacf.doAction( 'update_field_parent', field.$el );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  ConditionalLogicFieldSetting\n\t *\n\t *  description\n\t *\n\t *  @date\t3/2/18\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar ConditionalLogicFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: '',\n\t\tname: 'conditional_logic',\n\t\tevents: {\n\t\t\t'change .conditions-toggle': 'onChangeToggle',\n\t\t\t'click .add-conditional-group': 'onClickAddGroup',\n\t\t\t'focus .condition-rule-field': 'onFocusField',\n\t\t\t'change .condition-rule-field': 'onChangeField',\n\t\t\t'change .condition-rule-operator': 'onChangeOperator',\n\t\t\t'click .add-conditional-rule': 'onClickAdd',\n\t\t\t'click .remove-conditional-rule': 'onClickRemove',\n\t\t},\n\n\t\t$rule: false,\n\n\t\tscope: function ( $rule ) {\n\t\t\tthis.$rule = $rule;\n\t\t\treturn this;\n\t\t},\n\n\t\truleData: function ( name, value ) {\n\t\t\treturn this.$rule.data.apply( this.$rule, arguments );\n\t\t},\n\n\t\t$input: function ( name ) {\n\t\t\treturn this.$rule.find( '.condition-rule-' + name );\n\t\t},\n\n\t\t$td: function ( name ) {\n\t\t\treturn this.$rule.find( 'td.' + name );\n\t\t},\n\n\t\t$toggle: function () {\n\t\t\treturn this.$( '.conditions-toggle' );\n\t\t},\n\n\t\t$control: function () {\n\t\t\treturn this.$( '.rule-groups' );\n\t\t},\n\n\t\t$groups: function () {\n\t\t\treturn this.$( '.rule-group' );\n\t\t},\n\n\t\t$rules: function () {\n\t\t\treturn this.$( '.rule' );\n\t\t},\n\n\t\t$tabLabel: function () {\n\t\t\treturn this.fieldObject.$el.find('.conditional-logic-badge');\n\t\t},\n\n\t\topen: function () {\n\t\t\tvar $div = this.$control();\n\t\t\t$div.show();\n\t\t\tacf.enable( $div );\n\t\t},\n\n\t\tclose: function () {\n\t\t\tvar $div = this.$control();\n\t\t\t$div.hide();\n\t\t\tacf.disable( $div );\n\t\t},\n\n\t\trender: function () {\n\t\t\t// show\n\t\t\tif ( this.$toggle().prop( 'checked' ) ) {\n\t\t\t\tthis.$tabLabel().addClass('is-enabled');\n\t\t\t\tthis.renderRules();\n\t\t\t\tthis.open();\n\n\t\t\t\t// hide\n\t\t\t} else {\n\t\t\t\tthis.$tabLabel().removeClass('is-enabled');\n\t\t\t\tthis.close();\n\t\t\t}\n\t\t},\n\n\t\trenderRules: function () {\n\t\t\t// vars\n\t\t\tvar self = this;\n\n\t\t\t// loop\n\t\t\tthis.$rules().each( function () {\n\t\t\t\tself.renderRule( $( this ) );\n\t\t\t} );\n\t\t},\n\n\t\trenderRule: function ( $rule ) {\n\t\t\tthis.scope( $rule );\n\t\t\tthis.renderField();\n\t\t\tthis.renderOperator();\n\t\t\tthis.renderValue();\n\t\t},\n\n\t\trenderField: function () {\n\t\t\t// vars\n\t\t\tvar choices = [];\n\t\t\tvar validFieldTypes = [];\n\t\t\tvar cid = this.fieldObject.cid;\n\t\t\tvar $select = this.$input( 'field' );\n\n\t\t\t// loop\n\t\t\tacf.getFieldObjects().map( function ( fieldObject ) {\n\t\t\t\t// vars\n\t\t\t\tvar choice = {\n\t\t\t\t\tid: fieldObject.getKey(),\n\t\t\t\t\ttext: fieldObject.getLabel(),\n\t\t\t\t};\n\n\t\t\t\t// bail early if is self\n\t\t\t\tif ( fieldObject.cid === cid ) {\n\t\t\t\t\tchoice.text += acf.__( '(this field)' );\n\t\t\t\t\tchoice.disabled = true;\n\t\t\t\t}\n\n\t\t\t\t// get selected field conditions\n\t\t\t\tvar conditionTypes = acf.getConditionTypes( {\n\t\t\t\t\tfieldType: fieldObject.getType(),\n\t\t\t\t} );\n\n\t\t\t\t// bail early if no types\n\t\t\t\tif ( ! conditionTypes.length ) {\n\t\t\t\t\tchoice.disabled = true;\n\t\t\t\t}\n\n\t\t\t\t// calulate indents\n\t\t\t\tvar indents = fieldObject.getParents().length;\n\t\t\t\tchoice.text = '- '.repeat( indents ) + choice.text;\n\n\t\t\t\t// append\n\t\t\t\tchoices.push( choice );\n\t\t\t} );\n\n\t\t\t// allow for scenario where only one field exists\n\t\t\tif ( ! choices.length ) {\n\t\t\t\tchoices.push( {\n\t\t\t\t\tid: '',\n\t\t\t\t\ttext: acf.__( 'No toggle fields available' ),\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\t// render\n\t\t\tacf.renderSelect( $select, choices );\n\n\t\t\t// set\n\t\t\tthis.ruleData( 'field', $select.val() );\n\t\t},\n\n\t\trenderOperator: function () {\n\t\t\t// bail early if no field selected\n\t\t\tif ( ! this.ruleData( 'field' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// vars\n\t\t\tvar $select = this.$input( 'operator' );\n\t\t\tvar val = $select.val();\n\t\t\tvar choices = [];\n\n\t\t\t// set saved value on first render\n\t\t\t// - this allows the 2nd render to correctly select an option\n\t\t\tif ( $select.val() === null ) {\n\t\t\t\tacf.renderSelect( $select, [\n\t\t\t\t\t{\n\t\t\t\t\t\tid: this.ruleData( 'operator' ),\n\t\t\t\t\t\ttext: '',\n\t\t\t\t\t},\n\t\t\t\t] );\n\t\t\t}\n\n\t\t\t// get selected field\n\t\t\tvar $field = acf.findFieldObject( this.ruleData( 'field' ) );\n\t\t\tvar field = acf.getFieldObject( $field );\n\n\t\t\t// get selected field conditions\n\t\t\tvar conditionTypes = acf.getConditionTypes( {\n\t\t\t\tfieldType: field.getType(),\n\t\t\t} );\n\n\t\t\t// html\n\t\t\tconditionTypes.map( function ( model ) {\n\t\t\t\tchoices.push( {\n\t\t\t\t\tid: model.prototype.operator,\n\t\t\t\t\ttext: model.prototype.label,\n\t\t\t\t} );\n\t\t\t} );\n\n\t\t\t// render\n\t\t\tacf.renderSelect( $select, choices );\n\n\t\t\t// set\n\t\t\tthis.ruleData( 'operator', $select.val() );\n\t\t},\n\n\t\trenderValue: function () {\n\t\t\t// bail early if no field selected\n\t\t\tif ( ! this.ruleData( 'field' ) || ! this.ruleData( 'operator' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// vars\n\t\t\tvar $select = this.$input( 'value' );\n\t\t\tvar $td = this.$td( 'value' );\n\t\t\tvar val = $select.val();\n\n\t\t\t// get selected field\n\t\t\tvar $field = acf.findFieldObject( this.ruleData( 'field' ) );\n\t\t\tvar field = acf.getFieldObject( $field );\n\n\t\t\t// get selected field conditions\n\t\t\tvar conditionTypes = acf.getConditionTypes( {\n\t\t\t\tfieldType: field.getType(),\n\t\t\t\toperator: this.ruleData( 'operator' ),\n\t\t\t} );\n\n\t\t\t// html\n\t\t\tvar conditionType = conditionTypes[ 0 ].prototype;\n\t\t\tvar choices = conditionType.choices( field );\n\n\t\t\t// create html: array\n\t\t\tif ( choices instanceof Array ) {\n\t\t\t\tvar $newSelect = $( '<select></select>' );\n\t\t\t\tacf.renderSelect( $newSelect, choices );\n\n\t\t\t\t// create html: string (<input />)\n\t\t\t} else {\n\t\t\t\tvar $newSelect = $( choices );\n\t\t\t}\n\n\t\t\t// append\n\t\t\t$select.detach();\n\t\t\t$td.html( $newSelect );\n\n\t\t\t// copy attrs\n\t\t\t// timeout needed to avoid browser bug where \"disabled\" attribute is not applied\n\t\t\tsetTimeout( function () {\n\t\t\t\t[ 'class', 'name', 'id' ].map( function ( attr ) {\n\t\t\t\t\t$newSelect.attr( attr, $select.attr( attr ) );\n\t\t\t\t} );\n\t\t\t}, 0 );\n\n\t\t\t// select existing value (if not a disabled input)\n\t\t\tif ( ! $newSelect.prop( 'disabled' ) ) {\n\t\t\t\tacf.val( $newSelect, val, true );\n\t\t\t}\n\n\t\t\t// set\n\t\t\tthis.ruleData( 'value', $newSelect.val() );\n\t\t},\n\n\t\tonChangeToggle: function () {\n\t\t\tthis.render();\n\t\t},\n\n\t\tonClickAddGroup: function ( e, $el ) {\n\t\t\tthis.addGroup();\n\t\t},\n\n\t\taddGroup: function () {\n\t\t\t// vars\n\t\t\tvar $group = this.$( '.rule-group:last' );\n\n\t\t\t// duplicate\n\t\t\tvar $group2 = acf.duplicate( $group );\n\n\t\t\t// update h4\n\t\t\t$group2.find( 'h4' ).text( acf.__( 'or' ) );\n\n\t\t\t// remove all tr's except the first one\n\t\t\t$group2.find( 'tr' ).not( ':first' ).remove();\n\n\t\t\t// save field\n\t\t\tthis.fieldObject.save();\n\t\t},\n\n\t\tonFocusField: function ( e, $el ) {\n\t\t\tthis.renderField();\n\t\t},\n\n\t\tonChangeField: function ( e, $el ) {\n\t\t\t// scope\n\t\t\tthis.scope( $el.closest( '.rule' ) );\n\n\t\t\t// set data\n\t\t\tthis.ruleData( 'field', $el.val() );\n\n\t\t\t// render\n\t\t\tthis.renderOperator();\n\t\t\tthis.renderValue();\n\t\t},\n\n\t\tonChangeOperator: function ( e, $el ) {\n\t\t\t// scope\n\t\t\tthis.scope( $el.closest( '.rule' ) );\n\n\t\t\t// set data\n\t\t\tthis.ruleData( 'operator', $el.val() );\n\n\t\t\t// render\n\t\t\tthis.renderValue();\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\t// duplciate\n\t\t\tvar $rule = acf.duplicate( $el.closest( '.rule' ) );\n\n\t\t\t// render\n\t\t\tthis.renderRule( $rule );\n\t\t},\n\n\t\tonClickRemove: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar $rule = $el.closest( '.rule' );\n\n\t\t\t// save field\n\t\t\tthis.fieldObject.save();\n\n\t\t\t// remove group\n\t\t\tif ( $rule.siblings( '.rule' ).length == 0 ) {\n\t\t\t\t$rule.closest( '.rule-group' ).remove();\n\t\t\t}\n\n\t\t\t// remove\n\t\t\t$rule.remove();\n\t\t},\n\t} );\n\n\tacf.registerFieldSetting( ConditionalLogicFieldSetting );\n\n\t/**\n\t *  conditionalLogicHelper\n\t *\n\t *  description\n\t *\n\t *  @date\t20/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar conditionalLogicHelper = new acf.Model( {\n\t\tactions: {\n\t\t\tduplicate_field_objects: 'onDuplicateFieldObjects',\n\t\t},\n\n\t\tonDuplicateFieldObjects: function ( children, newField, prevField ) {\n\t\t\t// vars\n\t\t\tvar data = {};\n\t\t\tvar $selects = $();\n\n\t\t\t// reference change in key\n\t\t\tchildren.map( function ( child ) {\n\t\t\t\t// store reference of changed key\n\t\t\t\tdata[ child.get( 'prevKey' ) ] = child.get( 'key' );\n\n\t\t\t\t// append condition select\n\t\t\t\t$selects = $selects.add( child.$( '.condition-rule-field' ) );\n\t\t\t} );\n\n\t\t\t// loop\n\t\t\t$selects.each( function () {\n\t\t\t\t// vars\n\t\t\t\tvar $select = $( this );\n\t\t\t\tvar val = $select.val();\n\n\t\t\t\t// bail early if val is not a ref key\n\t\t\t\tif ( ! val || ! data[ val ] ) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// modify selected option\n\t\t\t\t$select.find( 'option:selected' ).attr( 'value', data[ val ] );\n\n\t\t\t\t// set new val\n\t\t\t\t$select.val( data[ val ] );\n\t\t\t} );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\tacf.FieldObject = acf.Model.extend( {\n\t\t// class used to avoid nested event triggers\n\t\teventScope: '.acf-field-object',\n\n\t\t// events\n\t\tevents: {\n\t\t\t'click .copyable': 'onClickCopy',\n\t\t\t'click .handle': 'onClickEdit',\n\t\t\t'click .close-field': 'onClickEdit',\n\t\t\t'click a[data-key=\"acf_field_settings_tabs\"]':\n\t\t\t\t'onChangeSettingsTab',\n\t\t\t'click .delete-field': 'onClickDelete',\n\t\t\t'click .duplicate-field': 'duplicate',\n\t\t\t'click .move-field': 'move',\n\n\t\t\t'focus .edit-field': 'onFocusEdit',\n\t\t\t'blur .edit-field, .row-options a': 'onBlurEdit',\n\n\t\t\t'change .field-type': 'onChangeType',\n\t\t\t'change .field-required': 'onChangeRequired',\n\t\t\t'blur .field-label': 'onChangeLabel',\n\t\t\t'blur .field-name': 'onChangeName',\n\n\t\t\tchange: 'onChange',\n\t\t\tchanged: 'onChanged',\n\t\t},\n\n\t\t// data\n\t\tdata: {\n\t\t\t// Similar to ID, but used for HTML puposes.\n\t\t\t// It is possbile for a new field to have an ID of 0, but an id of 'field_123' */\n\t\t\tid: 0,\n\n\t\t\t// The field key ('field_123')\n\t\t\tkey: '',\n\n\t\t\t// The field type (text, image, etc)\n\t\t\ttype: '',\n\n\t\t\t// The $post->ID of this field\n\t\t\t//ID: 0,\n\n\t\t\t// The field's parent\n\t\t\t//parent: 0,\n\n\t\t\t// The menu order\n\t\t\t//menu_order: 0\n\t\t},\n\n\t\tsetup: function ( $field ) {\n\t\t\t// set $el\n\t\t\tthis.$el = $field;\n\n\t\t\t// inherit $field data (id, key, type)\n\t\t\tthis.inherit( $field );\n\n\t\t\t// load additional props\n\t\t\t// - this won't trigger 'changed'\n\t\t\tthis.prop( 'ID' );\n\t\t\tthis.prop( 'parent' );\n\t\t\tthis.prop( 'menu_order' );\n\t\t},\n\n\t\t$input: function ( name ) {\n\t\t\treturn $( '#' + this.getInputId() + '-' + name );\n\t\t},\n\n\t\t$meta: function () {\n\t\t\treturn this.$( '.meta:first' );\n\t\t},\n\n\t\t$handle: function () {\n\t\t\treturn this.$( '.handle:first' );\n\t\t},\n\n\t\t$settings: function () {\n\t\t\treturn this.$( '.settings:first' );\n\t\t},\n\n\t\t$setting: function ( name ) {\n\t\t\treturn this.$(\n\t\t\t\t'.acf-field-settings:first .acf-field-setting-' + name\n\t\t\t);\n\t\t},\n\n\t\tgetParent: function () {\n\t\t\treturn acf.getFieldObjects( { child: this.$el, limit: 1 } ).pop();\n\t\t},\n\n\t\tgetParents: function () {\n\t\t\treturn acf.getFieldObjects( { child: this.$el } );\n\t\t},\n\n\t\tgetFields: function () {\n\t\t\treturn acf.getFieldObjects( { parent: this.$el } );\n\t\t},\n\n\t\tgetInputName: function () {\n\t\t\treturn 'acf_fields[' + this.get( 'id' ) + ']';\n\t\t},\n\n\t\tgetInputId: function () {\n\t\t\treturn 'acf_fields-' + this.get( 'id' );\n\t\t},\n\n\t\tnewInput: function ( name, value ) {\n\t\t\t// vars\n\t\t\tvar inputId = this.getInputId();\n\t\t\tvar inputName = this.getInputName();\n\n\t\t\t// append name\n\t\t\tif ( name ) {\n\t\t\t\tinputId += '-' + name;\n\t\t\t\tinputName += '[' + name + ']';\n\t\t\t}\n\n\t\t\t// create input (avoid HTML + JSON value issues)\n\t\t\tvar $input = $( '<input />' ).attr( {\n\t\t\t\tid: inputId,\n\t\t\t\tname: inputName,\n\t\t\t\tvalue: value,\n\t\t\t} );\n\t\t\tthis.$( '> .meta' ).append( $input );\n\n\t\t\t// return\n\t\t\treturn $input;\n\t\t},\n\n\t\tgetProp: function ( name ) {\n\t\t\t// check data\n\t\t\tif ( this.has( name ) ) {\n\t\t\t\treturn this.get( name );\n\t\t\t}\n\n\t\t\t// get input value\n\t\t\tvar $input = this.$input( name );\n\t\t\tvar value = $input.length ? $input.val() : null;\n\n\t\t\t// set data silently (cache)\n\t\t\tthis.set( name, value, true );\n\n\t\t\t// return\n\t\t\treturn value;\n\t\t},\n\n\t\tsetProp: function ( name, value ) {\n\t\t\t// get input\n\t\t\tvar $input = this.$input( name );\n\t\t\tvar prevVal = $input.val();\n\n\t\t\t// create if new\n\t\t\tif ( ! $input.length ) {\n\t\t\t\t$input = this.newInput( name, value );\n\t\t\t}\n\n\t\t\t// remove\n\t\t\tif ( value === null ) {\n\t\t\t\t$input.remove();\n\n\t\t\t\t// update\n\t\t\t} else {\n\t\t\t\t$input.val( value );\n\t\t\t}\n\n\t\t\t//console.log('setProp', name, value, this);\n\n\t\t\t// set data silently (cache)\n\t\t\tif ( ! this.has( name ) ) {\n\t\t\t\t//console.log('setting silently');\n\t\t\t\tthis.set( name, value, true );\n\n\t\t\t\t// set data allowing 'change' event to fire\n\t\t\t} else {\n\t\t\t\t//console.log('setting loudly!');\n\t\t\t\tthis.set( name, value );\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn this;\n\t\t},\n\n\t\tprop: function ( name, value ) {\n\t\t\tif ( value !== undefined ) {\n\t\t\t\treturn this.setProp( name, value );\n\t\t\t} else {\n\t\t\t\treturn this.getProp( name );\n\t\t\t}\n\t\t},\n\n\t\tprops: function ( props ) {\n\t\t\tObject.keys( props ).map( function ( key ) {\n\t\t\t\tthis.setProp( key, props[ key ] );\n\t\t\t}, this );\n\t\t},\n\n\t\tgetLabel: function () {\n\t\t\t// get label with empty default\n\t\t\tvar label = this.prop( 'label' );\n\t\t\tif ( label === '' ) {\n\t\t\t\tlabel = acf.__( '(no label)' );\n\t\t\t}\n\n\t\t\t// return\n\t\t\treturn label;\n\t\t},\n\n\t\tgetName: function () {\n\t\t\treturn this.prop( 'name' );\n\t\t},\n\n\t\tgetType: function () {\n\t\t\treturn this.prop( 'type' );\n\t\t},\n\n\t\tgetTypeLabel: function () {\n\t\t\tvar type = this.prop( 'type' );\n\t\t\tvar types = acf.get( 'fieldTypes' );\n\t\t\treturn types[ type ] ? types[ type ].label : type;\n\t\t},\n\n\t\tgetKey: function () {\n\t\t\treturn this.prop( 'key' );\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tthis.addProFields();\n\t\t\tthis.checkCopyable();\n\t\t},\n\n\t\tmakeCopyable: function ( text ) {\n\t\t\tif ( ! navigator.clipboard ) return '<span class=\"copyable copy-unsupported\">' + text + '</span>';\n\t\t\treturn '<span class=\"copyable\">' + text + '</span>';\n\t\t},\n\n\t\tcheckCopyable: function () {\n\t\t\tif ( ! navigator.clipboard ) {\n\t\t\t\tthis.$el.find( '.copyable' ).addClass( 'copy-unsupported' );\n\t\t\t}\n\t\t},\n\n\t\taddProFields: function () {\n\t\t\t// Make sure we're only running this on free version.\n\t\t\tif ( acf.data.fieldTypes.hasOwnProperty( 'clone' ) ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Make sure we haven't appended these fields before.\n\t\t\tvar $fieldTypeSelect = $( '.field-type' ).not(\n\t\t\t\t'.acf-free-field-type'\n\t\t\t);\n\n\t\t\t// Append pro fields to \"Layout\" group.\n\t\t\tvar $layoutGroup = $fieldTypeSelect\n\t\t\t\t.find( 'optgroup option[value=\"group\"]' )\n\t\t\t\t.parent();\n\t\t\t$layoutGroup.append(\n\t\t\t\t'<option value=\"null\" disabled=\"disabled\">' +\n\t\t\t\t\tacf.__( 'Repeater (Pro only)' ) +\n\t\t\t\t\t'</option>' +\n\t\t\t\t\t'<option value=\"null\" disabled=\"disabled\">' +\n\t\t\t\t\tacf.__( 'Flexible Content (Pro only)' ) +\n\t\t\t\t\t'</option>' +\n\t\t\t\t\t'<option value=\"null\" disabled=\"disabled\">' +\n\t\t\t\t\tacf.__( 'Clone (Pro only)' ) +\n\t\t\t\t\t'</option>'\n\t\t\t);\n\n\t\t\t// Add pro fields to \"Content\" group.\n\t\t\tvar $contentGroup = $fieldTypeSelect\n\t\t\t\t.find( 'optgroup option[value=\"image\"]' )\n\t\t\t\t.parent();\n\t\t\t$contentGroup.append(\n\t\t\t\t'<option value=\"null\" disabled=\"disabled\">' +\n\t\t\t\t\tacf.__( 'Gallery (Pro only)' ) +\n\t\t\t\t\t'</option>'\n\t\t\t);\n\n\t\t\t$fieldTypeSelect.addClass( 'acf-free-field-type' );\n\t\t},\n\n\t\trender: function () {\n\t\t\t// vars\n\t\t\tvar $handle = this.$( '.handle:first' );\n\t\t\tvar menu_order = this.prop( 'menu_order' );\n\t\t\tvar label = this.getLabel();\n\t\t\tvar name = this.prop( 'name' );\n\t\t\tvar type = this.getTypeLabel();\n\t\t\tvar key = this.prop( 'key' );\n\t\t\tvar required = this.$input( 'required' ).prop( 'checked' );\n\n\t\t\t// update menu order\n\t\t\t$handle.find( '.acf-icon' ).html( parseInt( menu_order ) + 1 );\n\n\t\t\t// update required\n\t\t\tif ( required ) {\n\t\t\t\tlabel += ' <span class=\"acf-required\">*</span>';\n\t\t\t}\n\n\t\t\t// update label\n\t\t\t$handle.find( '.li-field-label strong a' ).html( label );\n\n\t\t\t// update name\n\t\t\t$handle.find( '.li-field-name' ).html( this.makeCopyable( name ) );\n\n\t\t\t// update type\n\t\t\tconst iconName = acf.strSlugify( this.getType() );\n\t\t\t$handle.find( '.field-type-label' ).text( ' ' + type );\n\t\t\t$handle\n\t\t\t\t.find( '.field-type-icon' )\n\t\t\t\t.removeClass()\n\t\t\t\t.addClass( 'field-type-icon field-type-icon-' + iconName );\n\n\t\t\t// update key\n\t\t\t$handle.find( '.li-field-key' ).html( this.makeCopyable( key ) );\n\n\t\t\t// action for 3rd party customization\n\t\t\tacf.doAction( 'render_field_object', this );\n\t\t},\n\n\t\trefresh: function () {\n\t\t\tacf.doAction( 'refresh_field_object', this );\n\t\t},\n\n\t\tisOpen: function () {\n\t\t\treturn this.$el.hasClass( 'open' );\n\t\t},\n\n\t\tonClickCopy: function ( e ) {\n\t\t\te.stopPropagation();\n\t\t\tif ( ! navigator.clipboard ) return;\n\t\t\tnavigator.clipboard.writeText( $( e.target ).text() ).then( () => {\n\t\t\t\t$( e.target ).addClass( 'copied' );\n\t\t\t\tsetTimeout( function () {\n\t\t\t\t\t$( e.target ).removeClass( 'copied' );\n\t\t\t\t}, 2000 );\n\t\t\t} );\n\t\t},\n\n\t\tonClickEdit: function ( e ) {\n\t\t\t$target = $( e.target );\n\t\t\tif (\n\t\t\t\t$target.parent().hasClass( 'row-options' ) &&\n\t\t\t\t! $target.hasClass( 'edit-field' )\n\t\t\t)\n\t\t\t\treturn;\n\t\t\tthis.isOpen() ? this.close() : this.open();\n\t\t},\n\n\t\tonChangeSettingsTab: function () {\n\t\t\tconst $settings = this.$el.children( '.settings' );\n\t\t\tacf.doAction( 'show', $settings );\n\t\t},\n\n\t\t/**\n\t\t * Adds 'active' class to row options nearest to the target.\n\t\t */\n\t\tonFocusEdit: function ( e ) {\n\t\t\tvar $rowOptions = $( e.target )\n\t\t\t\t.closest( 'li' )\n\t\t\t\t.find( '.row-options' );\n\t\t\t$rowOptions.addClass( 'active' );\n\t\t},\n\n\t\t/**\n\t\t * Removes 'active' class from row options if links in same row options area are no longer in focus.\n\t\t */\n\t\tonBlurEdit: function ( e ) {\n\t\t\tvar focusDelayMilliseconds = 50;\n\t\t\tvar $rowOptionsBlurElement = $( e.target )\n\t\t\t\t.closest( 'li' )\n\t\t\t\t.find( '.row-options' );\n\n\t\t\t// Timeout so that `activeElement` gives the new element in focus instead of the body.\n\t\t\tsetTimeout( function () {\n\t\t\t\tvar $rowOptionsFocusElement = $( document.activeElement )\n\t\t\t\t\t.closest( 'li' )\n\t\t\t\t\t.find( '.row-options' );\n\t\t\t\tif ( ! $rowOptionsBlurElement.is( $rowOptionsFocusElement ) ) {\n\t\t\t\t\t$rowOptionsBlurElement.removeClass( 'active' );\n\t\t\t\t}\n\t\t\t}, focusDelayMilliseconds );\n\t\t},\n\n\t\topen: function () {\n\t\t\t// vars\n\t\t\tvar $settings = this.$el.children( '.settings' );\n\n\t\t\t// action (open)\n\t\t\tacf.doAction( 'open_field_object', this );\n\t\t\tthis.trigger( 'openFieldObject' );\n\n\t\t\t// action (show)\n\t\t\tacf.doAction( 'show', $settings );\n\n\t\t\t// open\n\t\t\t$settings.slideDown();\n\t\t\tthis.$el.addClass( 'open' );\n\t\t},\n\n\t\tclose: function () {\n\t\t\t// vars\n\t\t\tvar $settings = this.$el.children( '.settings' );\n\n\t\t\t// close\n\t\t\t$settings.slideUp();\n\t\t\tthis.$el.removeClass( 'open' );\n\n\t\t\t// action (close)\n\t\t\tacf.doAction( 'close_field_object', this );\n\t\t\tthis.trigger( 'closeFieldObject' );\n\n\t\t\t// action (hide)\n\t\t\tacf.doAction( 'hide', $settings );\n\t\t},\n\n\t\tserialize: function () {\n\t\t\treturn acf.serialize( this.$el, this.getInputName() );\n\t\t},\n\n\t\tsave: function ( type ) {\n\t\t\t// defaults\n\t\t\ttype = type || 'settings'; // meta, settings\n\n\t\t\t// vars\n\t\t\tvar save = this.getProp( 'save' );\n\n\t\t\t// bail if already saving settings\n\t\t\tif ( save === 'settings' ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// prop\n\t\t\tthis.setProp( 'save', type );\n\n\t\t\t// debug\n\t\t\tthis.$el.attr( 'data-save', type );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'save_field_object', this, type );\n\t\t},\n\n\t\tsubmit: function () {\n\t\t\t// vars\n\t\t\tvar inputName = this.getInputName();\n\t\t\tvar save = this.get( 'save' );\n\n\t\t\t// close\n\t\t\tif ( this.isOpen() ) {\n\t\t\t\tthis.close();\n\t\t\t}\n\n\t\t\t// allow all inputs to save\n\t\t\tif ( save == 'settings' ) {\n\t\t\t\t// do nothing\n\t\t\t\t// allow only meta inputs to save\n\t\t\t} else if ( save == 'meta' ) {\n\t\t\t\tthis.$( '> .settings [name^=\"' + inputName + '\"]' ).remove();\n\n\t\t\t\t// prevent all inputs from saving\n\t\t\t} else {\n\t\t\t\tthis.$( '[name^=\"' + inputName + '\"]' ).remove();\n\t\t\t}\n\n\t\t\t// action\n\t\t\tacf.doAction( 'submit_field_object', this );\n\t\t},\n\n\t\tonChange: function ( e, $el ) {\n\t\t\t// save settings\n\t\t\tthis.save();\n\n\t\t\t// action for 3rd party customization\n\t\t\tacf.doAction( 'change_field_object', this );\n\t\t},\n\n\t\tonChanged: function ( e, $el, name, value ) {\n\t\t\t// ignore 'save'\n\t\t\tif ( name == 'save' ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// save meta\n\t\t\tif ( [ 'menu_order', 'parent' ].indexOf( name ) > -1 ) {\n\t\t\t\tthis.save( 'meta' );\n\n\t\t\t\t// save field\n\t\t\t} else {\n\t\t\t\tthis.save();\n\t\t\t}\n\n\t\t\t// render\n\t\t\tif (\n\t\t\t\t[\n\t\t\t\t\t'menu_order',\n\t\t\t\t\t'label',\n\t\t\t\t\t'required',\n\t\t\t\t\t'name',\n\t\t\t\t\t'type',\n\t\t\t\t\t'key',\n\t\t\t\t].indexOf( name ) > -1\n\t\t\t) {\n\t\t\t\tthis.render();\n\t\t\t}\n\n\t\t\t// action for 3rd party customization\n\t\t\tacf.doAction( 'change_field_object_' + name, this, value );\n\t\t},\n\n\t\tonChangeLabel: function ( e, $el ) {\n\t\t\t// set\n\t\t\tvar label = $el.val();\n\t\t\tthis.set( 'label', label );\n\n\t\t\t// render name\n\t\t\tif ( this.prop( 'name' ) == '' ) {\n\t\t\t\tvar name = acf.applyFilters(\n\t\t\t\t\t'generate_field_object_name',\n\t\t\t\t\tacf.strSanitize( label ),\n\t\t\t\t\tthis\n\t\t\t\t);\n\t\t\t\tthis.prop( 'name', name );\n\t\t\t}\n\t\t},\n\n\t\tonChangeName: function ( e, $el ) {\n\t\t\t// set\n\t\t\tvar name = $el.val();\n\t\t\tthis.set( 'name', name );\n\n\t\t\t// error\n\t\t\tif ( name.substr( 0, 6 ) === 'field_' ) {\n\t\t\t\talert(\n\t\t\t\t\tacf.__(\n\t\t\t\t\t\t'The string \"field_\" may not be used at the start of a field name'\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t\t}\n\t\t},\n\n\t\tonChangeRequired: function ( e, $el ) {\n\t\t\t// set\n\t\t\tvar required = $el.prop( 'checked' ) ? 1 : 0;\n\t\t\tthis.set( 'required', required );\n\t\t},\n\n\t\tdelete: function ( args ) {\n\t\t\t// defaults\n\t\t\targs = acf.parseArgs( args, {\n\t\t\t\tanimate: true,\n\t\t\t} );\n\n\t\t\t// add to remove list\n\t\t\tvar id = this.prop( 'ID' );\n\n\t\t\tif ( id ) {\n\t\t\t\tvar $input = $( '#_acf_delete_fields' );\n\t\t\t\tvar newVal = $input.val() + '|' + id;\n\t\t\t\t$input.val( newVal );\n\t\t\t}\n\n\t\t\t// action\n\t\t\tacf.doAction( 'delete_field_object', this );\n\n\t\t\t// animate\n\t\t\tif ( args.animate ) {\n\t\t\t\tthis.removeAnimate();\n\t\t\t} else {\n\t\t\t\tthis.remove();\n\t\t\t}\n\t\t},\n\n\t\tonClickDelete: function ( e, $el ) {\n\t\t\t// Bypass confirmation when holding down \"shift\" key.\n\t\t\tif ( e.shiftKey ) {\n\t\t\t\treturn this.delete();\n\t\t\t}\n\n\t\t\t// add class\n\t\t\tthis.$el.addClass( '-hover' );\n\n\t\t\t// add tooltip\n\t\t\tvar tooltip = acf.newTooltip( {\n\t\t\t\tconfirmRemove: true,\n\t\t\t\ttarget: $el,\n\t\t\t\tcontext: this,\n\t\t\t\tconfirm: function () {\n\t\t\t\t\tthis.delete();\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\tthis.$el.removeClass( '-hover' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tremoveAnimate: function () {\n\t\t\t// vars\n\t\t\tvar field = this;\n\t\t\tvar $list = this.$el.parent();\n\t\t\tvar $fields = acf.findFieldObjects( {\n\t\t\t\tsibling: this.$el,\n\t\t\t} );\n\n\t\t\t// remove\n\t\t\tacf.remove( {\n\t\t\t\ttarget: this.$el,\n\t\t\t\tendHeight: $fields.length ? 0 : 50,\n\t\t\t\tcomplete: function () {\n\t\t\t\t\tfield.remove();\n\t\t\t\t\tacf.doAction( 'removed_field_object', field, $list );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'remove_field_object', field, $list );\n\t\t},\n\n\t\tduplicate: function () {\n\t\t\t// vars\n\t\t\tvar newKey = acf.uniqid( 'field_' );\n\n\t\t\t// duplicate\n\t\t\tvar $newField = acf.duplicate( {\n\t\t\t\ttarget: this.$el,\n\t\t\t\tsearch: this.get( 'id' ),\n\t\t\t\treplace: newKey,\n\t\t\t} );\n\n\t\t\t// set new key\n\t\t\t$newField.attr( 'data-key', newKey );\n\n\t\t\t// get instance\n\t\t\tvar newField = acf.getFieldObject( $newField );\n\n\t\t\t// open / close\n\t\t\tif ( this.isOpen() ) {\n\t\t\t\tthis.close();\n\t\t\t} else {\n\t\t\t\tnewField.open();\n\t\t\t}\n\n\t\t\t// focus label\n\t\t\tvar $label = newField.$setting( 'label input' );\n\t\t\tsetTimeout( function () {\n\t\t\t\t$label.trigger( 'focus' );\n\t\t\t}, 251 );\n\n\t\t\t// update newField label / name\n\t\t\tvar label = newField.prop( 'label' );\n\t\t\tvar name = newField.prop( 'name' );\n\t\t\tvar end = name.split( '_' ).pop();\n\t\t\tvar copy = acf.__( 'copy' );\n\n\t\t\t// increase suffix \"1\"\n\t\t\tif ( acf.isNumeric( end ) ) {\n\t\t\t\tvar i = end * 1 + 1;\n\t\t\t\tlabel = label.replace( end, i );\n\t\t\t\tname = name.replace( end, i );\n\n\t\t\t\t// increase suffix \"(copy1)\"\n\t\t\t} else if ( end.indexOf( copy ) === 0 ) {\n\t\t\t\tvar i = end.replace( copy, '' ) * 1;\n\t\t\t\ti = i ? i + 1 : 2;\n\n\t\t\t\t// replace\n\t\t\t\tlabel = label.replace( end, copy + i );\n\t\t\t\tname = name.replace( end, copy + i );\n\n\t\t\t\t// add default \"(copy)\"\n\t\t\t} else {\n\t\t\t\tlabel += ' (' + copy + ')';\n\t\t\t\tname += '_' + copy;\n\t\t\t}\n\n\t\t\tnewField.prop( 'ID', 0 );\n\t\t\tnewField.prop( 'label', label );\n\t\t\tnewField.prop( 'name', name );\n\t\t\tnewField.prop( 'key', newKey );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'duplicate_field_object', this, newField );\n\t\t\tacf.doAction( 'append_field_object', newField );\n\t\t},\n\n\t\twipe: function () {\n\t\t\t// vars\n\t\t\tvar prevId = this.get( 'id' );\n\t\t\tvar prevKey = this.get( 'key' );\n\t\t\tvar newKey = acf.uniqid( 'field_' );\n\n\t\t\t// rename\n\t\t\tacf.rename( {\n\t\t\t\ttarget: this.$el,\n\t\t\t\tsearch: prevId,\n\t\t\t\treplace: newKey,\n\t\t\t} );\n\n\t\t\t// data\n\t\t\tthis.set( 'id', newKey );\n\t\t\tthis.set( 'prevId', prevId );\n\t\t\tthis.set( 'prevKey', prevKey );\n\n\t\t\t// props\n\t\t\tthis.prop( 'key', newKey );\n\t\t\tthis.prop( 'ID', 0 );\n\n\t\t\t// attr\n\t\t\tthis.$el.attr( 'data-key', newKey );\n\t\t\tthis.$el.attr( 'data-id', newKey );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'wipe_field_object', this );\n\t\t},\n\n\t\tmove: function () {\n\t\t\t// helper\n\t\t\tvar hasChanged = function ( field ) {\n\t\t\t\treturn field.get( 'save' ) == 'settings';\n\t\t\t};\n\n\t\t\t// vars\n\t\t\tvar changed = hasChanged( this );\n\n\t\t\t// has sub fields changed\n\t\t\tif ( ! changed ) {\n\t\t\t\tacf.getFieldObjects( {\n\t\t\t\t\tparent: this.$el,\n\t\t\t\t} ).map( function ( field ) {\n\t\t\t\t\tchanged = hasChanged( field ) || field.changed;\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\t// bail early if changed\n\t\t\tif ( changed ) {\n\t\t\t\talert(\n\t\t\t\t\tacf.__(\n\t\t\t\t\t\t'This field cannot be moved until its changes have been saved'\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// step 1.\n\t\t\tvar id = this.prop( 'ID' );\n\t\t\tvar field = this;\n\t\t\tvar popup = false;\n\t\t\tvar step1 = function () {\n\t\t\t\t// popup\n\t\t\t\tpopup = acf.newPopup( {\n\t\t\t\t\ttitle: acf.__( 'Move Custom Field' ),\n\t\t\t\t\tloading: true,\n\t\t\t\t\twidth: '300px',\n\t\t\t\t\topenedBy: field.$el.find( '.move-field' ),\n\t\t\t\t} );\n\n\t\t\t\t// ajax\n\t\t\t\tvar ajaxData = {\n\t\t\t\t\taction: 'acf/field_group/move_field',\n\t\t\t\t\tfield_id: id,\n\t\t\t\t};\n\n\t\t\t\t// get HTML\n\t\t\t\t$.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tdataType: 'html',\n\t\t\t\t\tsuccess: step2,\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\tvar step2 = function ( html ) {\n\t\t\t\t// update popup\n\t\t\t\tpopup.loading( false );\n\t\t\t\tpopup.content( html );\n\n\t\t\t\t// submit form\n\t\t\t\tpopup.on( 'submit', 'form', step3 );\n\t\t\t};\n\n\t\t\tvar step3 = function ( e, $el ) {\n\t\t\t\t// prevent\n\t\t\t\te.preventDefault();\n\n\t\t\t\t// disable\n\t\t\t\tacf.startButtonLoading( popup.$( '.button' ) );\n\n\t\t\t\t// ajax\n\t\t\t\tvar ajaxData = {\n\t\t\t\t\taction: 'acf/field_group/move_field',\n\t\t\t\t\tfield_id: id,\n\t\t\t\t\tfield_group_id: popup.$( 'select' ).val(),\n\t\t\t\t};\n\n\t\t\t\t// get HTML\n\t\t\t\t$.ajax( {\n\t\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\t\ttype: 'post',\n\t\t\t\t\tdataType: 'html',\n\t\t\t\t\tsuccess: step4,\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\tvar step4 = function ( html ) {\n\t\t\t\tpopup.content( html );\n\n\t\t\t\tif ( wp.a11y && wp.a11y.speak && acf.__ ) {\n\t\t\t\t\twp.a11y.speak(\n\t\t\t\t\t\tacf.__( 'Field moved to other group' ),\n\t\t\t\t\t\t'polite'\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tpopup.$( '.acf-close-popup' ).focus();\n\n\t\t\t\tfield.removeAnimate();\n\t\t\t};\n\n\t\t\t// start\n\t\t\tstep1();\n\t\t},\n\n\t\tonChangeType: function ( e, $el ) {\n\t\t\t// clea previous timout\n\t\t\tif ( this.changeTimeout ) {\n\t\t\t\tclearTimeout( this.changeTimeout );\n\t\t\t}\n\n\t\t\t// set new timeout\n\t\t\t// - prevents changing type multiple times whilst user types in newType\n\t\t\tthis.changeTimeout = this.setTimeout( function () {\n\t\t\t\tthis.changeType( $el.val() );\n\t\t\t}, 300 );\n\t\t},\n\n\t\tchangeType: function ( newType ) {\n\t\t\tvar prevType = this.prop( 'type' );\n\t\t\tvar prevClass = acf.strSlugify( 'acf-field-object-' + prevType );\n\t\t\tvar newClass = acf.strSlugify( 'acf-field-object-' + newType );\n\n\t\t\t// Update props.\n\t\t\tthis.$el.removeClass( prevClass ).addClass( newClass );\n\t\t\tthis.$el.attr( 'data-type', newType );\n\t\t\tthis.$el.data( 'type', newType );\n\n\t\t\t// Abort XHR if this field is already loading AJAX data.\n\t\t\tif ( this.has( 'xhr' ) ) {\n\t\t\t\tthis.get( 'xhr' ).abort();\n\t\t\t}\n\n\t\t\t// Store old settings so they can be reused later.\n\t\t\tconst $oldSettings = {};\n\n\t\t\tthis.$el\n\t\t\t\t.find(\n\t\t\t\t\t'.acf-field-settings:first > .acf-field-settings-main > .acf-field-type-settings'\n\t\t\t\t)\n\t\t\t\t.each( function () {\n\t\t\t\t\tlet tab = $( this ).data( 'parent-tab' );\n\t\t\t\t\tlet $tabSettings = $( this ).children().removeData();\n\n\t\t\t\t\t$oldSettings[ tab ] = $tabSettings;\n\n\t\t\t\t\t$tabSettings.detach();\n\t\t\t\t} );\n\n\t\t\tthis.set( 'settings-' + prevType, $oldSettings );\n\n\t\t\t// Show the settings if we already have them cached.\n\t\t\tif ( this.has( 'settings-' + newType ) ) {\n\t\t\t\tlet $newSettings = this.get( 'settings-' + newType );\n\n\t\t\t\tthis.showFieldTypeSettings( $newSettings );\n\t\t\t\tthis.set( 'type', newType );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Add loading spinner.\n\t\t\tconst $loading = $(\n\t\t\t\t'<div class=\"acf-field\"><div class=\"acf-input\"><div class=\"acf-loading\"></div></div></div>'\n\t\t\t);\n\t\t\tthis.$el\n\t\t\t\t.find(\n\t\t\t\t\t'.acf-field-settings-main-general .acf-field-type-settings'\n\t\t\t\t)\n\t\t\t\t.before( $loading );\n\n\t\t\tconst ajaxData = {\n\t\t\t\taction: 'acf/field_group/render_field_settings',\n\t\t\t\tfield: this.serialize(),\n\t\t\t\tprefix: this.getInputName(),\n\t\t\t};\n\n\t\t\t// Get the settings for this field type over AJAX.\n\t\t\tvar xhr = $.ajax( {\n\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\tdata: acf.prepareForAjax( ajaxData ),\n\t\t\t\ttype: 'post',\n\t\t\t\tdataType: 'json',\n\t\t\t\tcontext: this,\n\t\t\t\tsuccess: function ( response ) {\n\t\t\t\t\tif ( ! acf.isAjaxSuccess( response ) ) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.showFieldTypeSettings( response.data );\n\t\t\t\t},\n\t\t\t\tcomplete: function () {\n\t\t\t\t\t// also triggered by xhr.abort();\n\t\t\t\t\t$loading.remove();\n\t\t\t\t\tthis.set( 'type', newType );\n\t\t\t\t\t//this.refresh();\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// set\n\t\t\tthis.set( 'xhr', xhr );\n\t\t},\n\n\t\tshowFieldTypeSettings: function ( settings ) {\n\t\t\tif ( 'object' !== typeof settings ) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = this;\n\t\t\tconst tabs = Object.keys( settings );\n\n\t\t\ttabs.forEach( ( tab ) => {\n\t\t\t\tconst $tab = self.$el.find(\n\t\t\t\t\t'.acf-field-settings-main-' +\n\t\t\t\t\t\ttab +\n\t\t\t\t\t\t' .acf-field-type-settings'\n\t\t\t\t);\n\t\t\t\tlet tabContent = '';\n\n\t\t\t\tif (\n\t\t\t\t\t[ 'object', 'string' ].includes( typeof settings[ tab ] )\n\t\t\t\t) {\n\t\t\t\t\ttabContent = settings[ tab ];\n\t\t\t\t}\n\n\t\t\t\t$tab.prepend( tabContent );\n\t\t\t\tacf.doAction( 'append', $tab );\n\t\t\t} );\n\t\t},\n\n\t\tupdateParent: function () {\n\t\t\t// vars\n\t\t\tvar ID = acf.get( 'post_id' );\n\n\t\t\t// check parent\n\t\t\tvar parent = this.getParent();\n\t\t\tif ( parent ) {\n\t\t\t\tID = parseInt( parent.prop( 'ID' ) ) || parent.prop( 'key' );\n\t\t\t}\n\n\t\t\t// update\n\t\t\tthis.prop( 'parent', ID );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  acf.findFieldObject\n\t *\n\t *  Returns a single fieldObject $el for a given field key\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tstring key The field key\n\t *  @return\tjQuery\n\t */\n\n\tacf.findFieldObject = function ( key ) {\n\t\treturn acf.findFieldObjects( {\n\t\t\tkey: key,\n\t\t\tlimit: 1,\n\t\t} );\n\t};\n\n\t/**\n\t *  acf.findFieldObjects\n\t *\n\t *  Returns an array of fieldObject $el for the given args\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tobject args\n\t *  @return\tjQuery\n\t */\n\n\tacf.findFieldObjects = function ( args ) {\n\t\t// vars\n\t\targs = args || {};\n\t\tvar selector = '.acf-field-object';\n\t\tvar $fields = false;\n\n\t\t// args\n\t\targs = acf.parseArgs( args, {\n\t\t\tid: '',\n\t\t\tkey: '',\n\t\t\ttype: '',\n\t\t\tlimit: false,\n\t\t\tlist: null,\n\t\t\tparent: false,\n\t\t\tsibling: false,\n\t\t\tchild: false,\n\t\t} );\n\n\t\t// id\n\t\tif ( args.id ) {\n\t\t\tselector += '[data-id=\"' + args.id + '\"]';\n\t\t}\n\n\t\t// key\n\t\tif ( args.key ) {\n\t\t\tselector += '[data-key=\"' + args.key + '\"]';\n\t\t}\n\n\t\t// type\n\t\tif ( args.type ) {\n\t\t\tselector += '[data-type=\"' + args.type + '\"]';\n\t\t}\n\n\t\t// query\n\t\tif ( args.list ) {\n\t\t\t$fields = args.list.children( selector );\n\t\t} else if ( args.parent ) {\n\t\t\t$fields = args.parent.find( selector );\n\t\t} else if ( args.sibling ) {\n\t\t\t$fields = args.sibling.siblings( selector );\n\t\t} else if ( args.child ) {\n\t\t\t$fields = args.child.parents( selector );\n\t\t} else {\n\t\t\t$fields = $( selector );\n\t\t}\n\n\t\t// limit\n\t\tif ( args.limit ) {\n\t\t\t$fields = $fields.slice( 0, args.limit );\n\t\t}\n\n\t\t// return\n\t\treturn $fields;\n\t};\n\n\t/**\n\t *  acf.getFieldObject\n\t *\n\t *  Returns a single fieldObject instance for a given $el|key\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tstring|jQuery $field The field $el or key\n\t *  @return\tjQuery\n\t */\n\n\tacf.getFieldObject = function ( $field ) {\n\t\t// allow key\n\t\tif ( typeof $field === 'string' ) {\n\t\t\t$field = acf.findFieldObject( $field );\n\t\t}\n\n\t\t// instantiate\n\t\tvar field = $field.data( 'acf' );\n\t\tif ( ! field ) {\n\t\t\tfield = acf.newFieldObject( $field );\n\t\t}\n\n\t\t// return\n\t\treturn field;\n\t};\n\n\t/**\n\t *  acf.getFieldObjects\n\t *\n\t *  Returns an array of fieldObject instances for the given args\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tobject args\n\t *  @return\tarray\n\t */\n\n\tacf.getFieldObjects = function ( args ) {\n\t\t// query\n\t\tvar $fields = acf.findFieldObjects( args );\n\n\t\t// loop\n\t\tvar fields = [];\n\t\t$fields.each( function () {\n\t\t\tvar field = acf.getFieldObject( $( this ) );\n\t\t\tfields.push( field );\n\t\t} );\n\n\t\t// return\n\t\treturn fields;\n\t};\n\n\t/**\n\t *  acf.newFieldObject\n\t *\n\t *  Initializes and returns a new FieldObject instance\n\t *\n\t *  @date\t1/2/18\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tjQuery $field The field $el\n\t *  @return\tobject\n\t */\n\n\tacf.newFieldObject = function ( $field ) {\n\t\t// instantiate\n\t\tvar field = new acf.FieldObject( $field );\n\n\t\t// action\n\t\tacf.doAction( 'new_field_object', field );\n\n\t\t// return\n\t\treturn field;\n\t};\n\n\t/**\n\t *  actionManager\n\t *\n\t *  description\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar eventManager = new acf.Model( {\n\t\tpriority: 5,\n\n\t\tinitialize: function () {\n\t\t\t// actions\n\t\t\tvar actions = [ 'prepare', 'ready', 'append', 'remove' ];\n\n\t\t\t// loop\n\t\t\tactions.map( function ( action ) {\n\t\t\t\tthis.addFieldActions( action );\n\t\t\t}, this );\n\t\t},\n\n\t\taddFieldActions: function ( action ) {\n\t\t\t// vars\n\t\t\tvar pluralAction = action + '_field_objects'; // ready_field_objects\n\t\t\tvar singleAction = action + '_field_object'; // ready_field_object\n\t\t\tvar singleEvent = action + 'FieldObject'; // readyFieldObject\n\n\t\t\t// global action\n\t\t\tvar callback = function ( $el /*, arg1, arg2, etc*/ ) {\n\t\t\t\t// vars\n\t\t\t\tvar fieldObjects = acf.getFieldObjects( { parent: $el } );\n\n\t\t\t\t// call plural\n\t\t\t\tif ( fieldObjects.length ) {\n\t\t\t\t\t/// get args [$el, arg1]\n\t\t\t\t\tvar args = acf.arrayArgs( arguments );\n\n\t\t\t\t\t// modify args [pluralAction, fields, arg1]\n\t\t\t\t\targs.splice( 0, 1, pluralAction, fieldObjects );\n\t\t\t\t\tacf.doAction.apply( null, args );\n\t\t\t\t}\n\t\t\t};\n\n\t\t\t// plural action\n\t\t\tvar pluralCallback = function (\n\t\t\t\tfieldObjects /*, arg1, arg2, etc*/\n\t\t\t) {\n\t\t\t\t/// get args [fields, arg1]\n\t\t\t\tvar args = acf.arrayArgs( arguments );\n\n\t\t\t\t// modify args [singleAction, fields, arg1]\n\t\t\t\targs.unshift( singleAction );\n\n\t\t\t\t// loop\n\t\t\t\tfieldObjects.map( function ( fieldObject ) {\n\t\t\t\t\t// modify args [singleAction, field, arg1]\n\t\t\t\t\targs[ 1 ] = fieldObject;\n\t\t\t\t\tacf.doAction.apply( null, args );\n\t\t\t\t} );\n\t\t\t};\n\n\t\t\t// single action\n\t\t\tvar singleCallback = function (\n\t\t\t\tfieldObject /*, arg1, arg2, etc*/\n\t\t\t) {\n\t\t\t\t/// get args [$field, arg1]\n\t\t\t\tvar args = acf.arrayArgs( arguments );\n\n\t\t\t\t// modify args [singleAction, $field, arg1]\n\t\t\t\targs.unshift( singleAction );\n\n\t\t\t\t// action variations (ready_field/type=image)\n\t\t\t\tvar variations = [ 'type', 'name', 'key' ];\n\t\t\t\tvariations.map( function ( variation ) {\n\t\t\t\t\targs[ 0 ] =\n\t\t\t\t\t\tsingleAction +\n\t\t\t\t\t\t'/' +\n\t\t\t\t\t\tvariation +\n\t\t\t\t\t\t'=' +\n\t\t\t\t\t\tfieldObject.get( variation );\n\t\t\t\t\tacf.doAction.apply( null, args );\n\t\t\t\t} );\n\n\t\t\t\t// modify args [arg1]\n\t\t\t\targs.splice( 0, 2 );\n\n\t\t\t\t// event\n\t\t\t\tfieldObject.trigger( singleEvent, args );\n\t\t\t};\n\n\t\t\t// add actions\n\t\t\tacf.addAction( action, callback, 5 );\n\t\t\tacf.addAction( pluralAction, pluralCallback, 5 );\n\t\t\tacf.addAction( singleAction, singleCallback, 5 );\n\t\t},\n\t} );\n\n\t/**\n\t *  fieldManager\n\t *\n\t *  description\n\t *\n\t *  @date\t4/1/18\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tvar fieldManager = new acf.Model( {\n\t\tid: 'fieldManager',\n\n\t\tevents: {\n\t\t\t'submit #post': 'onSubmit',\n\t\t\t'mouseenter .acf-field-list': 'onHoverSortable',\n\t\t\t'click .add-field': 'onClickAdd',\n\t\t},\n\n\t\tactions: {\n\t\t\tremoved_field_object: 'onRemovedField',\n\t\t\tsortstop_field_object: 'onReorderField',\n\t\t\tdelete_field_object: 'onDeleteField',\n\t\t\tchange_field_object_type: 'onChangeFieldType',\n\t\t\tduplicate_field_object: 'onDuplicateField',\n\t\t},\n\n\t\tonSubmit: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar fields = acf.getFieldObjects();\n\n\t\t\t// loop\n\t\t\tfields.map( function ( field ) {\n\t\t\t\tfield.submit();\n\t\t\t} );\n\t\t},\n\n\t\tsetFieldMenuOrder: function ( field ) {\n\t\t\tthis.renderFields( field.$el.parent() );\n\t\t},\n\n\t\tonHoverSortable: function ( e, $el ) {\n\t\t\t// bail early if already sortable\n\t\t\tif ( $el.hasClass( 'ui-sortable' ) ) return;\n\n\t\t\t// sortable\n\t\t\t$el.sortable( {\n\t\t\t\thelper: function( event, element ) {\n\t\t\t\t\t// https://core.trac.wordpress.org/ticket/16972#comment:22\n\t\t\t\t\treturn element.clone()\n\t\t\t\t\t\t.find( ':input' )\n\t\t\t\t\t\t\t.attr( 'name', function( i, currentName ) {\n\t\t\t\t\t\t\t\t\treturn 'sort_' + parseInt( Math.random() * 100000, 10 ).toString() + '_' + currentName;\n\t\t\t\t\t\t\t} )\n\t\t\t\t\t\t.end();\n\t\t\t\t},\n\t\t\t\thandle: '.acf-sortable-handle',\n\t\t\t\tconnectWith: '.acf-field-list',\n\t\t\t\tstart: function ( e, ui ) {\n\t\t\t\t\tvar field = acf.getFieldObject( ui.item );\n\t\t\t\t\tui.placeholder.height( ui.item.height() );\n\t\t\t\t\tacf.doAction( 'sortstart_field_object', field, $el );\n\t\t\t\t},\n\t\t\t\tupdate: function ( e, ui ) {\n\t\t\t\t\tvar field = acf.getFieldObject( ui.item );\n\t\t\t\t\tacf.doAction( 'sortstop_field_object', field, $el );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonRemovedField: function ( field, $list ) {\n\t\t\tthis.renderFields( $list );\n\t\t},\n\n\t\tonReorderField: function ( field, $list ) {\n\t\t\tfield.updateParent();\n\t\t\tthis.renderFields( $list );\n\t\t},\n\n\t\tonDeleteField: function ( field ) {\n\t\t\t// delete children\n\t\t\tfield.getFields().map( function ( child ) {\n\t\t\t\tchild.delete( { animate: false } );\n\t\t\t} );\n\t\t},\n\n\t\tonChangeFieldType: function ( field ) {\n\t\t\t// this caused sub fields to disapear if changing type back...\n\t\t\t//this.onDeleteField( field );\n\t\t},\n\n\t\tonDuplicateField: function ( field, newField ) {\n\t\t\t// check for children\n\t\t\tvar children = newField.getFields();\n\t\t\tif ( children.length ) {\n\t\t\t\t// loop\n\t\t\t\tchildren.map( function ( child ) {\n\t\t\t\t\t// wipe field\n\t\t\t\t\tchild.wipe();\n\n\t\t\t\t\t// update parent\n\t\t\t\t\tchild.updateParent();\n\t\t\t\t} );\n\n\t\t\t\t// action\n\t\t\t\tacf.doAction(\n\t\t\t\t\t'duplicate_field_objects',\n\t\t\t\t\tchildren,\n\t\t\t\t\tnewField,\n\t\t\t\t\tfield\n\t\t\t\t);\n\t\t\t}\n\n\t\t\t// set menu order\n\t\t\tthis.setFieldMenuOrder( newField );\n\t\t},\n\n\t\trenderFields: function ( $list ) {\n\t\t\t// vars\n\t\t\tvar fields = acf.getFieldObjects( {\n\t\t\t\tlist: $list,\n\t\t\t} );\n\n\t\t\t// no fields\n\t\t\tif ( ! fields.length ) {\n\t\t\t\t$list.addClass( '-empty' );\n\t\t\t\t$list\n\t\t\t\t\t.parents( '.acf-field-list-wrap' )\n\t\t\t\t\t.first()\n\t\t\t\t\t.addClass( '-empty' );\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// has fields\n\t\t\t$list.removeClass( '-empty' );\n\t\t\t$list\n\t\t\t\t.parents( '.acf-field-list-wrap' )\n\t\t\t\t.first()\n\t\t\t\t.removeClass( '-empty' );\n\n\t\t\t// prop\n\t\t\tfields.map( function ( field, i ) {\n\t\t\t\tfield.prop( 'menu_order', i );\n\t\t\t} );\n\t\t},\n\n\t\tonClickAdd: function ( e, $el ) {\n\t\t\tlet $list;\n\n\t\t\tif ( $el.hasClass( 'add-first-field' ) ) {\n\t\t\t\t$list = $el.parents( '.acf-field-list' ).eq( 0 );\n\t\t\t} else if (\n\t\t\t\t$el.parent().hasClass( 'acf-headerbar-actions' ) ||\n\t\t\t\t$el.parent().hasClass( 'no-fields-message-inner' )\n\t\t\t) {\n\t\t\t\t$list = $( '.acf-field-list:first' );\n\t\t\t} else if ( $el.parent().hasClass( 'acf-sub-field-list-header' ) ) {\n\t\t\t\t$list = $el\n\t\t\t\t\t.parents( '.acf-input:first' )\n\t\t\t\t\t.find( '.acf-field-list:first' );\n\t\t\t} else {\n\t\t\t\t$list = $el\n\t\t\t\t\t.closest( '.acf-tfoot' )\n\t\t\t\t\t.siblings( '.acf-field-list' );\n\t\t\t}\n\n\t\t\tthis.addField( $list );\n\t\t},\n\n\t\taddField: function ( $list ) {\n\t\t\t// vars\n\t\t\tvar html = $( '#tmpl-acf-field' ).html();\n\t\t\tvar $el = $( html );\n\t\t\tvar prevId = $el.data( 'id' );\n\t\t\tvar newKey = acf.uniqid( 'field_' );\n\n\t\t\t// duplicate\n\t\t\tvar $newField = acf.duplicate( {\n\t\t\t\ttarget: $el,\n\t\t\t\tsearch: prevId,\n\t\t\t\treplace: newKey,\n\t\t\t\tappend: function ( $el, $el2 ) {\n\t\t\t\t\t$list.append( $el2 );\n\t\t\t\t},\n\t\t\t} );\n\n\t\t\t// get instance\n\t\t\tvar newField = acf.getFieldObject( $newField );\n\n\t\t\t// props\n\t\t\tnewField.prop( 'key', newKey );\n\t\t\tnewField.prop( 'ID', 0 );\n\t\t\tnewField.prop( 'label', '' );\n\t\t\tnewField.prop( 'name', '' );\n\n\t\t\t// attr\n\t\t\t$newField.attr( 'data-key', newKey );\n\t\t\t$newField.attr( 'data-id', newKey );\n\n\t\t\t// update parent prop\n\t\t\tnewField.updateParent();\n\n\t\t\t// focus type\n\t\t\tvar $type = newField.$input( 'type' );\n\t\t\tsetTimeout( function () {\n\t\t\t\tif ( $list.hasClass( 'acf-auto-add-field' ) ) {\n\t\t\t\t\t$list.removeClass( 'acf-auto-add-field' );\n\t\t\t\t} else {\n\t\t\t\t\t$type.trigger( 'focus' );\n\t\t\t\t}\n\t\t\t}, 251 );\n\n\t\t\t// open\n\t\t\tnewField.open();\n\n\t\t\t// set menu order\n\t\t\tthis.renderFields( $list );\n\n\t\t\t// action\n\t\t\tacf.doAction( 'add_field_object', newField );\n\t\t\tacf.doAction( 'append_field_object', newField );\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  locationManager\n\t *\n\t *  Field group location rules functionality\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar locationManager = new acf.Model( {\n\t\tid: 'locationManager',\n\t\twait: 'ready',\n\n\t\tevents: {\n\t\t\t'click .add-location-rule': 'onClickAddRule',\n\t\t\t'click .add-location-group': 'onClickAddGroup',\n\t\t\t'click .remove-location-rule': 'onClickRemoveRule',\n\t\t\t'change .refresh-location-rule': 'onChangeRemoveRule',\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tthis.$el = $( '#acf-field-group-options' );\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\tonClickAddRule: function ( e, $el ) {\n\t\t\tthis.addRule( $el.closest( 'tr' ) );\n\t\t},\n\n\t\tonClickRemoveRule: function ( e, $el ) {\n\t\t\tthis.removeRule( $el.closest( 'tr' ) );\n\t\t},\n\n\t\tonChangeRemoveRule: function ( e, $el ) {\n\t\t\tthis.changeRule( $el.closest( 'tr' ) );\n\t\t},\n\n\t\tonClickAddGroup: function ( e, $el ) {\n\t\t\tthis.addGroup();\n\t\t},\n\n\t\taddRule: function ( $tr ) {\n\t\t\tacf.duplicate( $tr );\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\tremoveRule: function ( $tr ) {\n\t\t\tif ( $tr.siblings( 'tr' ).length == 0 ) {\n\t\t\t\t$tr.closest( '.rule-group' ).remove();\n\t\t\t} else {\n\t\t\t\t$tr.remove();\n\t\t\t}\n\n\t\t\t// Update h4\n\t\t\tvar $group = this.$( '.rule-group:first' );\n\t\t\t$group.find( 'h4' ).text( acf.__( 'Show this field group if' ) );\n\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\tchangeRule: function ( $rule ) {\n\t\t\t// vars\n\t\t\tvar $group = $rule.closest( '.rule-group' );\n\t\t\tvar prefix = $rule\n\t\t\t\t.find( 'td.param select' )\n\t\t\t\t.attr( 'name' )\n\t\t\t\t.replace( '[param]', '' );\n\n\t\t\t// ajaxdata\n\t\t\tvar ajaxdata = {};\n\t\t\tajaxdata.action = 'acf/field_group/render_location_rule';\n\t\t\tajaxdata.rule = acf.serialize( $rule, prefix );\n\t\t\tajaxdata.rule.id = $rule.data( 'id' );\n\t\t\tajaxdata.rule.group = $group.data( 'id' );\n\n\t\t\t// temp disable\n\t\t\tacf.disable( $rule.find( 'td.value' ) );\n\n\t\t\t// ajax\n\t\t\t$.ajax( {\n\t\t\t\turl: acf.get( 'ajaxurl' ),\n\t\t\t\tdata: acf.prepareForAjax( ajaxdata ),\n\t\t\t\ttype: 'post',\n\t\t\t\tdataType: 'html',\n\t\t\t\tsuccess: function ( html ) {\n\t\t\t\t\tif ( ! html ) return;\n\t\t\t\t\t$rule.replaceWith( html );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\taddGroup: function () {\n\t\t\t// vars\n\t\t\tvar $group = this.$( '.rule-group:last' );\n\n\t\t\t// duplicate\n\t\t\t$group2 = acf.duplicate( $group );\n\n\t\t\t// update h4\n\t\t\t$group2.find( 'h4' ).text( acf.__( 'or' ) );\n\n\t\t\t// remove all tr's except the first one\n\t\t\t$group2.find( 'tr' ).not( ':first' ).remove();\n\n\t\t\t// update the groups class\n\t\t\tthis.updateGroupsClass();\n\t\t},\n\n\t\tupdateGroupsClass: function () {\n\t\t\tvar $group = this.$( '.rule-group:last' );\n\n\t\t\tvar $ruleGroups = $group.closest( '.rule-groups' );\n\n\t\t\tvar rows_count = $ruleGroups.find( '.acf-table tr' ).length;\n\n\t\t\tif ( rows_count > 1 ) {\n\t\t\t\t$ruleGroups.addClass( 'rule-groups-multiple' );\n\t\t\t} else {\n\t\t\t\t$ruleGroups.removeClass( 'rule-groups-multiple' );\n\t\t\t}\n\t\t},\n\t} );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  mid\n\t *\n\t *  Calculates the model ID for a field type\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\tstring type\n\t *  @return\tstring\n\t */\n\n\tvar modelId = function ( type ) {\n\t\treturn acf.strPascalCase( type || '' ) + 'FieldSetting';\n\t};\n\n\t/**\n\t *  registerFieldType\n\t *\n\t *  description\n\t *\n\t *  @date\t14/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tacf.registerFieldSetting = function ( model ) {\n\t\tvar proto = model.prototype;\n\t\tvar mid = modelId( proto.type + ' ' + proto.name );\n\t\tthis.models[ mid ] = model;\n\t};\n\n\t/**\n\t *  newField\n\t *\n\t *  description\n\t *\n\t *  @date\t14/12/17\n\t *  @since\t5.6.5\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tacf.newFieldSetting = function ( field ) {\n\t\t// vars\n\t\tvar type = field.get( 'setting' ) || '';\n\t\tvar name = field.get( 'name' ) || '';\n\t\tvar mid = modelId( type + ' ' + name );\n\t\tvar model = acf.models[ mid ] || null;\n\n\t\t// bail early if no setting\n\t\tif ( model === null ) return false;\n\n\t\t// instantiate\n\t\tvar setting = new model( field );\n\n\t\t// return\n\t\treturn setting;\n\t};\n\n\t/**\n\t *  acf.getFieldSetting\n\t *\n\t *  description\n\t *\n\t *  @date\t19/4/18\n\t *  @since\t5.6.9\n\t *\n\t *  @param\ttype $var Description. Default.\n\t *  @return\ttype Description.\n\t */\n\n\tacf.getFieldSetting = function ( field ) {\n\t\t// allow jQuery\n\t\tif ( field instanceof jQuery ) {\n\t\t\tfield = acf.getField( field );\n\t\t}\n\n\t\t// return\n\t\treturn field.setting;\n\t};\n\n\t/**\n\t * settingsManager\n\t *\n\t * @since\t5.6.5\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar settingsManager = new acf.Model( {\n\t\tactions: {\n\t\t\tnew_field: 'onNewField',\n\t\t},\n\t\tonNewField: function ( field ) {\n\t\t\tfield.setting = acf.newFieldSetting( field );\n\t\t},\n\t} );\n\n\t/**\n\t * acf.FieldSetting\n\t *\n\t * @since\t5.6.5\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tacf.FieldSetting = acf.Model.extend( {\n\t\tfield: false,\n\t\ttype: '',\n\t\tname: '',\n\t\twait: 'ready',\n\t\teventScope: '.acf-field',\n\n\t\tevents: {\n\t\t\tchange: 'render',\n\t\t},\n\n\t\tsetup: function ( field ) {\n\t\t\t// vars\n\t\t\tvar $field = field.$el;\n\n\t\t\t// set props\n\t\t\tthis.$el = $field;\n\t\t\tthis.field = field;\n\t\t\tthis.$fieldObject = $field.closest( '.acf-field-object' );\n\t\t\tthis.fieldObject = acf.getFieldObject( this.$fieldObject );\n\n\t\t\t// inherit data\n\t\t\t$.extend( this.data, field.data );\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function () {\n\t\t\t// do nothing\n\t\t},\n\t} );\n\n\t/**\n\t * Accordion and Tab Endpoint Settings\n\t *\n\t * The 'endpoint' setting on accordions and tabs requires an additional class on the\n\t * field object row when enabled.\n\t *\n\t * @since\t6.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar EndpointFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: '',\n\t\tname: '',\n\t\trender: function () {\n\t\t\tvar $endpoint_setting = this.fieldObject.$setting( 'endpoint' );\n\t\t\tvar $endpoint_field = $endpoint_setting.find(\n\t\t\t\t'input[type=\"checkbox\"]:first'\n\t\t\t);\n\t\t\tif ( $endpoint_field.is( ':checked' ) ) {\n\t\t\t\tthis.fieldObject.$el.addClass( 'acf-field-is-endpoint' );\n\t\t\t} else {\n\t\t\t\tthis.fieldObject.$el.removeClass( 'acf-field-is-endpoint' );\n\t\t\t}\n\t\t},\n\t} );\n\n\tvar AccordionEndpointFieldSetting = EndpointFieldSetting.extend( {\n\t\ttype: 'accordion',\n\t\tname: 'endpoint',\n\t} );\n\n\tvar TabEndpointFieldSetting = EndpointFieldSetting.extend( {\n\t\ttype: 'tab',\n\t\tname: 'endpoint',\n\t} );\n\n\tacf.registerFieldSetting( AccordionEndpointFieldSetting );\n\tacf.registerFieldSetting( TabEndpointFieldSetting );\n\n\t/**\n\t * Date Picker\n\t *\n\t * This field type requires some extra logic for its settings\n\t *\n\t * @since\t5.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar DisplayFormatFieldSetting = acf.FieldSetting.extend( {\n\t\ttype: '',\n\t\tname: '',\n\t\trender: function () {\n\t\t\tvar $input = this.$( 'input[type=\"radio\"]:checked' );\n\t\t\tif ( $input.val() != 'other' ) {\n\t\t\t\tthis.$( 'input[type=\"text\"]' ).val( $input.val() );\n\t\t\t}\n\t\t},\n\t} );\n\n\tvar DatePickerDisplayFormatFieldSetting = DisplayFormatFieldSetting.extend(\n\t\t{\n\t\t\ttype: 'date_picker',\n\t\t\tname: 'display_format',\n\t\t}\n\t);\n\n\tvar DatePickerReturnFormatFieldSetting = DisplayFormatFieldSetting.extend( {\n\t\ttype: 'date_picker',\n\t\tname: 'return_format',\n\t} );\n\n\tacf.registerFieldSetting( DatePickerDisplayFormatFieldSetting );\n\tacf.registerFieldSetting( DatePickerReturnFormatFieldSetting );\n\n\t/**\n\t * Date Time Picker\n\t *\n\t * This field type requires some extra logic for its settings\n\t *\n\t * @since\t5.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar DateTimePickerDisplayFormatFieldSetting =\n\t\tDisplayFormatFieldSetting.extend( {\n\t\t\ttype: 'date_time_picker',\n\t\t\tname: 'display_format',\n\t\t} );\n\n\tvar DateTimePickerReturnFormatFieldSetting =\n\t\tDisplayFormatFieldSetting.extend( {\n\t\t\ttype: 'date_time_picker',\n\t\t\tname: 'return_format',\n\t\t} );\n\n\tacf.registerFieldSetting( DateTimePickerDisplayFormatFieldSetting );\n\tacf.registerFieldSetting( DateTimePickerReturnFormatFieldSetting );\n\n\t/**\n\t * Time Picker\n\t *\n\t * This field type requires some extra logic for its settings\n\t *\n\t * @since\t5.0.0\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar TimePickerDisplayFormatFieldSetting = DisplayFormatFieldSetting.extend(\n\t\t{\n\t\t\ttype: 'time_picker',\n\t\t\tname: 'display_format',\n\t\t}\n\t);\n\n\tvar TimePickerReturnFormatFieldSetting = DisplayFormatFieldSetting.extend( {\n\t\ttype: 'time_picker',\n\t\tname: 'return_format',\n\t} );\n\n\tacf.registerFieldSetting( TimePickerDisplayFormatFieldSetting );\n\tacf.registerFieldSetting( TimePickerReturnFormatFieldSetting );\n\n\t/**\n\t * Color Picker Settings.\n\t *\n\t * @date\t16/12/20\n\t * @since\t5.9.4\n\t *\n\t * @param\tobject The object containing the extended variables and methods.\n\t * @return\tvoid\n\t */\n\tvar ColorPickerReturnFormat = acf.FieldSetting.extend( {\n\t\ttype: 'color_picker',\n\t\tname: 'enable_opacity',\n\t\trender: function () {\n\t\t\tvar $return_format_setting =\n\t\t\t\tthis.fieldObject.$setting( 'return_format' );\n\t\t\tvar $default_value_setting =\n\t\t\t\tthis.fieldObject.$setting( 'default_value' );\n\t\t\tvar $labelText = $return_format_setting\n\t\t\t\t.find( 'input[type=\"radio\"][value=\"string\"]' )\n\t\t\t\t.parent( 'label' )\n\t\t\t\t.contents()\n\t\t\t\t.last();\n\t\t\tvar $defaultPlaceholder =\n\t\t\t\t$default_value_setting.find( 'input[type=\"text\"]' );\n\t\t\tvar l10n = acf.get( 'colorPickerL10n' );\n\n\t\t\tif ( this.field.val() ) {\n\t\t\t\t$labelText.replaceWith( l10n.rgba_string );\n\t\t\t\t$defaultPlaceholder.attr(\n\t\t\t\t\t'placeholder',\n\t\t\t\t\t'rgba(255,255,255,0.8)'\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\t$labelText.replaceWith( l10n.hex_string );\n\t\t\t\t$defaultPlaceholder.attr( 'placeholder', '#FFFFFF' );\n\t\t\t}\n\t\t},\n\t} );\n\tacf.registerFieldSetting( ColorPickerReturnFormat );\n} )( jQuery );\n", "( function ( $, undefined ) {\n\t/**\n\t *  fieldGroupManager\n\t *\n\t *  Generic field group functionality\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar fieldGroupManager = new acf.Model( {\n\t\tid: 'fieldGroupManager',\n\n\t\tevents: {\n\t\t\t'submit #post': 'onSubmit',\n\t\t\t'click a[href=\"#\"]': 'onClick',\n\t\t\t'click .acf-delete-field-group': 'onClickDeleteFieldGroup',\n\t\t\t'blur input#title': 'onBlurValidateTitle',\n\t\t},\n\n\t\tfilters: {\n\t\t\tfind_fields_args: 'filterFindFieldArgs',\n\t\t\tfind_fields_selector: 'filterFindFieldsSelector',\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\tacf.addAction( 'prepare', this.maybeInitNewFieldGroup );\n\t\t},\n\n\t\tmaybeInitNewFieldGroup: function() {\n\t\t\tlet $field_list_wrapper = $(\n\t\t\t\t'#acf-field-group-fields > .inside > .acf-field-list-wrap.acf-auto-add-field'\n\t\t\t);\n\n\t\t\tif ( $field_list_wrapper.length ) {\n\t\t\t\t$( '.acf-headerbar-actions .add-field' ).trigger( 'click' );\n\t\t\t\t$( '.acf-title-wrap #title' ).trigger( 'focus' );\n\t\t\t}\n\n\t\t\t// Watch title input\n\t\t\t// if title empty disable submit button\n\t\t\t// if title exists allow submitting form\n\t\t\tlet $submitButton = $( '.acf-publish' );\n\t\t\t$( 'input#title' ).on( 'input', function() {\n\t\t\t\tconst titleValue = $(this).val(); \n\n\t\t\t\tif ( ! titleValue ) {\n\t\t\t\t\t$submitButton.addClass( 'disabled' );\n\t\t\t\t} else {\n\t\t\t\t\t$submitButton.removeClass( 'disabled' );\n\t\t\t\t\t$(this).removeClass( 'acf-input-error' );\n\t\t\t\t}\n\t\t\t });\n\t\t},\n\n\t\tonSubmit: function ( e, $el ) {\n\t\t\t// vars\n\t\t\tvar $title = $( '.acf-title-wrap #title' );\n\n\t\t\t// empty\n\t\t\tif ( ! $title.val() ) {\n\t\t\t\t// prevent default\n\t\t\t\te.preventDefault();\n\n\t\t\t\t// unlock form\n\t\t\t\tacf.unlockForm( $el );\n\n\t\t\t\t// focus\n\t\t\t\t$title.trigger( 'focus' );\n\t\t\t}\n\t\t},\n\n\t\tonClick: function ( e ) {\n\t\t\te.preventDefault();\n\t\t},\n\n\t\tonClickDeleteFieldGroup: function ( e, $el ) {\n\t\t\te.preventDefault();\n\t\t\t$el.addClass( '-hover' );\n\n\t\t\t// Add confirmation tooltip.\n\t\t\tacf.newTooltip( {\n\t\t\t\tconfirm: true,\n\t\t\t\ttarget: $el,\n\t\t\t\tcontext: this,\n\t\t\t\ttext: acf.__( 'Move field group to trash?' ),\n\t\t\t\tconfirm: function () {\n\t\t\t\t\twindow.location.href = $el.attr( 'href' );\n\t\t\t\t},\n\t\t\t\tcancel: function () {\n\t\t\t\t\t$el.removeClass( '-hover' );\n\t\t\t\t},\n\t\t\t} );\n\t\t},\n\n\t\tonBlurValidateTitle: function ( e, $el ) {\n\t\t\tif ( ! $el.val() ) {\n\t\t\t\t$el.addClass( 'acf-input-error' );\n\t\t\t\t$( '.acf-publish' ).addClass( 'disabled' );\n\t\t\t} else {\n\t\t\t\t$el.removeClass( 'acf-input-error' );\n\t\t\t\t$( '.acf-publish' ).removeClass( 'disabled' );\n\t\t\t}\n\t\t},\n\n\t\tfilterFindFieldArgs: function ( args ) {\n\t\t\targs.visible = true;\n\n\t\t\tif ( args.parent && ( args.parent.hasClass( 'acf-field-object' ) || args.parent.parents( '.acf-field-object' ).length ) ) {\n\t\t\t\targs.visible = false;\n\t\t\t\targs.excludeSubFields = true;\n\t\t\t}\n\n\t\t\treturn args;\n\t\t},\n\n\t\tfilterFindFieldsSelector: function ( selector ) {\n\t\t\treturn selector + ', .acf-field-acf-field-group-settings-tabs';\n\t\t},\n\t} );\n\n\t/**\n\t *  screenOptionsManager\n\t *\n\t *  Screen options functionality\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar screenOptionsManager = new acf.Model( {\n\t\tid: 'screenOptionsManager',\n\t\twait: 'prepare',\n\n\t\tevents: {\n\t\t\t'change #acf-field-key-hide': 'onFieldKeysChange',\n\t\t\t'change #acf-field-settings-tabs': 'onFieldSettingsTabsChange',\n\t\t\t'change [name=\"screen_columns\"]': 'render',\n\t\t},\n\n\t\tinitialize: function () {\n\t\t\t// vars\n\t\t\tvar $div = $( '#adv-settings' );\n\t\t\tvar $append = $( '#acf-append-show-on-screen' );\n\n\t\t\t// append\n\t\t\t$div.find( '.metabox-prefs' ).append( $append.html() );\n\t\t\t$div.find( '.metabox-prefs br' ).remove();\n\n\t\t\t// clean up\n\t\t\t$append.remove();\n\n\t\t\t// initialize\n\t\t\tthis.$el = $( '#screen-options-wrap' );\n\n\t\t\t// render\n\t\t\tthis.render();\n\t\t},\n\n\t\tisFieldKeysChecked: function () {\n\t\t\treturn this.$el.find( '#acf-field-key-hide' ).prop( 'checked' );\n\t\t},\n\n\t\tisFieldSettingsTabsChecked: function () {\n\t\t\tconst $input = this.$el.find( '#acf-field-settings-tabs' );\n\n\t\t\t// Screen option is hidden by filter.\n\t\t\tif ( ! $input.length ) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\treturn $input.prop( 'checked' );\n\t\t},\n\n\t\tgetSelectedColumnCount: function () {\n\t\t\treturn this.$el\n\t\t\t\t.find( 'input[name=\"screen_columns\"]:checked' )\n\t\t\t\t.val();\n\t\t},\n\n\t\tonFieldKeysChange: function ( e, $el ) {\n\t\t\tvar val = this.isFieldKeysChecked() ? 1 : 0;\n\t\t\tacf.updateUserSetting( 'show_field_keys', val );\n\t\t\tthis.render();\n\t\t},\n\n\t\tonFieldSettingsTabsChange: function () {\n\t\t\tconst val = this.isFieldSettingsTabsChecked() ? 1 : 0;\n\t\t\tacf.updateUserSetting( 'show_field_settings_tabs', val );\n\t\t\tthis.render();\n\t\t},\n\n\t\trender: function () {\n\t\t\tif ( this.isFieldKeysChecked() ) {\n\t\t\t\t$( '#acf-field-group-fields' ).addClass( 'show-field-keys' );\n\t\t\t} else {\n\t\t\t\t$( '#acf-field-group-fields' ).removeClass( 'show-field-keys' );\n\t\t\t}\n\n\t\t\tif ( ! this.isFieldSettingsTabsChecked() ) {\n\t\t\t\t$( '#acf-field-group-fields' ).addClass( 'hide-tabs' );\n\t\t\t\t$( '.acf-field-settings-main' )\n\t\t\t\t\t.removeClass( 'acf-hidden' )\n\t\t\t\t\t.prop( 'hidden', false );\n\t\t\t} else {\n\t\t\t\t$( '#acf-field-group-fields' ).removeClass( 'hide-tabs' );\n\n\t\t\t\t$( '.acf-field-object.open' ).each( function () {\n\t\t\t\t\tconst tabFields = acf.getFields( {\n\t\t\t\t\t\ttype: 'tab',\n\t\t\t\t\t\tparent: $( this ),\n\t\t\t\t\t\texcludeSubFields: true,\n\t\t\t\t\t\tlimit: 1,\n\t\t\t\t\t} );\n\n\t\t\t\t\tif ( tabFields.length ) {\n\t\t\t\t\t\ttabFields[ 0 ].tabs.set( 'initialized', false );\n\t\t\t\t\t}\n\n\t\t\t\t\tacf.doAction( 'show', $( this ) );\n\t\t\t\t} );\n\t\t\t}\n\n\t\t\tif ( this.getSelectedColumnCount() == 1 ) {\n\t\t\t\t$( 'body' ).removeClass( 'columns-2' );\n\t\t\t\t$( 'body' ).addClass( 'columns-1' );\n\t\t\t} else {\n\t\t\t\t$( 'body' ).removeClass( 'columns-1' );\n\t\t\t\t$( 'body' ).addClass( 'columns-2' );\n\t\t\t}\n\t\t},\n\t} );\n\n\t/**\n\t *  appendFieldManager\n\t *\n\t *  Appends fields together\n\t *\n\t *  @date\t15/12/17\n\t *  @since\t5.7.0\n\t *\n\t *  @param\tvoid\n\t *  @return\tvoid\n\t */\n\n\tvar appendFieldManager = new acf.Model( {\n\t\tactions: {\n\t\t\tnew_field: 'onNewField',\n\t\t},\n\n\t\tonNewField: function ( field ) {\n\t\t\t// bail early if not append\n\t\t\tif ( ! field.has( 'append' ) ) return;\n\n\t\t\t// vars\n\t\t\tvar append = field.get( 'append' );\n\t\t\tvar $sibling = field.$el\n\t\t\t\t.siblings( '[data-name=\"' + append + '\"]' )\n\t\t\t\t.first();\n\n\t\t\t// bail early if no sibling\n\t\t\tif ( ! $sibling.length ) return;\n\n\t\t\t// ul\n\t\t\tvar $div = $sibling.children( '.acf-input' );\n\t\t\tvar $ul = $div.children( 'ul' );\n\n\t\t\t// create ul\n\t\t\tif ( ! $ul.length ) {\n\t\t\t\t$div.wrapInner( '<ul class=\"acf-hl\"><li></li></ul>' );\n\t\t\t\t$ul = $div.children( 'ul' );\n\t\t\t}\n\n\t\t\t// li\n\t\t\tvar html = field.$( '.acf-input' ).html();\n\t\t\tvar $li = $( '<li>' + html + '</li>' );\n\t\t\t$ul.append( $li );\n\t\t\t$ul.attr( 'data-cols', $ul.children().length );\n\n\t\t\t// clean up\n\t\t\tfield.remove();\n\t\t},\n\t} );\n} )( jQuery );\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import './_field-group.js';\nimport './_field-group-field.js';\nimport './_field-group-settings.js';\nimport './_field-group-conditions.js';\nimport './_field-group-fields.js';\nimport './_field-group-locations.js';\nimport './_field-group-compatibility.js';\n"], "names": ["$", "undefined", "_acf", "acf", "getCompatibility", "field_group", "save_field", "$field", "type", "getFieldObject", "save", "delete_field", "animate", "delete", "update_field_meta", "name", "value", "prop", "delete_field_meta", "field_object", "model", "extend", "o", "$settings", "tag", "tags", "split", "splice", "join", "selector", "str_replace", "_add_action", "callback", "add_action", "set", "apply", "arguments", "_add_filter", "add_filter", "_add_event", "event", "substr", "indexOf", "context", "document", "on", "e", "$el", "closest", "_set_$field", "data", "find", "focus", "setting", "actionManager", "Model", "actions", "open_field_object", "close_field_object", "add_field_object", "duplicate_field_object", "delete_field_object", "change_field_object_type", "change_field_object_label", "change_field_object_name", "change_field_object_parent", "sortstop_field_object", "onOpenFieldObject", "field", "doAction", "get", "onCloseFieldObject", "onAddFieldObject", "onDuplicateFieldObject", "onDeleteFieldObject", "onChangeFieldObjectType", "onChangeFieldObjectLabel", "onChangeFieldObjectName", "onChangeFieldObjectParent", "j<PERSON><PERSON><PERSON>", "ConditionalLogicFieldSetting", "FieldSetting", "events", "$rule", "scope", "ruleData", "$input", "$td", "$toggle", "$control", "$groups", "$rules", "$tabLabel", "fieldObject", "open", "$div", "show", "enable", "close", "hide", "disable", "render", "addClass", "renderRules", "removeClass", "self", "each", "renderRule", "renderField", "renderOperator", "renderValue", "choices", "validFieldTypes", "cid", "$select", "getFieldObjects", "map", "choice", "id", "<PERSON><PERSON><PERSON>", "text", "get<PERSON><PERSON><PERSON>", "__", "disabled", "conditionTypes", "getConditionTypes", "fieldType", "getType", "length", "indents", "getParents", "repeat", "push", "renderSelect", "val", "findFieldObject", "prototype", "operator", "label", "conditionType", "Array", "$newSelect", "detach", "html", "setTimeout", "attr", "onChangeToggle", "onClickAddGroup", "addGroup", "$group", "$group2", "duplicate", "not", "remove", "onFocusField", "onChangeField", "onChangeOperator", "onClickAdd", "onClickRemove", "siblings", "registerFieldSetting", "conditionalLog<PERSON><PERSON><PERSON><PERSON>", "duplicate_field_objects", "onDuplicateFieldObjects", "children", "newField", "prevField", "$selects", "child", "add", "FieldObject", "eventScope", "change", "changed", "key", "setup", "inherit", "getInputId", "$meta", "$handle", "$setting", "getParent", "limit", "pop", "getFields", "parent", "getInputName", "newInput", "inputId", "inputName", "append", "getProp", "has", "setProp", "prevVal", "props", "Object", "keys", "getName", "getTypeLabel", "types", "initialize", "addProFields", "checkCopyable", "makeCopyable", "navigator", "clipboard", "fieldTypes", "hasOwnProperty", "$fieldTypeSelect", "$layoutGroup", "$contentGroup", "menu_order", "required", "parseInt", "iconName", "strSlugify", "refresh", "isOpen", "hasClass", "onClickCopy", "stopPropagation", "writeText", "target", "then", "onClickEdit", "$target", "onChangeSettingsTab", "onFocusEdit", "$rowOptions", "onBlurEdit", "focusDelayMilliseconds", "$rowOptionsBlurElement", "$rowOptionsFocusElement", "activeElement", "is", "trigger", "slideDown", "slideUp", "serialize", "submit", "onChange", "onChanged", "onChangeLabel", "applyFilters", "strSanitize", "onChangeName", "alert", "onChangeRequired", "args", "parseArgs", "newVal", "removeAnimate", "onClickDelete", "shift<PERSON>ey", "tooltip", "newTooltip", "confirmRemove", "confirm", "cancel", "$list", "$fields", "findFieldObjects", "sibling", "endHeight", "complete", "new<PERSON>ey", "uniqid", "$newField", "search", "replace", "$label", "end", "copy", "isNumeric", "i", "wipe", "prevId", "prev<PERSON><PERSON>", "rename", "move", "has<PERSON><PERSON>ed", "popup", "step1", "newPopup", "title", "loading", "width", "openedBy", "ajaxData", "action", "field_id", "ajax", "url", "prepareForAjax", "dataType", "success", "step2", "content", "step3", "preventDefault", "startButtonLoading", "field_group_id", "step4", "wp", "a11y", "speak", "onChangeType", "changeTimeout", "clearTimeout", "changeType", "newType", "prevType", "prevClass", "newClass", "abort", "$oldSettings", "tab", "$tabSettings", "removeData", "$newSettings", "showFieldTypeSettings", "$loading", "before", "prefix", "xhr", "response", "isAjaxSuccess", "settings", "tabs", "for<PERSON>ach", "$tab", "tab<PERSON>ontent", "includes", "prepend", "updateParent", "ID", "list", "parents", "slice", "newFieldObject", "fields", "eventManager", "priority", "addFieldActions", "pluralAction", "singleAction", "singleEvent", "fieldObjects", "arrayArgs", "plural<PERSON><PERSON><PERSON>", "unshift", "singleCallback", "variations", "variation", "addAction", "fieldManager", "removed_field_object", "onSubmit", "setFieldMenuOrder", "renderFields", "onHoverSortable", "sortable", "helper", "element", "clone", "currentName", "Math", "random", "toString", "handle", "connectWith", "start", "ui", "item", "placeholder", "height", "update", "onRemovedField", "onReorderField", "onDeleteField", "onChangeFieldType", "onDuplicateField", "first", "eq", "addField", "$el2", "$type", "locationManager", "wait", "updateGroupsClass", "onClickAddRule", "addRule", "onClickRemoveRule", "removeRule", "onChangeRemoveRule", "changeRule", "$tr", "ajaxdata", "rule", "group", "replaceWith", "$ruleGroups", "rows_count", "modelId", "strPascalCase", "proto", "mid", "models", "newFieldSetting", "getFieldSetting", "getField", "settingsManager", "new_field", "onNewField", "$fieldObject", "EndpointFieldSetting", "$endpoint_setting", "$endpoint_field", "AccordionEndpointFieldSetting", "TabEndpointFieldSetting", "DisplayFormatFieldSetting", "DatePickerDisplayFormatFieldSetting", "DatePickerReturnFormatFieldSetting", "DateTimePickerDisplayFormatFieldSetting", "DateTimePickerReturnFormatFieldSetting", "TimePickerDisplayFormatFieldSetting", "TimePickerReturnFormatFieldSetting", "ColorPickerReturnFormat", "$return_format_setting", "$default_value_setting", "$labelText", "contents", "last", "$defaultPlaceholder", "l10n", "rgba_string", "hex_string", "fieldGroupManager", "filters", "find_fields_args", "find_fields_selector", "maybeInitNewFieldGroup", "$field_list_wrapper", "$submitButton", "titleValue", "$title", "unlockForm", "onClick", "onClickDeleteFieldGroup", "window", "location", "href", "onBlurValidateTitle", "filterFindFieldArgs", "visible", "excludeSubFields", "filterFindFieldsSelector", "screenOptionsManager", "$append", "isFieldKeysChecked", "isFieldSettingsTabsChecked", "getSelectedColumnCount", "onFieldKeysChange", "updateUserSetting", "onFieldSettingsTabsChange", "tabFields", "appendFieldManager", "$sibling", "$ul", "wrapInner", "$li"], "sourceRoot": ""}