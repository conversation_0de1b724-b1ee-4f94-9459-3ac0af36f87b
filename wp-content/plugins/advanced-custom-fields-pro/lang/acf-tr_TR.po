# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-01-18T14:07:28+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: tr_TR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/admin/admin-field-group.php:326
msgid "Field Settings Tabs"
msgstr ""

#. Author URI of the plugin
msgid ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""

#: includes/api/api-template.php:867
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-field-group.php:673
msgid "Close Modal"
msgstr ""

#: includes/admin/admin-field-group.php:162
#: assets/build/js/acf-field-group.js:1304
#: assets/build/js/acf-field-group.js:1468
msgid "Field moved to other group"
msgstr ""

#: includes/admin/admin-field-group.php:161 assets/build/js/acf.js:1397
#: assets/build/js/acf.js:1463
msgid "Close modal"
msgstr ""

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr ""

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr ""

#: includes/fields/class-acf-field-select.php:450
#: includes/fields/class-acf-field-true_false.php:197
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:257
msgid "Save Other Choice"
msgstr ""

#: includes/fields/class-acf-field-radio.php:246
msgid "Allow Other Choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:446
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:405
msgid "Save Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:394
msgid "Allow Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:144
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Güncellemeler"

#: includes/admin/views/html-admin-navigation.php:79
msgid "Advanced Custom Fields logo"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:22
msgid "Save Changes"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:16
msgid "Field Group Title"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:3
msgid "Add title"
msgstr ""

#. translators: %s url to getting started guide
#: includes/admin/views/field-groups-empty.php:20
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""

#: includes/admin/views/field-groups-empty.php:15
msgid "Add Field Group"
msgstr ""

#. translators: %s url to creating a field group page
#: includes/admin/views/field-groups-empty.php:10
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/field-groups-empty.php:5
msgid "Add Your First Field Group"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:16
msgid "Upgrade Now"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:11
msgid "Options Pages"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:10
msgid "ACF Blocks"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:8
msgid "Gallery Field"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:7
msgid "Flexible Content Field"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:6
msgid "Repeater Field"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:4
#: includes/admin/views/html-admin-navigation.php:97
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/field-group-options.php:242
msgid "Delete Field Group"
msgstr ""

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/field-group-options.php:236
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/admin/views/field-group-options.php:179
msgid "Group Settings"
msgstr ""

#: includes/admin/views/field-group-options.php:28
msgid "Location Rules"
msgstr ""

#. translators: %s url to field types list
#: includes/admin/views/field-group-fields.php:61
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/field-group-fields.php:54
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/field-group-fields.php:53
msgid "Add Your First Field"
msgstr ""

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/field-group-fields.php:32
msgid "#"
msgstr ""

#: includes/admin/views/field-group-fields.php:22
#: includes/admin/views/field-group-fields.php:56
#: includes/admin/views/field-group-fields.php:92
#: includes/admin/views/html-admin-form-top.php:21
msgid "Add Field"
msgstr ""

#: includes/admin/views/field-group-field.php:191
#: includes/admin/views/field-group-options.php:40
msgid "Presentation"
msgstr ""

#: includes/admin/views/field-group-field.php:159
msgid "Validation"
msgstr ""

#: includes/admin/views/field-group-field.php:93
msgid "General"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:70
msgid "Import JSON"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:361
msgid "Export Field Groups - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid "Export As JSON"
msgstr ""

#: includes/admin/admin-field-group.php:158
#: assets/build/js/acf-field-group.js:2366
#: assets/build/js/acf-field-group.js:2635
msgid "Move field group to trash?"
msgstr ""

#. Author of the plugin
msgid "WP Engine"
msgstr ""

#: acf.php:506
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""

#: acf.php:504
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#: includes/acf-value-functions.php:374
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - ACF başlatılmadan önce ACF alan değerlerini almak "
"için bir veya daha fazla çağrı algıladık. Bu desteklenmez ve hatalı "
"biçimlendirilmiş veya eksik verilere neden olabilir. <a href=\"%2$s\" target="
"\"_blank\">Bunu nasıl düzelteceğinizi öğrenin</a>."

#: includes/fields/class-acf-field-user.php:537
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s %2$s rolüne sahip bir kullanıcıya sahip olmalıdır."
msgstr[1] "%1$s şu rollerden birine ait bir kullanıcıya sahip olmalıdır: %2$s"

#: includes/fields/class-acf-field-user.php:528
msgid "%1$s must have a valid user ID."
msgstr "%1$s geçerli bir kullanıcı kimliğine sahip olmalıdır."

#: includes/fields/class-acf-field-user.php:366
msgid "Invalid request."
msgstr "Geçersiz istek."

#: includes/fields/class-acf-field-select.php:683
msgid "%1$s is not one of %2$s"
msgstr "%1$s bir %2$s değil"

#: includes/fields/class-acf-field-post_object.php:669
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s %2$s terimine sahip olmalı."
msgstr[1] "%1$s şu terimlerden biri olmalı: %2$s"

#: includes/fields/class-acf-field-post_object.php:653
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s %2$s yazı tipinde olmalıdır."
msgstr[1] "%1$s şu yazı tiplerinden birinde olmalıdır: %2$s"

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must have a valid post ID."
msgstr "%1$s geçerli bir yazı kimliği olmalıdır."

#: includes/fields/class-acf-field-file.php:472
msgid "%s requires a valid attachment ID."
msgstr "%s geçerli bir ek kimliği gerektirir."

#: includes/admin/views/field-group-options.php:205
msgid "Show in REST API"
msgstr "REST API'da göster"

#: includes/fields/class-acf-field-color_picker.php:167
msgid "Enable Transparency"
msgstr "Saydamlığı etkinleştir"

#: includes/fields/class-acf-field-color_picker.php:186
msgid "RGBA Array"
msgstr "RGBA dizisi"

#: includes/fields/class-acf-field-color_picker.php:96
msgid "RGBA String"
msgstr "RGBA metni"

#: includes/fields/class-acf-field-color_picker.php:95
#: includes/fields/class-acf-field-color_picker.php:185
msgid "Hex String"
msgstr "Hex metin"

#: includes/admin/admin-field-group.php:187
#: assets/build/js/acf-field-group.js:837
#: assets/build/js/acf-field-group.js:933
msgid "Gallery (Pro only)"
msgstr "Galeri (Yalnızca Pro)"

#: includes/admin/admin-field-group.php:186
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:923
msgid "Clone (Pro only)"
msgstr "Çoğalt (Yalnızca Pro)"

#: includes/admin/admin-field-group.php:185
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:920
msgid "Flexible Content (Pro only)"
msgstr "Esnek İçerik (Yalnızca Pro)"

#: includes/admin/admin-field-group.php:184
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:917
msgid "Repeater (Pro only)"
msgstr "Yineleyici (Yalnızca Pro)"

#: includes/admin/admin-field-group.php:354
msgctxt "post status"
msgid "Active"
msgstr "Etkin"

#: includes/fields/class-acf-field-email.php:178
msgid "'%s' is not a valid email address"
msgstr "'%s' geçerli bir e-posta adresi değil"

#: includes/fields/class-acf-field-color_picker.php:74
msgid "Color value"
msgstr "Renk değeri"

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Select default color"
msgstr "Varsayılan rengi seç"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Clear color"
msgstr "Rengi temizle"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Bloklar"

#: includes/acf-wp-functions.php:83
msgid "Options"
msgstr "Ayarlar"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Kullanıcılar"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Menü ögeleri"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Bileşenler"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Dosya ekleri"

#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "Taksonomiler"

#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "İletiler"

#: includes/ajax/class-acf-ajax-local-json-diff.php:68
msgid "JSON field group (newer)"
msgstr "JSON alan grubu (daha yeni)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:64
msgid "Original field group"
msgstr "Orijinal alan grubu"

#: includes/ajax/class-acf-ajax-local-json-diff.php:59
msgid "Last updated: %s"
msgstr "Son güncellenme: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:53
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr "Üzgünüz, bu alan grubu fark karşılaştırma için uygun değil."

#: includes/ajax/class-acf-ajax-local-json-diff.php:43
msgid "Invalid field group ID."
msgstr "Geçersiz alan grup no."

#: includes/ajax/class-acf-ajax-local-json-diff.php:36
msgid "Invalid field group parameter(s)."
msgstr "Geçersiz alan grubu parametresi/leri."

#: includes/admin/admin-field-groups.php:506
msgid "Awaiting save"
msgstr "Kayıt edilmeyi bekliyor"

#: includes/admin/admin-field-groups.php:503
msgid "Saved"
msgstr "Kaydedildi"

#: includes/admin/admin-field-groups.php:499
msgid "Import"
msgstr "İçe aktar"

#: includes/admin/admin-field-groups.php:495
msgid "Review changes"
msgstr "Değişiklikleri incele"

#: includes/admin/admin-field-groups.php:471
msgid "Located in: %s"
msgstr "Konumu: %s"

#: includes/admin/admin-field-groups.php:467
msgid "Located in plugin: %s"
msgstr "Eklenti içinde konumlu: %s"

#: includes/admin/admin-field-groups.php:463
msgid "Located in theme: %s"
msgstr "Tema içinde konumlu: %s"

#: includes/admin/admin-field-groups.php:441
msgid "Various"
msgstr "Çeşitli"

#: includes/admin/admin-field-groups.php:200
#: includes/admin/admin-field-groups.php:565
msgid "Sync changes"
msgstr "Değişiklikleri eşitle"

#: includes/admin/admin-field-groups.php:199
msgid "Loading diff"
msgstr "Fark yükleniyor"

#: includes/admin/admin-field-groups.php:198
msgid "Review local JSON changes"
msgstr "Yerel JSON değişikliklerini incele"

#: includes/admin/admin.php:172
msgid "Visit website"
msgstr "Web sitesini ziyaret et"

#: includes/admin/admin.php:171
msgid "View details"
msgstr "Ayrıntıları görüntüle"

#: includes/admin/admin.php:170
msgid "Version %s"
msgstr "Sürüm %s"

#: includes/admin/admin.php:169
msgid "Information"
msgstr "Bilgi"

#: includes/admin/admin.php:160
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Yardım masası</a>. Yardım masamızdaki "
"profesyonel destek çalışanlarımızı daha derin, teknik sorunların üstesinden "
"gelmenize yardımcı olabilirler."

#: includes/admin/admin.php:156
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Tartışmalar</a>. Topluluk forumlarımızda "
"etkin ve dost canlısı bir topluluğumuz var, sizi ACF dünyasının 'nasıl "
"yaparım'ları ile ilgili yardımcı olabilirler."

#: includes/admin/admin.php:152
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Belgeler</a>. Karşınıza çıkabilecek bir çok "
"konu hakkında geniş içerikli belgelerimize baş vurabilirsiniz."

#: includes/admin/admin.php:149
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Destek konusunu çok ciddiye alıyoruz ve size ACF ile sitenizde en iyi "
"çözümlere ulaşmanızı istiyoruz. Eğer bir sorunla karşılaşırsanız yardım "
"alabileceğiniz bir kaç yer var:"

#: includes/admin/admin.php:146 includes/admin/admin.php:148
msgid "Help & Support"
msgstr "Yardım ve destek"

#: includes/admin/admin.php:137
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"İşin içinden çıkamadığınızda lütfen Yardım ve destek sekmesinden irtibata "
"geçin."

#: includes/admin/admin.php:134
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"İlk alan grubunuzu oluşturmadan önce <a href=\"%s\" target=\"_blank"
"\">Başlarken</a> rehberimize okumanızı öneririz, bu sayede eklentinin "
"filozofisini daha iyi anlayabilir ve en iyi çözümleri öğrenebilirsiniz."

#: includes/admin/admin.php:132
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"The Advanced Custom Fields eklentisi bir görsel form oluşturucu ile "
"WordPress düzenleme ekranlarını ek alanlarla özelleştirme imkanı sağlıyor, "
"ve sezgisel API ile her türlü tema şablon dosyasında bu özel alanlar "
"gösterilebiliyor."

#: includes/admin/admin.php:129 includes/admin/admin.php:131
msgid "Overview"
msgstr "Genel görünüm"

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr "Konum türü \"%s\" zaten kayıtlı."

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr "\"%s\" sınıfı mevcut değil."

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Geçersiz nonce."

#: includes/fields/class-acf-field-user.php:361
msgid "Error loading field."
msgstr "Alan yükleme sırasında hata."

#: assets/build/js/acf-input.js:2742 assets/build/js/acf-input.js:2811
#: assets/build/js/acf-input.js:2913 assets/build/js/acf-input.js:2987
msgid "Location not found: %s"
msgstr "Konum bulunamadı: %s"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Hata</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Bileşen"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Kullanıcı kuralı"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Yorum"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Yazı biçimi"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Menü ögesi"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Yazı durumu"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menüler"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Menü konumları"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menü"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Yazı taksonomisi"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Alt sayfa (ebeveyni olan)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Üst sayfa (alt sayfası olan)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Üst düzey sayfa (ebeveynsiz)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Yazılar sayfası"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Ön Sayfa"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Sayfa tipi"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Arka yüz görüntüleniyor"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Ön yüz görüntüleniyor"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Giriş yapıldı"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Şu anki kullanıcı"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Sayfa şablonu"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Kayıt ol"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Ekle / düzenle"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Kullanıcı formu"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Sayfa ebeveyni"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Süper yönetici"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Şu anki kullanıcı rolü"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Varsayılan şablon"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Yazı şablonu"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Yazı kategorisi"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Tüm %s biçimleri"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Eklenti"

#: includes/validation.php:365
msgid "%s value is required"
msgstr "%s değeri gerekli"

#: includes/admin/views/field-group-field-conditional-logic.php:59
msgid "Show this field if"
msgstr "Alanı bu şart gerçekleşirse göster"

#: includes/admin/views/field-group-field-conditional-logic.php:26
#: includes/admin/views/field-group-field.php:279
msgid "Conditional Logic"
msgstr "Koşullu mantık"

#: includes/admin/admin.php:207
#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/html-location-rule.php:92
msgid "and"
msgstr "ve"

#: includes/admin/admin-field-groups.php:290
msgid "Local JSON"
msgstr "Yerel JSON"

#: includes/admin/views/html-notice-upgrade.php:30
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Lütfen ayrıca premium eklentilerin de (%s) en üst sürüme güncellendiğinden "
"emin olun."

#: includes/admin/views/html-notice-upgrade.php:28
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Bu sürüm veritabanınız için iyileştirmeler içeriyor ve yükseltme "
"gerektiriyor."

#: includes/admin/views/html-notice-upgrade.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "%1$s v%2$s sürümüne güncellediğiniz için teşekkür ederiz!"

#: includes/admin/views/html-notice-upgrade.php:27
msgid "Database Upgrade Required"
msgstr "Veritabanı yükseltmesi gerekiyor"

#: includes/admin/views/html-notice-upgrade.php:18
msgid "Options Page"
msgstr "Seçenekler sayfası"

#: includes/admin/views/html-notice-upgrade.php:15
msgid "Gallery"
msgstr "Galeri"

#: includes/admin/views/html-notice-upgrade.php:12
msgid "Flexible Content"
msgstr "Esnek içerik"

#: includes/admin/views/html-notice-upgrade.php:9
msgid "Repeater"
msgstr "Tekrarlayıcı"

#: includes/admin/views/html-admin-tools.php:24
msgid "Back to all tools"
msgstr "Tüm araçlara geri dön"

#: includes/admin/views/field-group-options.php:160
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Eğer düzenleme ekranında birden çok alan grubu ortaya çıkarsa, ilk alan "
"grubunun seçenekleri kullanılır (en düşük sıralama numarasına sahip olan)"

#: includes/admin/views/field-group-options.php:160
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "Düzenleme ekranından <b>gizlemek</b> istediğiniz ögeleri <b>seçin</b>."

#: includes/admin/views/field-group-options.php:159
msgid "Hide on screen"
msgstr "Ekranda gizle"

#: includes/admin/views/field-group-options.php:151
msgid "Send Trackbacks"
msgstr "Geri izlemeleri gönder"

#: includes/admin/views/field-group-options.php:150
msgid "Tags"
msgstr "Etiketler"

#: includes/admin/views/field-group-options.php:149
msgid "Categories"
msgstr "Kategoriler"

#: includes/admin/views/field-group-options.php:147
msgid "Page Attributes"
msgstr "Sayfa özellikleri"

#: includes/admin/views/field-group-options.php:146
msgid "Format"
msgstr "Biçim"

#: includes/admin/views/field-group-options.php:145
msgid "Author"
msgstr "Yazar"

#: includes/admin/views/field-group-options.php:144
msgid "Slug"
msgstr "Kısa isim"

#: includes/admin/views/field-group-options.php:143
msgid "Revisions"
msgstr "Sürümler"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/field-group-options.php:142
msgid "Comments"
msgstr "Yorumlar"

#: includes/admin/views/field-group-options.php:141
msgid "Discussion"
msgstr "Tartışma"

#: includes/admin/views/field-group-options.php:139
msgid "Excerpt"
msgstr "Özet"

#: includes/admin/views/field-group-options.php:138
msgid "Content Editor"
msgstr "İçerik düzenleyici"

#: includes/admin/views/field-group-options.php:137
msgid "Permalink"
msgstr "Kalıcı bağlantı"

#: includes/admin/views/field-group-options.php:222
msgid "Shown in field group list"
msgstr "Alan grubu listesinde görüntülenir"

#: includes/admin/views/field-group-options.php:122
msgid "Field groups with a lower order will appear first"
msgstr "Daha düşük sıralamaya sahip alan grupları daha önce görünür"

#: includes/admin/views/field-group-options.php:121
msgid "Order No."
msgstr "Sipariş No."

#: includes/admin/views/field-group-options.php:112
msgid "Below fields"
msgstr "Alanlarının altında"

#: includes/admin/views/field-group-options.php:111
msgid "Below labels"
msgstr "Etiketlerin altında"

#: includes/admin/views/field-group-options.php:104
msgid "Instruction placement"
msgstr "Yönerge yerleştirme"

#: includes/admin/views/field-group-options.php:87
msgid "Label placement"
msgstr "Etiket yerleştirme"

#: includes/admin/views/field-group-options.php:77
msgid "Side"
msgstr "Yan"

#: includes/admin/views/field-group-options.php:76
msgid "Normal (after content)"
msgstr "Normal (içerikten sonra)"

#: includes/admin/views/field-group-options.php:75
msgid "High (after title)"
msgstr "Yüksek (başlıktan sonra)"

#: includes/admin/views/field-group-options.php:68
msgid "Position"
msgstr "Konum"

#: includes/admin/views/field-group-options.php:59
msgid "Seamless (no metabox)"
msgstr "Pürüzsüz (metabox yok)"

#: includes/admin/views/field-group-options.php:58
msgid "Standard (WP metabox)"
msgstr "Standart (WP metabox)"

#: includes/admin/views/field-group-options.php:51
msgid "Style"
msgstr "Stil"

#: includes/admin/views/field-group-fields.php:44
msgid "Type"
msgstr "Tür"

#: includes/admin/admin-field-groups.php:285
#: includes/admin/views/field-group-fields.php:43
msgid "Key"
msgstr "Anahtar"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/field-group-fields.php:37
msgid "Order"
msgstr "Düzen"

#: includes/admin/views/field-group-field.php:294
msgid "Close Field"
msgstr "Alanı kapat"

#: includes/admin/views/field-group-field.php:235
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:219
msgid "class"
msgstr "sınıf"

#: includes/admin/views/field-group-field.php:256
msgid "width"
msgstr "genişlik"

#: includes/admin/views/field-group-field.php:250
msgid "Wrapper Attributes"
msgstr "Kapsayıcı öznitelikleri"

#: includes/admin/views/field-group-field.php:171
msgid "Required"
msgstr ""

#: includes/admin/views/field-group-field.php:203
msgid "Instructions for authors. Shown when submitting data"
msgstr "Yazarlara gösterilecek talimatlar. Veri gönderirken gösterilir"

#: includes/admin/views/field-group-field.php:202
msgid "Instructions"
msgstr "Yönergeler"

#: includes/admin/views/field-group-field.php:106
msgid "Field Type"
msgstr "Alan tipi"

#: includes/admin/views/field-group-field.php:134
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Tek kelime, boşluksuz. Alt çizgi ve tireye izin var"

#: includes/admin/views/field-group-field.php:133
msgid "Field Name"
msgstr "Alan adı"

#: includes/admin/views/field-group-field.php:121
msgid "This is the name which will appear on the EDIT page"
msgstr "Bu isim DÜZENLEME sayfasında görüntülenecek isimdir"

#: includes/admin/views/field-group-field.php:120
msgid "Field Label"
msgstr "Alan etiketi"

#: includes/admin/views/field-group-field.php:70
msgid "Delete"
msgstr "Sil"

#: includes/admin/views/field-group-field.php:70
msgid "Delete field"
msgstr "Sil alanı"

#: includes/admin/views/field-group-field.php:68
msgid "Move"
msgstr "Taşı"

#: includes/admin/views/field-group-field.php:68
msgid "Move field to another group"
msgstr "Alanı başka gruba taşı"

#: includes/admin/views/field-group-field.php:66
msgid "Duplicate field"
msgstr "Alanı çoğalt"

#: includes/admin/views/field-group-field.php:62
#: includes/admin/views/field-group-field.php:65
msgid "Edit field"
msgstr "Alanı düzenle"

#: includes/admin/views/field-group-field.php:58
msgid "Drag to reorder"
msgstr "Yeniden düzenlemek için sürükleyin"

#: includes/admin/admin-field-group.php:168
#: includes/admin/views/html-location-group.php:3
#: assets/build/js/acf-field-group.js:1935
#: assets/build/js/acf-field-group.js:2166
msgid "Show this field group if"
msgstr "Bu alan grubunu şu koşulda göster"

#: includes/admin/views/html-admin-page-upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Güncelleme yok."

#: includes/admin/views/html-admin-page-upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Veritabanı yükseltme tamamlandı. <a href=\"%s\">Yenilikler </a>"

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Yükseltme görevlerini okuyor..."

#: includes/admin/views/html-admin-page-upgrade-network.php:165
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "Yükseltme başarısız oldu."

#: includes/admin/views/html-admin-page-upgrade-network.php:162
msgid "Upgrade complete."
msgstr "Yükseltme başarılı."

#: includes/admin/views/html-admin-page-upgrade-network.php:148
#: includes/admin/views/html-admin-page-upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "Veri %s sürümüne yükseltiliyor"

#: includes/admin/views/html-admin-page-upgrade-network.php:121
#: includes/admin/views/html-notice-upgrade.php:44
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Devam etmeden önce veritabanınızı yedeklemeniz önemle önerilir. "
"Güncelleştiriciyi şimdi çalıştırmak istediğinizden emin misiniz?"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
msgid "Please select at least one site to upgrade."
msgstr "Lütfen yükseltmek için en az site seçin."

#: includes/admin/views/html-admin-page-upgrade-network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Veritabanı güncellemesi tamamlandı. <a href=\"%s\">Ağ panosuna geri dön</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:80
msgid "Site is up to date"
msgstr "Site güncel"

#: includes/admin/views/html-admin-page-upgrade-network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Site %1$s sürümünden %2$s sürümüne veritabanı yükseltmesi gerektiriyor"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Site"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:96
msgid "Upgrade Sites"
msgstr "Siteleri yükselt"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Şu siteler için VT güncellemesi gerekiyor. Güncellemek istediklerinizi "
"işaretleyin ve %s tuşuna basın."

#: includes/admin/views/field-group-field-conditional-logic.php:171
#: includes/admin/views/field-group-locations.php:38
msgid "Add rule group"
msgstr "Kural grubu ekle"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Bu gelişmiş özel alanları hangi düzenleme ekranlarının kullanacağını "
"belirlemek için bir kural seti oluşturun"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Kurallar"

#: includes/admin/tools/class-acf-admin-tool-export.php:478
msgid "Copied"
msgstr "Kopyalandı"

#: includes/admin/tools/class-acf-admin-tool-export.php:441
msgid "Copy to clipboard"
msgstr "Panoya kopyala"

#: includes/admin/tools/class-acf-admin-tool-export.php:362
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Aşağıdaki kod seçilmiş alan grubu/grupları için yerel bir sürüm kaydetmek "
"için kullanılır. Yerel alan grubu daha hızlı yüklenme süreleri, sürüm "
"yönetimi ve dinamik alanlar/ayarlar gibi faydalar sağlar. Yapmanız gereken "
"bu kodu kopyalayıp temanızın functions.php dosyasına eklemek ya da harici "
"bir dosya olarak temanıza dahil etmek."

#: includes/admin/tools/class-acf-admin-tool-export.php:329
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:233
#: includes/admin/tools/class-acf-admin-tool-export.php:262
msgid "Select Field Groups"
msgstr "Alan gruplarını seç"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "1 alan grubu içeri aktarıldı."
msgstr[1] "%s alan grubu içeri aktarıldı."

#: includes/admin/tools/class-acf-admin-tool-export.php:96
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "Hiç alan grubu seçilmemiş"

#: includes/admin/tools/class-acf-admin-tool-export.php:39
#: includes/admin/tools/class-acf-admin-tool-export.php:337
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "PHP oluştur"

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Alan gruplarını dışarı aktar"

#: includes/admin/tools/class-acf-admin-tool-import.php:146
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "1 alan grubu içeri aktarıldı"
msgstr[1] "%s alan grubu içeri aktarıldı"

#: includes/admin/tools/class-acf-admin-tool-import.php:115
msgid "Import file empty"
msgstr "İçe aktarılan dosya boş"

#: includes/admin/tools/class-acf-admin-tool-import.php:106
msgid "Incorrect file type"
msgstr "Geçersiz dosya tipi"

#: includes/admin/tools/class-acf-admin-tool-import.php:101
msgid "Error uploading file. Please try again"
msgstr "Dosya yüklenirken hata oluştu. Lütfen tekrar deneyin"

#: includes/admin/tools/class-acf-admin-tool-import.php:51
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:28
#: includes/admin/tools/class-acf-admin-tool-import.php:50
msgid "Import Field Groups"
msgstr "Alan gruplarını içeri aktar"

#: includes/admin/admin-field-groups.php:494
msgid "Sync"
msgstr "Eşitle"

#: includes/admin/admin-field-groups.php:942
msgid "Select %s"
msgstr "Seç %s"

#: includes/admin/admin-field-groups.php:527
#: includes/admin/admin-field-groups.php:556
#: includes/admin/views/field-group-field.php:66
msgid "Duplicate"
msgstr "Mükerrer"

#: includes/admin/admin-field-groups.php:527
msgid "Duplicate this item"
msgstr "Bu ögeyi çoğalt"

#: includes/admin/admin-field-groups.php:284
#: includes/admin/views/field-group-options.php:221
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
msgid "Description"
msgstr "Açıklama"

#: includes/admin/admin-field-groups.php:491
#: includes/admin/admin-field-groups.php:829
msgid "Sync available"
msgstr "Eşitleme mevcut"

#: includes/admin/admin-field-groups.php:754
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Alan grubu eşitlendi."
msgstr[1] "%s alan grubu eşitlendi."

#: includes/admin/admin-field-groups.php:696
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Alan grubu çoğaltıldı."
msgstr[1] "%s alan grubu çoğaltıldı."

#: includes/admin/admin-field-groups.php:118
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Etkin <span class=\"count\">(%s)</span>"
msgstr[1] "Etkin <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:237
msgid "Review sites & upgrade"
msgstr "Siteleri incele ve güncelle"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:93
#: includes/admin/admin-upgrade.php:94 includes/admin/admin-upgrade.php:213
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Veritabanını güncelle"

#: includes/admin/admin.php:49 includes/admin/views/field-group-options.php:140
msgid "Custom Fields"
msgstr "Ek alanlar"

#: includes/admin/admin-field-group.php:719
msgid "Move Field"
msgstr "Alanı taşı"

#: includes/admin/admin-field-group.php:708
#: includes/admin/admin-field-group.php:712
msgid "Please select the destination for this field"
msgstr "Lütfen bu alan için bir hedef seçin"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/admin-field-group.php:669
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "%1$s alanı artık %2$s alan grubunda bulunabilir"

#: includes/admin/admin-field-group.php:666
msgid "Move Complete."
msgstr "Taşıma tamamlandı."

#: includes/admin/views/field-group-field.php:273
#: includes/admin/views/field-group-options.php:189
msgid "Active"
msgstr "Etkinleştir"

#: includes/admin/admin-field-group.php:323
msgid "Field Keys"
msgstr "Alan anahtarları"

#: includes/admin/admin-field-group.php:224
#: includes/admin/tools/class-acf-admin-tool-export.php:286
msgid "Settings"
msgstr "Ayarlar"

#: includes/admin/admin-field-groups.php:286
msgid "Location"
msgstr "Konum"

#: includes/admin/admin-field-group.php:169 assets/build/js/acf-input.js:983
#: assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Boş"

#: includes/acf-field-group-functions.php:846
#: includes/admin/admin-field-group.php:166
#: assets/build/js/acf-field-group.js:1158
#: assets/build/js/acf-field-group.js:1310
msgid "copy"
msgstr "kopyala"

#: includes/admin/admin-field-group.php:165
#: assets/build/js/acf-field-group.js:344
#: assets/build/js/acf-field-group.js:389
msgid "(this field)"
msgstr "(bu alan)"

#: includes/admin/admin-field-group.php:163 assets/build/js/acf-input.js:918
#: assets/build/js/acf-input.js:943 assets/build/js/acf-input.js:1002
#: assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "İşaretlendi"

#: includes/admin/admin-field-group.php:160
#: assets/build/js/acf-field-group.js:1249
#: assets/build/js/acf-field-group.js:1408
msgid "Move Custom Field"
msgstr "Özel alanı taşı"

#: includes/admin/admin-field-group.php:159
#: assets/build/js/acf-field-group.js:370
#: assets/build/js/acf-field-group.js:415
msgid "No toggle fields available"
msgstr "Kullanılabilir aç-kapa alan yok"

#: includes/admin/admin-field-group.php:157
msgid "Field group title is required"
msgstr "Alan grubu başlığı gerekli"

#: includes/admin/admin-field-group.php:156
#: assets/build/js/acf-field-group.js:1238
#: assets/build/js/acf-field-group.js:1394
msgid "This field cannot be moved until its changes have been saved"
msgstr "Bu alan, üzerinde yapılan değişiklikler kaydedilene kadar taşınamaz"

#: includes/admin/admin-field-group.php:155
#: assets/build/js/acf-field-group.js:1049
#: assets/build/js/acf-field-group.js:1192
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Artık alan isimlerinin başlangıcında “field_” kullanılmayacak"

#: includes/admin/admin-field-group.php:82
msgid "Field group draft updated."
msgstr "Alan grubu taslağı güncellendi."

#: includes/admin/admin-field-group.php:81
msgid "Field group scheduled for."
msgstr "Alan grubu zamanlandı."

#: includes/admin/admin-field-group.php:80
msgid "Field group submitted."
msgstr "Alan grubu gönderildi."

#: includes/admin/admin-field-group.php:79
msgid "Field group saved."
msgstr "Alan grubu kaydedildi."

#: includes/admin/admin-field-group.php:78
msgid "Field group published."
msgstr "Alan grubu yayımlandı."

#: includes/admin/admin-field-group.php:75
msgid "Field group deleted."
msgstr "Alan grubu silindi."

#: includes/admin/admin-field-group.php:73
#: includes/admin/admin-field-group.php:74
#: includes/admin/admin-field-group.php:76
msgid "Field group updated."
msgstr "Alan grubu güncellendi."

#: includes/admin/admin-tools.php:119
#: includes/admin/views/html-admin-navigation.php:109
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Araçlar"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "eşit değilse"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "eşitse"

#: includes/locations.php:102
msgid "Forms"
msgstr "Formlar"

#: includes/locations.php:100 includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Sayfa"

#: includes/locations.php:99 includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Yazı"

#: includes/fields.php:358
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:357
msgid "Relational"
msgstr "İlişkisel"

#: includes/fields.php:356
msgid "Choice"
msgstr "Seçim"

#: includes/fields.php:354
msgid "Basic"
msgstr "Basit"

#: includes/fields.php:313
msgid "Unknown"
msgstr "Bilinmeyen"

#: includes/fields.php:313
msgid "Field type does not exist"
msgstr "Var olmayan alan tipi"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "İstenmeyen tespit edildi"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "Yazı güncellendi"

#: includes/forms/form-front.php:106
msgid "Update"
msgstr "Güncelleme"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "E-postayı doğrula"

#: includes/fields.php:355 includes/forms/form-front.php:49
msgid "Content"
msgstr "İçerik"

#: includes/forms/form-front.php:40
msgid "Title"
msgstr "Ba&#351l&#305k"

#: includes/assets.php:371 includes/forms/form-comment.php:160
#: assets/build/js/acf-input.js:7337 assets/build/js/acf-input.js:7915
msgid "Edit field group"
msgstr "Alan grubunu düzenle"

#: includes/admin/admin-field-group.php:181 assets/build/js/acf-input.js:1125
#: assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr "Seçim daha az"

#: includes/admin/admin-field-group.php:180 assets/build/js/acf-input.js:1106
#: assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr "Seçin daha büyük"

#: includes/admin/admin-field-group.php:179 assets/build/js/acf-input.js:1075
#: assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "Değer daha az"

#: includes/admin/admin-field-group.php:178 assets/build/js/acf-input.js:1045
#: assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "Değer daha büyük"

#: includes/admin/admin-field-group.php:177 assets/build/js/acf-input.js:888
#: assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "Değer içeriyor"

#: includes/admin/admin-field-group.php:176 assets/build/js/acf-input.js:862
#: assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr "Değer bir desenle eşleşir"

#: includes/admin/admin-field-group.php:175 assets/build/js/acf-input.js:840
#: assets/build/js/acf-input.js:1023 assets/build/js/acf-input.js:903
#: assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "Değer eşit değilse"

#: includes/admin/admin-field-group.php:174 assets/build/js/acf-input.js:810
#: assets/build/js/acf-input.js:964 assets/build/js/acf-input.js:864
#: assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "Değer eşitse"

#: includes/admin/admin-field-group.php:173 assets/build/js/acf-input.js:788
#: assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr "Hiçbir değer"

#: includes/admin/admin-field-group.php:172 assets/build/js/acf-input.js:758
#: assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr "Herhangi bir değer"

#: includes/assets.php:352 assets/build/js/acf.js:1524
#: assets/build/js/acf.js:1604
msgid "Cancel"
msgstr "Vazgeç"

#: includes/assets.php:348 assets/build/js/acf.js:1698
#: assets/build/js/acf.js:1801
msgid "Are you sure?"
msgstr "Emin misiniz?"

#: includes/assets.php:368 assets/build/js/acf-input.js:9370
#: assets/build/js/acf-input.js:10211
msgid "%d fields require attention"
msgstr "%d alan dikkatinizi gerektiriyor"

#: includes/assets.php:367 assets/build/js/acf-input.js:9368
#: assets/build/js/acf-input.js:10207
msgid "1 field requires attention"
msgstr "1 alan dikkatinizi gerektiriyor"

#: includes/assets.php:366 includes/validation.php:287
#: includes/validation.php:297 assets/build/js/acf-input.js:9363
#: assets/build/js/acf-input.js:10202
msgid "Validation failed"
msgstr "Doğrulama başarısız"

#: includes/assets.php:365 assets/build/js/acf-input.js:9526
#: assets/build/js/acf-input.js:10385
msgid "Validation successful"
msgstr "Doğrulama başarılı"

#: includes/media.php:54 assets/build/js/acf-input.js:7165
#: assets/build/js/acf-input.js:7719
msgid "Restricted"
msgstr "Kısıtlı"

#: includes/media.php:53 assets/build/js/acf-input.js:6980
#: assets/build/js/acf-input.js:7483
msgid "Collapse Details"
msgstr "Detayları daralt"

#: includes/media.php:52 assets/build/js/acf-input.js:6980
#: assets/build/js/acf-input.js:7480
msgid "Expand Details"
msgstr "Ayrıntıları genişlet"

#: includes/media.php:51 assets/build/js/acf-input.js:6847
#: assets/build/js/acf-input.js:7328
msgid "Uploaded to this post"
msgstr "Bu yazıya yüklenmiş"

#: includes/media.php:50 assets/build/js/acf-input.js:6886
#: assets/build/js/acf-input.js:7367
msgctxt "verb"
msgid "Update"
msgstr "Güncelleme"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Düzenle"

#: includes/assets.php:362 assets/build/js/acf-input.js:9140
#: assets/build/js/acf-input.js:9973
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Bu sayfadan başka bir sayfaya geçerseniz yaptığınız değişiklikler kaybolacak"

#: includes/api/api-helpers.php:3395
msgid "File type must be %s."
msgstr "Dosya tipi %s olmalı."

#: includes/admin/admin-field-group.php:167
#: includes/admin/views/field-group-field-conditional-logic.php:59
#: includes/admin/views/field-group-field-conditional-logic.php:169
#: includes/admin/views/field-group-locations.php:36
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3391 assets/build/js/acf-field-group.js:492
#: assets/build/js/acf-field-group.js:1973
#: assets/build/js/acf-field-group.js:544
#: assets/build/js/acf-field-group.js:2210
msgid "or"
msgstr "veya"

#: includes/api/api-helpers.php:3364
msgid "File size must not exceed %s."
msgstr "Dosya boyutu %s boyutunu geçmemeli."

#: includes/api/api-helpers.php:3359
msgid "File size must be at least %s."
msgstr "Dosya boyutu en az %s olmalı."

#: includes/api/api-helpers.php:3344
msgid "Image height must not exceed %dpx."
msgstr "Görsel yüksekliği %dpx değerini geçmemeli."

#: includes/api/api-helpers.php:3339
msgid "Image height must be at least %dpx."
msgstr "Görsel yüksekliği en az %dpx olmalı."

#: includes/api/api-helpers.php:3325
msgid "Image width must not exceed %dpx."
msgstr "Görsel genişliği %dpx değerini geçmemeli."

#: includes/api/api-helpers.php:3320
msgid "Image width must be at least %dpx."
msgstr "Görsel genişliği en az %dpx olmalı."

#: includes/api/api-helpers.php:1566 includes/api/api-term.php:147
msgid "(no title)"
msgstr "(başlık yok)"

#: includes/api/api-helpers.php:861
msgid "Full Size"
msgstr "Tam boyut"

#: includes/api/api-helpers.php:820
msgid "Large"
msgstr "Büyük"

#: includes/api/api-helpers.php:819
msgid "Medium"
msgstr "Orta"

#: includes/api/api-helpers.php:818
msgid "Thumbnail"
msgstr "Küçük resim"

#: includes/acf-field-functions.php:846
#: includes/admin/admin-field-group.php:164
#: assets/build/js/acf-field-group.js:789
#: assets/build/js/acf-field-group.js:859
msgid "(no label)"
msgstr "(etiket yok)"

#: includes/fields/class-acf-field-textarea.php:142
msgid "Sets the textarea height"
msgstr "Metin alanı yüksekliğini ayarla"

#: includes/fields/class-acf-field-textarea.php:141
msgid "Rows"
msgstr "Satırlar"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Metin alanı"

#: includes/fields/class-acf-field-checkbox.php:447
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"En başa tüm seçimleri tersine çevirmek için ekstra bir seçim kutusu ekle"

#: includes/fields/class-acf-field-checkbox.php:409
msgid "Save 'custom' values to the field's choices"
msgstr "‘Özel’ değerleri alanın seçenekleri arasına kaydet"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Allow 'custom' values to be added"
msgstr "‘Özel’ alanların eklenebilmesine izin ver"

#: includes/fields/class-acf-field-checkbox.php:233
msgid "Add new choice"
msgstr "Yeni seçenek ekle"

#: includes/fields/class-acf-field-checkbox.php:170
msgid "Toggle All"
msgstr "Tümünü aç/kapat"

#: includes/fields/class-acf-field-page_link.php:477
msgid "Allow Archives URLs"
msgstr "Arşivler adresine izin ver"

#: includes/fields/class-acf-field-page_link.php:165
msgid "Archives"
msgstr "Arşivler"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Sayfa bağlantısı"

#: includes/fields/class-acf-field-taxonomy.php:945
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Ekle"

#: includes/admin/views/field-group-fields.php:42
#: includes/fields/class-acf-field-taxonomy.php:910
msgid "Name"
msgstr "Bağlantı ismi"

#: includes/fields/class-acf-field-taxonomy.php:894
msgid "%s added"
msgstr "%s eklendi"

#: includes/fields/class-acf-field-taxonomy.php:858
msgid "%s already exists"
msgstr "%s zaten mevcut"

#: includes/fields/class-acf-field-taxonomy.php:846
msgid "User unable to add new %s"
msgstr "Kullanıcı yeni %s ekleyemiyor"

#: includes/fields/class-acf-field-taxonomy.php:756
msgid "Term ID"
msgstr "Terim no"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Term Object"
msgstr "Terim nesnesi"

#: includes/fields/class-acf-field-taxonomy.php:740
msgid "Load value from posts terms"
msgstr "Yazının terimlerinden değerleri yükle"

#: includes/fields/class-acf-field-taxonomy.php:739
msgid "Load Terms"
msgstr "Terimleri yükle"

#: includes/fields/class-acf-field-taxonomy.php:729
msgid "Connect selected terms to the post"
msgstr "Seçilmiş terimleri yazıya bağla"

#: includes/fields/class-acf-field-taxonomy.php:728
msgid "Save Terms"
msgstr "Terimleri kaydet"

#: includes/fields/class-acf-field-taxonomy.php:718
msgid "Allow new terms to be created whilst editing"
msgstr "Düzenlenirken yeni terimlerin oluşabilmesine izin ver"

#: includes/fields/class-acf-field-taxonomy.php:717
msgid "Create Terms"
msgstr "Terimleri oluştur"

#: includes/fields/class-acf-field-taxonomy.php:776
msgid "Radio Buttons"
msgstr "Tekli seçim"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Single Value"
msgstr "Tek değer"

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Multi Select"
msgstr "Çoklu seçim"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Checkbox"
msgstr "İşaret kutusu"

#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Multiple Values"
msgstr "Çoklu değer"

#: includes/fields/class-acf-field-taxonomy.php:766
msgid "Select the appearance of this field"
msgstr "Bu alanın görünümünü seçin"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Appearance"
msgstr "Görünüm"

#: includes/fields/class-acf-field-taxonomy.php:707
msgid "Select the taxonomy to be displayed"
msgstr "Görüntülenecek taksonomiyi seçin"

#: includes/fields/class-acf-field-taxonomy.php:668
msgctxt "No Terms"
msgid "No %s"
msgstr ""

#: includes/fields/class-acf-field-number.php:263
msgid "Value must be equal to or lower than %d"
msgstr "Değer %d değerine eşit ya da daha küçük olmalı"

#: includes/fields/class-acf-field-number.php:256
msgid "Value must be equal to or higher than %d"
msgstr "Değer %d değerine eşit ya da daha büyük olmalı"

#: includes/fields/class-acf-field-number.php:241
msgid "Value must be a number"
msgstr "Değer bir sayı olmalı"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Numara"

#: includes/fields/class-acf-field-radio.php:261
msgid "Save 'other' values to the field's choices"
msgstr "‘Diğer’ değerlerini alanın seçenekleri arasına kaydet"

#: includes/fields/class-acf-field-radio.php:250
msgid "Add 'other' choice to allow for custom values"
msgstr "Özel değerlere izin vermek için 'diğer' seçeneği ekle"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Radyo düğmesi"

#: includes/fields/class-acf-field-accordion.php:104
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Önceki akordeonun durması için bir son nokta tanımlayın. Bu akordeon "
"görüntülenmeyecek."

#: includes/fields/class-acf-field-accordion.php:93
msgid "Allow this accordion to open without closing others."
msgstr "Bu akordeonun diğerlerini kapatmadan açılmasını sağla."

#: includes/fields/class-acf-field-accordion.php:92
msgid "Multi-expand"
msgstr "Çoklu genişletme"

#: includes/fields/class-acf-field-accordion.php:82
msgid "Display this accordion as open on page load."
msgstr "Sayfa yüklemesi sırasında bu akordeonu açık olarak görüntüle."

#: includes/fields/class-acf-field-accordion.php:81
msgid "Open"
msgstr "Açık"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Akordiyon"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-file.php:276
msgid "Restrict which files can be uploaded"
msgstr "Yüklenebilecek dosyaları sınırlandırın"

#: includes/fields/class-acf-field-file.php:217
msgid "File ID"
msgstr "Dosya no"

#: includes/fields/class-acf-field-file.php:216
msgid "File URL"
msgstr "Dosya adresi"

#: includes/fields/class-acf-field-file.php:215
msgid "File Array"
msgstr "Dosya dizisi"

#: includes/fields/class-acf-field-file.php:183
msgid "Add File"
msgstr "Dosya Ekle"

#: includes/admin/tools/class-acf-admin-tool-import.php:94
#: includes/fields/class-acf-field-file.php:183
msgid "No file selected"
msgstr "Dosya seçilmedi"

#: includes/fields/class-acf-field-file.php:147
msgid "File name"
msgstr "Dosya adı"

#: includes/fields/class-acf-field-file.php:60
#: assets/build/js/acf-input.js:2466 assets/build/js/acf-input.js:2612
msgid "Update File"
msgstr "Dosyayı güncelle"

#: includes/fields/class-acf-field-file.php:59
#: assets/build/js/acf-input.js:2465 assets/build/js/acf-input.js:2611
msgid "Edit File"
msgstr "Dosya düzenle"

#: includes/admin/tools/class-acf-admin-tool-import.php:59
#: includes/fields/class-acf-field-file.php:58
#: assets/build/js/acf-input.js:2439 assets/build/js/acf-input.js:2584
msgid "Select File"
msgstr "Dosya seç"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Dosya"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Parola"

#: includes/fields/class-acf-field-select.php:391
msgid "Specify the value returned"
msgstr "Dönecek değeri belirt"

#: includes/fields/class-acf-field-select.php:460
msgid "Use AJAX to lazy load choices?"
msgstr "Seçimlerin tembel yüklenmesi için AJAX kullanılsın mı?"

#: includes/fields/class-acf-field-checkbox.php:358
#: includes/fields/class-acf-field-select.php:380
msgid "Enter each default value on a new line"
msgstr "Her satıra bir değer girin"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:48
#: assets/build/js/acf-input.js:6745 assets/build/js/acf-input.js:7213
msgctxt "verb"
msgid "Select"
msgstr "Seçim"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Yükleme başarısız oldu"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Aranıyor&hellip;"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Daha fazla sonuç yükleniyor&hellip;"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Sadece %d öge seçebilirsiniz"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Sadece 1 öge seçebilirsiniz"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Lütfen %d karakter silin"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Lütfen 1 karakter silin"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Lütfen %d veya daha fazla karakter girin"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Lütfen 1 veya daha fazla karakter girin"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Eşleşme yok"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d sonuç bulundu. Dolaşmak için yukarı ve aşağı okları kullanın."

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Bir sonuç bulundu, seçmek için enter tuşuna basın."

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:777
msgctxt "noun"
msgid "Select"
msgstr "Seçim"

#: includes/fields/class-acf-field-user.php:74
msgid "User ID"
msgstr "Kullanıcı No"

#: includes/fields/class-acf-field-user.php:73
msgid "User Object"
msgstr "Kullanıcı nesnesi"

#: includes/fields/class-acf-field-user.php:72
msgid "User Array"
msgstr "Kullanıcı dizisi"

#: includes/fields/class-acf-field-user.php:60
msgid "All user roles"
msgstr "Bütün kullanıcı rolleri"

#: includes/fields/class-acf-field-user.php:52
msgid "Filter by role"
msgstr "Kurala göre filtrele"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:101
msgid "User"
msgstr "Kullanıcı"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Ayırıcı"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Select Color"
msgstr "Renk seç"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Default"
msgstr "Varsayılan"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Clear"
msgstr "Temizle"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Renk seçici"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seçim"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Bitti"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Şimdi"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Zaman dilimi"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosaniye"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisaniye"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "İkinci"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Dakika"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Saat"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Zaman"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Zamanı se"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Tarih zaman seçici"

#: includes/fields/class-acf-field-accordion.php:103
msgid "Endpoint"
msgstr "Uç nokta"

#: includes/admin/views/field-group-options.php:95
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Sola hizalı"

#: includes/admin/views/field-group-options.php:94
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Üste hizalı"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Konumlandırma"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Sekme"

#: includes/fields/class-acf-field-url.php:159
msgid "Value must be a valid URL"
msgstr "Değer geçerli bir web adresi olmalı"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Web adresi"

#: includes/fields/class-acf-field-link.php:174
msgid "Link URL"
msgstr "Bağlantı adresi"

#: includes/fields/class-acf-field-link.php:173
msgid "Link Array"
msgstr "Bağlantı dizisi"

#: includes/fields/class-acf-field-link.php:142
msgid "Opens in a new window/tab"
msgstr "Yeni pencerede/sekmede açılır"

#: includes/fields/class-acf-field-link.php:137
msgid "Select Link"
msgstr "Bağlantı seç"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "EPosta"

#: includes/fields/class-acf-field-number.php:185
#: includes/fields/class-acf-field-range.php:214
msgid "Step Size"
msgstr "Adım boyutu"

#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-range.php:192
msgid "Maximum Value"
msgstr "En fazla değer"

#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-range.php:181
msgid "Minimum Value"
msgstr "En az değer"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Aralık"

#: includes/fields/class-acf-field-button-group.php:172
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-radio.php:217
#: includes/fields/class-acf-field-select.php:398
msgid "Both (Array)"
msgstr "İkisi de (Dizi)"

#: includes/admin/views/field-group-fields.php:41
#: includes/fields/class-acf-field-button-group.php:171
#: includes/fields/class-acf-field-checkbox.php:374
#: includes/fields/class-acf-field-radio.php:216
#: includes/fields/class-acf-field-select.php:397
msgid "Label"
msgstr "Etiket"

#: includes/fields/class-acf-field-button-group.php:170
#: includes/fields/class-acf-field-checkbox.php:373
#: includes/fields/class-acf-field-radio.php:215
#: includes/fields/class-acf-field-select.php:396
msgid "Value"
msgstr "Değer"

#: includes/fields/class-acf-field-button-group.php:219
#: includes/fields/class-acf-field-checkbox.php:437
#: includes/fields/class-acf-field-radio.php:289
msgid "Vertical"
msgstr "Dikey"

#: includes/fields/class-acf-field-button-group.php:218
#: includes/fields/class-acf-field-checkbox.php:438
#: includes/fields/class-acf-field-radio.php:290
msgid "Horizontal"
msgstr "Yatay"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "kirmizi : Kırmızı"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Daha fazla kontrol için, hem bir değeri hem de bir etiketi şu şekilde "
"belirtebilirsiniz:"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "Her seçeneği yeni bir satıra girin."

#: includes/fields/class-acf-field-button-group.php:144
#: includes/fields/class-acf-field-checkbox.php:347
#: includes/fields/class-acf-field-radio.php:189
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "Seçimler"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Tuş grubu"

#: includes/fields/class-acf-field-page_link.php:488
#: includes/fields/class-acf-field-post_object.php:404
#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-user.php:83
msgid "Select multiple values?"
msgstr "Birden çok değer seçilsin mi?"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-page_link.php:509
#: includes/fields/class-acf-field-post_object.php:426
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:428
#: includes/fields/class-acf-field-taxonomy.php:786
#: includes/fields/class-acf-field-user.php:104
msgid "Allow Null?"
msgstr "Boş geçilebilir mi?"

#: includes/fields/class-acf-field-page_link.php:249
#: includes/fields/class-acf-field-post_object.php:250
#: includes/fields/class-acf-field-taxonomy.php:932
msgid "Parent"
msgstr "Ana"

#: includes/fields/class-acf-field-wysiwyg.php:394
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "Alan tıklanana kadar TinyMCE hazırlanmayacaktır"

#: includes/fields/class-acf-field-wysiwyg.php:393
msgid "Delay initialization?"
msgstr "Hazırlık geciktirilsin mi?"

#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Show Media Upload Buttons?"
msgstr "Ortam yükleme tuşları gösterilsin mi?"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Toolbar"
msgstr "Araç çubuğu"

#: includes/fields/class-acf-field-wysiwyg.php:358
msgid "Text Only"
msgstr "Sadece Metin"

#: includes/fields/class-acf-field-wysiwyg.php:357
msgid "Visual Only"
msgstr "Sadece görsel"

#: includes/fields/class-acf-field-wysiwyg.php:356
msgid "Visual & Text"
msgstr "Görsel ve metin"

#: includes/fields/class-acf-field-wysiwyg.php:351
msgid "Tabs"
msgstr "Seklemeler"

#: includes/fields/class-acf-field-wysiwyg.php:289
msgid "Click to initialize TinyMCE"
msgstr "TinyMCE hazırlamak için tıklayın"

#: includes/fields/class-acf-field-wysiwyg.php:283
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Metin"

#: includes/fields/class-acf-field-wysiwyg.php:282
msgid "Visual"
msgstr "Görsel"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Wysiwyg düzenleyici"

#: includes/fields/class-acf-field-text.php:180
#: includes/fields/class-acf-field-textarea.php:233
msgid "Value must not exceed %d characters"
msgstr "Değer %d karakteri geçmemelidir"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Limit olmaması için boş bırakın"

#: includes/fields/class-acf-field-text.php:114
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Karakter limiti"

#: includes/fields/class-acf-field-email.php:155
#: includes/fields/class-acf-field-number.php:206
#: includes/fields/class-acf-field-password.php:102
#: includes/fields/class-acf-field-range.php:236
#: includes/fields/class-acf-field-text.php:155
msgid "Appears after the input"
msgstr "Girdi alanından sonra görünür"

#: includes/fields/class-acf-field-email.php:154
#: includes/fields/class-acf-field-number.php:205
#: includes/fields/class-acf-field-password.php:101
#: includes/fields/class-acf-field-range.php:235
#: includes/fields/class-acf-field-text.php:154
msgid "Append"
msgstr "Sonuna ekle"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:196
#: includes/fields/class-acf-field-password.php:92
#: includes/fields/class-acf-field-range.php:226
#: includes/fields/class-acf-field-text.php:145
msgid "Appears before the input"
msgstr "Girdi alanından önce görünür"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:195
#: includes/fields/class-acf-field-password.php:91
#: includes/fields/class-acf-field-range.php:225
#: includes/fields/class-acf-field-text.php:144
msgid "Prepend"
msgstr "Önüne ekle"

#: includes/fields/class-acf-field-email.php:135
#: includes/fields/class-acf-field-number.php:176
#: includes/fields/class-acf-field-password.php:82
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:153
#: includes/fields/class-acf-field-url.php:119
msgid "Appears within the input"
msgstr "Girdi alanının içinde görünür"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:175
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-text.php:134
#: includes/fields/class-acf-field-textarea.php:152
#: includes/fields/class-acf-field-url.php:118
msgid "Placeholder Text"
msgstr "Yer tutucu metin"

#: includes/fields/class-acf-field-button-group.php:155
#: includes/fields/class-acf-field-email.php:115
#: includes/fields/class-acf-field-number.php:126
#: includes/fields/class-acf-field-radio.php:200
#: includes/fields/class-acf-field-range.php:162
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:101
#: includes/fields/class-acf-field-url.php:99
#: includes/fields/class-acf-field-wysiwyg.php:316
msgid "Appears when creating a new post"
msgstr "Yeni bir yazı oluştururken görünür"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Metin"

#: includes/fields/class-acf-field-relationship.php:760
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s en az %2$s seçim gerektirir"
msgstr[1] "%1$s en az %2$s seçim gerektirir"

#: includes/fields/class-acf-field-post_object.php:395
#: includes/fields/class-acf-field-relationship.php:622
msgid "Post ID"
msgstr "Yazı ID"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:394
#: includes/fields/class-acf-field-relationship.php:621
msgid "Post Object"
msgstr "Yazı nesnesi"

#: includes/fields/class-acf-field-relationship.php:654
msgid "Maximum posts"
msgstr "En fazla yazı"

#: includes/fields/class-acf-field-relationship.php:644
msgid "Minimum posts"
msgstr "En az gönderi"

#: includes/admin/views/field-group-options.php:148
#: includes/fields/class-acf-field-relationship.php:679
msgid "Featured Image"
msgstr "Öne çıkan görsel"

#: includes/fields/class-acf-field-relationship.php:675
msgid "Selected elements will be displayed in each result"
msgstr "Her sonuç içinde seçilmiş elemanlar görüntülenir"

#: includes/fields/class-acf-field-relationship.php:674
msgid "Elements"
msgstr "Elemanlar"

#: includes/fields/class-acf-field-relationship.php:608
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:706
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Etiketleme"

#: includes/fields/class-acf-field-relationship.php:607
#: includes/locations/class-acf-location-post-type.php:22
msgid "Post Type"
msgstr "Yazı tipi"

#: includes/fields/class-acf-field-relationship.php:601
msgid "Filters"
msgstr "Filtreler"

#: includes/fields/class-acf-field-page_link.php:470
#: includes/fields/class-acf-field-post_object.php:382
#: includes/fields/class-acf-field-relationship.php:594
msgid "All taxonomies"
msgstr "Tüm taksonomiler"

#: includes/fields/class-acf-field-page_link.php:462
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:586
msgid "Filter by Taxonomy"
msgstr "Taksonomiye göre filtre"

#: includes/fields/class-acf-field-page_link.php:455
#: includes/fields/class-acf-field-post_object.php:367
#: includes/fields/class-acf-field-relationship.php:579
msgid "All post types"
msgstr "Tüm yazı tipleri"

#: includes/fields/class-acf-field-page_link.php:447
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:571
msgid "Filter by Post Type"
msgstr "Yazı tipine göre filtre"

#: includes/fields/class-acf-field-relationship.php:469
msgid "Search..."
msgstr "Ara..."

#: includes/fields/class-acf-field-relationship.php:399
msgid "Select taxonomy"
msgstr "Taksonomi seç"

#: includes/fields/class-acf-field-relationship.php:390
msgid "Select post type"
msgstr "Yazı tipi seç"

#: includes/fields/class-acf-field-relationship.php:65
#: assets/build/js/acf-input.js:3917 assets/build/js/acf-input.js:4195
msgid "No matches found"
msgstr "Eşleşme yok"

#: includes/fields/class-acf-field-relationship.php:64
#: assets/build/js/acf-input.js:3900 assets/build/js/acf-input.js:4174
msgid "Loading"
msgstr "Yükleniyor"

#: includes/fields/class-acf-field-relationship.php:63
#: assets/build/js/acf-input.js:3809 assets/build/js/acf-input.js:4070
msgid "Maximum values reached ( {max} values )"
msgstr "En yüksek değerlere ulaşıldı ({max} değerleri)"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "İlişkili"

#: includes/fields/class-acf-field-file.php:288
#: includes/fields/class-acf-field-image.php:314
msgid "Comma separated list. Leave blank for all types"
msgstr "Virgül ile ayrılmış liste. Tüm tipler için boş bırakın"

#: includes/fields/class-acf-field-file.php:287
#: includes/fields/class-acf-field-image.php:313
msgid "Allowed file types"
msgstr "İzin verilen dosya tipleri"

#: includes/fields/class-acf-field-file.php:275
#: includes/fields/class-acf-field-image.php:277
msgid "Maximum"
msgstr "En fazla"

#: includes/fields/class-acf-field-file.php:151
#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-file.php:279
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-image.php:304
msgid "File size"
msgstr "Dosya boyutu"

#: includes/fields/class-acf-field-image.php:242
#: includes/fields/class-acf-field-image.php:278
msgid "Restrict which images can be uploaded"
msgstr "Hangi görsellerin yüklenebileceğini sınırlandırın"

#: includes/fields/class-acf-field-file.php:263
#: includes/fields/class-acf-field-image.php:241
msgid "Minimum"
msgstr "En az"

#: includes/fields/class-acf-field-file.php:232
#: includes/fields/class-acf-field-image.php:207
msgid "Uploaded to post"
msgstr "Yazıya yüklendi"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:206
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Tümü"

#: includes/fields/class-acf-field-file.php:226
#: includes/fields/class-acf-field-image.php:201
msgid "Limit the media library choice"
msgstr "Ortam kitaplığı seçimini sınırlayın"

#: includes/fields/class-acf-field-file.php:225
#: includes/fields/class-acf-field-image.php:200
msgid "Library"
msgstr "Kitaplık"

#: includes/fields/class-acf-field-image.php:333
msgid "Preview Size"
msgstr "Önizleme boyutu"

#: includes/fields/class-acf-field-image.php:192
msgid "Image ID"
msgstr "Görsel no"

#: includes/fields/class-acf-field-image.php:191
msgid "Image URL"
msgstr "Resim Adresi"

#: includes/fields/class-acf-field-image.php:190
msgid "Image Array"
msgstr "Görsel dizisi"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:368
#: includes/fields/class-acf-field-file.php:210
#: includes/fields/class-acf-field-link.php:168
#: includes/fields/class-acf-field-radio.php:210
msgid "Specify the returned value on front end"
msgstr "Ön yüzden dönecek değeri belirleyin"

#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:367
#: includes/fields/class-acf-field-file.php:209
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-taxonomy.php:750
msgid "Return Value"
msgstr "Dönüş değeri"

#: includes/fields/class-acf-field-image.php:159
msgid "Add Image"
msgstr "Görsel ekle"

#: includes/fields/class-acf-field-image.php:159
msgid "No image selected"
msgstr "Resim seçilmedi"

#: includes/assets.php:351 includes/fields/class-acf-field-file.php:159
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:142 assets/build/js/acf.js:1523
#: assets/build/js/acf.js:1603
msgid "Remove"
msgstr "Kaldır"

#: includes/admin/views/field-group-field.php:65
#: includes/fields/class-acf-field-file.php:157
#: includes/fields/class-acf-field-image.php:137
#: includes/fields/class-acf-field-link.php:142
msgid "Edit"
msgstr "Düzenle"

#: includes/fields/class-acf-field-image.php:67 includes/media.php:55
#: assets/build/js/acf-input.js:6792 assets/build/js/acf-input.js:7267
msgid "All images"
msgstr "Tüm görseller"

#: includes/fields/class-acf-field-image.php:66
#: assets/build/js/acf-input.js:3173 assets/build/js/acf-input.js:3386
msgid "Update Image"
msgstr "Görseli güncelle"

#: includes/fields/class-acf-field-image.php:65
#: assets/build/js/acf-input.js:3172 assets/build/js/acf-input.js:3385
msgid "Edit Image"
msgstr "Resmi düzenle"

#: includes/fields/class-acf-field-image.php:64
#: assets/build/js/acf-input.js:3148 assets/build/js/acf-input.js:3360
msgid "Select Image"
msgstr "Resim Seç"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Görsel"

#: includes/fields/class-acf-field-message.php:123
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Görünür metin olarak HTML kodlamasının görüntülenmesine izin ver"

#: includes/fields/class-acf-field-message.php:122
msgid "Escape HTML"
msgstr "HTML’i güvenli hale getir"

#: includes/fields/class-acf-field-message.php:114
#: includes/fields/class-acf-field-textarea.php:169
msgid "No Formatting"
msgstr "Biçimlendirme yok"

#: includes/fields/class-acf-field-message.php:113
#: includes/fields/class-acf-field-textarea.php:168
msgid "Automatically add &lt;br&gt;"
msgstr "Otomatik ekle &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:112
#: includes/fields/class-acf-field-textarea.php:167
msgid "Automatically add paragraphs"
msgstr "Otomatik paragraf ekle"

#: includes/fields/class-acf-field-message.php:108
#: includes/fields/class-acf-field-textarea.php:163
msgid "Controls how new lines are rendered"
msgstr "Yeni satırların nasıl görüntüleneceğini denetler"

#: includes/fields/class-acf-field-message.php:107
#: includes/fields/class-acf-field-textarea.php:162
msgid "New Lines"
msgstr "Yeni satırlar"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "Hafta başlangıcı"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "Bir değer kaydedilirken kullanılacak biçim"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "Biçimi kaydet"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Hf"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Önceki"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Sonraki"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Bugün"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Bitti"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Tarih seçici"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:281
#: includes/fields/class-acf-field-oembed.php:265
msgid "Width"
msgstr "Genişlik"

#: includes/fields/class-acf-field-oembed.php:262
#: includes/fields/class-acf-field-oembed.php:274
msgid "Embed Size"
msgstr "Gömme boyutu"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "Adres girin"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr "Etkin değilken görüntülenen metin"

#: includes/fields/class-acf-field-true_false.php:180
msgid "Off Text"
msgstr "Kapalı metni"

#: includes/fields/class-acf-field-true_false.php:165
msgid "Text shown when active"
msgstr "Etkinken görüntülenen metin"

#: includes/fields/class-acf-field-true_false.php:164
msgid "On Text"
msgstr "Açık metni"

#: includes/fields/class-acf-field-select.php:449
#: includes/fields/class-acf-field-true_false.php:196
msgid "Stylized UI"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:154
#: includes/fields/class-acf-field-checkbox.php:357
#: includes/fields/class-acf-field-color_picker.php:155
#: includes/fields/class-acf-field-email.php:114
#: includes/fields/class-acf-field-number.php:125
#: includes/fields/class-acf-field-radio.php:199
#: includes/fields/class-acf-field-range.php:161
#: includes/fields/class-acf-field-select.php:379
#: includes/fields/class-acf-field-text.php:94
#: includes/fields/class-acf-field-textarea.php:100
#: includes/fields/class-acf-field-true_false.php:144
#: includes/fields/class-acf-field-url.php:98
#: includes/fields/class-acf-field-wysiwyg.php:315
msgid "Default Value"
msgstr "Varsayılan değer"

#: includes/fields/class-acf-field-true_false.php:135
msgid "Displays text alongside the checkbox"
msgstr "İşaret kutusunun yanında görüntülenen metin"

#: includes/fields/class-acf-field-message.php:26
#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-true_false.php:134
msgid "Message"
msgstr "Mesaj"

#: includes/assets.php:350 includes/fields/class-acf-field-true_false.php:83
#: includes/fields/class-acf-field-true_false.php:184
#: assets/build/js/acf.js:1700 assets/build/js/acf.js:1803
msgid "No"
msgstr "Hayır"

#: includes/assets.php:349 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:168
#: assets/build/js/acf.js:1699 assets/build/js/acf.js:1802
msgid "Yes"
msgstr "Evet"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Doğru / yanlış"

#: includes/fields/class-acf-field-group.php:471
msgid "Row"
msgstr "Satır"

#: includes/fields/class-acf-field-group.php:470
msgid "Table"
msgstr "Tablo"

#: includes/fields/class-acf-field-group.php:469
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:464
msgid "Specify the style used to render the selected fields"
msgstr "Seçili alanları görüntülemek için kullanılacak stili belirtin"

#: includes/fields.php:359 includes/fields/class-acf-field-button-group.php:212
#: includes/fields/class-acf-field-checkbox.php:431
#: includes/fields/class-acf-field-group.php:463
#: includes/fields/class-acf-field-radio.php:283
msgid "Layout"
msgstr "Yerleşim"

#: includes/fields/class-acf-field-group.php:447
msgid "Sub Fields"
msgstr "Alt alanlar"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Grup"

#: includes/fields/class-acf-field-google-map.php:232
msgid "Customize the map height"
msgstr "Harita yüksekliğini özelleştir"

#: includes/fields/class-acf-field-google-map.php:231
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:292
#: includes/fields/class-acf-field-oembed.php:277
msgid "Height"
msgstr "Ağırlık"

#: includes/fields/class-acf-field-google-map.php:220
msgid "Set the initial zoom level"
msgstr "Temel yaklaşma seviyesini belirle"

#: includes/fields/class-acf-field-google-map.php:219
msgid "Zoom"
msgstr "Yakınlaşma"

#: includes/fields/class-acf-field-google-map.php:193
#: includes/fields/class-acf-field-google-map.php:206
msgid "Center the initial map"
msgstr "Haritayı ortala"

#: includes/fields/class-acf-field-google-map.php:192
#: includes/fields/class-acf-field-google-map.php:205
msgid "Center"
msgstr "Merkez"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Search for address..."
msgstr "Adres arayın…"

#: includes/fields/class-acf-field-google-map.php:157
msgid "Find current location"
msgstr "Şu anki konumu bul"

#: includes/fields/class-acf-field-google-map.php:156
msgid "Clear location"
msgstr "Konumu temizle"

#: includes/fields/class-acf-field-google-map.php:155
#: includes/fields/class-acf-field-relationship.php:606
msgid "Search"
msgstr "Ara"

#: includes/fields/class-acf-field-google-map.php:60
#: assets/build/js/acf-input.js:2832 assets/build/js/acf-input.js:3013
msgid "Sorry, this browser does not support geolocation"
msgstr "Üzgünüz, bu tarayıcı konumlandırma desteklemiyor"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google haritası"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:129
msgid "The format returned via template functions"
msgstr "Tema işlevlerinden dönen biçim"

#: includes/fields/class-acf-field-color_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:197
#: includes/fields/class-acf-field-image.php:184
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:616
#: includes/fields/class-acf-field-select.php:390
#: includes/fields/class-acf-field-time_picker.php:128
#: includes/fields/class-acf-field-user.php:67
msgid "Return Format"
msgstr "Dönüş biçimi"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-time_picker.php:120
#: includes/fields/class-acf-field-time_picker.php:136
msgid "Custom:"
msgstr "Özel:"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-time_picker.php:113
msgid "The format displayed when editing a post"
msgstr "Bir yazı düzenlenirken görüntülenecek biçim"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:179
#: includes/fields/class-acf-field-time_picker.php:112
msgid "Display Format"
msgstr "Gösterim biçimi"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Zaman seçici"

#: acf.php:413
msgid "No Fields found in Trash"
msgstr "Çöpte alan bulunamadı"

#: acf.php:412
msgid "No Fields found"
msgstr "Hiç alan bulunamadı"

#: acf.php:411
msgid "Search Fields"
msgstr "Alanlarda ara"

#: acf.php:410
msgid "View Field"
msgstr "Alanı görüntüle"

#: acf.php:409 includes/admin/views/field-group-fields.php:104
msgid "New Field"
msgstr "Yeni alan"

#: acf.php:408
msgid "Edit Field"
msgstr "Alanı düzenle"

#: acf.php:407
msgid "Add New Field"
msgstr "Yeni elan ekle"

#: acf.php:405
msgid "Field"
msgstr "Alan"

#: acf.php:404 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:287
#: includes/admin/views/field-group-fields.php:21
msgid "Fields"
msgstr "Alanlar"

#: acf.php:379
msgid "No Field Groups found in Trash"
msgstr "Çöpte alan grubu bulunamadı"

#: acf.php:378
msgid "No Field Groups found"
msgstr "Hiç alan grubu bulunamadı"

#: acf.php:377
msgid "Search Field Groups"
msgstr "Alan gruplarında ara"

#: acf.php:376
msgid "View Field Group"
msgstr "Alan grubunu görüntüle"

#: acf.php:375
msgid "New Field Group"
msgstr "Yeni alan grubu"

#: acf.php:374
msgid "Edit Field Group"
msgstr "Alan grubunu düzenle"

#: acf.php:373
msgid "Add New Field Group"
msgstr "Yeni alan grubu ekle"

#: acf.php:372 acf.php:406 includes/admin/admin.php:51
msgid "Add New"
msgstr "Yeni ekle"

#: acf.php:371
msgid "Field Group"
msgstr "Alan grubu"

#: acf.php:370 includes/admin/admin.php:50
msgid "Field Groups"
msgstr "Alan grupları"

#. Description of the plugin
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Güçlü, profesyonel ve sezgisel alanlar ile WordPress'i özelleştirin."

#. Plugin URI of the plugin
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php:91
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:166
msgid "Block type name is required."
msgstr "Blok türü adı gereklidir."

#: pro/blocks.php:173
msgid "Block type \"%s\" is already registered."
msgstr "Blok türü \"%s\" zaten kayıtlı."

#: pro/blocks.php:731
msgid "Switch to Edit"
msgstr "Düzenlemeye geç"

#: pro/blocks.php:732
msgid "Switch to Preview"
msgstr "Önizlemeye geç"

#: pro/blocks.php:733
msgid "Change content alignment"
msgstr "İçerik hizalamasını değiştir"

#. translators: %s: Block type title
#: pro/blocks.php:736
msgid "%s settings"
msgstr "%s ayarları"

#: pro/blocks.php:949
msgid "This block contains no editable fields."
msgstr ""

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:955
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Seçenekler güncellendi"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a href=\"%1$s"
"\">Updates</a> page. If you don't have a licence key, please see <a href="
"\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Güncellemeleri etkinleştirmek için lütfen <a href=\"%1$s\">Güncellemeler</a> "
"sayfasında lisans anahtarınızı girin. Eğer bir lisans anahtarınız yoksa "
"lütfen <a href=\"%2$s\">detaylar ve fiyatlama</a> sayfasına bakın."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>ACF etkinleştirme hatası</b>. Tanımlı lisans anahtarınız değişti, ancak "
"eski lisansınızı devre dışı bırakırken bir hata oluştu"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>ACF etkinleştirme hatası</b>. Tanımlı lisans anahtarınız değişti, ancak "
"etkinleştirme sunucusuna bağlanırken bir hata oluştu"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr "<b>ACF etkinleştirme hatası</b>"

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""
"<b>ACF etkinleştirme hatası</b>. Etkinleştirme sunucusuna bağlanırken bir "
"hata oluştu"

#: pro/updates.php:279, pro/admin/views/html-settings-updates.php:117
msgid "Check Again"
msgstr "Tekrar kontrol et"

#: pro/updates.php:561
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""
"<b>ACF etkinleştirme hatası</b>. Etkinleştirme sunucusu ile bağlantı "
"kurulamadı"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Yayımla"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Bu seçenekler sayfası için hiç özel alan grubu bulunamadı. <a href=\"%s"
"\">Bir özel alan grubu oluştur</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b> Hata</b>. Güncelleme sunucusu ile bağlantı kurulamadı"

#: pro/admin/admin-updates.php:209
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Hata</b>. Güncelleme paketi için kimlik doğrulaması yapılamadı. Lütfen "
"ACF PRO lisansınızı kontrol edin ya da lisansınızı etkisizleştirip, tekrar "
"etkinleştirin."

#: pro/admin/admin-updates.php:196
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Hata</b>. Bu sitenin lisansının süresi dolmuş veya devre dışı bırakılmış. "
"Lütfen ACF PRO lisansınızı yeniden etkinleştirin."

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Kopyala"

#: pro/fields/class-acf-field-clone.php:815
msgid "Select one or more fields you wish to clone"
msgstr "Çoğaltmak için bir ya da daha fazla alan seçin"

#: pro/fields/class-acf-field-clone.php:834
msgid "Display"
msgstr "Görüntüle"

#: pro/fields/class-acf-field-clone.php:835
msgid "Specify the style used to render the clone field"
msgstr "Çoğaltılacak alanın görünümü için stili belirleyin"

#: pro/fields/class-acf-field-clone.php:840
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grup (bu alanın içinde seçili alanları grup olarak gösterir)"

#: pro/fields/class-acf-field-clone.php:841
msgid "Seamless (replaces this field with selected fields)"
msgstr "Pürüzsüz (bu alanı seçişmiş olan alanlarla değiştirir)"

#: pro/fields/class-acf-field-clone.php:864
msgid "Labels will be displayed as %s"
msgstr "Etiketler %s olarak görüntülenir"

#: pro/fields/class-acf-field-clone.php:869
msgid "Prefix Field Labels"
msgstr "Alan etiketlerine ön ek ekle"

#: pro/fields/class-acf-field-clone.php:880
msgid "Values will be saved as %s"
msgstr "Değerler %s olarak kaydedilecek"

#: pro/fields/class-acf-field-clone.php:885
msgid "Prefix Field Names"
msgstr "Alan isimlerine ön ek ekle"

#: pro/fields/class-acf-field-clone.php:1001
msgid "Unknown field"
msgstr "Bilinmeyen alan"

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field group"
msgstr "Bilinmeyen alan grubu"

#: pro/fields/class-acf-field-clone.php:1042
msgid "All fields from %s field group"
msgstr "%s alan grubundaki tüm alanlar"

#: pro/fields/class-acf-field-flexible-content.php:31,
#: pro/fields/class-acf-field-repeater.php:79,
#: pro/fields/class-acf-field-repeater.php:263
msgid "Add Row"
msgstr "Satır ekle"

#: pro/fields/class-acf-field-flexible-content.php:71,
#: pro/fields/class-acf-field-flexible-content.php:917,
#: pro/fields/class-acf-field-flexible-content.php:996
msgid "layout"
msgid_plural "layouts"
msgstr[0] "yerleşim"
msgstr[1] "yerleşimler"

#: pro/fields/class-acf-field-flexible-content.php:72
msgid "layouts"
msgstr "yerleşimler"

#: pro/fields/class-acf-field-flexible-content.php:75,
#: pro/fields/class-acf-field-flexible-content.php:916,
#: pro/fields/class-acf-field-flexible-content.php:995
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Bu alan için en az gereken {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:76
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Bu alan için sınır {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} kullanılabilir (en fazla {max})"

#: pro/fields/class-acf-field-flexible-content.php:80
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} gerekli (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:83
msgid "Flexible Content requires at least 1 layout"
msgstr "Esnek içerik, en az 1 yerleşim gerektirir"

#: pro/fields/class-acf-field-flexible-content.php:276
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""
"Kendi yerleşiminizi oluşturmaya başlamak için aşağıdaki \"%s \" tuşuna "
"tıklayın"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Yerleşim ekle"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Duplicate layout"
msgstr "Düzeni çoğalt"

#: pro/fields/class-acf-field-flexible-content.php:415
msgid "Remove layout"
msgstr "Yerleşimi çıkar"

#: pro/fields/class-acf-field-flexible-content.php:416,
#: pro/fields/class-acf-repeater-table.php:369
msgid "Click to toggle"
msgstr "Geçiş yapmak için tıklayın"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder Layout"
msgstr "Yerleşimi yeniden sırala"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder"
msgstr "Yeniden sırala"

#: pro/fields/class-acf-field-flexible-content.php:552
msgid "Delete Layout"
msgstr "Yerleşimi sil"

#: pro/fields/class-acf-field-flexible-content.php:553
msgid "Duplicate Layout"
msgstr "Yerleşimi çoğalt"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New Layout"
msgstr "Yeni yerleşim ekle"

#: pro/fields/class-acf-field-flexible-content.php:635
msgid "Min"
msgstr "En düşük"

#: pro/fields/class-acf-field-flexible-content.php:650
msgid "Max"
msgstr "En yüksek"

#: pro/fields/class-acf-field-flexible-content.php:679,
#: pro/fields/class-acf-field-repeater.php:259
msgid "Button Label"
msgstr "Tuş etiketi"

#: pro/fields/class-acf-field-flexible-content.php:690
msgid "Minimum Layouts"
msgstr "En az yerleşim"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "En fazla yerleşim"

#: pro/fields/class-acf-field-flexible-content.php:1704,
#: pro/fields/class-acf-field-repeater.php:861
msgid "%s must be of type array or null."
msgstr "%s dizi veya null türünde olmalıdır."

#: pro/fields/class-acf-field-flexible-content.php:1715
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "%1$s en az %2$s %3$s düzen içermelidir."
msgstr[1] "%1$s en az %2$s %3$s düzen içermelidir."

#: pro/fields/class-acf-field-flexible-content.php:1731
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "%1$s en fazla %2$s %3$s düzeni içermelidir."
msgstr[1] "%1$s en fazla %2$s %3$s düzeni içermelidir."

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "Galeriye görsel ekle"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "En fazla seçim aşıldı"

#: pro/fields/class-acf-field-gallery.php:320
msgid "Length"
msgstr "Uzunluk"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Başlık"

#: pro/fields/class-acf-field-gallery.php:376
msgid "Alt Text"
msgstr "Alternatif metin"

#: pro/fields/class-acf-field-gallery.php:500
msgid "Add to gallery"
msgstr "Galeriye ekle"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Bulk actions"
msgstr "Toplu eylemler"

#: pro/fields/class-acf-field-gallery.php:505
msgid "Sort by date uploaded"
msgstr "Yüklenme tarihine göre sırala"

#: pro/fields/class-acf-field-gallery.php:506
msgid "Sort by date modified"
msgstr "Değiştirme tarihine göre sırala"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Sort by title"
msgstr "Başlığa göre sırala"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Reverse current order"
msgstr "Sıralamayı ters çevir"

#: pro/fields/class-acf-field-gallery.php:520
msgid "Close"
msgstr "Kapat"

#: pro/fields/class-acf-field-gallery.php:602
msgid "Insert"
msgstr "Ekle"

#: pro/fields/class-acf-field-gallery.php:603
msgid "Specify where new attachments are added"
msgstr "Yeni eklerin nereye ekleneceğini belirtin"

#: pro/fields/class-acf-field-gallery.php:607
msgid "Append to the end"
msgstr "Sona ekle"

#: pro/fields/class-acf-field-gallery.php:608
msgid "Prepend to the beginning"
msgstr "En başa ekleyin"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Minimum Selection"
msgstr "En az seçim"

#: pro/fields/class-acf-field-gallery.php:644
msgid "Maximum Selection"
msgstr "En fazla seçim"

#: pro/fields/class-acf-field-repeater.php:53,
#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum rows reached ({min} rows)"
msgstr "En az satır sayısına ulaşıldı ({min} satır)"

#: pro/fields/class-acf-field-repeater.php:54
msgid "Maximum rows reached ({max} rows)"
msgstr "En fazla satır değerine ulaşıldı ({max} satır)"

#: pro/fields/class-acf-field-repeater.php:55
msgid "Error loading page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:174
msgid "Collapsed"
msgstr "Daraltılmış"

#: pro/fields/class-acf-field-repeater.php:175
msgid "Select a sub field to show when row is collapsed"
msgstr "Satır toparlandığında görüntülenecek alt alanı seçin"

#: pro/fields/class-acf-field-repeater.php:187
msgid "Minimum Rows"
msgstr "En az satır"

#: pro/fields/class-acf-field-repeater.php:199
msgid "Maximum Rows"
msgstr "En fazla satır"

#: pro/fields/class-acf-field-repeater.php:228
msgid "Pagination"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:229
msgid "Useful for fields with a large number of rows."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:240
msgid "Rows Per Page"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:241
msgid "Set the number of rows to be displayed on a page."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:959
msgid "Invalid field key."
msgstr ""

#: pro/fields/class-acf-field-repeater.php:968
msgid "There was an error retrieving the field."
msgstr ""

#: pro/fields/class-acf-repeater-table.php:389
msgid "Add row"
msgstr "Satır ekle"

#: pro/fields/class-acf-repeater-table.php:390
msgid "Duplicate row"
msgstr "Satırı çoğalt"

#: pro/fields/class-acf-repeater-table.php:391
msgid "Remove row"
msgstr "Satır çıkar"

#: pro/fields/class-acf-repeater-table.php:435,
#: pro/fields/class-acf-repeater-table.php:452
msgid "Current Page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:444
msgid "First page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:448
msgid "Previous page"
msgstr ""

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:457
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:465
msgid "Next page"
msgstr ""

#: pro/fields/class-acf-repeater-table.php:469
msgid "Last page"
msgstr ""

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "Hiç blok tipi yok"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Seçenekler sayfayı mevcut değil"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Lisansı devre dışı bırak"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Lisansı etkinleştir"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Lisans bilgisi"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Güncellemeleri açmak için lisans anahtarınızı aşağıya girin. Eğer bir lisans "
"anahtarınız yoksa lütfen <a href=\"%s\" target=“_blank”>detaylar ve "
"fiyatlama</a> sayfasına bakın."

#: pro/admin/views/html-settings-updates.php:41
msgid "License Key"
msgstr "Lisans anahtarı"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "Lisans anahtarınız wp-config.php içinde tanımlanmış."

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr "Etkinleştirmeyi yeniden dene"

#: pro/admin/views/html-settings-updates.php:76
msgid "Update Information"
msgstr "Güncelleme bilgisi"

#: pro/admin/views/html-settings-updates.php:83
msgid "Current Version"
msgstr "Mevcut sürüm"

#: pro/admin/views/html-settings-updates.php:91
msgid "Latest Version"
msgstr "En son sürüm"

#: pro/admin/views/html-settings-updates.php:99
msgid "Update Available"
msgstr "Güncelleme mevcut"

#: pro/admin/views/html-settings-updates.php:111
msgid "Please enter your license key above to unlock updates"
msgstr ""
"Güncelleştirmelerin kilidini açmak için yukardaki alana lisans anahtarını "
"girin"

#: pro/admin/views/html-settings-updates.php:109
msgid "Update Plugin"
msgstr "Eklentiyi güncelle"

#: pro/admin/views/html-settings-updates.php:107
msgid "Please reactivate your license to unlock updates"
msgstr ""
"Güncellemelerin kilidini açmak için lütfen lisansınızı yeniden etkinleştirin"

#: pro/admin/views/html-settings-updates.php:124
msgid "Changelog"
msgstr "Değişiklik kayıtları"

#: pro/admin/views/html-settings-updates.php:134
msgid "Upgrade Notice"
msgstr "Yükseltme bildirimi"

#, php-format
#~ msgid "Field group deactivated."
#~ msgid_plural "%s field groups deactivated."
#~ msgstr[0] "Alan grubu silindi."
#~ msgstr[1] "%s alan grubu silindi."

#, php-format
#~ msgid "Field group activated."
#~ msgid_plural "%s field groups activated."
#~ msgstr[0] "Alan grubu kaydedildi."
#~ msgstr[1] "%s alan grubu kaydedildi."

#~ msgid "Deactivate"
#~ msgstr "Devre dışı bırak"

#~ msgid "Deactivate this item"
#~ msgstr "Bu öğeyi devre dışı bırak"

#~ msgid "Activate"
#~ msgstr "Etkinleştir"

#~ msgid "Activate this item"
#~ msgstr "Bu öğeyi etkinleştir"

#~ msgctxt "post status"
#~ msgid "Inactive"
#~ msgstr "Etkin değil"

#~ msgid "Clone Field"
#~ msgstr "Kopya alanı"

#, php-format
#~ msgid "Inactive <span class=\"count\">(%s)</span>"
#~ msgid_plural "Inactive <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Etkin olmayan <span class=“count”>(%s)</span>"
#~ msgstr[1] "Etkin olmayan <span class=“count”>(%s)</span>"

#~ msgid ""
#~ "Advanced Custom Fields and Advanced Custom Fields PRO should not be "
#~ "active at the same time. We've automatically deactivated Advanced Custom "
#~ "Fields."
#~ msgstr ""
#~ "Advanced Custom Fields ve Advanced Custom Fields PRO aynı anda etkin "
#~ "olmamalıdır. Advanced Custom Fields eklentisini otomatik olarak devre "
#~ "dışı bıraktık."

#~ msgid ""
#~ "Advanced Custom Fields and Advanced Custom Fields PRO should not be "
#~ "active at the same time. We've automatically deactivated Advanced Custom "
#~ "Fields PRO."
#~ msgstr ""
#~ "Advanced Custom Fields ve Advanced Custom Fields PRO aynı anda etkin "
#~ "olmamalıdır. Advanced Custom Fields PRO eklentisini otomatik olarak devre "
#~ "dışı bıraktık."

#~ msgid "Move to trash. Are you sure?"
#~ msgstr "Çöpe taşımak istediğinizden emin misiniz?"

#~ msgid "Close Window"
#~ msgstr "Pencereyi kapat"

#~ msgid ""
#~ "Select the field groups you would like to export and then select your "
#~ "export method. Use the download button to export to a .json file which "
#~ "you can then import to another ACF installation. Use the generate button "
#~ "to export to PHP code which you can place in your theme."
#~ msgstr ""
#~ "Dışa aktarma ve sonra dışa aktarma yöntemini seçtikten sonra alan "
#~ "gruplarını seçin. Sonra başka bir ACF yükleme içe bir .json dosyaya "
#~ "vermek için indirme düğmesini kullanın. Tema yerleştirebilirsiniz PHP "
#~ "kodu aktarma düğmesini kullanın."

#~ msgid "Export File"
#~ msgstr "Dışarı aktarım dosyası"

#~ msgid ""
#~ "Select the Advanced Custom Fields JSON file you would like to import. "
#~ "When you click the import button below, ACF will import the field groups."
#~ msgstr ""
#~ "İçeri aktarmak istediğiniz Advanced Custom Fields JSON dosyasını seçin. "
#~ "Aşağıdaki içeri aktar tuşuna bastığınızda ACF alan gruplarını içeri "
#~ "aktaracak."

#~ msgid "Import File"
#~ msgstr "Dosyayı içeri aktar"

#~ msgid "Required?"
#~ msgstr "Gerekli mi?"

#~ msgid ""
#~ "No fields. Click the <strong>+ Add Field</strong> button to create your "
#~ "first field."
#~ msgstr ""
#~ "Hiç alan yok. İlk alanınızı oluşturmak için <strong>+ Alan ekle</strong> "
#~ "düğmesine tıklayın."

#~ msgid "+ Add Field"
#~ msgstr "+ Alan ekle"

#~ msgid "Upgrade to Pro"
#~ msgstr "Pro sürüme yükselt"

#~ msgid "Allow Custom"
#~ msgstr "Özel değere izin ver"

#~ msgid "Save Custom"
#~ msgstr "Özel alanı kaydet"

#~ msgid "Toggle"
#~ msgstr "Aç - kapat"

#~ msgid "Other"
#~ msgstr "Diğer"

#~ msgid "Save Other"
#~ msgstr "Diğerini kaydet"

#~ msgid "Stylised UI"
#~ msgstr "Stilize edilmiş kullanıcı arabirimi"

#~ msgid ""
#~ "Define an endpoint for the previous tabs to stop. This will start a new "
#~ "group of tabs."
#~ msgstr ""
#~ "Önceki sekmelerin durması için bir uç nokta tanımlayın. Bu yeni sekmeler "
#~ "için bir grup başlatacaktır."

#, php-format
#~ msgctxt "No terms"
#~ msgid "No %s"
#~ msgstr "%s yok"

#~ msgid "Delicious Brains"
#~ msgstr "Delicious Brains"

#~ msgctxt "post status"
#~ msgid "Disabled"
#~ msgstr "Devre dışı"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Devre dışı <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Devre dışı <span class=\"count\">(%s)</span>"

#~ msgid "Hero"
#~ msgstr "Manşet"

#~ msgid "Display a random hero image."
#~ msgstr "Rastgele bir manşet görseli göster."

#~ msgid "Test JS"
#~ msgstr "JS test et"

#~ msgid "A block for testing JS."
#~ msgstr "JS test etmek için bir blok."

#~ msgid "Current Color"
#~ msgstr "Şu anki renk"

#~ msgid "Elliot Condon"
#~ msgstr "Elliot Condon"

#~ msgid "Status"
#~ msgstr "Durum"

#~ msgid "See what's new in <a href=\"%s\">version %s</a>."
#~ msgstr "<a href=\"%s\">%s sürümünde</a> neler yeni bir göz atın."

#~ msgid "Resources"
#~ msgstr "Kaynaklar"

#~ msgid "Documentation"
#~ msgstr "Belgeler"

#~ msgid "Pro"
#~ msgstr "Pro"

#~ msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
#~ msgstr "<a href=\"%s\">ACF</a> ile oluşturduğunuz için teşekkürler."

#~ msgid "Synchronise field group"
#~ msgstr "Alan grubunu eşitle"

#~ msgid "Apply"
#~ msgstr "Uygula"

#~ msgid "Bulk Actions"
#~ msgstr "Toplu eylemler"

#~ msgid "Info"
#~ msgstr "Bilgi"

#~ msgid "What's New"
#~ msgstr "Neler yeni"

#~ msgid "Welcome to Advanced Custom Fields"
#~ msgstr "Advanced Custom Fields eklentisine hoş geldiniz"

#~ msgid ""
#~ "Thank you for updating! ACF %s is bigger and better than ever before. We "
#~ "hope you like it."
#~ msgstr ""
#~ "Güncelleme için teşekkür ederiz! ACF %s zamankinden daha büyük ve daha "
#~ "iyi. Umarız beğenirsiniz."

#~ msgid "A Smoother Experience"
#~ msgstr "Daha pürüzsüz bir deneyim"

#~ msgid "Improved Usability"
#~ msgstr "Geliştirilmiş kullanılabilirlik"

#~ msgid ""
#~ "Including the popular Select2 library has improved both usability and "
#~ "speed across a number of field types including post object, page link, "
#~ "taxonomy and select."
#~ msgstr ""
#~ "Popüler Select2 kütüphanesini ekleyerek yazı nesnesi, sayfa bağlantısı, "
#~ "taksonomi ve seçim kutusu gibi bir çok alan tipinde hem kullanışlılık hem "
#~ "de hız iyileştirmeleri gerçekleşti."

#~ msgid "Improved Design"
#~ msgstr "Geliştirilmiş tasarım"

#~ msgid ""
#~ "Many fields have undergone a visual refresh to make ACF look better than "
#~ "ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
#~ "(new) fields!"
#~ msgstr ""
#~ "ACF daha iyi görünsün diye bir çok alan görsel yenilemeden geçirildi! "
#~ "Gözle görülür değişiklikler galeri, ilişki ve oEmbed (yeni) alanlarında!"

#~ msgid "Improved Data"
#~ msgstr "Geliştirilmiş veri"

#~ msgid ""
#~ "Redesigning the data architecture has allowed sub fields to live "
#~ "independently from their parents. This allows you to drag and drop fields "
#~ "in and out of parent fields!"
#~ msgstr ""
#~ "Veri mimarisinin yeniden düzenlenmesi sayesinde alt alanlar üst alanlara "
#~ "bağlı olmadan var olabiliyorlar. Bu da üst alanların dışına sürükle bırak "
#~ "yapılabilmesine olanak sağlıyor!"

#~ msgid "Goodbye Add-ons. Hello PRO"
#~ msgstr "Elveda eklentiler. Merhaba PRO"

#~ msgid "Introducing ACF PRO"
#~ msgstr "Karşınızda ACF PRO"

#~ msgid ""
#~ "We're changing the way premium functionality is delivered in an exciting "
#~ "way!"
#~ msgstr ""
#~ "Premium işlevlerin size ulaştırılmasını daha heyecanlı bir hale "
#~ "getiriyoruz!"

#~ msgid ""
#~ "All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
#~ "version of ACF</a>. With both personal and developer licenses available, "
#~ "premium functionality is more affordable and accessible than ever before!"
#~ msgstr ""
#~ "Yeni <a href=\"%s\">ACF Pro sürümününe</a> 4 premium eklenti dahil "
#~ "edildi. Hem kişisel hem geliştirici lisansında, özel beceriler hiç "
#~ "olmadığı kadar edinilebilir ve erişilebilir!"

#~ msgid "Powerful Features"
#~ msgstr "Güçlü özellikler"

#~ msgid ""
#~ "ACF PRO contains powerful features such as repeatable data, flexible "
#~ "content layouts, a beautiful gallery field and the ability to create "
#~ "extra admin options pages!"
#~ msgstr ""
#~ "ACF PRO, tekrarlanabilir veri, esnek içerik yerleşimleri, harika bir "
#~ "galeri alanı ve ekstra yönetim seçenekleri sayfaları oluşturma gibi güçlü "
#~ "özellikler içerir!"

#~ msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
#~ msgstr ""
#~ "<a href=\"%s\">ACF PRO özellikleri</a> hakkında daha fazlasını okuyun."

#~ msgid "Easy Upgrading"
#~ msgstr "Kolay yükseltme"

#~ msgid ""
#~ "Upgrading to ACF PRO is easy. Simply purchase a license online and "
#~ "download the plugin!"
#~ msgstr ""
#~ "ACF PRO’ya yükseltmek çok kolay. Çevrimiçi bir lisans satın alın ve "
#~ "eklentiyi indirin!"

#~ msgid ""
#~ "We also wrote an <a href=\"%s\">upgrade guide</a> to answer any "
#~ "questions, but if you do have one, please contact our support team via "
#~ "the <a href=\"%s\">help desk</a>."
#~ msgstr ""
#~ "Her türlü soruya cevap verebilecek <a href=\"%s\">bir yükseltme rehberi</"
#~ "a> hazırladık, fakat yine de bir sorunuz varsa lütfen <a href=\"%s"
#~ "\">yardım masası</a>nı kullanarak destek ekibimize danışın."

#~ msgid "New Features"
#~ msgstr "Yeni özellikler"

#~ msgid "Link Field"
#~ msgstr "Bağlantı alanı"

#~ msgid ""
#~ "The Link field provides a simple way to select or define a link (url, "
#~ "title, target)."
#~ msgstr ""
#~ "Bağlantı alanı bir bağlantı (adres, başlık, hedef) seçmek ya da "
#~ "tanımlamak için basit bir yol sunar."

#~ msgid "Group Field"
#~ msgstr "Grup alanı"

#~ msgid "The Group field provides a simple way to create a group of fields."
#~ msgstr "Grup alanı birden çok alanı basitçe gruplamanıza olanak sağlar."

#~ msgid "oEmbed Field"
#~ msgstr "oEmbed alanı"

#~ msgid ""
#~ "The oEmbed field allows an easy way to embed videos, images, tweets, "
#~ "audio, and other content."
#~ msgstr ""
#~ "oEmbed alanı videolar, görseller, tweetler, ses ve diğer içeriği kolayca "
#~ "gömebilmenizi sağlar."

#~ msgid "The clone field allows you to select and display existing fields."
#~ msgstr ""
#~ "Kopya alanı var olan alanları seçme ve görüntülemenize olanak sağlar."

#~ msgid "More AJAX"
#~ msgstr "Daha fazla AJAX"

#~ msgid "More fields use AJAX powered search to speed up page loading."
#~ msgstr ""
#~ "Sayfa yüklenmesini hızlandırmak adına daha çok alan AJAX ile "
#~ "güçlendirilmiş arama kullanıyor."

#~ msgid ""
#~ "New auto export to JSON feature improves speed and allows for "
#~ "syncronisation."
#~ msgstr ""
#~ "Yeni otomatik JSON dışarı aktarma özelliği ile hız artıyor ve "
#~ "senkronizasyona imkan sağlanıyor."

#~ msgid "Easy Import / Export"
#~ msgstr "Kolayca içe / dışa aktarma"

#~ msgid "Both import and export can easily be done through a new tools page."
#~ msgstr ""
#~ "İçeri ve dışarı aktarma işlemleri yeni araçlar sayfasından kolayca "
#~ "yapılabilir."

#~ msgid "New Form Locations"
#~ msgstr "Yeni form konumları"

#~ msgid ""
#~ "Fields can now be mapped to menus, menu items, comments, widgets and all "
#~ "user forms!"
#~ msgstr ""
#~ "Alanlar artık menülere, menü elemanlarına, yorumlara, bileşenlere ve tüm "
#~ "kullanıcı formlarına eşlenebiliyor!"

#~ msgid "More Customization"
#~ msgstr "Daha fazla özelleştirme"

#~ msgid ""
#~ "New PHP (and JS) actions and filters have been added to allow for more "
#~ "customization."
#~ msgstr ""
#~ "Daha fazla özelleştirmeye izin veren yeni PHP (ve JS) eylem ve filtreleri "
#~ "eklendi."

#~ msgid "Fresh UI"
#~ msgstr "Taze arayüz"

#~ msgid ""
#~ "The entire plugin has had a design refresh including new field types, "
#~ "settings and design!"
#~ msgstr ""
#~ "Eklentinin tasarımı yeni alan tipleri, ayarlar ve tasarımı da içerecek "
#~ "şekilde yenilendi!"

#~ msgid "New Settings"
#~ msgstr "Yeni ayarlar"

#~ msgid ""
#~ "Field group settings have been added for Active, Label Placement, "
#~ "Instructions Placement and Description."
#~ msgstr ""
#~ "Etkin, etiket yerleşimi, talimatlar yerleşimi ve açıklama için alan grubu "
#~ "ayarları eklendi."

#~ msgid "Better Front End Forms"
#~ msgstr "Daha iyi ön yüz formları"

#~ msgid ""
#~ "acf_form() can now create a new post on submission with lots of new "
#~ "settings."
#~ msgstr ""
#~ "acf_form() artık gönderim halinde bir sürü yeni ayar ile yeni bir yazı "
#~ "oluşturabilir."

#~ msgid "Form validation is now done via PHP + AJAX in favour of only JS."
#~ msgstr "Form doğrulama artık sadece JS yerine PHP + AJAX ile yapılıyor."

#~ msgid "Moving Fields"
#~ msgstr "Taşınabilir alanlar"

#~ msgid ""
#~ "New field group functionality allows you to move a field between groups & "
#~ "parents."
#~ msgstr ""
#~ "Yeni gruplama becerisi, bir alanı gruplar ve üst alanlar arasında "
#~ "taşıyabilmenize olanak sağlar."

#~ msgid "We think you'll love the changes in %s."
#~ msgstr "%s sürümündeki değişiklikleri seveceğinizi düşünüyoruz."

#~ msgid "Normal"
#~ msgstr "Normal"

#~ msgid "Fancy"
#~ msgstr "Süslü"

#~ msgid "Add-ons"
#~ msgstr "Eklentiler"

#~ msgid "Download & Install"
#~ msgstr "İndir ve yükle"

#~ msgid "Installed"
#~ msgstr "Yüklendi"

#~ msgid "Shown when entering data"
#~ msgstr "Veri girilirken gösterilir"

#~ msgid "http://www.elliotcondon.com/"
#~ msgstr "http://www.elliotcondon.com/"

#~ msgid "%s field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "%s alan grubu eşitlendi."
#~ msgstr[1] "%s alan grubu eşitlendi."

#~ msgid "<b>Error</b>. Could not load add-ons list"
#~ msgstr "<b>Hata</b>. Eklenti listesi yüklenemedi"

#~ msgid "Parent fields"
#~ msgstr "Üst alanlar"

#~ msgid "Sibling fields"
#~ msgstr "Kardeş alanlar"

#~ msgid "Error validating request"
#~ msgstr "İstek doğrulanırken hata oluştu"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Advanced Custom Fields veritabanı güncellemesi"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Yeni muhteşem özellikleri kullanmadan önce lütfen veritabanınızı en yeni "
#~ "sürüme güncelleyin."

#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Yükseltmeyi kolaylaştırmak için <a href=\"%s\">mağaza hesabınıza</a> "
#~ "giriş yapın ve bir adet ücretsiz ACF PRO kopyası edinin!"

#~ msgid "Under the Hood"
#~ msgstr "Kaputun altında"

#~ msgid "Smarter field settings"
#~ msgstr "Daha akıllı alan ayarları"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr "ACF artık alan ayarlarını münferit yazı nesneleri olarak saklıyor"

#~ msgid "Better version control"
#~ msgstr "Daha iyi sürüm kontrolü"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "Otomatik JSON dışarı aktarma özelliği sayesinde artık alan ayarları sürüm "
#~ "kontrolü ile yönetilebilir"

#~ msgid "Swapped XML for JSON"
#~ msgstr "XML yerine JSON kullanımına geçildi"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "İçeri / dışarı aktarma artık XML yerine JSON kullanıyor"

#~ msgid "New Forms"
#~ msgstr "Yeni formlar"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "Gömülü içerik için yeni bir alan eklendi"

#~ msgid "New Gallery"
#~ msgstr "Yeni galeri"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "Galeri alanı oldukça gerekli bir makyaj ile yenilendi"

#~ msgid "Relationship Field"
#~ msgstr "İlişkili alan"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr "'Filtreler' için yeni ilişki ayarı (Arama, yazı tipi, taksonomi)"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "Yeni arşivler page_link alanı seçiminde gruplanır"

#~ msgid "Better Options Pages"
#~ msgstr "Daha iyi seçenekler sayfası"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Seçenekler sayfası için yeni işlevler sayesinde hem üst hem alt menü "
#~ "sayfaları oluşturulabiliyor"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Alan gruplarını PHP için dışa aktar"

#~ msgid "Download export file"
#~ msgstr "Dışarı aktarma dosyasını indir"

#~ msgid "Generate export code"
#~ msgstr "Dışarı aktarma kodu oluştur"

#~ msgid "Locating"
#~ msgstr "Konum bulunuyor"

#~ msgid "Error."
#~ msgstr "Hata."

#~ msgid "No embed found for the given URL."
#~ msgstr "Verilen adres için gömülecek bir şey bulunamadı."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "En düşün değerlere ulaşıldı ( {min} değerleri )"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Bir tablo stili tekrarlayıcı ya da esnek içerik alanı yerleşimi "
#~ "eklendiğinde sekme alanı yanlış görüntülenir"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "“Sekme alanları”nı kullanarak düzenleme ekranında alanları gruplayıp daha "
#~ "kolay organize olun."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Bu “sekme alanı”nı takip eden (ya da başka bir “sekme alanı” tanımlıysa) "
#~ "tüm alanlar sekmenin başlığını etiket olarak kullanarak "
#~ "gruplandırılacaklar."

#~ msgid "None"
#~ msgstr "Yok"

#~ msgid "Taxonomy Term"
#~ msgstr "Taksonomi terimi"

#~ msgid "remove {layout}?"
#~ msgstr "{layout} kaldırılsın mı?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "Bu alan için en az gereken {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "En yüksek {label} sınırına ulaşıldı ({max} {identifier})"

#~ msgid "Getting Started"
#~ msgstr "Başlarken"

#~ msgid "Field Types"
#~ msgstr "Alan Tipleri"

#~ msgid "Functions"
#~ msgstr "Fonksiyonlar"

#~ msgid "Actions"
#~ msgstr "Eylemler"

#~ msgid "Tutorials"
#~ msgstr "Örnekler"

#~ msgid "Error"
#~ msgstr "Hata"

#, fuzzy
#~| msgid "This field requires at least {min} {identifier}"
#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "Bu alan gerektirir, en azından {min} {identifier}"
#~ msgstr[1] "Bu alan gerektirir, en azından {min} {identifier}"

#~ msgid "See what's new in"
#~ msgstr "Neler yeni gözat"

#~ msgid "'How to' guides"
#~ msgstr "Nasıl Yapılır"

#~ msgid "Created by"
#~ msgstr "Oluşturan"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Başarılı</b>. İçe aktarma aracı %s alan gruplarını aktardı: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Uyarı </b>. İçe aktarma aracı zaten var olan %s alan gruplarını tespit "
#~ "etti. Bu kayıtlar gözardı edildi: %s"

#~ msgid "Upgrade"
#~ msgstr "Yükselt"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Yeniden sıralama için sürükle ve bırak"

#~ msgid "See what's new"
#~ msgstr "Neler yeni görün"

#~ msgid "Show a different month"
#~ msgstr "Başka bir ay göster"

#~ msgid "Return format"
#~ msgstr "Dönüş formatı"

#~ msgid "uploaded to this post"
#~ msgstr "Bu yazıya yükledi"

#~ msgid "File Size"
#~ msgstr "Dosya Boyutu"

#~ msgid "No File selected"
#~ msgstr "Dosya seçilmedi"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr "Tüm metin ilk wp fonksiyonu sayesinde geçilecek unutmayın"

#~ msgid "Warning"
#~ msgstr "Uyarı"

#~ msgid "eg. Show extra content"
#~ msgstr "örn. Ekstra içerik göster"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b> Bağlantı Hatası </ b>. Üzgünüm, lütfen tekrar deneyin"

#~ msgid "Save Options"
#~ msgstr "Ayarları Kaydet"

#~ msgid "License"
#~ msgstr "Lisans"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Güncelleştirmeleri kilidini açmak için, aşağıdaki lisans anahtarını "
#~ "girin. Eğer bir lisans anahtarı yoksa, lütfen"

#~ msgid "details & pricing"
#~ msgstr "detaylar & fiyatlandırma"

#~ msgid "Hide / Show All"
#~ msgstr "Gizle / Hepsini Göster"

#~ msgid "Show Field Keys"
#~ msgstr "Alan Anahtarlarını Göster"

#~ msgid "Pending Review"
#~ msgstr "İnceleme Bekliyor"

#~ msgid "Draft"
#~ msgstr "Taslak"

#~ msgid "Private"
#~ msgstr "Gizli"

#~ msgid "Revision"
#~ msgstr "Revizyon"

#~ msgid "Trash"
#~ msgstr "Çöp"

#, fuzzy
#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr "Alan grupları oluşturulma sırası <br/> sırayla alttan yukarı"

#~ msgid "ACF PRO Required"
#~ msgstr "ACF PRO Gerekli"

#~ msgid ""
#~ "We have detected an issue which requires your attention: This website "
#~ "makes use of premium add-ons (%s) which are no longer compatible with ACF."
#~ msgstr ""
#~ "Biz dikkat gerektiren bir sorunu tespit ettik: Bu ​​web sitesi artık ACF "
#~ "ile uyumlu olan eklentileriyle (%s) kullanımını kolaylaştırır."

#~ msgid ""
#~ "Don't panic, you can simply roll back the plugin and continue using ACF "
#~ "as you know it!"
#~ msgstr ""
#~ "Panik yapmayın, sadece eklenti geri almak ve bunu bildiğiniz gibi ACF "
#~ "kullanmaya devam edebilirsiniz!"

#~ msgid "Roll back to ACF v%s"
#~ msgstr "ACF v %s ye geri al"

#~ msgid "Learn why ACF PRO is required for my site"
#~ msgstr "ACF PRO Sitem için neden gereklidir öğrenin"

#~ msgid "Update Database"
#~ msgstr "Veritabanını Güncelle"

#~ msgid "Data Upgrade"
#~ msgstr "Veri Yükseltme"

#~ msgid "Data upgraded successfully."
#~ msgstr "Veri başarıyla yükseltildi."

#~ msgid "Data is at the latest version."
#~ msgstr "Verinin en son sürümü."

#~ msgid "1 required field below is empty"
#~ msgid_plural "%s required fields below are empty"
#~ msgstr[0] "%s Gerekli alan boş"

#~ msgid "Load & Save Terms to Post"
#~ msgstr "Yazı Yükleme ve Kaydet Şartları"

#~ msgid ""
#~ "Load value based on the post's terms and update the post's terms on save"
#~ msgstr ""
#~ "Yükleme değeri yazılar için terimlere dayalı ve kaydetme üzerindeki "
#~ "yazılar için şartlarını güncelleyecek"

#, fuzzy
#~ msgid "image"
#~ msgstr "Resim"

#, fuzzy
#~ msgid "expand_details"
#~ msgstr "Ayrıntıları Genişlet"

#, fuzzy
#~ msgid "collapse_details"
#~ msgstr "Detayları Daralt"

#, fuzzy
#~ msgid "relationship"
#~ msgstr "İlişkili"

#, fuzzy
#~ msgid "title_is_required"
#~ msgstr "Alan grubu için başlık gerekli"

#, fuzzy
#~ msgid "move_field"
#~ msgstr "Alanı Taşı"

#, fuzzy
#~ msgid "flexible_content"
#~ msgstr "Esnek İçerik"

#, fuzzy
#~ msgid "gallery"
#~ msgstr "Galeri"

#, fuzzy
#~ msgid "repeater"
#~ msgstr "Tekrarlayıcı"

#, fuzzy
#~ msgid "Controls how HTML tags are rendered"
#~ msgstr "Yeni satırlar nasıl oluşturulacağını denetler"

#~ msgid "Custom field updated."
#~ msgstr "Özel alan güncellendi."

#~ msgid "Custom field deleted."
#~ msgstr "Özel alan silindi."

#~ msgid "Field group duplicated! Edit the new \"%s\" field group."
#~ msgstr "Alan grup çoğaltıldı! Yeni  \"%s \" alan grubu düzenleyin."

#~ msgid "Import/Export"
#~ msgstr "İçe/Dışa Aktar"

#~ msgid "Column Width"
#~ msgstr "Sütun Genişliği"

#~ msgid "Attachment Details"
#~ msgstr "Ek Detayları"
