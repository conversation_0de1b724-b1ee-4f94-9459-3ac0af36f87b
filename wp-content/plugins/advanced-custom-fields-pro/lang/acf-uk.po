# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2023-01-18T14:07:28+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/admin/admin-field-group.php:326
msgid "Field Settings Tabs"
msgstr ""

#. Author URI of the plugin
msgid ""
"https://wpengine.com/?utm_source=wordpress."
"org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""

#: includes/api/api-template.php:867
msgid "[ACF shortcode value disabled for preview]"
msgstr ""

#: includes/admin/admin-field-group.php:673
msgid "Close Modal"
msgstr ""

#: includes/admin/admin-field-group.php:162
#: assets/build/js/acf-field-group.js:1304
#: assets/build/js/acf-field-group.js:1468
msgid "Field moved to other group"
msgstr ""

#: includes/admin/admin-field-group.php:161 assets/build/js/acf.js:1397
#: assets/build/js/acf.js:1463
msgid "Close modal"
msgstr ""

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr ""

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr ""

#: includes/fields/class-acf-field-select.php:450
#: includes/fields/class-acf-field-true_false.php:197
msgid "Use a stylized checkbox using select2"
msgstr ""

#: includes/fields/class-acf-field-radio.php:257
msgid "Save Other Choice"
msgstr ""

#: includes/fields/class-acf-field-radio.php:246
msgid "Allow Other Choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:446
msgid "Add Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:405
msgid "Save Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:394
msgid "Allow Custom Values"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:144
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Оновлення"

#: includes/admin/views/html-admin-navigation.php:79
msgid "Advanced Custom Fields logo"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:22
msgid "Save Changes"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:16
msgid "Field Group Title"
msgstr ""

#: includes/admin/views/html-admin-form-top.php:3
msgid "Add title"
msgstr ""

#. translators: %s url to getting started guide
#: includes/admin/views/field-groups-empty.php:20
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""

#: includes/admin/views/field-groups-empty.php:15
msgid "Add Field Group"
msgstr ""

#. translators: %s url to creating a field group page
#: includes/admin/views/field-groups-empty.php:10
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""

#: includes/admin/views/field-groups-empty.php:5
msgid "Add Your First Field Group"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:16
msgid "Upgrade Now"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:11
msgid "Options Pages"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:10
msgid "ACF Blocks"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:7
msgid "Flexible Content Field"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:6
msgid "Repeater Field"
msgstr ""

#: includes/admin/views/field-group-pro-features.php:4
#: includes/admin/views/html-admin-navigation.php:97
msgid "Unlock Extra Features with ACF PRO"
msgstr ""

#: includes/admin/views/field-group-options.php:242
msgid "Delete Field Group"
msgstr ""

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/field-group-options.php:236
msgid "Created on %1$s at %2$s"
msgstr ""

#: includes/admin/views/field-group-options.php:179
msgid "Group Settings"
msgstr ""

#: includes/admin/views/field-group-options.php:28
msgid "Location Rules"
msgstr ""

#. translators: %s url to field types list
#: includes/admin/views/field-group-fields.php:61
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""

#: includes/admin/views/field-group-fields.php:54
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""

#: includes/admin/views/field-group-fields.php:53
msgid "Add Your First Field"
msgstr ""

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/field-group-fields.php:32
msgid "#"
msgstr ""

#: includes/admin/views/field-group-fields.php:22
#: includes/admin/views/field-group-fields.php:56
#: includes/admin/views/field-group-fields.php:92
#: includes/admin/views/html-admin-form-top.php:21
msgid "Add Field"
msgstr ""

#: includes/admin/views/field-group-field.php:191
#: includes/admin/views/field-group-options.php:40
msgid "Presentation"
msgstr ""

#: includes/admin/views/field-group-field.php:159
msgid "Validation"
msgstr ""

#: includes/admin/views/field-group-field.php:93
msgid "General"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:70
msgid "Import JSON"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:361
msgid "Export Field Groups - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid "Export As JSON"
msgstr ""

#: includes/admin/admin-field-groups.php:638
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-field-groups.php:585
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-field-groups.php:537
#: includes/admin/admin-field-groups.php:558
msgid "Deactivate"
msgstr ""

#: includes/admin/admin-field-groups.php:537
msgid "Deactivate this item"
msgstr ""

#: includes/admin/admin-field-groups.php:533
#: includes/admin/admin-field-groups.php:557
msgid "Activate"
msgstr ""

#: includes/admin/admin-field-groups.php:533
msgid "Activate this item"
msgstr ""

#: includes/admin/admin-field-group.php:158
#: assets/build/js/acf-field-group.js:2366
#: assets/build/js/acf-field-group.js:2635
msgid "Move field group to trash?"
msgstr ""

#: acf.php:448 includes/admin/admin-field-group.php:354
#: includes/admin/admin-field-groups.php:232
msgctxt "post status"
msgid "Inactive"
msgstr ""

#. Author of the plugin
msgid "WP Engine"
msgstr ""

#: acf.php:506
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Розширені настроювані поля та розширені настроювані поля не повинні бути "
"активними одночасно. Ми автоматично деактивували Advanced Custom Fields PRO."

#: acf.php:504
msgid ""
"Advanced Custom Fields and Advanced Custom Fields should not be active at "
"the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#: includes/acf-value-functions.php:374
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""

#: includes/fields/class-acf-field-user.php:537
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-user.php:528
msgid "%1$s must have a valid user ID."
msgstr ""

#: includes/fields/class-acf-field-user.php:366
msgid "Invalid request."
msgstr "Невірний запит."

#: includes/fields/class-acf-field-select.php:683
msgid "%1$s is not one of %2$s"
msgstr ""

#: includes/fields/class-acf-field-post_object.php:669
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-post_object.php:653
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must have a valid post ID."
msgstr ""

#: includes/fields/class-acf-field-file.php:472
msgid "%s requires a valid attachment ID."
msgstr ""

#: includes/admin/views/field-group-options.php:205
msgid "Show in REST API"
msgstr "Показати в REST API"

#: includes/fields/class-acf-field-color_picker.php:167
msgid "Enable Transparency"
msgstr "Увімкнути прозорість"

#: includes/fields/class-acf-field-color_picker.php:186
msgid "RGBA Array"
msgstr "RGBA Масив"

#: includes/fields/class-acf-field-color_picker.php:96
msgid "RGBA String"
msgstr "RGBA рядок"

#: includes/fields/class-acf-field-color_picker.php:95
#: includes/fields/class-acf-field-color_picker.php:185
msgid "Hex String"
msgstr "HEX рядок"

#: includes/admin/admin-field-group.php:187
#: assets/build/js/acf-field-group.js:837
#: assets/build/js/acf-field-group.js:933
msgid "Gallery (Pro only)"
msgstr ""

#: includes/admin/admin-field-group.php:186
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:923
msgid "Clone (Pro only)"
msgstr ""

#: includes/admin/admin-field-group.php:185
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:920
msgid "Flexible Content (Pro only)"
msgstr ""

#: includes/admin/admin-field-group.php:184
#: assets/build/js/acf-field-group.js:833
#: assets/build/js/acf-field-group.js:917
msgid "Repeater (Pro only)"
msgstr ""

#: includes/admin/admin-field-group.php:354
msgctxt "post status"
msgid "Active"
msgstr "Діюча"

#: includes/fields/class-acf-field-email.php:178
msgid "'%s' is not a valid email address"
msgstr "'%s' неправильна адреса електронної пошти"

#: includes/fields/class-acf-field-color_picker.php:74
msgid "Color value"
msgstr "Значення кольору"

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Select default color"
msgstr "Вибрати колір за замовчуванням"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Clear color"
msgstr "Очистити колір"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Блоки"

#: includes/acf-wp-functions.php:83
msgid "Options"
msgstr "Параметри"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Користувачі"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Пункти Меню"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Віджети"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Вкладення"

#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "Таксономії"

#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "Записи"

#: includes/ajax/class-acf-ajax-local-json-diff.php:68
msgid "JSON field group (newer)"
msgstr "Група полів JSON (новіша)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:64
msgid "Original field group"
msgstr "Оригінальна група  полів"

#: includes/ajax/class-acf-ajax-local-json-diff.php:59
msgid "Last updated: %s"
msgstr "Останнє оновлення: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:53
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr "На жаль, ця група полів недоступна для порівняння різниць."

#: includes/ajax/class-acf-ajax-local-json-diff.php:43
msgid "Invalid field group ID."
msgstr "Недійсний ідентифікатор ID групи полів."

#: includes/ajax/class-acf-ajax-local-json-diff.php:36
msgid "Invalid field group parameter(s)."
msgstr "Недійсний параметр(и) групи полів."

#: includes/admin/admin-field-groups.php:506
msgid "Awaiting save"
msgstr "Чекає збереження"

#: includes/admin/admin-field-groups.php:503
msgid "Saved"
msgstr "Збережено"

#: includes/admin/admin-field-groups.php:499
msgid "Import"
msgstr "Імпорт"

#: includes/admin/admin-field-groups.php:495
msgid "Review changes"
msgstr "Перегляньте зміни"

#: includes/admin/admin-field-groups.php:471
msgid "Located in: %s"
msgstr "Розташовано в: %s"

#: includes/admin/admin-field-groups.php:467
msgid "Located in plugin: %s"
msgstr "Розташовано в плагіні: %s"

#: includes/admin/admin-field-groups.php:463
msgid "Located in theme: %s"
msgstr "Розташовано в Темі: %s"

#: includes/admin/admin-field-groups.php:441
msgid "Various"
msgstr "Різні"

#: includes/admin/admin-field-groups.php:200
#: includes/admin/admin-field-groups.php:565
msgid "Sync changes"
msgstr "Синхронізувати зміни"

#: includes/admin/admin-field-groups.php:199
msgid "Loading diff"
msgstr "Завантаження різного"

#: includes/admin/admin-field-groups.php:198
msgid "Review local JSON changes"
msgstr "Перегляньте локальні зміни JSON"

#: includes/admin/admin.php:172
msgid "Visit website"
msgstr "Відвідати Сайт"

#: includes/admin/admin.php:171
msgid "View details"
msgstr "Переглянути деталі"

#: includes/admin/admin.php:170
msgid "Version %s"
msgstr "Версія %s"

#: includes/admin/admin.php:169
msgid "Information"
msgstr "Інформація"

#: includes/admin/admin.php:160
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Служба Підтримки</a>. Фахівці Служби "
"підтримки в нашому довідковому бюро допоможуть вирішити ваші більш детальні "
"технічні завдання."

#: includes/admin/admin.php:156
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Обговорення</a>. На наших форумах спільнот "
"ми маємо активну та доброзичливу спільноту, яка, можливо, зможе допомогти "
"вам з’ясувати «інструкції» стосовно ACF."

#: includes/admin/admin.php:152
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Документація</a>. Наша розширена "
"документація містить посилання та інструкції щодо більшості ситуацій, з "
"якими ви можете зіткнутися."

#: includes/admin/admin.php:149
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Ми відповідально ставимося до підтримки і хочемо, щоб ви отримали найкращі "
"результати від свого веб-сайту за допомогою ACF. Якщо у вас виникнуть "
"труднощі, ви можете знайти допомогу в кількох місцях:"

#: includes/admin/admin.php:146 includes/admin/admin.php:148
msgid "Help & Support"
msgstr "Довідка та Підтримка"

#: includes/admin/admin.php:137
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Будь ласка, скористайтесь вкладкою Довідка та Підтримка, щоб зв’язатись, "
"якщо вам потрібна допомога."

#: includes/admin/admin.php:134
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Перш ніж створювати свою першу групу полів, ми рекомендуємо спочатку "
"прочитати наш посібник <a href=\"%s\" target=\"_blank\"> Початок роботи </"
"a>, щоб ознайомитись із філософією та найкращими практиками плагіна."

#: includes/admin/admin.php:132
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Плагін Advanced Custom Fields пропонує візуальний конструктор форм для "
"налаштування екранів редагування WordPress з додатковими полями та "
"інтуїтивний API для відображення значень користувацьких полів у будь-якому "
"файлі шаблону теми."

#: includes/admin/admin.php:129 includes/admin/admin.php:131
msgid "Overview"
msgstr "Огляд"

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr "Тип розташування \"%s\" вже зареєстровано."

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr "Клас \"%s\" не існує."

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Невірний ідентифікатор."

#: includes/fields/class-acf-field-user.php:361
msgid "Error loading field."
msgstr "Помилка при завантаженні поля."

#: assets/build/js/acf-input.js:2742 assets/build/js/acf-input.js:2811
#: assets/build/js/acf-input.js:2913 assets/build/js/acf-input.js:2987
msgid "Location not found: %s"
msgstr "Локація не знайдена: %s"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Помилка</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Віджет"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Роль користувача"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Коментар"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Формат запису"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Пункт меню"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Статус запису"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Меню"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Області для меню"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Меню"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Таксономія запису"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Дочірня сторінка (має батьківську)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Батьківська сторінка (має нащадків)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Сторінка верхнього рівня (без батьків)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Сторінка записів"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Головна сторінка"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Тип сторінки"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Переглянути бекенд"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Переглянути фронтенд"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Авторизовані"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Поточний користувач"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Шаблон сторінки"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Зареєструватись"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Додати / Редагувати"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Форма користувача"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Батьківська сторінка"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Супер-адмін"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Поточна роль користувача"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Стандартний шаблон"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Шаблон запису"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Категорія запису"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Всі %s формати"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Вкладений файл"

#: includes/validation.php:365
msgid "%s value is required"
msgstr "%s значення обов'язкове"

#: includes/admin/views/field-group-field-conditional-logic.php:59
msgid "Show this field if"
msgstr "Показувати поле, якщо"

#: includes/admin/views/field-group-field-conditional-logic.php:26
#: includes/admin/views/field-group-field.php:279
msgid "Conditional Logic"
msgstr "Умовна логіка"

#: includes/admin/admin.php:207
#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/html-location-rule.php:92
msgid "and"
msgstr "та"

#: includes/admin/admin-field-groups.php:290
msgid "Local JSON"
msgstr "Локальний JSON"

#: includes/admin/views/field-group-pro-features.php:9
msgid "Clone Field"
msgstr "Клонувати поле"

#: includes/admin/views/html-notice-upgrade.php:30
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Також перевірте, чи всі додаткові доповнення (%s) оновлені до останньої "
"версії."

#: includes/admin/views/html-notice-upgrade.php:28
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "Ця версія містить вдосконалення вашої бази даних і вимагає оновлення."

#: includes/admin/views/html-notice-upgrade.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Дякуємо за оновлення до %1$s v%2$s!"

#: includes/admin/views/html-notice-upgrade.php:27
msgid "Database Upgrade Required"
msgstr "Необхідно оновити базу даних"

#: includes/admin/views/html-notice-upgrade.php:18
msgid "Options Page"
msgstr "Сторінка опцій"

#: includes/admin/views/html-notice-upgrade.php:15
msgid "Gallery"
msgstr "Галерея"

#: includes/admin/views/html-notice-upgrade.php:12
msgid "Flexible Content"
msgstr "Гнучкий вміст"

#: includes/admin/views/html-notice-upgrade.php:9
msgid "Repeater"
msgstr "Повторювальне поле"

#: includes/admin/views/html-admin-tools.php:24
msgid "Back to all tools"
msgstr "Повернутися до всіх інструментів"

#: includes/admin/views/field-group-options.php:160
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Якщо декілька груп полів відображаються на екрані редагування, то "
"використовуватимуться параметри першої групи. (з найменшим порядковим "
"номером)"

#: includes/admin/views/field-group-options.php:160
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Оберіть</b> що <b>ховати</b> з екрану редагування/створення."

#: includes/admin/views/field-group-options.php:159
msgid "Hide on screen"
msgstr "Ховати на екрані"

#: includes/admin/views/field-group-options.php:151
msgid "Send Trackbacks"
msgstr "Надіслати трекбеки"

#: includes/admin/views/field-group-options.php:150
msgid "Tags"
msgstr "Позначки"

#: includes/admin/views/field-group-options.php:149
msgid "Categories"
msgstr "Категорії"

#: includes/admin/views/field-group-options.php:147
msgid "Page Attributes"
msgstr "Властивості сторінки"

#: includes/admin/views/field-group-options.php:146
msgid "Format"
msgstr "Формат"

#: includes/admin/views/field-group-options.php:145
msgid "Author"
msgstr "Автор"

#: includes/admin/views/field-group-options.php:144
msgid "Slug"
msgstr "Частина посилання"

#: includes/admin/views/field-group-options.php:143
msgid "Revisions"
msgstr "Редакції"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/field-group-options.php:142
msgid "Comments"
msgstr "Коментарі"

#: includes/admin/views/field-group-options.php:141
msgid "Discussion"
msgstr "Обговорення"

#: includes/admin/views/field-group-options.php:139
msgid "Excerpt"
msgstr "Уривок"

#: includes/admin/views/field-group-options.php:138
msgid "Content Editor"
msgstr "Редактор матеріалу"

#: includes/admin/views/field-group-options.php:137
msgid "Permalink"
msgstr "Постійне посилання"

#: includes/admin/views/field-group-options.php:222
msgid "Shown in field group list"
msgstr "Відображається на сторінці груп полів"

#: includes/admin/views/field-group-options.php:122
msgid "Field groups with a lower order will appear first"
msgstr "Групи полів з нижчим порядком з’являться спочатку"

#: includes/admin/views/field-group-options.php:121
msgid "Order No."
msgstr "Порядок розташування"

#: includes/admin/views/field-group-options.php:112
msgid "Below fields"
msgstr "Під полями"

#: includes/admin/views/field-group-options.php:111
msgid "Below labels"
msgstr "Під ярликами"

#: includes/admin/views/field-group-options.php:104
msgid "Instruction placement"
msgstr "Розміщення інструкцій"

#: includes/admin/views/field-group-options.php:87
msgid "Label placement"
msgstr "Розміщення ярликів"

#: includes/admin/views/field-group-options.php:77
msgid "Side"
msgstr "Збоку"

#: includes/admin/views/field-group-options.php:76
msgid "Normal (after content)"
msgstr "Стандартно (після тектового редактора)"

#: includes/admin/views/field-group-options.php:75
msgid "High (after title)"
msgstr "Вгорі (під заголовком)"

#: includes/admin/views/field-group-options.php:68
msgid "Position"
msgstr "Положення"

#: includes/admin/views/field-group-options.php:59
msgid "Seamless (no metabox)"
msgstr "Спрощений (без метабоксу)"

#: includes/admin/views/field-group-options.php:58
msgid "Standard (WP metabox)"
msgstr "Стандартний (WP метабокс)"

#: includes/admin/views/field-group-options.php:51
msgid "Style"
msgstr "Стиль"

#: includes/admin/views/field-group-fields.php:44
msgid "Type"
msgstr "Тип"

#: includes/admin/admin-field-groups.php:285
#: includes/admin/views/field-group-fields.php:43
msgid "Key"
msgstr "Ключ"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/field-group-fields.php:37
msgid "Order"
msgstr "Порядок"

#: includes/admin/views/field-group-field.php:294
msgid "Close Field"
msgstr "Закрити поле"

#: includes/admin/views/field-group-field.php:235
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:219
msgid "class"
msgstr "клас"

#: includes/admin/views/field-group-field.php:256
msgid "width"
msgstr "ширина"

#: includes/admin/views/field-group-field.php:250
msgid "Wrapper Attributes"
msgstr "Атрибути обгортки"

#: includes/admin/views/field-group-field.php:171
msgid "Required"
msgstr "Вимагається"

#: includes/admin/views/field-group-field.php:203
msgid "Instructions for authors. Shown when submitting data"
msgstr "Напишіть короткий опис для поля"

#: includes/admin/views/field-group-field.php:202
msgid "Instructions"
msgstr "Інструкція"

#: includes/admin/views/field-group-field.php:106
msgid "Field Type"
msgstr "Тип поля"

#: includes/admin/views/field-group-field.php:134
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Одне слово, без пробілів. Можете використовувати нижнє підкреслення."

#: includes/admin/views/field-group-field.php:133
msgid "Field Name"
msgstr "Ярлик"

#: includes/admin/views/field-group-field.php:121
msgid "This is the name which will appear on the EDIT page"
msgstr "Ця назва відображується на сторінці редагування"

#: includes/admin/views/field-group-field.php:120
msgid "Field Label"
msgstr "Назва поля"

#: includes/admin/views/field-group-field.php:70
msgid "Delete"
msgstr "Видалити"

#: includes/admin/views/field-group-field.php:70
msgid "Delete field"
msgstr "Видалити поле"

#: includes/admin/views/field-group-field.php:68
msgid "Move"
msgstr "Перемістити"

#: includes/admin/views/field-group-field.php:68
msgid "Move field to another group"
msgstr "Перемістити поле до іншої групи"

#: includes/admin/views/field-group-field.php:66
msgid "Duplicate field"
msgstr "Дублювати поле"

#: includes/admin/views/field-group-field.php:62
#: includes/admin/views/field-group-field.php:65
msgid "Edit field"
msgstr "Редагувати поле"

#: includes/admin/views/field-group-field.php:58
msgid "Drag to reorder"
msgstr "Перетягніть, щоб змінити порядок"

#: includes/admin/admin-field-group.php:168
#: includes/admin/views/html-location-group.php:3
#: assets/build/js/acf-field-group.js:1935
#: assets/build/js/acf-field-group.js:2166
msgid "Show this field group if"
msgstr "Показувати групу полів, якщо"

#: includes/admin/views/html-admin-page-upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Немає оновлень."

#: includes/admin/views/html-admin-page-upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Оновлення бази даних завершено. <a href=\"%s\">Подивіться, що нового</a>"

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Читання завдань для оновлення…"

#: includes/admin/views/html-admin-page-upgrade-network.php:165
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "Помилка оновлення."

#: includes/admin/views/html-admin-page-upgrade-network.php:162
msgid "Upgrade complete."
msgstr "Оновлення завершено."

#: includes/admin/views/html-admin-page-upgrade-network.php:148
#: includes/admin/views/html-admin-page-upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "Оновлення даних до версії %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:121
#: includes/admin/views/html-notice-upgrade.php:44
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Настійно рекомендується зробити резервну копію вашої бази даних, перш ніж "
"продовжити. Ви впевнені, що хочете запустити програму оновлення зараз?"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
msgid "Please select at least one site to upgrade."
msgstr "Виберіть принаймні один сайт для оновлення."

#: includes/admin/views/html-admin-page-upgrade-network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Оновлення бази даних завершено. <a href=\"%s\">Повернутися до Майстерні</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:80
msgid "Site is up to date"
msgstr "Сайт оновлено"

#: includes/admin/views/html-admin-page-upgrade-network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Для сайту потрібно оновити базу даних з %1$s до %2$s"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Сайт"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:96
msgid "Upgrade Sites"
msgstr "Оновити сайти"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Для наступних сайтів потрібне оновлення БД. Позначте ті, які потрібно "
"оновити, а потім натисніть %s."

#: includes/admin/views/field-group-field-conditional-logic.php:171
#: includes/admin/views/field-group-locations.php:38
msgid "Add rule group"
msgstr "Додати групу умов"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Створіть набір умов, щоб визначити де використовувати  ці додаткові поля"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Умови"

#: includes/admin/tools/class-acf-admin-tool-export.php:478
msgid "Copied"
msgstr "Скопійовано"

#: includes/admin/tools/class-acf-admin-tool-export.php:441
msgid "Copy to clipboard"
msgstr "Копіювати в буфер обміну"

#: includes/admin/tools/class-acf-admin-tool-export.php:362
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:329
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Виберіть групи полів, які потрібно експортувати, а потім виберіть метод "
"експорту. Експортувати як JSON, щоб експортувати у файл .json, який потім "
"можна імпортувати в іншу установку ACF. Згенеруйте PHP для експорту в код "
"PHP, який ви можете розмістити у своїй темі."

#: includes/admin/tools/class-acf-admin-tool-export.php:233
#: includes/admin/tools/class-acf-admin-tool-export.php:262
msgid "Select Field Groups"
msgstr "Оберіть групи полів"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Експортована 1 група полів."
msgstr[1] "Експортовано %s групи полів."
msgstr[2] "Експортовано %s груп полів."

#: includes/admin/tools/class-acf-admin-tool-export.php:96
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "Не обрано груп полів"

#: includes/admin/tools/class-acf-admin-tool-export.php:39
#: includes/admin/tools/class-acf-admin-tool-export.php:337
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Генерувати PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Експортувати групи полів"

#: includes/admin/tools/class-acf-admin-tool-import.php:146
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Імпортовано 1 групу полів."
msgstr[1] "Імпортовано %s групи полів."
msgstr[2] "Імпортовано %s груп полів."

#: includes/admin/tools/class-acf-admin-tool-import.php:115
msgid "Import file empty"
msgstr "Файл імпорту порожній"

#: includes/admin/tools/class-acf-admin-tool-import.php:106
msgid "Incorrect file type"
msgstr "Невірний тип файлу"

#: includes/admin/tools/class-acf-admin-tool-import.php:101
msgid "Error uploading file. Please try again"
msgstr "Помилка завантаження файлу. Спробуйте знову"

#: includes/admin/tools/class-acf-admin-tool-import.php:51
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups"
msgstr ""
"Виберіть файл JSON Advanced Custom Fields, який потрібно імпортувати. Якщо "
"натиснути кнопку імпорту нижче, ACF імпортує групи полів"

#: includes/admin/tools/class-acf-admin-tool-import.php:28
#: includes/admin/tools/class-acf-admin-tool-import.php:50
msgid "Import Field Groups"
msgstr "Імпортувати групи полів"

#: includes/admin/admin-field-groups.php:494
msgid "Sync"
msgstr "Синхронізація"

#: includes/admin/admin-field-groups.php:942
msgid "Select %s"
msgstr "Вибрати %s"

#: includes/admin/admin-field-groups.php:527
#: includes/admin/admin-field-groups.php:556
#: includes/admin/views/field-group-field.php:66
msgid "Duplicate"
msgstr "Дублювати"

#: includes/admin/admin-field-groups.php:527
msgid "Duplicate this item"
msgstr "Дублювати цей елемент"

#: includes/admin/admin-field-groups.php:284
#: includes/admin/views/field-group-options.php:221
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
msgid "Description"
msgstr "Опис"

#: includes/admin/admin-field-groups.php:491
#: includes/admin/admin-field-groups.php:829
msgid "Sync available"
msgstr "Доступна синхронізація"

#: includes/admin/admin-field-groups.php:754
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Групу полів синхронізовано."
msgstr[1] "Синхронізовано %s групи полів."
msgstr[2] "Синхронізовано %s груп полів."

#: includes/admin/admin-field-groups.php:696
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Групу полів продубльовано."
msgstr[1] "%s групи полів продубльовано."
msgstr[2] "%s груп полів продубльовано."

#: includes/admin/admin-field-groups.php:118
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Активні <span class=\"count\">(%s)</span>"
msgstr[1] "Активні <span class=\"count\">(%s)</span>"
msgstr[2] "Активні <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:237
msgid "Review sites & upgrade"
msgstr "Перегляд сайтів & оновлення"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:93
#: includes/admin/admin-upgrade.php:94 includes/admin/admin-upgrade.php:213
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Оновити базу даних"

#: includes/admin/admin.php:49 includes/admin/views/field-group-options.php:140
msgid "Custom Fields"
msgstr "Додаткові поля"

#: includes/admin/admin-field-group.php:719
msgid "Move Field"
msgstr "Перемістити поле"

#: includes/admin/admin-field-group.php:708
#: includes/admin/admin-field-group.php:712
msgid "Please select the destination for this field"
msgstr "Будь ласка, оберіть групу, в яку перемістити"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/admin-field-group.php:669
msgid "The %1$s field can now be found in the %2$s field group"
msgstr ""

#: includes/admin/admin-field-group.php:666
msgid "Move Complete."
msgstr "Переміщення завершене."

#: includes/admin/views/field-group-field.php:273
#: includes/admin/views/field-group-options.php:189
msgid "Active"
msgstr "Активно"

#: includes/admin/admin-field-group.php:323
msgid "Field Keys"
msgstr "Ключі поля"

#: includes/admin/admin-field-group.php:224
#: includes/admin/tools/class-acf-admin-tool-export.php:286
msgid "Settings"
msgstr "Налаштування"

#: includes/admin/admin-field-groups.php:286
msgid "Location"
msgstr "Розташування"

#: includes/admin/admin-field-group.php:169 assets/build/js/acf-input.js:983
#: assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Нуль"

#: includes/acf-field-group-functions.php:846
#: includes/admin/admin-field-group.php:166
#: assets/build/js/acf-field-group.js:1158
#: assets/build/js/acf-field-group.js:1310
msgid "copy"
msgstr "копіювати"

#: includes/admin/admin-field-group.php:165
#: assets/build/js/acf-field-group.js:344
#: assets/build/js/acf-field-group.js:389
msgid "(this field)"
msgstr "(це поле)"

#: includes/admin/admin-field-group.php:163 assets/build/js/acf-input.js:918
#: assets/build/js/acf-input.js:943 assets/build/js/acf-input.js:1002
#: assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "Перевірено"

#: includes/admin/admin-field-group.php:160
#: assets/build/js/acf-field-group.js:1249
#: assets/build/js/acf-field-group.js:1408
msgid "Move Custom Field"
msgstr "Перемістити поле"

#: includes/admin/admin-field-group.php:159
#: assets/build/js/acf-field-group.js:370
#: assets/build/js/acf-field-group.js:415
msgid "No toggle fields available"
msgstr ""

#: includes/admin/admin-field-group.php:157
msgid "Field group title is required"
msgstr "Заголовок обов’язковий"

#: includes/admin/admin-field-group.php:156
#: assets/build/js/acf-field-group.js:1238
#: assets/build/js/acf-field-group.js:1394
msgid "This field cannot be moved until its changes have been saved"
msgstr ""

#: includes/admin/admin-field-group.php:155
#: assets/build/js/acf-field-group.js:1049
#: assets/build/js/acf-field-group.js:1192
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""

#: includes/admin/admin-field-group.php:82
msgid "Field group draft updated."
msgstr "Чернетку групи полів оновлено."

#: includes/admin/admin-field-group.php:81
msgid "Field group scheduled for."
msgstr ""

#: includes/admin/admin-field-group.php:80
msgid "Field group submitted."
msgstr "Групу полів надіслано."

#: includes/admin/admin-field-group.php:79
msgid "Field group saved."
msgstr "Групу полів збережено."

#: includes/admin/admin-field-group.php:78
msgid "Field group published."
msgstr "Групу полів опубліковано."

#: includes/admin/admin-field-group.php:75
msgid "Field group deleted."
msgstr "Групу полів видалено."

#: includes/admin/admin-field-group.php:73
#: includes/admin/admin-field-group.php:74
#: includes/admin/admin-field-group.php:76
msgid "Field group updated."
msgstr "Групу полів оновлено."

#: includes/admin/admin-tools.php:119
#: includes/admin/views/html-admin-navigation.php:109
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Інструменти"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "не дорівнює"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "дорівнює"

#: includes/locations.php:102
msgid "Forms"
msgstr "Форми"

#: includes/locations.php:100 includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Сторінка"

#: includes/locations.php:99 includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Запис"

#: includes/fields.php:358
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:357
msgid "Relational"
msgstr ""

#: includes/fields.php:356
msgid "Choice"
msgstr "Вибір"

#: includes/fields.php:354
msgid "Basic"
msgstr "Загальне"

#: includes/fields.php:313
msgid "Unknown"
msgstr "Невідомий"

#: includes/fields.php:313
msgid "Field type does not exist"
msgstr "Тип поля не існує"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr ""

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "Запис оновлено"

#: includes/forms/form-front.php:106
msgid "Update"
msgstr "Оновити"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr ""

#: includes/fields.php:355 includes/forms/form-front.php:49
msgid "Content"
msgstr "Вміст"

#: includes/forms/form-front.php:40
msgid "Title"
msgstr "Заголовок"

#: includes/assets.php:371 includes/forms/form-comment.php:160
#: assets/build/js/acf-input.js:7337 assets/build/js/acf-input.js:7915
msgid "Edit field group"
msgstr "Редагувати групу полів"

#: includes/admin/admin-field-group.php:181 assets/build/js/acf-input.js:1125
#: assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr ""

#: includes/admin/admin-field-group.php:180 assets/build/js/acf-input.js:1106
#: assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr ""

#: includes/admin/admin-field-group.php:179 assets/build/js/acf-input.js:1075
#: assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "Значення меньше ніж"

#: includes/admin/admin-field-group.php:178 assets/build/js/acf-input.js:1045
#: assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "Значення більше ніж"

#: includes/admin/admin-field-group.php:177 assets/build/js/acf-input.js:888
#: assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "Значення містить"

#: includes/admin/admin-field-group.php:176 assets/build/js/acf-input.js:862
#: assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr "Значення відповідає шаблону"

#: includes/admin/admin-field-group.php:175 assets/build/js/acf-input.js:840
#: assets/build/js/acf-input.js:1023 assets/build/js/acf-input.js:903
#: assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "Значення не дорівноє"

#: includes/admin/admin-field-group.php:174 assets/build/js/acf-input.js:810
#: assets/build/js/acf-input.js:964 assets/build/js/acf-input.js:864
#: assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "Значення дорівнює"

#: includes/admin/admin-field-group.php:173 assets/build/js/acf-input.js:788
#: assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr ""

#: includes/admin/admin-field-group.php:172 assets/build/js/acf-input.js:758
#: assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr ""

#: includes/assets.php:352 assets/build/js/acf.js:1524
#: assets/build/js/acf.js:1604
msgid "Cancel"
msgstr "Скасувати"

#: includes/assets.php:348 assets/build/js/acf.js:1698
#: assets/build/js/acf.js:1801
msgid "Are you sure?"
msgstr "Ви впевнені?"

#: includes/assets.php:368 assets/build/js/acf-input.js:9370
#: assets/build/js/acf-input.js:10211
msgid "%d fields require attention"
msgstr ""

#: includes/assets.php:367 assets/build/js/acf-input.js:9368
#: assets/build/js/acf-input.js:10207
msgid "1 field requires attention"
msgstr "1 поле потребує уваги"

#: includes/assets.php:366 includes/validation.php:287
#: includes/validation.php:297 assets/build/js/acf-input.js:9363
#: assets/build/js/acf-input.js:10202
msgid "Validation failed"
msgstr "Помилка валідації"

#: includes/assets.php:365 assets/build/js/acf-input.js:9526
#: assets/build/js/acf-input.js:10385
msgid "Validation successful"
msgstr "Валідація успішна"

#: includes/media.php:54 assets/build/js/acf-input.js:7165
#: assets/build/js/acf-input.js:7719
msgid "Restricted"
msgstr "Обмежено"

#: includes/media.php:53 assets/build/js/acf-input.js:6980
#: assets/build/js/acf-input.js:7483
msgid "Collapse Details"
msgstr "Згорнути деталі"

#: includes/media.php:52 assets/build/js/acf-input.js:6980
#: assets/build/js/acf-input.js:7480
msgid "Expand Details"
msgstr "Показати деталі"

#: includes/media.php:51 assets/build/js/acf-input.js:6847
#: assets/build/js/acf-input.js:7328
msgid "Uploaded to this post"
msgstr "Завантажено до цього запису."

#: includes/media.php:50 assets/build/js/acf-input.js:6886
#: assets/build/js/acf-input.js:7367
msgctxt "verb"
msgid "Update"
msgstr "Оновлення"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Редагувати"

#: includes/assets.php:362 assets/build/js/acf-input.js:9140
#: assets/build/js/acf-input.js:9973
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Зміни, які ви внесли, буде втрачено, якщо ви перейдете з цієї сторінки"

#: includes/api/api-helpers.php:3395
msgid "File type must be %s."
msgstr "Тип файлу має бути %s."

#: includes/admin/admin-field-group.php:167
#: includes/admin/views/field-group-field-conditional-logic.php:59
#: includes/admin/views/field-group-field-conditional-logic.php:169
#: includes/admin/views/field-group-locations.php:36
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3391 assets/build/js/acf-field-group.js:492
#: assets/build/js/acf-field-group.js:1973
#: assets/build/js/acf-field-group.js:544
#: assets/build/js/acf-field-group.js:2210
msgid "or"
msgstr "або"

#: includes/api/api-helpers.php:3364
msgid "File size must not exceed %s."
msgstr "Розмір файлу не повинен перевищувати %s."

#: includes/api/api-helpers.php:3359
msgid "File size must be at least %s."
msgstr "Розмір файлу має бути принаймні %s."

#: includes/api/api-helpers.php:3344
msgid "Image height must not exceed %dpx."
msgstr "Висота зображення не повинна перевищувати %dpx."

#: includes/api/api-helpers.php:3339
msgid "Image height must be at least %dpx."
msgstr "Висота зображення має бути принаймні %dpx."

#: includes/api/api-helpers.php:3325
msgid "Image width must not exceed %dpx."
msgstr "Ширина зображення не повинна перевищувати %dpx."

#: includes/api/api-helpers.php:3320
msgid "Image width must be at least %dpx."
msgstr "Ширина зображення має бути принаймні %dpx."

#: includes/api/api-helpers.php:1566 includes/api/api-term.php:147
msgid "(no title)"
msgstr "(без назви)"

#: includes/api/api-helpers.php:861
msgid "Full Size"
msgstr "Повний розмір"

#: includes/api/api-helpers.php:820
msgid "Large"
msgstr "Великий"

#: includes/api/api-helpers.php:819
msgid "Medium"
msgstr "Середній"

#: includes/api/api-helpers.php:818
msgid "Thumbnail"
msgstr "Мініатюра"

#: includes/acf-field-functions.php:846
#: includes/admin/admin-field-group.php:164
#: assets/build/js/acf-field-group.js:789
#: assets/build/js/acf-field-group.js:859
msgid "(no label)"
msgstr "(Без мітки)"

#: includes/fields/class-acf-field-textarea.php:142
msgid "Sets the textarea height"
msgstr "Встановлює висоту текстової області"

#: includes/fields/class-acf-field-textarea.php:141
msgid "Rows"
msgstr "Рядки"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Текстова область"

#: includes/fields/class-acf-field-checkbox.php:447
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Додайте додатковий прапорець, щоб перемикати всі варіанти"

#: includes/fields/class-acf-field-checkbox.php:409
msgid "Save 'custom' values to the field's choices"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Allow 'custom' values to be added"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:233
msgid "Add new choice"
msgstr "Додати новий вибір"

#: includes/fields/class-acf-field-checkbox.php:170
msgid "Toggle All"
msgstr "Перемкнути всі"

#: includes/fields/class-acf-field-page_link.php:477
msgid "Allow Archives URLs"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:165
msgid "Archives"
msgstr "Архіви"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Посилання сторінки"

#: includes/fields/class-acf-field-taxonomy.php:945
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Додати"

#: includes/admin/views/field-group-fields.php:42
#: includes/fields/class-acf-field-taxonomy.php:910
msgid "Name"
msgstr "Ім'я"

#: includes/fields/class-acf-field-taxonomy.php:894
msgid "%s added"
msgstr "%s доданий"

#: includes/fields/class-acf-field-taxonomy.php:858
msgid "%s already exists"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:846
msgid "User unable to add new %s"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:756
msgid "Term ID"
msgstr "ID терміну"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Term Object"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:740
msgid "Load value from posts terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:739
msgid "Load Terms"
msgstr "Завантажити терміни"

#: includes/fields/class-acf-field-taxonomy.php:729
msgid "Connect selected terms to the post"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:728
msgid "Save Terms"
msgstr "Зберегти терміни"

#: includes/fields/class-acf-field-taxonomy.php:718
msgid "Allow new terms to be created whilst editing"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:717
msgid "Create Terms"
msgstr "Створити терміни"

#: includes/fields/class-acf-field-taxonomy.php:776
msgid "Radio Buttons"
msgstr "Радіо кнопка"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Single Value"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Multi Select"
msgstr "Вибір декількох"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Checkbox"
msgstr "Галочка"

#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Multiple Values"
msgstr "Декілька значень"

#: includes/fields/class-acf-field-taxonomy.php:766
msgid "Select the appearance of this field"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Appearance"
msgstr "Вигляд"

#: includes/fields/class-acf-field-taxonomy.php:707
msgid "Select the taxonomy to be displayed"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:668
msgctxt "No Terms"
msgid "No %s"
msgstr "Ні %s"

#: includes/fields/class-acf-field-number.php:263
msgid "Value must be equal to or lower than %d"
msgstr ""

#: includes/fields/class-acf-field-number.php:256
msgid "Value must be equal to or higher than %d"
msgstr ""

#: includes/fields/class-acf-field-number.php:241
msgid "Value must be a number"
msgstr "Значення має бути числом"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Число"

#: includes/fields/class-acf-field-radio.php:261
msgid "Save 'other' values to the field's choices"
msgstr ""

#: includes/fields/class-acf-field-radio.php:250
msgid "Add 'other' choice to allow for custom values"
msgstr "Додати вибір 'Інше', для користувацьких значень"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Радіо Кнопки"

#: includes/fields/class-acf-field-accordion.php:104
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:93
msgid "Allow this accordion to open without closing others."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:92
msgid "Multi-expand"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:82
msgid "Display this accordion as open on page load."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:81
msgid "Open"
msgstr "Відкрити"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Акордеон"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-file.php:276
msgid "Restrict which files can be uploaded"
msgstr ""

#: includes/fields/class-acf-field-file.php:217
msgid "File ID"
msgstr "ID файлу"

#: includes/fields/class-acf-field-file.php:216
msgid "File URL"
msgstr "URL файлу"

#: includes/fields/class-acf-field-file.php:215
msgid "File Array"
msgstr "Масив файлу"

#: includes/fields/class-acf-field-file.php:183
msgid "Add File"
msgstr "Додати файл"

#: includes/admin/tools/class-acf-admin-tool-import.php:94
#: includes/fields/class-acf-field-file.php:183
msgid "No file selected"
msgstr "Файл не вибрано"

#: includes/fields/class-acf-field-file.php:147
msgid "File name"
msgstr "Назва файлу"

#: includes/fields/class-acf-field-file.php:60
#: assets/build/js/acf-input.js:2466 assets/build/js/acf-input.js:2612
msgid "Update File"
msgstr "Оновити файл"

#: includes/fields/class-acf-field-file.php:59
#: assets/build/js/acf-input.js:2465 assets/build/js/acf-input.js:2611
msgid "Edit File"
msgstr "Редагувати файл"

#: includes/admin/tools/class-acf-admin-tool-import.php:59
#: includes/fields/class-acf-field-file.php:58
#: assets/build/js/acf-input.js:2439 assets/build/js/acf-input.js:2584
msgid "Select File"
msgstr "Оберіть файл"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Файл"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Пароль"

#: includes/fields/class-acf-field-select.php:391
msgid "Specify the value returned"
msgstr ""

#: includes/fields/class-acf-field-select.php:460
msgid "Use AJAX to lazy load choices?"
msgstr "Використати AJAX для завантаження значень?"

#: includes/fields/class-acf-field-checkbox.php:358
#: includes/fields/class-acf-field-select.php:380
msgid "Enter each default value on a new line"
msgstr "Введіть значення. Одне значення в одному рядку"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:48
#: assets/build/js/acf-input.js:6745 assets/build/js/acf-input.js:7213
msgctxt "verb"
msgid "Select"
msgstr "Вибрати"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Помилка завантаження"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Пошук…"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Завантаження результатів…"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr ""

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Ви можете вибрати тільки 1 позицію"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Будь ласка видаліть 1 символ"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Введіть %d або більше символів"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Будь ласка введіть 1 або більше символів"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Співпадінь не знайдено"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr ""
"Лише один результат доступний, натисніть клавішу ENTER, щоб вибрати його."

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:777
msgctxt "noun"
msgid "Select"
msgstr "Вибрати"

#: includes/fields/class-acf-field-user.php:74
msgid "User ID"
msgstr "ID користувача"

#: includes/fields/class-acf-field-user.php:73
msgid "User Object"
msgstr "Об'єкт користувача"

#: includes/fields/class-acf-field-user.php:72
msgid "User Array"
msgstr "Користувацький масив"

#: includes/fields/class-acf-field-user.php:60
msgid "All user roles"
msgstr "Всі ролі користувачів"

#: includes/fields/class-acf-field-user.php:52
msgid "Filter by role"
msgstr "Фільтр за ролями"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:101
msgid "User"
msgstr "Користувач"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Роздільник"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Select Color"
msgstr "Обрати колір"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Default"
msgstr "За замовчуванням"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Clear"
msgstr "Очистити"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Вибір кольору"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Обрати"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Готово"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Зараз"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Часовий пояс"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Мікросекунд"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Мілісекунд"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Секунд"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Хвилина"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Година"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Час"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Виберіть час"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Вибір дати і часу"

#: includes/fields/class-acf-field-accordion.php:103
msgid "Endpoint"
msgstr "Кінцева точка"

#: includes/admin/views/field-group-options.php:95
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Зліва"

#: includes/admin/views/field-group-options.php:94
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Зверху"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Розміщення"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Вкладка"

#: includes/fields/class-acf-field-url.php:159
msgid "Value must be a valid URL"
msgstr "Значення має бути адресою URl"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-link.php:174
msgid "Link URL"
msgstr "URL посилання"

#: includes/fields/class-acf-field-link.php:173
msgid "Link Array"
msgstr "Масив посилання"

#: includes/fields/class-acf-field-link.php:142
msgid "Opens in a new window/tab"
msgstr ""

#: includes/fields/class-acf-field-link.php:137
msgid "Select Link"
msgstr "Оберіть посилання"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Посилання"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-number.php:185
#: includes/fields/class-acf-field-range.php:214
msgid "Step Size"
msgstr "Розмір кроку"

#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-range.php:192
msgid "Maximum Value"
msgstr "Максимальне значення"

#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-range.php:181
msgid "Minimum Value"
msgstr "Мінімальне значення"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Діапазон (Range)"

#: includes/fields/class-acf-field-button-group.php:172
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-radio.php:217
#: includes/fields/class-acf-field-select.php:398
msgid "Both (Array)"
msgstr "Галочка"

#: includes/admin/views/field-group-fields.php:41
#: includes/fields/class-acf-field-button-group.php:171
#: includes/fields/class-acf-field-checkbox.php:374
#: includes/fields/class-acf-field-radio.php:216
#: includes/fields/class-acf-field-select.php:397
msgid "Label"
msgstr "Мітка"

#: includes/fields/class-acf-field-button-group.php:170
#: includes/fields/class-acf-field-checkbox.php:373
#: includes/fields/class-acf-field-radio.php:215
#: includes/fields/class-acf-field-select.php:396
msgid "Value"
msgstr "Значення"

#: includes/fields/class-acf-field-button-group.php:219
#: includes/fields/class-acf-field-checkbox.php:437
#: includes/fields/class-acf-field-radio.php:289
msgid "Vertical"
msgstr "Вертикально"

#: includes/fields/class-acf-field-button-group.php:218
#: includes/fields/class-acf-field-checkbox.php:438
#: includes/fields/class-acf-field-radio.php:290
msgid "Horizontal"
msgstr "Горизонтально"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "red : Червоний"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr "Для більшого контролю, Ви можете вказати маркувати значення:"

#: includes/fields/class-acf-field-button-group.php:145
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:190
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "У кожному рядку по варіанту"

#: includes/fields/class-acf-field-button-group.php:144
#: includes/fields/class-acf-field-checkbox.php:347
#: includes/fields/class-acf-field-radio.php:189
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "Варіанти вибору"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Група кнопок"

#: includes/fields/class-acf-field-page_link.php:488
#: includes/fields/class-acf-field-post_object.php:404
#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-user.php:83
msgid "Select multiple values?"
msgstr "Дозволити множинний вибір?"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-page_link.php:509
#: includes/fields/class-acf-field-post_object.php:426
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:428
#: includes/fields/class-acf-field-taxonomy.php:786
#: includes/fields/class-acf-field-user.php:104
msgid "Allow Null?"
msgstr "Дозволити порожнє значення?"

#: includes/fields/class-acf-field-page_link.php:249
#: includes/fields/class-acf-field-post_object.php:250
#: includes/fields/class-acf-field-taxonomy.php:932
msgid "Parent"
msgstr "Предок"

#: includes/fields/class-acf-field-wysiwyg.php:394
msgid "TinyMCE will not be initialized until field is clicked"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:393
msgid "Delay initialization?"
msgstr "Відкладена ініціалізація?"

#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Show Media Upload Buttons?"
msgstr "Показувати кнопки завантаження файлів?"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Toolbar"
msgstr "Верхня панель"

#: includes/fields/class-acf-field-wysiwyg.php:358
msgid "Text Only"
msgstr "Лише текст"

#: includes/fields/class-acf-field-wysiwyg.php:357
msgid "Visual Only"
msgstr "Візуальний лише"

#: includes/fields/class-acf-field-wysiwyg.php:356
msgid "Visual & Text"
msgstr "Візуальний і Текстовий"

#: includes/fields/class-acf-field-wysiwyg.php:351
msgid "Tabs"
msgstr "Вкладки"

#: includes/fields/class-acf-field-wysiwyg.php:289
msgid "Click to initialize TinyMCE"
msgstr "Натисніть, щоб ініціалізувати TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:283
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Текст"

#: includes/fields/class-acf-field-wysiwyg.php:282
msgid "Visual"
msgstr "Візуальний"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Візуальний редактор"

#: includes/fields/class-acf-field-text.php:180
#: includes/fields/class-acf-field-textarea.php:233
msgid "Value must not exceed %d characters"
msgstr "Значення не має перевищувати %d символів"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Щоб зняти обмеження — нічого не вказуйте тут"

#: includes/fields/class-acf-field-text.php:114
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Ліміт символів"

#: includes/fields/class-acf-field-email.php:155
#: includes/fields/class-acf-field-number.php:206
#: includes/fields/class-acf-field-password.php:102
#: includes/fields/class-acf-field-range.php:236
#: includes/fields/class-acf-field-text.php:155
msgid "Appears after the input"
msgstr "Розміщується в кінці поля"

#: includes/fields/class-acf-field-email.php:154
#: includes/fields/class-acf-field-number.php:205
#: includes/fields/class-acf-field-password.php:101
#: includes/fields/class-acf-field-range.php:235
#: includes/fields/class-acf-field-text.php:154
msgid "Append"
msgstr "Після поля"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:196
#: includes/fields/class-acf-field-password.php:92
#: includes/fields/class-acf-field-range.php:226
#: includes/fields/class-acf-field-text.php:145
msgid "Appears before the input"
msgstr "Розміщується на початку поля"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:195
#: includes/fields/class-acf-field-password.php:91
#: includes/fields/class-acf-field-range.php:225
#: includes/fields/class-acf-field-text.php:144
msgid "Prepend"
msgstr "Перед полем"

#: includes/fields/class-acf-field-email.php:135
#: includes/fields/class-acf-field-number.php:176
#: includes/fields/class-acf-field-password.php:82
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:153
#: includes/fields/class-acf-field-url.php:119
msgid "Appears within the input"
msgstr "Показується, якщо поле порожнє"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:175
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-text.php:134
#: includes/fields/class-acf-field-textarea.php:152
#: includes/fields/class-acf-field-url.php:118
msgid "Placeholder Text"
msgstr "Текст заповнювач"

#: includes/fields/class-acf-field-button-group.php:155
#: includes/fields/class-acf-field-email.php:115
#: includes/fields/class-acf-field-number.php:126
#: includes/fields/class-acf-field-radio.php:200
#: includes/fields/class-acf-field-range.php:162
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:101
#: includes/fields/class-acf-field-url.php:99
#: includes/fields/class-acf-field-wysiwyg.php:316
msgid "Appears when creating a new post"
msgstr "З'являється при створенні нового матеріалу"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Текст"

#: includes/fields/class-acf-field-relationship.php:760
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s вимагає принаймні %2$s вибір"
msgstr[1] "%1$s вимагає принаймні %2$s виділення"
msgstr[2] "%1$s вимагає принаймні %2$s виділень"

#: includes/fields/class-acf-field-post_object.php:395
#: includes/fields/class-acf-field-relationship.php:622
msgid "Post ID"
msgstr "ID Запису"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:394
#: includes/fields/class-acf-field-relationship.php:621
msgid "Post Object"
msgstr "Об’єкт запису"

#: includes/fields/class-acf-field-relationship.php:654
msgid "Maximum posts"
msgstr "Максимум матеріалів"

#: includes/fields/class-acf-field-relationship.php:644
msgid "Minimum posts"
msgstr "Мінімум записів"

#: includes/admin/views/field-group-options.php:148
#: includes/fields/class-acf-field-relationship.php:679
msgid "Featured Image"
msgstr "Головне зображення"

#: includes/fields/class-acf-field-relationship.php:675
msgid "Selected elements will be displayed in each result"
msgstr "Вибрані елементи будуть відображені в кожному результаті"

#: includes/fields/class-acf-field-relationship.php:674
msgid "Elements"
msgstr "Елементи"

#: includes/fields/class-acf-field-relationship.php:608
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:706
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Таксономія"

#: includes/fields/class-acf-field-relationship.php:607
#: includes/locations/class-acf-location-post-type.php:22
msgid "Post Type"
msgstr "Тип запису"

#: includes/fields/class-acf-field-relationship.php:601
msgid "Filters"
msgstr "Фільтри"

#: includes/fields/class-acf-field-page_link.php:470
#: includes/fields/class-acf-field-post_object.php:382
#: includes/fields/class-acf-field-relationship.php:594
msgid "All taxonomies"
msgstr "Всі таксономії"

#: includes/fields/class-acf-field-page_link.php:462
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:586
msgid "Filter by Taxonomy"
msgstr "Фільтр за типом таксономією"

#: includes/fields/class-acf-field-page_link.php:455
#: includes/fields/class-acf-field-post_object.php:367
#: includes/fields/class-acf-field-relationship.php:579
msgid "All post types"
msgstr "Всі типи матеріалів"

#: includes/fields/class-acf-field-page_link.php:447
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:571
msgid "Filter by Post Type"
msgstr "Фільтр за типом матеріалу"

#: includes/fields/class-acf-field-relationship.php:469
msgid "Search..."
msgstr "Шукати..."

#: includes/fields/class-acf-field-relationship.php:399
msgid "Select taxonomy"
msgstr "Оберіть таксономію"

#: includes/fields/class-acf-field-relationship.php:390
msgid "Select post type"
msgstr "Вибір типу матеріалу"

#: includes/fields/class-acf-field-relationship.php:65
#: assets/build/js/acf-input.js:3917 assets/build/js/acf-input.js:4195
msgid "No matches found"
msgstr "Співпадінь не знайдено"

#: includes/fields/class-acf-field-relationship.php:64
#: assets/build/js/acf-input.js:3900 assets/build/js/acf-input.js:4174
msgid "Loading"
msgstr "Завантаження"

#: includes/fields/class-acf-field-relationship.php:63
#: assets/build/js/acf-input.js:3809 assets/build/js/acf-input.js:4070
msgid "Maximum values reached ( {max} values )"
msgstr "Досягнуто максимальних значень ( {max} values )"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Зв'язок"

#: includes/fields/class-acf-field-file.php:288
#: includes/fields/class-acf-field-image.php:314
msgid "Comma separated list. Leave blank for all types"
msgstr "Перелік, розділений комами. Залиште порожнім для всіх типів"

#: includes/fields/class-acf-field-file.php:287
#: includes/fields/class-acf-field-image.php:313
msgid "Allowed file types"
msgstr "Дозволені типи файлів"

#: includes/fields/class-acf-field-file.php:275
#: includes/fields/class-acf-field-image.php:277
msgid "Maximum"
msgstr "Максимум"

#: includes/fields/class-acf-field-file.php:151
#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-file.php:279
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-image.php:304
msgid "File size"
msgstr "Розмір файлу"

#: includes/fields/class-acf-field-image.php:242
#: includes/fields/class-acf-field-image.php:278
msgid "Restrict which images can be uploaded"
msgstr "Обмежте, які зображення можна завантажувати"

#: includes/fields/class-acf-field-file.php:263
#: includes/fields/class-acf-field-image.php:241
msgid "Minimum"
msgstr "Мінімум"

#: includes/fields/class-acf-field-file.php:232
#: includes/fields/class-acf-field-image.php:207
msgid "Uploaded to post"
msgstr "Завантажено до матеріалу"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:206
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Все"

#: includes/fields/class-acf-field-file.php:226
#: includes/fields/class-acf-field-image.php:201
msgid "Limit the media library choice"
msgstr "Обмежте вибір медіатеки"

#: includes/fields/class-acf-field-file.php:225
#: includes/fields/class-acf-field-image.php:200
msgid "Library"
msgstr "Бібліотека"

#: includes/fields/class-acf-field-image.php:333
msgid "Preview Size"
msgstr "Розмір мініатюр"

#: includes/fields/class-acf-field-image.php:192
msgid "Image ID"
msgstr "ID зображення"

#: includes/fields/class-acf-field-image.php:191
msgid "Image URL"
msgstr "URL зображення"

#: includes/fields/class-acf-field-image.php:190
msgid "Image Array"
msgstr "Масив зображення"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:368
#: includes/fields/class-acf-field-file.php:210
#: includes/fields/class-acf-field-link.php:168
#: includes/fields/class-acf-field-radio.php:210
msgid "Specify the returned value on front end"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:367
#: includes/fields/class-acf-field-file.php:209
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-taxonomy.php:750
msgid "Return Value"
msgstr "Повернення значення"

#: includes/fields/class-acf-field-image.php:159
msgid "Add Image"
msgstr "Додати зображення"

#: includes/fields/class-acf-field-image.php:159
msgid "No image selected"
msgstr "Зображення не вибране"

#: includes/assets.php:351 includes/fields/class-acf-field-file.php:159
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:142 assets/build/js/acf.js:1523
#: assets/build/js/acf.js:1603
msgid "Remove"
msgstr "Видалити"

#: includes/admin/views/field-group-field.php:65
#: includes/fields/class-acf-field-file.php:157
#: includes/fields/class-acf-field-image.php:137
#: includes/fields/class-acf-field-link.php:142
msgid "Edit"
msgstr "Редагувати"

#: includes/fields/class-acf-field-image.php:67 includes/media.php:55
#: assets/build/js/acf-input.js:6792 assets/build/js/acf-input.js:7267
msgid "All images"
msgstr "Усі зображення"

#: includes/fields/class-acf-field-image.php:66
#: assets/build/js/acf-input.js:3173 assets/build/js/acf-input.js:3386
msgid "Update Image"
msgstr "Оновити зображення"

#: includes/fields/class-acf-field-image.php:65
#: assets/build/js/acf-input.js:3172 assets/build/js/acf-input.js:3385
msgid "Edit Image"
msgstr "Редагувати зображення"

#: includes/fields/class-acf-field-image.php:64
#: assets/build/js/acf-input.js:3148 assets/build/js/acf-input.js:3360
msgid "Select Image"
msgstr "Обрати зображення"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Зображення"

#: includes/fields/class-acf-field-message.php:123
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""

#: includes/fields/class-acf-field-message.php:122
msgid "Escape HTML"
msgstr ""

#: includes/fields/class-acf-field-message.php:114
#: includes/fields/class-acf-field-textarea.php:169
msgid "No Formatting"
msgstr "Без форматування"

#: includes/fields/class-acf-field-message.php:113
#: includes/fields/class-acf-field-textarea.php:168
msgid "Automatically add &lt;br&gt;"
msgstr "Автоматичне перенесення рядків (додається теґ &lt;br&gt;)"

#: includes/fields/class-acf-field-message.php:112
#: includes/fields/class-acf-field-textarea.php:167
msgid "Automatically add paragraphs"
msgstr "Автоматично додавати абзаци"

#: includes/fields/class-acf-field-message.php:108
#: includes/fields/class-acf-field-textarea.php:163
msgid "Controls how new lines are rendered"
msgstr "Вкажіть спосіб обробки нових рядків"

#: includes/fields/class-acf-field-message.php:107
#: includes/fields/class-acf-field-textarea.php:162
msgid "New Lines"
msgstr "Перенесення рядків"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "Тиждень починається з"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "Зберегти формат"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Wk"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Попередній"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Далі"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Сьогодні"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Готово"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Вибір дати"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:281
#: includes/fields/class-acf-field-oembed.php:265
msgid "Width"
msgstr "Ширина"

#: includes/fields/class-acf-field-oembed.php:262
#: includes/fields/class-acf-field-oembed.php:274
msgid "Embed Size"
msgstr "Розмір вставки"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "Введіть URL"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr "Текст відображається, коли неактивний"

#: includes/fields/class-acf-field-true_false.php:180
msgid "Off Text"
msgstr "Текст вимкнено"

#: includes/fields/class-acf-field-true_false.php:165
msgid "Text shown when active"
msgstr "Текст відображається, коли активний"

#: includes/fields/class-acf-field-true_false.php:164
msgid "On Text"
msgstr "На тексті"

#: includes/fields/class-acf-field-select.php:449
#: includes/fields/class-acf-field-true_false.php:196
msgid "Stylized UI"
msgstr "Стилізований інтерфейс користувача"

#: includes/fields/class-acf-field-button-group.php:154
#: includes/fields/class-acf-field-checkbox.php:357
#: includes/fields/class-acf-field-color_picker.php:155
#: includes/fields/class-acf-field-email.php:114
#: includes/fields/class-acf-field-number.php:125
#: includes/fields/class-acf-field-radio.php:199
#: includes/fields/class-acf-field-range.php:161
#: includes/fields/class-acf-field-select.php:379
#: includes/fields/class-acf-field-text.php:94
#: includes/fields/class-acf-field-textarea.php:100
#: includes/fields/class-acf-field-true_false.php:144
#: includes/fields/class-acf-field-url.php:98
#: includes/fields/class-acf-field-wysiwyg.php:315
msgid "Default Value"
msgstr "Значення за замовчуванням"

#: includes/fields/class-acf-field-true_false.php:135
msgid "Displays text alongside the checkbox"
msgstr "Відображати текст поруч із прапорцем"

#: includes/fields/class-acf-field-message.php:26
#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-true_false.php:134
msgid "Message"
msgstr "Повідомлення"

#: includes/assets.php:350 includes/fields/class-acf-field-true_false.php:83
#: includes/fields/class-acf-field-true_false.php:184
#: assets/build/js/acf.js:1700 assets/build/js/acf.js:1803
msgid "No"
msgstr "Ні"

#: includes/assets.php:349 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:168
#: assets/build/js/acf.js:1699 assets/build/js/acf.js:1802
msgid "Yes"
msgstr "Так"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Так / Ні"

#: includes/fields/class-acf-field-group.php:471
msgid "Row"
msgstr "Рядок"

#: includes/fields/class-acf-field-group.php:470
msgid "Table"
msgstr "Таблиця"

#: includes/fields/class-acf-field-group.php:469
msgid "Block"
msgstr "Блок"

#: includes/fields/class-acf-field-group.php:464
msgid "Specify the style used to render the selected fields"
msgstr "Укажіть стиль для візуалізації вибраних полів"

#: includes/fields.php:359 includes/fields/class-acf-field-button-group.php:212
#: includes/fields/class-acf-field-checkbox.php:431
#: includes/fields/class-acf-field-group.php:463
#: includes/fields/class-acf-field-radio.php:283
msgid "Layout"
msgstr "Компонування"

#: includes/fields/class-acf-field-group.php:447
msgid "Sub Fields"
msgstr "Підполя"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Група"

#: includes/fields/class-acf-field-google-map.php:232
msgid "Customize the map height"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:231
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:292
#: includes/fields/class-acf-field-oembed.php:277
msgid "Height"
msgstr "Висота"

#: includes/fields/class-acf-field-google-map.php:220
msgid "Set the initial zoom level"
msgstr "Встановіть початковий рівень масштабування"

#: includes/fields/class-acf-field-google-map.php:219
msgid "Zoom"
msgstr "Збільшити"

#: includes/fields/class-acf-field-google-map.php:193
#: includes/fields/class-acf-field-google-map.php:206
msgid "Center the initial map"
msgstr "Відцентруйте початкову карту"

#: includes/fields/class-acf-field-google-map.php:192
#: includes/fields/class-acf-field-google-map.php:205
msgid "Center"
msgstr "Центр"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Search for address..."
msgstr "Шукати адресу..."

#: includes/fields/class-acf-field-google-map.php:157
msgid "Find current location"
msgstr "Знайдіть поточне місце розташування"

#: includes/fields/class-acf-field-google-map.php:156
msgid "Clear location"
msgstr "Очистити розміщення"

#: includes/fields/class-acf-field-google-map.php:155
#: includes/fields/class-acf-field-relationship.php:606
msgid "Search"
msgstr "Пошук"

#: includes/fields/class-acf-field-google-map.php:60
#: assets/build/js/acf-input.js:2832 assets/build/js/acf-input.js:3013
msgid "Sorry, this browser does not support geolocation"
msgstr "Вибачте, цей браузер не підтримує автоматичне визначення локації"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:129
msgid "The format returned via template functions"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:197
#: includes/fields/class-acf-field-image.php:184
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:616
#: includes/fields/class-acf-field-select.php:390
#: includes/fields/class-acf-field-time_picker.php:128
#: includes/fields/class-acf-field-user.php:67
msgid "Return Format"
msgstr "Формат повернення"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-time_picker.php:120
#: includes/fields/class-acf-field-time_picker.php:136
msgid "Custom:"
msgstr "Користувацький:"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-time_picker.php:113
msgid "The format displayed when editing a post"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:179
#: includes/fields/class-acf-field-time_picker.php:112
msgid "Display Format"
msgstr "Формат показу"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Вибір часу"

#. translators: counts for inactive field groups
#: acf.php:454
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Неактивний <span class=\"count\">(%s)</span>"
msgstr[1] "Неактивні <span class=\"count\">(%s)</span>"
msgstr[2] "Неактивних <span class=\"count\">(%s)</span>"

#: acf.php:413
msgid "No Fields found in Trash"
msgstr "Не знайдено полів у кошику"

#: acf.php:412
msgid "No Fields found"
msgstr "Не знайдено полів"

#: acf.php:411
msgid "Search Fields"
msgstr "Шукати поля"

#: acf.php:410
msgid "View Field"
msgstr "Переглянути\t поле"

#: acf.php:409 includes/admin/views/field-group-fields.php:104
msgid "New Field"
msgstr "Нове поле"

#: acf.php:408
msgid "Edit Field"
msgstr "Редагувати поле"

#: acf.php:407
msgid "Add New Field"
msgstr "Додати нове поле"

#: acf.php:405
msgid "Field"
msgstr "Поле"

#: acf.php:404 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:287
#: includes/admin/views/field-group-fields.php:21
msgid "Fields"
msgstr "Поля"

#: acf.php:379
msgid "No Field Groups found in Trash"
msgstr "У кошику немає груп полів"

#: acf.php:378
msgid "No Field Groups found"
msgstr "Не знайдено груп полів"

#: acf.php:377
msgid "Search Field Groups"
msgstr "Шукати групи полів"

#: acf.php:376
msgid "View Field Group"
msgstr "Переглянути групу полів"

#: acf.php:375
msgid "New Field Group"
msgstr "Нова група полів"

#: acf.php:374
msgid "Edit Field Group"
msgstr "Редагувати групу полів"

#: acf.php:373
msgid "Add New Field Group"
msgstr "Додати нову групу полів"

#: acf.php:372 acf.php:406 includes/admin/admin.php:51
msgid "Add New"
msgstr "Додати новий"

#: acf.php:371
msgid "Field Group"
msgstr "Група полів"

#: acf.php:370 includes/admin/admin.php:50
msgid "Field Groups"
msgstr "Групи полів"

#. Description of the plugin
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Налаштуйте WordPress за допомогою потужних, професійних та інтуїтивно "
"зрозумілих полів."

#. Plugin URI of the plugin
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php:91
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:449 includes/admin/admin-field-group.php:390
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Неактивно"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "Перемістити в кошик. Ви впевнені?"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr ""

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "Батьківські поля"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr ""

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Поле «%s» можете знайти у групі «%s»"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "Закрити вікно"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr ""

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr ""

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Статус"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""
"Розширте можливості WordPress за допомогою потужних, професійних та "
"інтуїтивно зрозумілих полів."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Список змін"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Перегляньте що нового у <a href=%s>версії %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Документація"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Сайт"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Документація"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Підтримка"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "Про"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Спасибі за використання <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:768
msgid "Synchronise field group"
msgstr ""

#: includes/admin/admin-field-groups.php:780
msgid "Apply"
msgstr "Застосувати"

#: includes/admin/admin-field-groups.php:798
msgid "Bulk Actions"
msgstr "Масові дії"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr ""

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Доповнення"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr ""

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Інформація"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Що нового"

#: includes/admin/views/field-group-field.php:108
msgid "Required?"
msgstr "Обов’язкове?"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Ще немає полів. Для створення полів натисніть <strong>+ Додати поле</strong>."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Додати поле"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr ""

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr ""

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Оновлення завершено"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr ""

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Завантажити і встановити"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Встановлено"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Вітаємо у Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr "Дякуємо за оновлення! ACF %s став ще кращим!"

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr ""

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr ""

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr ""

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr ""

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "До побачення доповнення. Привіт PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr ""

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Потужні можливості"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Прочитайте більше про <a href=\"%s\">можливості ACF PRO</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Легке оновлення"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Під капотом"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr ""

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr ""

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Більше AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr ""

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr ""

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr ""

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr ""

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr ""

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr ""

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Нові форми"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr ""

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Нова галерея"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr ""

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Нові налаштування"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr ""

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr ""

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "Поліпшена перевірка"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr "Перевірка форми відбувається на PHP + AJAX"

#: includes/admin/views/settings-info.php:132
#, fuzzy
msgid "Relationship Field"
msgstr "Закрити поле"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Переміщення полів"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr ""

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Краща сторінка опцій"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Думаємо, Вам сподобаються зміни у %s."

#: includes/admin/views/settings-tools-export.php:23
msgid "Export Field Groups to PHP"
msgstr "Експортувати групи полів в код PHP"

#: includes/admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Виберіть групи полів, які Ви хочете експортувати, а далі оберіть бажаний "
"метод експорту. Використовуйте кнопку завантаження для експорту в файл ."
"json, який можна імпортувати до іншої інсталяції ACF. Використовуйте кнопку "
"генерації для експорту в код PHP, який ви можете розмістити у своїй темі."

#: includes/admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "Завантажити файл експорту"

#: includes/admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "Створити код експорту"

#: includes/admin/views/settings-tools.php:67
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Виберіть JSON файл, який Ви хотіли б імпортувати. При натисканні кнопки "
"імпорту, нижче, ACF буде імпортовано групи полів."

#: includes/api/api-helpers.php:3934
#, php-format
msgid "File size must must not exceed %s."
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:353
#, fuzzy
#| msgid "Allow Null?"
msgid "Allow Custom"
msgstr "Дозволити порожнє значення?"

#: includes/fields/class-acf-field-checkbox.php:364
#, fuzzy
#| msgid "Move Custom Field"
msgid "Save Custom"
msgstr "Перемістити поле"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Toggle"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Поточна колір"

#: includes/fields/class-acf-field-google-map.php:40
msgid "Locating"
msgstr "Розміщення"

#: includes/fields/class-acf-field-google-map.php:181
msgid "Customise the map height"
msgstr "Налаштуйте висоту карти"

#: includes/fields/class-acf-field-image.php:204
msgid "Shown when entering data"
msgstr ""

#: includes/fields/class-acf-field-oembed.php:234
#: includes/fields/class-acf-field-taxonomy.php:898
msgid "Error."
msgstr "Помилка."

#: includes/fields/class-acf-field-oembed.php:234
msgid "No embed found for the given URL."
msgstr ""

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Інше"

#: includes/fields/class-acf-field-radio.php:265
#, fuzzy
msgid "Save Other"
msgstr "Зберегти інше"

#: includes/fields/class-acf-field-relationship.php:37
msgid "Minimum values reached ( {min} values )"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:790
#: pro/fields/class-acf-field-gallery.php:800
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Покращений стиль"

#: includes/fields/class-acf-field-tab.php:82
msgid ""
"The tab field will display incorrectly when added to a Table style repeater "
"field or flexible content field layout"
msgstr ""

#: includes/fields/class-acf-field-tab.php:83
msgid ""
"Use \"Tab Fields\" to better organize your edit screen by grouping fields "
"together."
msgstr ""

#: includes/fields/class-acf-field-tab.php:84
msgid ""
"All fields following this \"tab field\" (or until another \"tab field\" is "
"defined) will be grouped together using this field's label as the tab "
"heading."
msgstr ""

#: includes/fields/class-acf-field-tab.php:110
msgid "End-point"
msgstr ""

#: includes/fields/class-acf-field-tab.php:111
msgid "Use this field as an end-point and start a new group of tabs"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:713
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "None"
msgstr "Нічого"

#: includes/fields/class-acf-field-wysiwyg.php:454
msgid "TinyMCE will not be initalized until field is clicked"
msgstr ""

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "Термін таксономії"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Додаткові поля Pro"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Опублікувати"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Немає полів для цієї сторінки опцій. <a href=\"%s\">Створити групу "
"додаткових полів</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Помилка</b>. Неможливо під’єднатися до сервера оновлення"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Деактивувати ліцензію"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Активувати ліцензію"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Інформація про ліцензію"

#: pro/admin/views/html-settings-updates.php:20
#, fuzzy, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Щоб розблокувати оновлення, будь ласка, введіть код ліцензії. Якщо не маєте "
"ліцензії, перегляньте"

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Код ліцензії"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Інформація про оновлення"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Поточна версія"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Остання версія"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Доступні оновлення"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Оновити плаґін"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Будь ласка, введіть код ліцензії, щоб розблокувати оновлення"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Перевірити знову"

#: pro/admin/views/html-settings-updates.php:117
#, fuzzy
msgid "Upgrade Notice"
msgstr "Оновити базу даних"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Клон"

#: pro/fields/class-acf-field-clone.php:808
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:825
msgid "Display"
msgstr "Таблиця"

#: pro/fields/class-acf-field-clone.php:826
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:831
#, fuzzy
#| msgid "Please select the field group you wish this field to move to"
msgid "Group (displays selected fields in a group within this field)"
msgstr "Будь ласка, оберіть групу полів куди Ви хочете перемістити це поле"

#: pro/fields/class-acf-field-clone.php:832
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:853
#, php-format
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:856
#, fuzzy
#| msgid "Field Label"
msgid "Prefix Field Labels"
msgstr "Назва поля"

#: pro/fields/class-acf-field-clone.php:867
#, php-format
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:870
#, fuzzy
#| msgid "Field Name"
msgid "Prefix Field Names"
msgstr "Ярлик"

#: pro/fields/class-acf-field-clone.php:988
msgid "Unknown field"
msgstr "Невідоме поле"

#: pro/fields/class-acf-field-clone.php:1027
#, fuzzy
msgid "Unknown field group"
msgstr "Редагувати групу полів"

#: pro/fields/class-acf-field-clone.php:1031
#, php-format
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:174
#: pro/fields/class-acf-field-repeater.php:470
msgid "Add Row"
msgstr "Додати рядок"

#: pro/fields/class-acf-field-flexible-content.php:34
#, fuzzy
msgid "layout"
msgstr "Шаблон структури"

#: pro/fields/class-acf-field-flexible-content.php:35
#, fuzzy
msgid "layouts"
msgstr "Шаблон структури"

#: pro/fields/class-acf-field-flexible-content.php:36
msgid "remove {layout}?"
msgstr "видалити {layout}?"

#: pro/fields/class-acf-field-flexible-content.php:37
msgid "This field requires at least {min} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:38
msgid "This field has a limit of {max} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:39
msgid "This field requires at least {min} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:40
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:41
msgid "{available} {label} {identifier} available (max {max})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:42
msgid "{required} {label} {identifier} required (min {min})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:43
msgid "Flexible Content requires at least 1 layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:273
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:406
msgid "Add layout"
msgstr "Додати шаблон"

#: pro/fields/class-acf-field-flexible-content.php:407
msgid "Remove layout"
msgstr "Видалити шаблон"

#: pro/fields/class-acf-field-flexible-content.php:408
#: pro/fields/class-acf-field-repeater.php:298
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Delete Layout"
msgstr "Видалити шаблон"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Duplicate Layout"
msgstr "Дублювати шаблон"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Add New Layout"
msgstr "Додати новий шаблон"

#: pro/fields/class-acf-field-flexible-content.php:628
msgid "Min"
msgstr "Мін."

#: pro/fields/class-acf-field-flexible-content.php:641
msgid "Max"
msgstr "Макс."

#: pro/fields/class-acf-field-flexible-content.php:668
#: pro/fields/class-acf-field-repeater.php:466
msgid "Button Label"
msgstr "Текст для кнопки"

#: pro/fields/class-acf-field-flexible-content.php:677
msgid "Minimum Layouts"
msgstr "Мінімум шаблонів"

#: pro/fields/class-acf-field-flexible-content.php:686
msgid "Maximum Layouts"
msgstr "Максимум шаблонів"

#: pro/fields/class-acf-field-gallery.php:41
msgid "Add Image to Gallery"
msgstr "Додати зображення до галереї"

#: pro/fields/class-acf-field-gallery.php:45
#, fuzzy
msgid "Maximum selection reached"
msgstr "Досягнуто максимального вибору"

#: pro/fields/class-acf-field-gallery.php:321
msgid "Length"
msgstr "Довжина"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Підпис"

#: pro/fields/class-acf-field-gallery.php:373
msgid "Alt Text"
msgstr "Альтернативний текст"

#: pro/fields/class-acf-field-gallery.php:544
msgid "Add to gallery"
msgstr "Додати до галереї"

#: pro/fields/class-acf-field-gallery.php:548
msgid "Bulk actions"
msgstr "Масові дії"

#: pro/fields/class-acf-field-gallery.php:549
msgid "Sort by date uploaded"
msgstr "Сортувати за датою завантаження"

#: pro/fields/class-acf-field-gallery.php:550
msgid "Sort by date modified"
msgstr "Сортувати за датою зміни"

#: pro/fields/class-acf-field-gallery.php:551
msgid "Sort by title"
msgstr "Сортувати за назвою"

#: pro/fields/class-acf-field-gallery.php:552
msgid "Reverse current order"
msgstr "Зворотній поточний порядок"

#: pro/fields/class-acf-field-gallery.php:570
msgid "Close"
msgstr "Закрити"

#: pro/fields/class-acf-field-gallery.php:624
msgid "Minimum Selection"
msgstr "Мінімальна вибірка"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Maximum Selection"
msgstr "Максимальна вибірка"

#: pro/fields/class-acf-field-gallery.php:642
msgid "Insert"
msgstr "Вставити"

#: pro/fields/class-acf-field-gallery.php:643
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:647
msgid "Append to the end"
msgstr "Розміщується в кінці"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:36
msgid "Minimum rows reached ({min} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:37
msgid "Maximum rows reached ({max} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:343
msgid "Add row"
msgstr "Додати рядок"

#: pro/fields/class-acf-field-repeater.php:344
msgid "Remove row"
msgstr "Видалити рядок"

#: pro/fields/class-acf-field-repeater.php:419
#, fuzzy
#| msgid "Collapse Details"
msgid "Collapsed"
msgstr "Сховати деталі"

#: pro/fields/class-acf-field-repeater.php:420
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:430
msgid "Minimum Rows"
msgstr "Мінімум рядків"

#: pro/fields/class-acf-field-repeater.php:440
msgid "Maximum Rows"
msgstr "Максимум рядків"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr ""

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Опції оновлено"

#: pro/updates.php:97
#, fuzzy, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Щоб розблокувати оновлення, будь ласка, введіть код ліцензії. Якщо не маєте "
"ліцензії, перегляньте"

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr ""

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr ""

#~ msgid "Gallery Field"
#~ msgstr "Поле галереї"

#~ msgid "See what's new in"
#~ msgstr "Перегляньте, що нового у"

#~ msgid "version"
#~ msgstr "версії"

#~ msgid "Getting Started"
#~ msgstr "Початок роботи"

#~ msgid "Field Types"
#~ msgstr "Типи полів"

#~ msgid "Functions"
#~ msgstr "Функції"

#~ msgid "Actions"
#~ msgstr "Дії"

#~ msgid "'How to' guides"
#~ msgstr "Інструкції «як зробити»"

#~ msgid "Tutorials"
#~ msgstr "Документація"

#~ msgid "Created by"
#~ msgstr "Плаґін створив"

#~ msgid "Upgrade"
#~ msgstr "Оновити"

#~ msgid "Error"
#~ msgstr "Помилка"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Поля можна перетягувати"

#~ msgid "See what's new"
#~ msgstr "Перегляньте, що нового"

#~ msgid "Show a different month"
#~ msgstr "Показати інший місяць"

#~ msgid "Return format"
#~ msgstr "Формат повернення"

#~ msgid "uploaded to this post"
#~ msgstr "завантажено до цього матеріалу"

#~ msgid "File Size"
#~ msgstr "Розмір файлу"

#~ msgid "No File selected"
#~ msgstr "Файл не обрано"

#~ msgid "Warning"
#~ msgstr "Застереження"

#~ msgid "eg. Show extra content"
#~ msgstr "напр., Показати додаткові поля"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Помилка з’єднання</b>. Спробуйте знову"

#~ msgid "Save Options"
#~ msgstr "Зберегти опції"

#~ msgid "License"
#~ msgstr "Ліцензія"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Щоб розблокувати оновлення, будь ласка, введіть код ліцензії. Якщо не "
#~ "маєте ліцензії, перегляньте"

#~ msgid "details & pricing"
#~ msgstr "деталі і ціни"

#~ msgid "Hide / Show All"
#~ msgstr "Сховати / Показати все"

#~ msgid "Show Field Keys"
#~ msgstr "Показати ключі полів"

#~ msgid "Pending Review"
#~ msgstr "Очікує затвердження"

#~ msgid "Draft"
#~ msgstr "Чернетка"

#~ msgid "Future"
#~ msgstr "Заплановано"

#~ msgid "Private"
#~ msgstr "Приватний"

#~ msgid "Revision"
#~ msgstr "Ревізія"

#~ msgid "Trash"
#~ msgstr "В кошику"

#~ msgid "Import / Export"
#~ msgstr "Імпорт / Експорт"

#, fuzzy
#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr "Чим меше число <br> тим вище розміщення"

#, fuzzy
#~ msgid "ACF PRO Required"
#~ msgstr "Обов’язкове?"

#~ msgid "Update Database"
#~ msgstr "Оновити базу даних"

#~ msgid "Data Upgrade"
#~ msgstr "Дані оновлено"

#~ msgid "Data upgraded successfully."
#~ msgstr "Дані успішно оновлено."

#~ msgid "Data is at the latest version."
#~ msgstr "Дані останньої версії."

#~ msgid "Load & Save Terms to Post"
#~ msgstr "Завантажити і зберегти значення до матеріалу"

#, fuzzy
#~ msgid "image"
#~ msgstr "Зображення"

#, fuzzy
#~ msgid "expand_details"
#~ msgstr "Показати деталі"

#, fuzzy
#~ msgid "collapse_details"
#~ msgstr "Сховати деталі"

#, fuzzy
#~ msgid "relationship"
#~ msgstr "Закрити поле"

#, fuzzy
#~ msgid "title_is_required"
#~ msgstr "Заголовок обов’язковий"

#, fuzzy
#~ msgid "move_field"
#~ msgstr "Перемістити поле"

#, fuzzy
#~ msgid "flexible_content"
#~ msgstr "Гнучкий вміст"

#, fuzzy
#~ msgid "gallery"
#~ msgstr "Галерея"

#, fuzzy
#~ msgid "Controls how HTML tags are rendered"
#~ msgstr "Вкажіть спосіб обробки нових рядків"

#~ msgid "Field&nbsp;Groups"
#~ msgstr "Групи полів"

#~ msgid "Attachment Details"
#~ msgstr "Деталі вкладення"

#~ msgid "Custom field updated."
#~ msgstr "Додаткове поле оновлено."

#~ msgid "Custom field deleted."
#~ msgstr "Додаткове поле видалено."

#~ msgid "Import/Export"
#~ msgstr "Імпорт/Експорт"

#~ msgid "Column Width"
#~ msgstr "Ширина колонки"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "Заповніть всі поля! Одне або декілька полів нижче не заповнено."

#~ msgid "Success"
#~ msgstr "Готово"

#~ msgid "Run the updater"
#~ msgstr "Запустити оновлення"

#~ msgid "Return to custom fields"
#~ msgstr "Повернутися до додаткових полів"

#~ msgid "Size"
#~ msgstr "Розмір"

#~ msgid "Formatting"
#~ msgstr "Форматування"

#~ msgid "Effects value on front end"
#~ msgstr "Як показувати на сайті"

#~ msgid "Convert HTML into tags"
#~ msgstr "Конвертувати в теґи HTML"

#~ msgid "Plain text"
#~ msgstr "Простий текст"

#~ msgid "1 image selected"
#~ msgstr "1 обране зображення"

#~ msgid "%d images selected"
#~ msgstr "%d вибраних зображень"

#~ msgid "Normal"
#~ msgstr "Стандартно"

#~ msgid ""
#~ "Read documentation, learn the functions and find some tips &amp; tricks "
#~ "for your next web project."
#~ msgstr ""
#~ "В документації ви знайдете детальний опис функцій та декілька порад і "
#~ "трюків для кращого використання плаґіну."

#~ msgid "Visit the ACF website"
#~ msgstr "Відвідайте сайт плаґіну"

#~ msgid "Export XML"
#~ msgstr "Експортувати XML"

#~ msgid "Copy the PHP code generated"
#~ msgstr "Скопіюйте згенерований код PHP"

#~ msgid "Paste into your functions.php file"
#~ msgstr "Вставте  у  <code>functions.php</code>"

#~ msgid "Create PHP"
#~ msgstr "Створити PHP"

#~ msgid "Back to settings"
#~ msgstr "Повернутися до налаштувань"

#~ msgid "requires a database upgrade"
#~ msgstr "потребує оновлення бази даних"

#~ msgid "why?"
#~ msgstr "для чого?"

#~ msgid "Please"
#~ msgstr "Будь ласка,"

#~ msgid "backup your database"
#~ msgstr "створіть резервну копію БД"

#~ msgid "then click"
#~ msgstr "і натискайте цю кнопку"

#~ msgid "Red"
#~ msgstr "Червоний"

#~ msgid "Blue"
#~ msgstr "Синій"

#~ msgid "blue : Blue"
#~ msgstr "blue : Синій"

#, fuzzy
#~ msgid "jQuery date formats"
#~ msgstr "Формат дати"

#~ msgid "File Updated."
#~ msgstr "Файл оновлено."

#~ msgid "+ Add Row"
#~ msgstr "+ Додати рядок"

#~ msgid "Field Order"
#~ msgstr "Порядок полів"

#, fuzzy
#~ msgid ""
#~ "No fields. Click the \"+ Add Sub Field button\" to create your first "
#~ "field."
#~ msgstr ""
#~ "Ще немає полів. Click the \"+ Add Sub Field button\" to create your first "
#~ "field."

#~ msgid "Edit this Field"
#~ msgstr "Редагувати це поле"

#~ msgid "Docs"
#~ msgstr "Документація"

#~ msgid "Close Sub Field"
#~ msgstr "Закрити дочірнє поле"

#~ msgid "+ Add Sub Field"
#~ msgstr "+ Додати дочірнє поле"

#~ msgid "Image Updated"
#~ msgstr "Зображення оновлено"

#~ msgid "Grid"
#~ msgstr "Плитка"

#~ msgid "List"
#~ msgstr "Список"

#~ msgid "Added"
#~ msgstr "Додано"

#~ msgid "Image Updated."
#~ msgstr "Зображення оновлено."

#~ msgid "Add selected Images"
#~ msgstr "Додати обрані зображення"

#~ msgid "Field Instructions"
#~ msgstr "Опис поля"

#~ msgid "Table (default)"
#~ msgstr "Таблиця (за замовчуванням)"

#~ msgid "Define how to render html tags"
#~ msgstr "Оберіть спосіб обробки теґів html"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "Оберіть спосіб обробки теґів html та переносу рядків"

#~ msgid "Run filter \"the_content\"?"
#~ msgstr "Застосовувати фільтр «the_content»?"

#~ msgid "Page Specific"
#~ msgstr "Сторінки"

#~ msgid "Post Specific"
#~ msgstr "Публікації"

#~ msgid "Taxonomy (Add / Edit)"
#~ msgstr "Тип таксономії (Додати / Редагувати)"

#~ msgid "Media (Edit)"
#~ msgstr "Медіафайл (Редагувати)"

#~ msgid "match"
#~ msgstr "має співпадати"

#~ msgid "all"
#~ msgstr "все"

#~ msgid "of the above"
#~ msgstr "з вищевказаних умов"

#~ msgid "Add Fields to Edit Screens"
#~ msgstr "Додайте поля на сторінку редагування вмісту"

#, fuzzy
#~ msgid "eg. dd/mm/yy. read more about"
#~ msgstr "Напр. dd/mm/yy. read more about"
