msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2020-08-20 08:47+0700\n"
"PO-Revision-Date: 2020-08-20 11:13+0700\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: id_ID\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.4.1\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:68
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:348 includes/admin/admin.php:49
msgid "Field Groups"
msgstr "Grup Bidang"

#: acf.php:349
msgid "Field Group"
msgstr "Grup Bidang"

#: acf.php:350 acf.php:382 includes/admin/admin.php:50
#: pro/fields/class-acf-field-flexible-content.php:559
msgid "Add New"
msgstr "Tambah Baru"

#: acf.php:351
msgid "Add New Field Group"
msgstr "Tambah Grup Bidang Baru"

#: acf.php:352
msgid "Edit Field Group"
msgstr "Sunting Grup Bidang"

#: acf.php:353
msgid "New Field Group"
msgstr "Grup Bidang Baru"

#: acf.php:354
msgid "View Field Group"
msgstr "Lihat Grup Bidang"

#: acf.php:355
msgid "Search Field Groups"
msgstr "Cari Grup Bidang"

#: acf.php:356
msgid "No Field Groups found"
msgstr "Tidak Ada Grup Bidang Ditemukan"

#: acf.php:357
msgid "No Field Groups found in Trash"
msgstr "Tidak Ditemukan Grup Bidang di Tong Sampah"

#: acf.php:380 includes/admin/admin-field-group.php:232
#: includes/admin/admin-field-groups.php:262
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Bidang"

#: acf.php:381
msgid "Field"
msgstr "Bidang"

#: acf.php:383
msgid "Add New Field"
msgstr "Tambah bidang baru"

#: acf.php:384
msgid "Edit Field"
msgstr "Sunting Bidang"

#: acf.php:385 includes/admin/views/field-group-fields.php:41
msgid "New Field"
msgstr "Bidang Baru"

#: acf.php:386
msgid "View Field"
msgstr "Lihat Bidang"

#: acf.php:387
msgid "Search Fields"
msgstr "Bidang Pencarian"

#: acf.php:388
msgid "No Fields found"
msgstr "Tidak ada bidang yang ditemukan"

#: acf.php:389
msgid "No Fields found in Trash"
msgstr "Tidak ada bidang yang ditemukan di tempat sampah"

#: acf.php:424 includes/admin/admin-field-groups.php:226
msgctxt "post status"
msgid "Disabled"
msgstr "Dimatikan"

#: acf.php:429
#, php-format
msgid "Disabled <span class=\"count\">(%s)</span>"
msgid_plural "Disabled <span class=\"count\">(%s)</span>"
msgstr[0] "Dimatikan <span class=\"count\">(%s)</span>"

#: includes/acf-field-functions.php:831
#: includes/admin/admin-field-group.php:178
msgid "(no label)"
msgstr "(tanpa label)"

#: includes/acf-field-group-functions.php:820
#: includes/admin/admin-field-group.php:180
msgid "copy"
msgstr "salin"

#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "Pos"

#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "Taksonomi"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Lampiran"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr "Komentar"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Widget"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:87
msgid "Menus"
msgstr "Menu"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Menu item"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Pengguna"

#: includes/acf-wp-functions.php:83 pro/options-page.php:51
msgid "Options"
msgstr "Pengaturan"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Blok"

#: includes/admin/admin-field-group.php:86
#: includes/admin/admin-field-group.php:87
#: includes/admin/admin-field-group.php:89
msgid "Field group updated."
msgstr "Grup bidang diperbarui."

#: includes/admin/admin-field-group.php:88
msgid "Field group deleted."
msgstr "Grup bidang dihapus."

#: includes/admin/admin-field-group.php:91
msgid "Field group published."
msgstr "Grup bidang diterbitkan."

#: includes/admin/admin-field-group.php:92
msgid "Field group saved."
msgstr "Grup bidang disimpan."

#: includes/admin/admin-field-group.php:93
msgid "Field group submitted."
msgstr "Grup bidang dikirim."

#: includes/admin/admin-field-group.php:94
msgid "Field group scheduled for."
msgstr "Grup bidang dijadwalkan untuk."

#: includes/admin/admin-field-group.php:95
msgid "Field group draft updated."
msgstr "Draft grup bidang diperbarui."

#: includes/admin/admin-field-group.php:171
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "String \"field_\" tidak dapat digunakan pada awal nama field"

#: includes/admin/admin-field-group.php:172
msgid "This field cannot be moved until its changes have been saved"
msgstr "Bidang ini tidak dapat dipindahkan sampai perubahan sudah disimpan"

#: includes/admin/admin-field-group.php:173
msgid "Field group title is required"
msgstr "Judul grup bidang diperlukan"

#: includes/admin/admin-field-group.php:174
msgid "Move to trash. Are you sure?"
msgstr "Pindahkan ke tong sampah. Yakin?"

#: includes/admin/admin-field-group.php:175
msgid "No toggle fields available"
msgstr "Tidak ada bidang toggle yang tersedia"

#: includes/admin/admin-field-group.php:176
msgid "Move Custom Field"
msgstr "Pindahkan Bidang Kustom"

#: includes/admin/admin-field-group.php:177
msgid "Checked"
msgstr "Diperiksa"

#: includes/admin/admin-field-group.php:179
msgid "(this field)"
msgstr "(bidang ini)"

#: includes/admin/admin-field-group.php:181
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3675
msgid "or"
msgstr "atau"

#: includes/admin/admin-field-group.php:182
msgid "Null"
msgstr "Nol"

#: includes/admin/admin-field-group.php:185
msgid "Has any value"
msgstr "Memiliki banyak nilai"

#: includes/admin/admin-field-group.php:186
msgid "Has no value"
msgstr "Tidak memiliki nilai"

#: includes/admin/admin-field-group.php:187
msgid "Value is equal to"
msgstr "Nilai sama dengan"

#: includes/admin/admin-field-group.php:188
msgid "Value is not equal to"
msgstr "Nilai tidak sama dengan"

#: includes/admin/admin-field-group.php:189
msgid "Value matches pattern"
msgstr "Nilai cocok dengan pola"

#: includes/admin/admin-field-group.php:190
msgid "Value contains"
msgstr "Nilai mengandung"

#: includes/admin/admin-field-group.php:191
msgid "Value is greater than"
msgstr "Nilai lebih besar dari"

#: includes/admin/admin-field-group.php:192
msgid "Value is less than"
msgstr "Nilai lebih kurang dari"

#: includes/admin/admin-field-group.php:193
msgid "Selection is greater than"
msgstr "Seleksi lebih besar dari"

#: includes/admin/admin-field-group.php:194
msgid "Selection is less than"
msgstr "Seleksi kurang dari"

#: includes/admin/admin-field-group.php:233
#: includes/admin/admin-field-groups.php:261
msgid "Location"
msgstr "Lokasi"

#: includes/admin/admin-field-group.php:234
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Pengaturan"

#: includes/admin/admin-field-group.php:384
msgid "Field Keys"
msgstr "Kunci Bidang"

#: includes/admin/admin-field-group.php:414
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Aktif"

#: includes/admin/admin-field-group.php:414
msgid "Inactive"
msgstr "Tidak Aktif"

#: includes/admin/admin-field-group.php:775
msgid "Move Complete."
msgstr "Pindah yang Lengkap."

#: includes/admin/admin-field-group.php:776
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Bidang %s sekarang dapat ditemukan di bidang grup %s"

#: includes/admin/admin-field-group.php:777
msgid "Close Window"
msgstr "Tutup window"

#: includes/admin/admin-field-group.php:818
msgid "Please select the destination for this field"
msgstr "Silakan pilih tujuan untuk bidang ini"

#: includes/admin/admin-field-group.php:825
msgid "Move Field"
msgstr "Pindahkan Bidang"

#: includes/admin/admin-field-groups.php:114
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktif <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:193
msgid "Review local JSON changes"
msgstr "Tinjau perubahan JSON lokal"

#: includes/admin/admin-field-groups.php:194
msgid "Loading diff"
msgstr "Memuat perbedaan"

#: includes/admin/admin-field-groups.php:195
#: includes/admin/admin-field-groups.php:529
msgid "Sync changes"
msgstr "Sinkronkan perubahan"

#: includes/admin/admin-field-groups.php:259
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
#: pro/fields/class-acf-field-gallery.php:380
msgid "Description"
msgstr "Deskripsi"

#: includes/admin/admin-field-groups.php:260
#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Kunci"

#: includes/admin/admin-field-groups.php:265
msgid "Local JSON"
msgstr "JSON Lokal"

#: includes/admin/admin-field-groups.php:415
msgid "Various"
msgstr "Berbagai"

#: includes/admin/admin-field-groups.php:437
#, php-format
msgid "Located in theme: %s"
msgstr "Terletak di tema: %s"

#: includes/admin/admin-field-groups.php:441
#, php-format
msgid "Located in plugin: %s"
msgstr "Terletak di plugin: %s"

#: includes/admin/admin-field-groups.php:445
#, php-format
msgid "Located in: %s"
msgstr "Terletak di: %s"

#: includes/admin/admin-field-groups.php:465
#: includes/admin/admin-field-groups.php:683
msgid "Sync available"
msgstr "Sinkronisasi tersedia"

#: includes/admin/admin-field-groups.php:468
msgid "Sync"
msgstr "Sinkronkan"

#: includes/admin/admin-field-groups.php:469
msgid "Review changes"
msgstr "Tinjau perubahan"

#: includes/admin/admin-field-groups.php:473
msgid "Import"
msgstr "Impor"

#: includes/admin/admin-field-groups.php:477
msgid "Saved"
msgstr "Disimpan"

#: includes/admin/admin-field-groups.php:480
msgid "Awaiting save"
msgstr "Awaiting disimpan"

#: includes/admin/admin-field-groups.php:501
msgid "Duplicate this item"
msgstr "Gandakan item ini"

#: includes/admin/admin-field-groups.php:501
#: includes/admin/admin-field-groups.php:521
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Duplicate"
msgstr "Gandakan"

#: includes/admin/admin-field-groups.php:551
#, php-format
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s grup bidang diduplikasi."

#: includes/admin/admin-field-groups.php:608
#, php-format
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s grup lapangan disinkronkan."

#: includes/admin/admin-field-groups.php:794
#, php-format
msgid "Select %s"
msgstr "Pilih %s"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Perkakas"

#: includes/admin/admin-upgrade.php:49 includes/admin/admin-upgrade.php:111
#: includes/admin/admin-upgrade.php:112 includes/admin/admin-upgrade.php:175
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Tingkatkan Database"

#: includes/admin/admin-upgrade.php:199
msgid "Review sites & upgrade"
msgstr "Meninjau situs & tingkatkan"

#: includes/admin/admin.php:48 includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr "Bidang Kustom"

#: includes/admin/admin.php:128 includes/admin/admin.php:130
msgid "Overview"
msgstr "Gambaran"

#: includes/admin/admin.php:131
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Plugin Advanced Custom Fields menyediakan pembuat formulir visual untuk "
"menyesuaikan layar sunting WordPress dengan bidang ekstra, dan API intuitif "
"untuk menampilkan nilai bidang khusus dalam file template tema apa pun."

#: includes/admin/admin.php:133
#, php-format
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Sebelum membuat Grup Bidang pertama Anda, sebaiknya baca panduan <a "
"href=“%s” target=“_blank”>Memulai </a> kami terlebih dahulu untuk "
"membiasakan diri Anda dengan filosofi dan praktik terbaik plugin."

#: includes/admin/admin.php:136
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Silakan gunakan tab Bantuan & Dukungan untuk menghubungi jika Anda merasa "
"membutuhkan bantuan."

#: includes/admin/admin.php:145 includes/admin/admin.php:147
msgid "Help & Support"
msgstr "Bantuan & Dukungan"

#: includes/admin/admin.php:148
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Kami sangat fanatik tentang dukungan, dan ingin Anda mendapatkan yang "
"terbaik dari situs web Anda dengan ACF. Jika Anda mengalami kesulitan, ada "
"beberapa tempat untuk mendapatkan bantuan:"

#: includes/admin/admin.php:151
#, php-format
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=“%s” target=“_blank”>Dokumentasi</a>. Dokumentasi ekstensif kami "
"berisi referensi dan panduan untuk sebagian besar situasi yang mungkin Anda "
"temui."

#: includes/admin/admin.php:155
#, php-format
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""
"<a href=“%s” target=“_blank”>Diskusi</a>. Kami memiliki komunitas yang aktif "
"dan ramah di Forum Komunitas kami yang mungkin dapat membantu Anda "
"mengetahui ‘cara’ dari dunia ACF."

#: includes/admin/admin.php:159
#, php-format
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=“%s” target=“_blank”>Help Desk</a>. Profesional dukungan di Help "
"Desk kami akan membantu Anda mengatasi tantangan teknis yang lebih mendalam."

#: includes/admin/admin.php:168
msgid "Information"
msgstr "Informasi"

#: includes/admin/admin.php:169
#, php-format
msgid "Version %s"
msgstr "Versi %s"

#: includes/admin/admin.php:170
msgid "View details"
msgstr "Lihat Rincian"

#: includes/admin/admin.php:171
msgid "Visit website"
msgstr "Kunjungi Website"

#: includes/admin/admin.php:200
#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:86
msgid "and"
msgstr "dan"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Ekspor Grup Bidang"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Hasilkan PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "Tidak ada grup bidang yang dipilih"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Mengekspor %s grup bidang."

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Pilih Grup Bidang"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Pilih grup bidang yang Anda ingin ekspor dan pilih metode ekspor. Gunakan "
"tombol unduh untuk ekspor ke file .json yang nantinya bisa Anda impor ke "
"instalasi ACF yang lain. Gunakan tombol hasilkan untuk ekspor ke kode PHP "
"yang bisa Anda simpan di tema Anda."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Ekspor Berkas"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Kode berikut dapat digunakan untuk mendaftarkan versi lokal dari grup bidang "
"yang dipilih. Grup bidang lokal dapat memberikan banyak manfaat seperti "
"waktu muat yang lebih cepat, kontrol versi & bidang / setelan dinamis. Cukup "
"salin dan tempel kode berikut ke file functions.php tema Anda atau sertakan "
"di dalam file eksternal."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "Menyalin ke clipboard"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "Tersalin"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Impor grup bidang"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Pilih berkas JSON Advanced Custom Fields yang ingin Anda impor. Ketika anda "
"mengklik tombol impor, ACF akan impor grup bidang."

#: includes/admin/tools/class-acf-admin-tool-import.php:52
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "Pilih Berkas"

#: includes/admin/tools/class-acf-admin-tool-import.php:62
msgid "Import File"
msgstr "Impor Berkas"

#: includes/admin/tools/class-acf-admin-tool-import.php:85
#: includes/fields/class-acf-field-file.php:169
msgid "No file selected"
msgstr "Tak ada file yang dipilih"

#: includes/admin/tools/class-acf-admin-tool-import.php:93
msgid "Error uploading file. Please try again"
msgstr "Kesalahan mengunggah file. Silakan coba lagi"

#: includes/admin/tools/class-acf-admin-tool-import.php:98
msgid "Incorrect file type"
msgstr "Jenis file salah"

#: includes/admin/tools/class-acf-admin-tool-import.php:107
msgid "Import file empty"
msgstr "File yang diimpor kosong"

#: includes/admin/tools/class-acf-admin-tool-import.php:138
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Mengimpor %s grup bidang"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Logika Kondisional"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "Tampilkan bidang ini jika"

#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Tambahkan peraturan grup"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:410
#: pro/fields/class-acf-field-repeater.php:299
msgid "Drag to reorder"
msgstr "Seret untuk menyusun ulang"

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "Sunting Bidang"

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:151
#: includes/fields/class-acf-field-image.php:131
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:337
msgid "Edit"
msgstr "Sunting"

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "Duplikat Bidang"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "Pindahkan Bidang ke grup lain"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "Pindahkan"

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "Hapus bidang"

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Delete"
msgstr "Hapus"

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "Label Bidang"

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "Ini nama yang akan muncul pada laman PENYUNTINGAN"

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "Nama Bidang"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Satu kata, tanpa spasi. Garis bawah dan strip dibolehkan"

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "Jenis Bidang"

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "Instruksi"

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instruksi untuk author. Terlihat ketika mengirim data"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Diperlukan?"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Atribut Wrapper"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "lebar"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "class"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "Tutup Bidang"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Suruh"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:433
#: pro/fields/class-acf-field-flexible-content.php:583
msgid "Label"
msgstr "Label"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:936
#: pro/fields/class-acf-field-flexible-content.php:597
msgid "Name"
msgstr "Nama"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tipe"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Tidak ada bidang. Klik tombol <strong>+ Tambah Bidang</strong> untuk membuat "
"bidang pertama Anda."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Tambah Bidang"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Peraturan"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Buat sekumpulan aturan untuk menentukan layar sunting mana yang akan "
"menggunakan bidang kustom lanjutan ini"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Gaya"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Standar (WP metabox)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Mulus (tanpa metabox)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Posisi"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Tinggi (setelah judul)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normal (setelah konten)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Samping"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Penempatan Label"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Selaras atas"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Selaras kiri"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Penempatan instruksi"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Di bawah label"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Di bawah bidang"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Urutan No."

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr ""
"Bidang kelompok dengan urutan yang lebih rendah akan muncul pertama kali"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Ditampilkan dalam daftar Grup bidang"

#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr "Permalink"

#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr "Konten Edior"

#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr "Kutipan"

#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr "Diskusi"

#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr "Revisi"

#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr "Penulis"

#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr "Atribut Laman"

#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:601
msgid "Featured Image"
msgstr "Gambar Fitur"

#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr "Kategori"

#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr "Tag"

#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr "Kirim Pelacakan"

#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr "Sembunyikan pada layar"

#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Pilih</b> item untuk <b>menyembunyikan</b> mereka dari layar penyuntingan."

#: includes/admin/views/field-group-options.php:129
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Jika beberapa kelompok bidang ditampilkan pada layar penyuntingan, pilihan "
"bidang kelompok yang pertama akan digunakan (yang memiliki nomor urutan "
"terendah)"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Situs-situs berikut memerlukan upgrade DB. Centang yang ingin Anda perbarui "
"dan kemudian klik %s."

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr "Perbarui Situs"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Situs"

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Situs memerlukan pembaruan database dari %s ke %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:76
msgid "Site is up to date"
msgstr "Situs ini dalam versi terbaru"

#: includes/admin/views/html-admin-page-upgrade-network.php:93
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Pembaruan database selesai. <a href=\"%s\">Kembali ke dasbor jaringan</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:113
msgid "Please select at least one site to upgrade."
msgstr "Pilih setidaknya satu situs untuk ditingkatkan."

#: includes/admin/views/html-admin-page-upgrade-network.php:117
#: includes/admin/views/html-notice-upgrade.php:38
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Sangat direkomendasikan untuk mencadangkan database situs anda sebelum "
"memproses. Apakah Anda yakin menjalankan pembaruan sekarang?"

#: includes/admin/views/html-admin-page-upgrade-network.php:144
#: includes/admin/views/html-admin-page-upgrade.php:31
#, php-format
msgid "Upgrading data to version %s"
msgstr "Meningkatkan data ke versi %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:158
msgid "Upgrade complete."
msgstr "Pembaruan selesai."

#: includes/admin/views/html-admin-page-upgrade-network.php:161
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "Pembaruan gagal."

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Membaca tugas upgrade..."

#: includes/admin/views/html-admin-page-upgrade.php:33
#, php-format
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Pembaruan database selesai. <a href=\"%s\">Lihat apa yang baru</a>"

#: includes/admin/views/html-admin-page-upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:32
msgid "No updates available."
msgstr "Pembaruan tidak tersedia ."

#: includes/admin/views/html-admin-tools.php:21
msgid "Back to all tools"
msgstr "Kembali ke semua alat"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Tampilkan grup bidang jika"

#: includes/admin/views/html-notice-upgrade.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Pengulang"

#: includes/admin/views/html-notice-upgrade.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Konten Fleksibel"

#: includes/admin/views/html-notice-upgrade.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galeri"

#: includes/admin/views/html-notice-upgrade.php:11
#: pro/locations/class-acf-location-options-page.php:20
msgid "Options Page"
msgstr "Opsi Laman"

#: includes/admin/views/html-notice-upgrade.php:21
msgid "Database Upgrade Required"
msgstr "Diperlukan Peningkatan Database"

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Terimakasih sudah memperbarui ke %s v%s!"

#: includes/admin/views/html-notice-upgrade.php:22
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Versi ini berisi perbaikan pada database Anda dan membutuhkan peningkatan."

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Harap periksa juga semua pengaya premium (%s) diperbarui ke versi terbaru."

#: includes/ajax/class-acf-ajax-local-json-diff.php:34
msgid "Invalid field group parameter(s)."
msgstr "Parameter grup bidang tidak valid."

#: includes/ajax/class-acf-ajax-local-json-diff.php:41
msgid "Invalid field group ID."
msgstr "ID grup bidang tidak valid."

#: includes/ajax/class-acf-ajax-local-json-diff.php:51
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr "Maaf, grup bidang ini tidak tersedia untuk perbandingan perbedaan."

#: includes/ajax/class-acf-ajax-local-json-diff.php:57
#, php-format
msgid "Last updated: %s"
msgstr "Terakhir diperbarui: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:62
msgid "Original field group"
msgstr "Grup bidang asli"

#: includes/ajax/class-acf-ajax-local-json-diff.php:66
msgid "JSON field group (newer)"
msgstr "Grup bidang JSON (lebih baru)"

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nonce tidak valid."

#: includes/api/api-helpers.php:844
msgid "Thumbnail"
msgstr "Thumbnail"

#: includes/api/api-helpers.php:845
msgid "Medium"
msgstr "Sedang"

#: includes/api/api-helpers.php:846
msgid "Large"
msgstr "Besar"

#: includes/api/api-helpers.php:895
msgid "Full Size"
msgstr "Ukuran Penuh"

#: includes/api/api-helpers.php:1632 includes/api/api-term.php:147
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(tanpa judul)"

#: includes/api/api-helpers.php:3596
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Lebar gambar setidaknya harus %dpx."

#: includes/api/api-helpers.php:3601
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Lebar gambar tidak boleh melebihi %dpx."

#: includes/api/api-helpers.php:3617
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Tinggi gambar setidaknya harus %dpx."

#: includes/api/api-helpers.php:3622
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Tinggi gambar tidak boleh melebihi %dpx."

#: includes/api/api-helpers.php:3640
#, php-format
msgid "File size must be at least %s."
msgstr "Ukuran file setidaknya harus %s."

#: includes/api/api-helpers.php:3645
#, php-format
msgid "File size must not exceed %s."
msgstr "Ukuran file harus tidak boleh melebihi %s."

#: includes/api/api-helpers.php:3679
#, php-format
msgid "File type must be %s."
msgstr "Jenis file harus %s."

#: includes/assets.php:343
msgid "Are you sure?"
msgstr "Anda Yakin?"

#: includes/assets.php:344 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:88
msgid "Yes"
msgstr "Ya"

#: includes/assets.php:345 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:98
msgid "No"
msgstr "Tidak"

#: includes/assets.php:346 includes/fields/class-acf-field-file.php:153
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:338
#: pro/fields/class-acf-field-gallery.php:478
msgid "Remove"
msgstr "Singkirkan"

#: includes/assets.php:347
msgid "Cancel"
msgstr "Batalkan"

#: includes/assets.php:355
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Perubahan yang Anda buat akan hilang jika Anda menavigasi keluar dari laman "
"ini"

#: includes/assets.php:358
msgid "Validation successful"
msgstr "Validasi Sukses"

#: includes/assets.php:359 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Validasi Gagal"

#: includes/assets.php:360
msgid "1 field requires attention"
msgstr "1 Bidang memerlukan perhatian"

#: includes/assets.php:361
#, php-format
msgid "%d fields require attention"
msgstr "Bidang %d memerlukan perhatian"

#: includes/assets.php:364 includes/forms/form-comment.php:166
#: pro/admin/admin-options-page.php:325
msgid "Edit field group"
msgstr "Sunting Grup Bidang"

#: includes/fields.php:308
msgid "Field type does not exist"
msgstr "Jenis bidang tidak ada"

#: includes/fields.php:308
msgid "Unknown"
msgstr "Tidak diketahui"

#: includes/fields.php:349
msgid "Basic"
msgstr "Dasar"

#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr "Konten"

#: includes/fields.php:351
msgid "Choice"
msgstr "Pilihan"

#: includes/fields.php:352
msgid "Relational"
msgstr "Relasional"

#: includes/fields.php:353
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:354 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:554
#: pro/fields/class-acf-field-flexible-content.php:603
#: pro/fields/class-acf-field-repeater.php:449
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Accordion"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Buka"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Tampilkan accordion ini sebagai terbuka pada pemuatan halaman."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Multi-perluasan"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "Biarkan accordion ini terbuka tanpa menutup yang lain."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Titik akhir"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Tentukan titik akhir untuk Accordion sebelumnya untuk berhenti. Accordion "
"ini tidak akan terlihat."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Grup Tombol"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "Pilihan"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "Masukkan setiap pilihan pada baris baru."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Untuk kontrol lebih, Anda dapat menentukan keduanya antara nilai dan bidang "
"seperti ini:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "merah : Merah"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:506
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:382
#: includes/fields/class-acf-field-taxonomy.php:781
#: includes/fields/class-acf-field-user.php:63
msgid "Allow Null?"
msgstr "Izinkan Null?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:155
#: includes/fields/class-acf-field-select.php:373
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:371
msgid "Default Value"
msgstr "Nilai Default"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:156
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:372
msgid "Appears when creating a new post"
msgstr "Muncul ketika membuat sebuah post baru"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "Vertikal"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:214
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:826
msgid "Return Value"
msgstr "Nilai Kembali"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "Tentukan nilai yang dikembalikan di front-end"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:432
msgid "Value"
msgstr "Nilai"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:434
msgid "Both (Array)"
msgstr "Keduanya (Array)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:768
msgid "Checkbox"
msgstr "Kotak centang"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Centang Semua"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Tambah pilihan baru"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Izinkan Kustom"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Izinkan ‘kustom’ nilai untuk ditambahkan"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Simpan Kustom"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "Simpan nilai ‘kustom’ ke bidang pilihan"

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:374
msgid "Enter each default value on a new line"
msgstr "Masukkan setiap nilai default pada baris baru"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Toggle"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Tambahkan sebuah kotak centang untuk centang semua pilihan"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Pengambil Warna"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Bersihkan"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Default"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Pilih Warna"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Warna Saat Ini"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Pengambil Tanggal"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Selesai"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Hari ini"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Selanjutnya"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Sebelumnya"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Mg"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Format tampilan"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "Format tampilan ketika menyunting post"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Kustom:"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "Simpan Format"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "Format yang digunakan ketika menyimpan sebuah nilai"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-image.php:194
#: includes/fields/class-acf-field-post_object.php:431
#: includes/fields/class-acf-field-relationship.php:628
#: includes/fields/class-acf-field-select.php:427
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:79
#: pro/fields/class-acf-field-gallery.php:557
msgid "Return Format"
msgstr "Kembalikan format"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "Format dikembalikan via template function"

#: includes/fields/class-acf-field-date_picker.php:227
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "Minggu Dimulai Pada"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Pengambil Tanggal dan Jam"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Pilih Waktu"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Time"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Jam"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Menit"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Detik"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Mili detik"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrodetik"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Zona Waktu"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Sekarang"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Selesai"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Pilih"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:104
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Teks Placeholder"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:105
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Muncul didalam input"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:194
#: includes/fields/class-acf-field-text.php:113
msgid "Prepend"
msgstr "Prepend"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:195
#: includes/fields/class-acf-field-text.php:114
msgid "Appears before the input"
msgstr "Muncul sebelum input"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:203
#: includes/fields/class-acf-field-text.php:122
msgid "Append"
msgstr "Append"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:204
#: includes/fields/class-acf-field-text.php:123
msgid "Appears after the input"
msgstr "Muncul setelah input"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Berkas"

#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr "Sunting Berkas"

#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr "Perbarui Berkas"

#: includes/fields/class-acf-field-file.php:141
msgid "File name"
msgstr "Nama Berkas"

#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:247
#: includes/fields/class-acf-field-file.php:258
#: includes/fields/class-acf-field-image.php:254
#: includes/fields/class-acf-field-image.php:283
#: pro/fields/class-acf-field-gallery.php:642
#: pro/fields/class-acf-field-gallery.php:671
msgid "File size"
msgstr "Ukuran Berkas"

#: includes/fields/class-acf-field-file.php:169
msgid "Add File"
msgstr "Tambah Berkas"

#: includes/fields/class-acf-field-file.php:220
msgid "File Array"
msgstr "Berkas Array"

#: includes/fields/class-acf-field-file.php:221
msgid "File URL"
msgstr "URL Berkas"

#: includes/fields/class-acf-field-file.php:222
msgid "File ID"
msgstr "ID Berkas"

#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-image.php:219
#: pro/fields/class-acf-field-gallery.php:592
msgid "Library"
msgstr "Perpustakaan"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:220
#: pro/fields/class-acf-field-gallery.php:593
msgid "Limit the media library choice"
msgstr "Batasi pilihan pustaka media"

#: includes/fields/class-acf-field-file.php:235
#: includes/fields/class-acf-field-image.php:225
#: includes/locations/class-acf-location-attachment.php:71
#: includes/locations/class-acf-location-comment.php:59
#: includes/locations/class-acf-location-nav-menu.php:72
#: includes/locations/class-acf-location-taxonomy.php:61
#: includes/locations/class-acf-location-user-form.php:65
#: includes/locations/class-acf-location-user-role.php:76
#: includes/locations/class-acf-location-widget.php:63
#: pro/fields/class-acf-field-gallery.php:598
#: pro/locations/class-acf-location-block.php:64
msgid "All"
msgstr "Semua"

#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:226
#: pro/fields/class-acf-field-gallery.php:599
msgid "Uploaded to post"
msgstr "Diunggah ke post"

#: includes/fields/class-acf-field-file.php:243
#: includes/fields/class-acf-field-image.php:233
#: pro/fields/class-acf-field-gallery.php:621
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-file.php:255
msgid "Restrict which files can be uploaded"
msgstr "Batasi file mana yang dapat diunggah"

#: includes/fields/class-acf-field-file.php:254
#: includes/fields/class-acf-field-image.php:262
#: pro/fields/class-acf-field-gallery.php:650
msgid "Maximum"
msgstr "Maksimum"

#: includes/fields/class-acf-field-file.php:265
#: includes/fields/class-acf-field-image.php:291
#: pro/fields/class-acf-field-gallery.php:678
msgid "Allowed file types"
msgstr "Jenis berkas yang diperbolehkan"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:292
#: pro/fields/class-acf-field-gallery.php:679
msgid "Comma separated list. Leave blank for all types"
msgstr "Daftar dipisahkan koma. Kosongkan untuk semua jenis"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Peta Google"

#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr "Maaf, browser ini tidak support geolocation"

#: includes/fields/class-acf-field-google-map.php:146
#: includes/fields/class-acf-field-relationship.php:587
msgid "Search"
msgstr "Cari"

#: includes/fields/class-acf-field-google-map.php:147
msgid "Clear location"
msgstr "Bersihkan lokasi"

#: includes/fields/class-acf-field-google-map.php:148
msgid "Find current location"
msgstr "Temukan lokasi saat ini"

#: includes/fields/class-acf-field-google-map.php:151
msgid "Search for address..."
msgstr "Cari alamat..."

#: includes/fields/class-acf-field-google-map.php:181
#: includes/fields/class-acf-field-google-map.php:192
msgid "Center"
msgstr "Tengah"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:193
msgid "Center the initial map"
msgstr "Pusat peta awal"

#: includes/fields/class-acf-field-google-map.php:204
msgid "Zoom"
msgstr "Perbesar"

#: includes/fields/class-acf-field-google-map.php:205
msgid "Set the initial zoom level"
msgstr "Mengatur tingkat awal zoom"

#: includes/fields/class-acf-field-google-map.php:214
#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:633
#: pro/fields/class-acf-field-gallery.php:662
msgid "Height"
msgstr "Tinggi"

#: includes/fields/class-acf-field-google-map.php:215
msgid "Customize the map height"
msgstr "Sesuaikan ketinggian peta"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Grup"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:385
msgid "Sub Fields"
msgstr "Sub Bidang"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "Tentukan gaya yang digunakan untuk merender bidang yang dipilih"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:615
#: pro/fields/class-acf-field-repeater.php:457
#: pro/locations/class-acf-location-block.php:20
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:614
#: pro/fields/class-acf-field-repeater.php:456
msgid "Table"
msgstr "Tabel"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:616
#: pro/fields/class-acf-field-repeater.php:458
msgid "Row"
msgstr "Baris"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Gambar"

#: includes/fields/class-acf-field-image.php:63
msgid "Select Image"
msgstr "Pilih Gambar"

#: includes/fields/class-acf-field-image.php:64
msgid "Edit Image"
msgstr "Sunting Gambar"

#: includes/fields/class-acf-field-image.php:65
msgid "Update Image"
msgstr "Perbarui Gambar"

#: includes/fields/class-acf-field-image.php:66 includes/media.php:61
msgid "All images"
msgstr "Semua gambar"

#: includes/fields/class-acf-field-image.php:148
msgid "No image selected"
msgstr "Tak ada gambar yang dipilih"

#: includes/fields/class-acf-field-image.php:148
msgid "Add Image"
msgstr "Tambahkan Gambar"

#: includes/fields/class-acf-field-image.php:200
#: pro/fields/class-acf-field-gallery.php:563
msgid "Image Array"
msgstr "Gambar Array"

#: includes/fields/class-acf-field-image.php:201
#: pro/fields/class-acf-field-gallery.php:564
msgid "Image URL"
msgstr "URL Gambar"

#: includes/fields/class-acf-field-image.php:202
#: pro/fields/class-acf-field-gallery.php:565
msgid "Image ID"
msgstr "ID Gambar"

#: includes/fields/class-acf-field-image.php:209
#: pro/fields/class-acf-field-gallery.php:571
msgid "Preview Size"
msgstr "Ukuran Tinjauan"

#: includes/fields/class-acf-field-image.php:234
#: includes/fields/class-acf-field-image.php:263
#: pro/fields/class-acf-field-gallery.php:622
#: pro/fields/class-acf-field-gallery.php:651
msgid "Restrict which images can be uploaded"
msgstr "Batasi gambar mana yang dapat diunggah"

#: includes/fields/class-acf-field-image.php:237
#: includes/fields/class-acf-field-image.php:266
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:625
#: pro/fields/class-acf-field-gallery.php:654
msgid "Width"
msgstr "Lebar"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Tautan"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Pilih Tautan"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Buka di halaman/tab baru"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Tautan Array"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "URL Tautan"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Pesan"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Garis baru"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Kontrol bagaimana baris baru diberikan"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Tambah paragraf secara otomatis"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Otomatis Tambah &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Jangan format"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Keluar HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Memungkinkan HTML markup untuk menampilkan teks terlihat sebagai render"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Nomor"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:164
msgid "Minimum Value"
msgstr "Nilai Minimum"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:174
msgid "Maximum Value"
msgstr "Nilai Maksimum"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:184
msgid "Step Size"
msgstr "Ukuran Langkah"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "Nilai harus berupa angka"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Nilai harus sama dengan atau lebih tinggi dari %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Nilai harus sama dengan atau lebih rendah dari %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "Masukkan URL"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "Ukuran Embed (Semat)"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Link Halaman"

#: includes/fields/class-acf-field-page_link.php:170
msgid "Archives"
msgstr "Arsip"

#: includes/fields/class-acf-field-page_link.php:262
#: includes/fields/class-acf-field-post_object.php:267
#: includes/fields/class-acf-field-taxonomy.php:958
msgid "Parent"
msgstr "Induk"

#: includes/fields/class-acf-field-page_link.php:478
#: includes/fields/class-acf-field-post_object.php:383
#: includes/fields/class-acf-field-relationship.php:554
msgid "Filter by Post Type"
msgstr "Saring dengan jenis post"

#: includes/fields/class-acf-field-page_link.php:486
#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:562
msgid "All post types"
msgstr "Semua Tipe Post"

#: includes/fields/class-acf-field-page_link.php:492
#: includes/fields/class-acf-field-post_object.php:397
#: includes/fields/class-acf-field-relationship.php:568
msgid "Filter by Taxonomy"
msgstr "Filter dengan Taksonomi"

#: includes/fields/class-acf-field-page_link.php:500
#: includes/fields/class-acf-field-post_object.php:405
#: includes/fields/class-acf-field-relationship.php:576
msgid "All taxonomies"
msgstr "Semua Taksonomi"

#: includes/fields/class-acf-field-page_link.php:516
msgid "Allow Archives URLs"
msgstr "Izinkan mengarsipkan tautan-tautan"

#: includes/fields/class-acf-field-page_link.php:526
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:392
#: includes/fields/class-acf-field-user.php:71
msgid "Select multiple values?"
msgstr "Pilih beberapa nilai?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Kata Sandi"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:436
#: includes/fields/class-acf-field-relationship.php:633
msgid "Post Object"
msgstr "Objek Post"

#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:634
msgid "Post ID"
msgstr "ID Post"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Tombol Radio"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Lainnya"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Tambah pilihan 'lainnya' untuk mengizinkan nilai kustom"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Simpan Lainnya"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Simpan nilai 'lainnya' ke bidang pilihan"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Rentang"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Hubungan"

#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr "Nilai maksimum mencapai ( nilai {maks} )"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr "Silahkan Tunggu"

#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr "Tidak ada yang cocok"

#: includes/fields/class-acf-field-relationship.php:411
msgid "Select post type"
msgstr "Pilih jenis posting"

#: includes/fields/class-acf-field-relationship.php:420
msgid "Select taxonomy"
msgstr "Pilih taksonomi"

#: includes/fields/class-acf-field-relationship.php:476
msgid "Search..."
msgstr "Cari ..."

#: includes/fields/class-acf-field-relationship.php:582
msgid "Filters"
msgstr "Saringan"

#: includes/fields/class-acf-field-relationship.php:588
#: includes/locations/class-acf-location-post-type.php:20
msgid "Post Type"
msgstr "Jenis Post"

#: includes/fields/class-acf-field-relationship.php:589
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:751
#: includes/locations/class-acf-location-taxonomy.php:20
msgid "Taxonomy"
msgstr "Taksonomi"

#: includes/fields/class-acf-field-relationship.php:596
msgid "Elements"
msgstr "Elemen"

#: includes/fields/class-acf-field-relationship.php:597
msgid "Selected elements will be displayed in each result"
msgstr "Elemen terpilih akan ditampilkan disetiap hasil"

#: includes/fields/class-acf-field-relationship.php:608
msgid "Minimum posts"
msgstr "Posting minimal"

#: includes/fields/class-acf-field-relationship.php:617
msgid "Maximum posts"
msgstr "Posting maksimum"

#: includes/fields/class-acf-field-relationship.php:721
#: pro/fields/class-acf-field-gallery.php:779
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s diperlukan setidaknya %s pilihan"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:773
msgctxt "noun"
msgid "Select"
msgstr "Pilih"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Satu hasil tersedia, tekan enter untuk memilihnya."

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d hasil tersedia, gunakan tombol panah atas dan bawah untuk menavigasi."

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Tidak ada yang cocok"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Harap masukkan 1 karakter atau lebih"

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Harap masukkan %d karakter atau lebih"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Harap hapus 1 karakter"

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Please delete %d character"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Anda hanya dapat memilih 1 item"

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Anda hanya dapat memilih %d item"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Memuat lebih banyak hasil&hellip;"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Pencarian&hellip;"

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Gagal Memuat"

#: includes/fields/class-acf-field-select.php:259 includes/media.php:54
msgctxt "verb"
msgid "Select"
msgstr "Pilih"

#: includes/fields/class-acf-field-select.php:402
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "UI bergaya"

#: includes/fields/class-acf-field-select.php:412
msgid "Use AJAX to lazy load choices?"
msgstr "Gunakan AJAX untuk pilihan lazy load?"

#: includes/fields/class-acf-field-select.php:428
msgid "Specify the value returned"
msgstr "Tentukan nilai yang dikembalikan"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Pemisah"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Tab"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Penempatan"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Tentukan titik akhir untuk tab sebelumnya berhenti. Ini akan memulai grup "
"tab baru."

#: includes/fields/class-acf-field-taxonomy.php:711
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "No %s"

#: includes/fields/class-acf-field-taxonomy.php:752
msgid "Select the taxonomy to be displayed"
msgstr "Pilih taksonomi yang akan ditampilkan"

#: includes/fields/class-acf-field-taxonomy.php:761
msgid "Appearance"
msgstr "Tampilan"

#: includes/fields/class-acf-field-taxonomy.php:762
msgid "Select the appearance of this field"
msgstr "Pilih penampilan bidang ini"

#: includes/fields/class-acf-field-taxonomy.php:767
msgid "Multiple Values"
msgstr "Beberapa Nilai"

#: includes/fields/class-acf-field-taxonomy.php:769
msgid "Multi Select"
msgstr "Pilihan Multi"

#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Single Value"
msgstr "Nilai Tunggal"

#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Radio Buttons"
msgstr "Tombol Radio"

#: includes/fields/class-acf-field-taxonomy.php:796
msgid "Create Terms"
msgstr "Buat Ketentuan"

#: includes/fields/class-acf-field-taxonomy.php:797
msgid "Allow new terms to be created whilst editing"
msgstr "Izinkan istilah baru dibuat saat menyunting"

#: includes/fields/class-acf-field-taxonomy.php:806
msgid "Save Terms"
msgstr "Simpan Ketentuan"

#: includes/fields/class-acf-field-taxonomy.php:807
msgid "Connect selected terms to the post"
msgstr "Hubungkan ketentuan yang dipilih ke post"

#: includes/fields/class-acf-field-taxonomy.php:816
msgid "Load Terms"
msgstr "Load Ketentuan"

#: includes/fields/class-acf-field-taxonomy.php:817
msgid "Load value from posts terms"
msgstr "Muat nilai dari ketentuan post"

#: includes/fields/class-acf-field-taxonomy.php:831
msgid "Term Object"
msgstr "Objek ketentuan"

#: includes/fields/class-acf-field-taxonomy.php:832
msgid "Term ID"
msgstr "ID Ketentuan"

#: includes/fields/class-acf-field-taxonomy.php:882
#, php-format
msgid "User unable to add new %s"
msgstr "Pengguna tidak dapat menambahkan %s"

#: includes/fields/class-acf-field-taxonomy.php:892
#, php-format
msgid "%s already exists"
msgstr "%s sudah ada"

#: includes/fields/class-acf-field-taxonomy.php:924
#, php-format
msgid "%s added"
msgstr "%s ditambahkan"

#: includes/fields/class-acf-field-taxonomy.php:970
#: includes/locations/class-acf-location-user-form.php:66
msgid "Add"
msgstr "Tambah"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Teks"

#: includes/fields/class-acf-field-text.php:131
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Batas Karakter"

#: includes/fields/class-acf-field-text.php:132
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Biarkan kosong untuk tidak terbatas"

#: includes/fields/class-acf-field-text.php:157
#: includes/fields/class-acf-field-textarea.php:213
#, php-format
msgid "Value must not exceed %d characters"
msgstr "Nilai tidak boleh lebih dari %d karakter"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Area Teks"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Baris"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Atur tinggi area teks"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Pengambil Jam"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Benar / Salah"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Menampilkan teks di samping kotak centang"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Dalam Teks"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Teks muncul ketika aktif"

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr "Off Teks"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "Teks muncul ketika tidak aktif"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Nilai harus URL yang valid"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:99
msgid "User"
msgstr "Pengguna"

#: includes/fields/class-acf-field-user.php:51
msgid "Filter by role"
msgstr "Saring berdasarkan peran"

#: includes/fields/class-acf-field-user.php:59
msgid "All user roles"
msgstr "Semua peran pengguna"

#: includes/fields/class-acf-field-user.php:84
msgid "User Array"
msgstr "Array Pengguna"

#: includes/fields/class-acf-field-user.php:85
msgid "User Object"
msgstr "Object Pengguna"

#: includes/fields/class-acf-field-user.php:86
msgid "User ID"
msgstr "ID Pengguna"

#: includes/fields/class-acf-field-user.php:334
msgid "Error loading field."
msgstr "Kesalahan saat memproses bidang."

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Penyuntingan WYSIWYG"

#: includes/fields/class-acf-field-wysiwyg.php:320
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-wysiwyg.php:321
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Teks"

#: includes/fields/class-acf-field-wysiwyg.php:327
msgid "Click to initialize TinyMCE"
msgstr "Klik untuk inisiasi TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:380
msgid "Tabs"
msgstr "Tab"

#: includes/fields/class-acf-field-wysiwyg.php:385
msgid "Visual & Text"
msgstr "Visual & Teks"

#: includes/fields/class-acf-field-wysiwyg.php:386
msgid "Visual Only"
msgstr "Visual Saja"

#: includes/fields/class-acf-field-wysiwyg.php:387
msgid "Text Only"
msgstr "Teks saja"

#: includes/fields/class-acf-field-wysiwyg.php:394
msgid "Toolbar"
msgstr "Toolbar"

#: includes/fields/class-acf-field-wysiwyg.php:409
msgid "Show Media Upload Buttons?"
msgstr "Tampilkan Tombol Unggah Media?"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Delay initialization?"
msgstr "Tunda Inisiasi?"

#: includes/fields/class-acf-field-wysiwyg.php:420
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE tidak akan di inisiasi hingga bidang diklik"

#: includes/forms/form-front.php:38 pro/fields/class-acf-field-gallery.php:353
msgid "Title"
msgstr "Judul"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Validasi Email"

#: includes/forms/form-front.php:104 pro/fields/class-acf-field-gallery.php:510
#: pro/options-page.php:81
msgid "Update"
msgstr "Perbarui"

#: includes/forms/form-front.php:105
msgid "Post updated"
msgstr "Pos Diperbarui"

#: includes/forms/form-front.php:231
msgid "Spam Detected"
msgstr "Spam Terdeteksi"

#: includes/forms/form-user.php:336
#, php-format
msgid "<strong>ERROR</strong>: %s"
msgstr "<strong>TERJADI KESALAHAN</strong>: %s"

#: includes/locations.php:23
#, php-format
msgid "Class \"%s\" does not exist."
msgstr "Class “%s” tidak ditemukan."

#: includes/locations.php:34
#, php-format
msgid "Location type \"%s\" is already registered."
msgstr "Tipe lokasi “%s” telah terdaftar."

#: includes/locations.php:97 includes/locations/class-acf-location-post.php:20
msgid "Post"
msgstr "Pos"

#: includes/locations.php:98 includes/locations/class-acf-location-page.php:20
msgid "Page"
msgstr "Laman"

#: includes/locations.php:100
msgid "Forms"
msgstr "Form"

#: includes/locations/abstract-acf-location.php:103
msgid "is equal to"
msgstr "sama dengan"

#: includes/locations/abstract-acf-location.php:104
msgid "is not equal to"
msgstr "tidak sama dengan"

#: includes/locations/class-acf-location-attachment.php:20
msgid "Attachment"
msgstr "Lampiran"

#: includes/locations/class-acf-location-attachment.php:82
#, php-format
msgid "All %s formats"
msgstr "Semua %s format"

#: includes/locations/class-acf-location-comment.php:20
msgid "Comment"
msgstr "Komentar"

#: includes/locations/class-acf-location-current-user-role.php:20
msgid "Current User Role"
msgstr "Peran pengguna saat ini"

#: includes/locations/class-acf-location-current-user-role.php:75
msgid "Super Admin"
msgstr "Super Admin"

#: includes/locations/class-acf-location-current-user.php:20
msgid "Current User"
msgstr "Pengguna saat ini"

#: includes/locations/class-acf-location-current-user.php:69
msgid "Logged in"
msgstr "Log masuk"

#: includes/locations/class-acf-location-current-user.php:70
msgid "Viewing front end"
msgstr "Melihat front end"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Viewing back end"
msgstr "Melihat back end"

#: includes/locations/class-acf-location-nav-menu-item.php:20
msgid "Menu Item"
msgstr "Menu Item"

#: includes/locations/class-acf-location-nav-menu.php:20
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-nav-menu.php:78
msgid "Menu Locations"
msgstr "Lokasi Menu"

#: includes/locations/class-acf-location-page-parent.php:20
msgid "Page Parent"
msgstr "Laman Parent"

#: includes/locations/class-acf-location-page-template.php:20
msgid "Page Template"
msgstr "Template Laman"

#: includes/locations/class-acf-location-page-template.php:71
#: includes/locations/class-acf-location-post-template.php:83
msgid "Default Template"
msgstr "Template Default"

#: includes/locations/class-acf-location-page-type.php:20
msgid "Page Type"
msgstr "Jenis Laman"

#: includes/locations/class-acf-location-page-type.php:106
msgid "Front Page"
msgstr "Laman Depan"

#: includes/locations/class-acf-location-page-type.php:107
msgid "Posts Page"
msgstr "Laman Post"

#: includes/locations/class-acf-location-page-type.php:108
msgid "Top Level Page (no parent)"
msgstr "Laman Tingkat Atas (tanpa parent)"

#: includes/locations/class-acf-location-page-type.php:109
msgid "Parent Page (has children)"
msgstr "Laman Parent (memiliki anak)"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Child Page (has parent)"
msgstr "Laman Anak (memiliki parent)"

#: includes/locations/class-acf-location-post-category.php:20
msgid "Post Category"
msgstr "Kategori Post"

#: includes/locations/class-acf-location-post-format.php:20
msgid "Post Format"
msgstr "Format Post"

#: includes/locations/class-acf-location-post-status.php:20
msgid "Post Status"
msgstr "Status Post"

#: includes/locations/class-acf-location-post-taxonomy.php:20
msgid "Post Taxonomy"
msgstr "Post Taksonomi"

#: includes/locations/class-acf-location-post-template.php:20
msgid "Post Template"
msgstr "Template Laman"

#: includes/locations/class-acf-location-user-form.php:20
msgid "User Form"
msgstr "Form Pengguna"

#: includes/locations/class-acf-location-user-form.php:67
msgid "Add / Edit"
msgstr "Tambah / Sunting"

#: includes/locations/class-acf-location-user-form.php:68
msgid "Register"
msgstr "Daftar"

#: includes/locations/class-acf-location-user-role.php:22
msgid "User Role"
msgstr "Peran pengguna"

#: includes/locations/class-acf-location-widget.php:20
msgid "Widget"
msgstr "Widget"

#: includes/media.php:55
msgctxt "verb"
msgid "Edit"
msgstr "Sunting"

#: includes/media.php:56
msgctxt "verb"
msgid "Update"
msgstr "Perbarui"

#: includes/media.php:57
msgid "Uploaded to this post"
msgstr "Diunggah ke post ini"

#: includes/media.php:58
msgid "Expand Details"
msgstr "Perluas Rincian"

#: includes/media.php:59
msgid "Collapse Details"
msgstr "Persempit Rincian"

#: includes/media.php:60
msgid "Restricted"
msgstr "Dibatasi"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "Nilai %s diharuskan"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:198
msgid "Publish"
msgstr "Terbitkan"

#: pro/admin/admin-options-page.php:204
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Tidak ada Grup Bidang Kustom ditemukan untuk halaman pilihan ini. <a href="
"\"%s\">Buat Grup Bidang Kustom</a>"

#: pro/admin/admin-updates.php:49
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Kesalahan.</b> Tidak dapat terhubung ke server yang memperbarui"

#: pro/admin/admin-updates.php:118 pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Pembaruan"

#: pro/admin/admin-updates.php:191
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Terjadi Kesalahan</b>. Tidak dapat mengautentikasi paket pembaruan. "
"Silakan periksa lagi atau nonaktifkan dan aktifkan kembali lisensi ACF PRO "
"Anda."

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Nonaktifkan Lisensi"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Aktifkan Lisensi"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informasi Lisensi"

#: pro/admin/views/html-settings-updates.php:19
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Untuk membuka kunci pembaruan, masukkan kunci lisensi Anda di bawah. Jika "
"Anda tidak memiliki kunci lisensi, silakan lihat <a href=\"%s\">rincian & "
"harga</a>."

#: pro/admin/views/html-settings-updates.php:28
msgid "License Key"
msgstr "Kunci lisensi"

#: pro/admin/views/html-settings-updates.php:60
msgid "Update Information"
msgstr "Informasi Pembaruan"

#: pro/admin/views/html-settings-updates.php:67
msgid "Current Version"
msgstr "Versi sekarang"

#: pro/admin/views/html-settings-updates.php:75
msgid "Latest Version"
msgstr "Versi terbaru"

#: pro/admin/views/html-settings-updates.php:83
msgid "Update Available"
msgstr "Pembaruan Tersedia"

#: pro/admin/views/html-settings-updates.php:91
msgid "Update Plugin"
msgstr "Perbarui Plugin"

#: pro/admin/views/html-settings-updates.php:93
msgid "Please enter your license key above to unlock updates"
msgstr "Masukkan kunci lisensi Anda di atas untuk membuka pembaruan"

#: pro/admin/views/html-settings-updates.php:99
msgid "Check Again"
msgstr "Periksa lagi"

#: pro/admin/views/html-settings-updates.php:106
msgid "Changelog"
msgstr "Changelog"

#: pro/admin/views/html-settings-updates.php:116
msgid "Upgrade Notice"
msgstr "Pemberitahuan Upgrade"

#: pro/blocks.php:36
msgid "Block type name is required."
msgstr "Blok tipe nama diharuskan."

#: pro/blocks.php:43
#, php-format
msgid "Block type \"%s\" is already registered."
msgstr "Blok tipe “%s” telah terdaftar."

#: pro/blocks.php:418
msgid "Switch to Edit"
msgstr "Beralih ke Penyuntingan"

#: pro/blocks.php:419
msgid "Switch to Preview"
msgstr "Beralih ke Pratinjau"

#: pro/blocks.php:420
msgid "Change content alignment"
msgstr "Sunting perataan konten"

#: pro/blocks.php:423
#, php-format
msgid "%s settings"
msgstr "%s pengaturan"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Klon"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Pilih satu atau lebih bidang yang ingin Anda gandakan"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Tampilan"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Tentukan gaya yang digunakan untuk merender bidang ganda"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grup (menampilkan bidang yang dipilih dalam grup dalam bidang ini)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Seamless (mengganti bidang ini dengan bidang yang dipilih)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Label akan ditampilkan sebagai %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Awalan Label Bidang"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Nilai akan disimpan sebagai %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Awalan Nama Bidang"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Bidang tidak diketahui"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Grup bidang tidak diketahui"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Semua bidang dari %s grup bidang"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:469
msgid "Add Row"
msgstr "Tambah Baris"

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:926
#: pro/fields/class-acf-field-flexible-content.php:1008
msgid "layout"
msgid_plural "layouts"
msgstr[0] "tata letak"

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "layout"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:925
#: pro/fields/class-acf-field-flexible-content.php:1007
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Bidang ini membutuhkan setidaknya {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Bidang ini memiliki batas {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} tersedia (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} diperlukan (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "Konten fleksibel memerlukan setidaknya 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:287
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Klik tombol\"%s\" dibawah untuk mulai membuat layout Anda"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Tambah Layout"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Duplicate layout"
msgstr "Gandakan Layout"

#: pro/fields/class-acf-field-flexible-content.php:415
msgid "Remove layout"
msgstr "Hapus layout"

#: pro/fields/class-acf-field-flexible-content.php:416
#: pro/fields/class-acf-field-repeater.php:301
msgid "Click to toggle"
msgstr "Klik untuk toggle"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Reorder Layout"
msgstr "Susun ulang Layout"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Reorder"
msgstr "Susun Ulang"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Delete Layout"
msgstr "Hapus Layout"

#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Duplicate Layout"
msgstr "Duplikat Layout"

#: pro/fields/class-acf-field-flexible-content.php:559
msgid "Add New Layout"
msgstr "Tambah Layout Baru"

#: pro/fields/class-acf-field-flexible-content.php:631
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:644
msgid "Max"
msgstr "Maks"

#: pro/fields/class-acf-field-flexible-content.php:671
#: pro/fields/class-acf-field-repeater.php:465
msgid "Button Label"
msgstr "Label tombol"

#: pro/fields/class-acf-field-flexible-content.php:680
msgid "Minimum Layouts"
msgstr "Minimum Layouts"

#: pro/fields/class-acf-field-flexible-content.php:689
msgid "Maximum Layouts"
msgstr "Maksimum Layout"

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "Tambahkan Gambar ke Galeri"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "Batas pilihan maksimum"

#: pro/fields/class-acf-field-gallery.php:322
msgid "Length"
msgstr "Panjang"

#: pro/fields/class-acf-field-gallery.php:362
msgid "Caption"
msgstr "Judul"

#: pro/fields/class-acf-field-gallery.php:371
msgid "Alt Text"
msgstr "Alt Teks"

#: pro/fields/class-acf-field-gallery.php:487
msgid "Add to gallery"
msgstr "Tambahkan ke galeri"

#: pro/fields/class-acf-field-gallery.php:491
msgid "Bulk actions"
msgstr "Aksi besar"

#: pro/fields/class-acf-field-gallery.php:492
msgid "Sort by date uploaded"
msgstr "Urutkan berdasarkan tanggal unggah"

#: pro/fields/class-acf-field-gallery.php:493
msgid "Sort by date modified"
msgstr "Urutkan berdasarkan tanggal modifikasi"

#: pro/fields/class-acf-field-gallery.php:494
msgid "Sort by title"
msgstr "Urutkan menurut judul"

#: pro/fields/class-acf-field-gallery.php:495
msgid "Reverse current order"
msgstr "Balik urutan saat ini"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Close"
msgstr "Tutup"

#: pro/fields/class-acf-field-gallery.php:580
msgid "Insert"
msgstr "Masukkan"

#: pro/fields/class-acf-field-gallery.php:581
msgid "Specify where new attachments are added"
msgstr "Tentukan di mana lampiran baru ditambahkan"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Append to the end"
msgstr "Tambahkan ke bagian akhir"

#: pro/fields/class-acf-field-gallery.php:586
msgid "Prepend to the beginning"
msgstr "Tambahkan ke bagian awal"

#: pro/fields/class-acf-field-gallery.php:605
msgid "Minimum Selection"
msgstr "Seleksi Minimum"

#: pro/fields/class-acf-field-gallery.php:613
msgid "Maximum Selection"
msgstr "Seleksi maksimum"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:662
msgid "Minimum rows reached ({min} rows)"
msgstr "Baris minimal mencapai ({min} baris)"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "Baris maksimum mencapai ({max} baris)"

#: pro/fields/class-acf-field-repeater.php:338
msgid "Add row"
msgstr "Tambah Baris"

#: pro/fields/class-acf-field-repeater.php:339
msgid "Duplicate row"
msgstr "Gandakan baris"

#: pro/fields/class-acf-field-repeater.php:340
msgid "Remove row"
msgstr "Hapus baris"

#: pro/fields/class-acf-field-repeater.php:418
msgid "Collapsed"
msgstr "Disempitkan"

#: pro/fields/class-acf-field-repeater.php:419
msgid "Select a sub field to show when row is collapsed"
msgstr "Pilih sub bidang untuk ditampilkan ketika baris disempitkan"

#: pro/fields/class-acf-field-repeater.php:429
msgid "Minimum Rows"
msgstr "Minimum Baris"

#: pro/fields/class-acf-field-repeater.php:439
msgid "Maximum Rows"
msgstr "Maksimum Baris"

#: pro/locations/class-acf-location-block.php:69
msgid "No block types exist"
msgstr "Tidak ada tipe blok tersedia"

#: pro/locations/class-acf-location-options-page.php:68
msgid "No options pages exist"
msgstr "Tidak ada pilihan halaman yang ada"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Pilihan Diperbarui"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Untuk mengaktifkan update, masukkan kunci lisensi Anda pada halaman <a href="
"\"%s\">Pembaruan</a>. Jika anda tidak memiliki kunci lisensi, silakan lihat "
"<a href=\"%s\">rincian & harga</a>."

#: tests/basic/test-blocks.php:279
msgid "Hero"
msgstr "Utama"

#: tests/basic/test-blocks.php:280
msgid "Display a random hero image."
msgstr "Tampilkan sebuah gambar utama acak."

#. Plugin URI of the plugin/theme
#. Author URI of the plugin/theme
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com/"

#. Description of the plugin/theme
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Sesuaikan WordPress dengan bidang kustom yang kuat, profesional, dan "
"intuitif."

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#~ msgid "Parent fields"
#~ msgstr "Bidang parent"

#~ msgid "Sibling fields"
#~ msgstr "Bidang sibling"

#, php-format
#~ msgid "%s field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "bidang grup %s disinkronkan."

#~ msgid "Status"
#~ msgstr "Status"

#~ msgid "See what's new in"
#~ msgstr "Lihat apa yang baru di"

#~ msgid "Resources"
#~ msgstr "Sumber"

#~ msgid "Getting Started"
#~ msgstr "Perkenalan"

#~ msgid "Field Types"
#~ msgstr "Jenis Field"

#~ msgid "Functions"
#~ msgstr "Fungsi"

#~ msgid "Actions"
#~ msgstr "Tindakan"

#~ msgid "'How to' guides"
#~ msgstr "Panduan \"Bagaimana Caranya\""

#~ msgid "Tutorials"
#~ msgstr "Tutorial"

#~ msgid "Created by"
#~ msgstr "Dibuat oleh"

#~ msgid "Add-ons"
#~ msgstr "Add-on"

#~ msgid "<b>Error</b>. Could not load add-ons list"
#~ msgstr "<b>Kesalahan</b>. Tidak dapat memuat daftar add-on"

#~ msgid "Info"
#~ msgstr "Info"

#~ msgid "What's New"
#~ msgstr "Apa yang Baru"

#, php-format
#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Sukses</b>. Impor alat ditambahkan %s grup bidang: %s"

#, php-format
#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Peringatan</b>. Impor alat terdeteksi grup bidang %s sudah ada dan "
#~ "telah diabaikan: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Tingkatkan ACF"

#~ msgid "Upgrade"
#~ msgstr "Tingkatkan"

#~ msgid "Error"
#~ msgstr "Error"

#~ msgid "Error."
#~ msgstr "Error."

#~ msgid "Drag and drop to reorder"
#~ msgstr "Seret dan jatuhkan untuk mengatur ulang"

#~ msgid "Taxonomy Term"
#~ msgstr "Taksonomi Persyaratan"

#~ msgid "Download & Install"
#~ msgstr "Undah dan Instal"

#~ msgid "Installed"
#~ msgstr "Sudah Terinstall"

#~ msgid "Welcome to Advanced Custom Fields"
#~ msgstr "Selamat datang di Advanced Custom Fields"

#, php-format
#~ msgid ""
#~ "Thank you for updating! ACF %s is bigger and better than ever before. We "
#~ "hope you like it."
#~ msgstr ""
#~ "Terima kasih sudah memperbarui! ACF %s lebih besar dan lebih baik "
#~ "daripada sebelumnya. Kami harap Anda menyukainya."

#~ msgid "A smoother custom field experience"
#~ msgstr "Pengalaman bidang kustom yang halus"

#~ msgid "Improved Usability"
#~ msgstr "Peningkatan Kegunaan"

#~ msgid ""
#~ "Including the popular Select2 library has improved both usability and "
#~ "speed across a number of field types including post object, page link, "
#~ "taxonomy and select."
#~ msgstr ""
#~ "Termasuk Perpustakaan Select2 populer telah meningkatkan kegunaan dan "
#~ "kecepatan di sejumlah bidang jenis termasuk posting objek, link halaman, "
#~ "taksonomi, dan pilih."

#~ msgid "Improved Design"
#~ msgstr "Peningkatan Desain"

#~ msgid ""
#~ "Many fields have undergone a visual refresh to make ACF look better than "
#~ "ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
#~ "(new) fields!"
#~ msgstr ""
#~ "Berbagai bidang telah mengalami refresh visual untuk membuat ACF terlihat "
#~ "lebih baik daripada sebelumnya! Perubahan nyata terlihat pada galeri, "
#~ "hubungan dan oEmbed bidang (baru)!"

#~ msgid "Improved Data"
#~ msgstr "Peningkatan Data"

#~ msgid ""
#~ "Redesigning the data architecture has allowed sub fields to live "
#~ "independently from their parents. This allows you to drag and drop fields "
#~ "in and out of parent fields!"
#~ msgstr ""
#~ "Mendesain ulang arsitektur data telah memungkinkan sub bidang untuk yang "
#~ "mandiri dari parentnya. Hal ini memungkinkan Anda untuk seret dan "
#~ "jatuhkan bidang masuk dan keluar dari bidang parent!"

#~ msgid "Goodbye Add-ons. Hello PRO"
#~ msgstr "Selamat tinggal Add-on. Halo PRO"

#~ msgid "Introducing ACF PRO"
#~ msgstr "Memperkenalkan ACF PRO"

#~ msgid ""
#~ "We're changing the way premium functionality is delivered in an exciting "
#~ "way!"
#~ msgstr "Kami mengubah fungsionalitas premium dengan cara yang menarik!"

#, php-format
#~ msgid ""
#~ "All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
#~ "version of ACF</a>. With both personal and developer licenses available, "
#~ "premium functionality is more affordable and accessible than ever before!"
#~ msgstr ""
#~ "Semua 4 add-on premium sudah dikombinasikan kedalam  <a href=\"%s\">versi "
#~ "Pro ACF</a>. Dengan ketersediaan lisensi personal dan pengembang, fungsi "
#~ "premuim lebih terjangkau dan dapat diakses keseluruhan dibanding "
#~ "sebelumnya!"

#~ msgid "Powerful Features"
#~ msgstr "Fitur kuat"

#~ msgid ""
#~ "ACF PRO contains powerful features such as repeatable data, flexible "
#~ "content layouts, a beautiful gallery field and the ability to create "
#~ "extra admin options pages!"
#~ msgstr ""
#~ "ACF PRO memiliki fitur canggih seperti data yang berulang, layout konten "
#~ "yang fleksibel, bidang galeri yang cantik dan kemampuan membuat laman "
#~ "opsi ekstra admin!"

#, php-format
#~ msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
#~ msgstr "Baca selengkapnya tentang <a href=\"%s\">Fitur-fitur ACF PRO</a>."

#~ msgid "Easy Upgrading"
#~ msgstr "Upgrade Mudah"

#, php-format
#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Untuk membuat peningkatan yang mudah, <a href=\"%s\">masuk ke akun toko</"
#~ "a> dan klaim salinan gratis ACF PRO!"

#, php-format
#~ msgid ""
#~ "We also wrote an <a href=\"%s\">upgrade guide</a> to answer any "
#~ "questions, but if you do have one, please contact our support team via "
#~ "the <a href=\"%s\">help desk</a>"
#~ msgstr ""
#~ "Kami juga menulis  <a href=\"%s\">panduan upgrade</a> untuk menjawab "
#~ "pertanyaan apapun, jika Anda sudah punya, silahkan hubungi tim support "
#~ "kami via <a href=\"%s\">help desk</a>"

#~ msgid "Under the Hood"
#~ msgstr "Dibawah judul blog"

#~ msgid "Smarter field settings"
#~ msgstr "Pengaturan bidang yang pintar"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr ""
#~ "ACF sekarang menyimpan pengaturan bidang sebagai objek post individu"

#~ msgid "More AJAX"
#~ msgstr "Lebih banyak AJAX"

#~ msgid "More fields use AJAX powered search to speed up page loading"
#~ msgstr ""
#~ "Banyak bidang yang menggunakan pencarian AJAX untuk mempercepat loading "
#~ "laman"

#~ msgid "New auto export to JSON feature improves speed"
#~ msgstr "Ekspor otomatis ke fitur JSON meningkatkan kecepatan"

#~ msgid "Better version control"
#~ msgstr "Pengaturan versi terbaik"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "Ekspor otomatis ke fitur JSON memungkinkan pengaturan bidang menjadi "
#~ "versi yang terkontrol"

#~ msgid "Swapped XML for JSON"
#~ msgstr "Ubah XML ke JSON"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "Impor / ekspor sekarang menggunakan JSON yang mendukung XML"

#~ msgid "New Forms"
#~ msgstr "Form Baru"

#~ msgid "Fields can now be mapped to comments, widgets and all user forms!"
#~ msgstr ""
#~ "Bidang sekarang dapat dipetakan ke komentar, widget dan semua bentuk "
#~ "pengguna!"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "Bidang baru untuk melekatkan konten telah ditambahkan"

#~ msgid "New Gallery"
#~ msgstr "Galeri baru"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "Bidang Galeri telah mengalami banyak dibutuhkan facelift"

#~ msgid "New Settings"
#~ msgstr "Pengaturan baru"

#~ msgid ""
#~ "Field group settings have been added for label placement and instruction "
#~ "placement"
#~ msgstr ""
#~ "Pengaturan grup bidang telah ditambahkan untuk penempatan label dan "
#~ "penempatan instruksi"

#~ msgid "Better Front End Forms"
#~ msgstr "Form Front End Terbaik"

#~ msgid "acf_form() can now create a new post on submission"
#~ msgstr "acf_form() dapat membuat post baru saat di kirimkan"

#~ msgid "Better Validation"
#~ msgstr "Validasi lebih baik"

#~ msgid "Form validation is now done via PHP + AJAX in favour of only JS"
#~ msgstr ""
#~ "Validasi form sekarang dilakukan melalui PHP + AJAX yang sebelumnya hanya "
#~ "mendukung JS"

#~ msgid "Relationship Field"
#~ msgstr "Bidang hubungan"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr ""
#~ "Pengaturan bidang hubungan untuk 'Saringan' (Pencarian, Tipe Post, "
#~ "Taksonomi)"

#~ msgid "Moving Fields"
#~ msgstr "Memindahkan Bidang"

#~ msgid ""
#~ "New field group functionality allows you to move a field between groups & "
#~ "parents"
#~ msgstr ""
#~ "Fungsionalitas grup bidang memungkinkan Anda memindahkan bidang antara "
#~ "grup & parent"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "Grup arsip di page_link bidang seleksi"

#~ msgid "Better Options Pages"
#~ msgstr "Opsi Laman Lebih Baik"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Fungsi baru untuk opsi laman memungkinkan pembuatan laman menu parent dan "
#~ "child"

#, php-format
#~ msgid "We think you'll love the changes in %s."
#~ msgstr "Kami kira Anda akan menyukai perbahan di %s."

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Ekspor grup bidang ke PHP"

#~ msgid "Download export file"
#~ msgstr "Unduh berkas ekspor"

#~ msgid "Generate export code"
#~ msgstr "Hasilkan kode ekspor"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Peningkatan Database Advanced Custom Fields"

#~ msgid "Upgrading data to"
#~ msgstr "Meningkatkan data ke"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Sebelum anda mulai menggunakan fitur keren baru ini, silahkan perbarui "
#~ "database anda ke versi terbaru."

#~ msgid "See what's new"
#~ msgstr "Lihat apa yang baru"

#~ msgid "Show a different month"
#~ msgstr "Tampilkan bulan berbeda"

#~ msgid "Return format"
#~ msgstr "Kembalikan format"

#~ msgid "uploaded to this post"
#~ msgstr "diunggah ke post ini"

#~ msgid "File Size"
#~ msgstr "Ukuran Berkas"

#~ msgid "No File selected"
#~ msgstr "Tak ada file yang dipilih"

#~ msgid "Locating"
#~ msgstr "Melokasikan"

#~ msgid "Shown when entering data"
#~ msgstr "Tampilkan ketika memasukkan data"

#~ msgid "No embed found for the given URL."
#~ msgstr "Tidak ada embed ditemukan dari URL yang diberikan."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Nilai minimum mencapai (nilai {min})"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Bidang tab tidak akan tampil dengan baik ketika ditambahkan ke Gaya Tabel "
#~ "repeater atau layout bidang konten yang fleksibel"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "Gunakan \"Bidang Tab\" untuk mengatur penyuntingan layar anda dengan "
#~ "menggabungkan bidang bersamaan."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Semua bidang mengikuti \"bidang tab\" (atau sampai \"bidang tab\" lainnya "
#~ "ditemukan) akan dikelompokkan bersama-sama menggunakan label bidang ini "
#~ "sebagai judul tab."

#~ msgid "None"
#~ msgstr "Tidak ada"

#~ msgid "eg. Show extra content"
#~ msgstr "contoh. Tampilkan konten ekstra"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Kesalahan Koneksi.</b> Maaf, silakan coba lagi"

#~ msgid "Save Options"
#~ msgstr "Simpan Pengaturan"

#~ msgid "License"
#~ msgstr "Lisensi"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Untuk membuka update, masukkan kunci lisensi Anda di bawah ini. Jika Anda "
#~ "tidak memiliki kunci lisensi, silakan lihat"

#~ msgid "details & pricing"
#~ msgstr "rincian & harga"

#~ msgid "remove {layout}?"
#~ msgstr "singkirkan {layout}?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "Bidang ini membutuhkan setidaknya {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "Maksimum {label} mencapai ({max} {identifier})"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "http://www.elliotcondon.com/"
#~ msgstr "http://www.elliotcondon.com/"
