msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro\n"
"Report-Msgid-Bugs-To: https://support.advancedcustomfields.com\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: 2022-02-25 11:11+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: fi\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.4\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:3, pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: acf.php:4, acf.php:8
msgid "https://www.advancedcustomfields.com"
msgstr "http://www.advancedcustomfields.com"

#: acf.php:5
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Mukauta WordPressiä tehokkailla, ammattimaisilla ja intuitiivisilla kentillä."

#: acf.php:7
msgid "Delicious Brains"
msgstr "Delicious Brains"

#: acf.php:71
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:360, includes/admin/admin.php:50, includes/admin/admin.php:50
msgid "Field Groups"
msgstr "Kenttäryhmät"

#: acf.php:361
msgid "Field Group"
msgstr "Kenttäryhmä"

#: acf.php:362, acf.php:396, includes/admin/admin.php:51,
#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New"
msgstr "Lisää uusi"

#: acf.php:363
msgid "Add New Field Group"
msgstr "Lisää uusi kenttäryhmä"

#: acf.php:364
msgid "Edit Field Group"
msgstr "Muokkaa kenttäryhmää"

#: acf.php:365
msgid "New Field Group"
msgstr "Lisää uusi kenttäryhmä"

#: acf.php:366
msgid "View Field Group"
msgstr "Katso kenttäryhmää"

#: acf.php:367
msgid "Search Field Groups"
msgstr "Etsi kenttäryhmiä"

#: acf.php:368
msgid "No Field Groups found"
msgstr "Kenttäryhmiä ei löytynyt"

#: acf.php:369
msgid "No Field Groups found in Trash"
msgstr "Kenttäryhmiä ei löytynyt roskakorista"

#: acf.php:394, includes/admin/admin-field-group.php:233,
#: includes/admin/admin-field-groups.php:266,
#: pro/fields/class-acf-field-clone.php:814
msgid "Fields"
msgstr "Kentät"

#: acf.php:395
msgid "Field"
msgstr "Kenttä"

#: acf.php:397
msgid "Add New Field"
msgstr "Lisää uusi kenttä"

#: acf.php:398
msgid "Edit Field"
msgstr "Muokkaa kenttää"

#: acf.php:399, includes/admin/views/field-group-fields.php:56
msgid "New Field"
msgstr "Uusi kenttä"

#: acf.php:400
msgid "View Field"
msgstr "Näytä kenttä"

#: acf.php:401
msgid "Search Fields"
msgstr "Etsi kenttiä"

#: acf.php:402
msgid "No Fields found"
msgstr "Ei löytynyt kenttiä"

#: acf.php:403
msgid "No Fields found in Trash"
msgstr "Kenttiä ei löytynyt roskakorista"

#: acf.php:441, includes/admin/admin-field-group.php:385,
#: includes/admin/admin-field-groups.php:230
msgctxt "post status"
msgid "Disabled"
msgstr "Pois käytöstä"

#: acf.php:446
msgid "Disabled <span class=\"count\">(%s)</span>"
msgid_plural "Disabled <span class=\"count\">(%s)</span>"
msgstr[0] "Poistettu käytöstä <span class=”count”>(%s)</span>"
msgstr[1] "Poistettu käytöstä <span class=”count”>(%s)</span>"

#: acf.php:496
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Lisäosien Advanced Custom Fields ja Advanced Custom Fields PRO ei pitäisi "
"olla käytössä yhtäaikaa. Suljimme Advanced Custom Fields -lisäosan "
"automaattisesti."

#: acf.php:498
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Lisäosien Advanced Custom Fields ja Advanced Custom Fields PRO ei pitäisi "
"olla käytössä yhtäaikaa. Suljimme Advanced Custom Fields PRO -lisäosan "
"automaattisesti."

#: includes/acf-field-functions.php:841,
#: includes/admin/admin-field-group.php:171
msgid "(no label)"
msgstr "(ei nimiötä)"

#: includes/acf-field-group-functions.php:846,
#: includes/admin/admin-field-group.php:173
msgid "copy"
msgstr "kopio"

#: includes/acf-value-functions.php:353
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - Olemme havainneet yhden tai useamman kutsun ACF-"
"kenttäarvojen noutamiseksi ennen ACF:n alustamista. Tätä ei tueta ja se voi "
"johtaa väärin muotoiltuihin tai puuttuviin tietoihin. <a href=\"%2$s\" "
"target=\"_blank\">Lue lisää tämän korjaamisesta</a>."

#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "Artikkelit"

#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "Taksonomiat"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Liitteet"

#: includes/acf-wp-functions.php:63,
#: includes/admin/views/field-group-options.php:144
msgid "Comments"
msgstr "Kommentit"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Vimpaimet"

#: includes/acf-wp-functions.php:71,
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Valikot"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Valikkokohteet"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Käyttäjät"

#: includes/acf-wp-functions.php:83, pro/options-page.php:47
msgid "Options"
msgstr "Asetukset"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Lohkot"

#: includes/assets.php:348
msgid "Are you sure?"
msgstr "Oletko varma?"

#: includes/assets.php:349, includes/fields/class-acf-field-true_false.php:80,
#: includes/fields/class-acf-field-true_false.php:176,
#: pro/admin/views/html-settings-updates.php:104
msgid "Yes"
msgstr "Kyllä"

#: includes/assets.php:350, includes/fields/class-acf-field-true_false.php:83,
#: includes/fields/class-acf-field-true_false.php:193,
#: pro/admin/views/html-settings-updates.php:116
msgid "No"
msgstr "Ei"

#: includes/assets.php:351, includes/fields/class-acf-field-file.php:159,
#: includes/fields/class-acf-field-image.php:139,
#: includes/fields/class-acf-field-link.php:142,
#: pro/fields/class-acf-field-gallery.php:336,
#: pro/fields/class-acf-field-gallery.php:491
msgid "Remove"
msgstr "Poista"

#: includes/assets.php:352
msgid "Cancel"
msgstr "Peruuta"

#: includes/assets.php:362
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Tekemäsi muutokset menetetään, jos siirryt pois tältä sivulta"

#: includes/assets.php:365
msgid "Validation successful"
msgstr "Kenttäryhmän validointi onnistui"

#: includes/assets.php:366, includes/validation.php:286,
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Lisäkentän validointi epäonnistui"

#: includes/assets.php:367
msgid "1 field requires attention"
msgstr "Yksi kenttä vaatii huomiota"

#: includes/assets.php:368
msgid "%d fields require attention"
msgstr "%d kenttää vaativat huomiota"

#: includes/assets.php:371, includes/forms/form-comment.php:160,
#: pro/admin/admin-options-page.php:309
msgid "Edit field group"
msgstr "Muokkaa kenttäryhmää"

#: includes/fields.php:313
msgid "Field type does not exist"
msgstr "Kenttätyyppi ei ole olemassa"

#: includes/fields.php:313
msgid "Unknown"
msgstr "Tuntematon"

#: includes/fields.php:354
msgid "Basic"
msgstr "Perus"

#: includes/fields.php:355, includes/forms/form-front.php:49
msgid "Content"
msgstr "Sisältö"

#: includes/fields.php:356
msgid "Choice"
msgstr "Valintakentät"

#: includes/fields.php:357
msgid "Relational"
msgstr "Relationaalinen"

#: includes/fields.php:358
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:359,
#: includes/fields/class-acf-field-button-group.php:181,
#: includes/fields/class-acf-field-checkbox.php:377,
#: includes/fields/class-acf-field-group.php:462,
#: includes/fields/class-acf-field-radio.php:256,
#: pro/fields/class-acf-field-clone.php:850,
#: pro/fields/class-acf-field-flexible-content.php:549,
#: pro/fields/class-acf-field-flexible-content.php:604,
#: pro/fields/class-acf-field-repeater.php:451
msgid "Layout"
msgstr "Asettelu"

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr "Luokkaa \"%s\" ei ole."

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr "Sijaintityyppi \"%s\" on jo rekisteröity."

#: includes/locations.php:99, includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Artikkeli"

#: includes/locations.php:100,
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Sivu"

#: includes/locations.php:101, includes/fields/class-acf-field-user.php:20
msgid "User"
msgstr "Käyttäjä"

#: includes/locations.php:102
msgid "Forms"
msgstr "Lomakkeet"

#: includes/media.php:48, includes/fields/class-acf-field-select.php:255
msgctxt "verb"
msgid "Select"
msgstr "Valitse"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Muokkaa"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Päivitä"

#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Tähän kenttäryhmään ladatut kuvat"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Enemmän tietoja"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Vähemmän tietoja"

#: includes/media.php:54
msgid "Restricted"
msgstr "Rajoitettu"

#: includes/media.php:55, includes/fields/class-acf-field-image.php:67
msgid "All images"
msgstr "Kaikki kuvat"

#: includes/validation.php:364
msgid "%s value is required"
msgstr "%s arvo on pakollinen"

#: pro/blocks.php:37
msgid "Block type name is required."
msgstr "Lohkotyypin nimi on pakollinen."

#: pro/blocks.php:44
msgid "Block type \"%s\" is already registered."
msgstr "Lohkotyyppi \"%s\" on jo rekisteröity."

#: pro/blocks.php:495
msgid "Switch to Edit"
msgstr "Siirry muokkaamaan"

#: pro/blocks.php:496
msgid "Switch to Preview"
msgstr "Siirry esikatseluun"

#: pro/blocks.php:497
msgid "Change content alignment"
msgstr "Sisällön tasauksen muuttaminen"

#. translators: %s: Block type title
#: pro/blocks.php:500
msgid "%s settings"
msgstr "%s asetusta"

#: pro/options-page.php:77, includes/forms/form-front.php:106,
#: pro/fields/class-acf-field-gallery.php:523
msgid "Update"
msgstr "Päivitä"

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Asetukset päivitetty"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a href=\"%1$s"
"\">Updates</a> page. If you don't have a licence key, please see <a href="
"\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Ottaaksesi käyttöön päivitykset, syötä lisenssiavaimesi <a href=\"%1$s"
"\">Päivitykset</a> -sivulle. Jos sinulla ei ole lisenssiavainta, katso <a "
"href=\"%2$s\" target=\"_blank\">tiedot ja hinnoittelu</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>ACF:n aktivointivirhe</b>. Määritetty käyttöoikeusavain on muuttunut, "
"mutta vanhan käyttöoikeuden poistamisessa tapahtui virhe"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>ACF:n aktivointivirhe</b>. Määritetty käyttöoikeusavain on muuttunut, "
"mutta aktivointipalvelimeen yhdistämisessä tapahtui virhe"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr "<b>ACF:n aktivointivirhe</b>"

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""
"<b>ACF käynnistysvirhe</b>. Tapahtui virhe päivityspalvelimeen yhdistettäessä"

#: pro/updates.php:279, pro/admin/views/html-settings-updates.php:117
msgid "Check Again"
msgstr "Tarkista uudelleen"

#: pro/updates.php:561
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr "<b>ACF käynnistysvirhe</b>. Ei voitu yhdistää käynnistyspalvelimeen"

#: includes/admin/admin-field-group.php:84,
#: includes/admin/admin-field-group.php:85,
#: includes/admin/admin-field-group.php:87
msgid "Field group updated."
msgstr "Kenttäryhmä päivitetty."

#: includes/admin/admin-field-group.php:86
msgid "Field group deleted."
msgstr "Kenttäryhmä poistettu."

#: includes/admin/admin-field-group.php:89
msgid "Field group published."
msgstr "Kenttäryhmä julkaistu."

#: includes/admin/admin-field-group.php:90
msgid "Field group saved."
msgstr "Kenttäryhmä tallennettu."

#: includes/admin/admin-field-group.php:91
msgid "Field group submitted."
msgstr "Kenttäryhmä lähetetty."

#: includes/admin/admin-field-group.php:92
msgid "Field group scheduled for."
msgstr "Kenttäryhmä ajoitettu."

#: includes/admin/admin-field-group.php:93
msgid "Field group draft updated."
msgstr "Luonnos kenttäryhmästä päivitetty."

#: includes/admin/admin-field-group.php:164
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Merkkijonoa \"field_\" ei saa käyttää kentän nimen alussa"

#: includes/admin/admin-field-group.php:165
msgid "This field cannot be moved until its changes have been saved"
msgstr "Tätä kenttää ei voi siirtää ennen kuin muutokset on talletettu"

#: includes/admin/admin-field-group.php:166
msgid "Field group title is required"
msgstr "Kenttäryhmän otsikko on pakollinen"

#: includes/admin/admin-field-group.php:167
msgid "Move to trash. Are you sure?"
msgstr "Haluatko varmasti siirtää roskakoriin?"

#: includes/admin/admin-field-group.php:168
msgid "No toggle fields available"
msgstr "Ei vaihtokenttiä saatavilla"

#: includes/admin/admin-field-group.php:169
msgid "Move Custom Field"
msgstr "Siirrä muokattua kenttää"

#: includes/admin/admin-field-group.php:170
msgid "Checked"
msgstr "Valittu"

#: includes/admin/admin-field-group.php:172
msgid "(this field)"
msgstr "(tämä kenttä)"

#: includes/admin/admin-field-group.php:174, includes/api/api-helpers.php:3409,
#: includes/admin/views/field-group-field-conditional-logic.php:60,
#: includes/admin/views/field-group-field-conditional-logic.php:170,
#: includes/admin/views/field-group-locations.php:36,
#: includes/admin/views/html-location-group.php:3
msgid "or"
msgstr "tai"

#: includes/admin/admin-field-group.php:175,
#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Näytä tämä kenttäryhmä, jos"

#: includes/admin/admin-field-group.php:176
msgid "Null"
msgstr "Tyhjä"

#: includes/admin/admin-field-group.php:179
msgid "Has any value"
msgstr "On mitään arvoa"

#: includes/admin/admin-field-group.php:180
msgid "Has no value"
msgstr "Ei ole arvoa"

#: includes/admin/admin-field-group.php:181
msgid "Value is equal to"
msgstr "Arvo on sama kuin"

#: includes/admin/admin-field-group.php:182
msgid "Value is not equal to"
msgstr "Arvo ei ole sama kuin"

#: includes/admin/admin-field-group.php:183
msgid "Value matches pattern"
msgstr "Arvo vastaa kaavaa"

#: includes/admin/admin-field-group.php:184
msgid "Value contains"
msgstr "Arvo sisältää"

#: includes/admin/admin-field-group.php:185
msgid "Value is greater than"
msgstr "Arvo on suurempi kuin"

#: includes/admin/admin-field-group.php:186
msgid "Value is less than"
msgstr "Arvo on pienempi kuin"

#: includes/admin/admin-field-group.php:187
msgid "Selection is greater than"
msgstr "Valinta on suurempi kuin"

#: includes/admin/admin-field-group.php:188
msgid "Selection is less than"
msgstr "Valinta on pienempi kuin"

#: includes/admin/admin-field-group.php:191
msgid "Repeater (Pro only)"
msgstr "Toistin (vain Pro)"

#: includes/admin/admin-field-group.php:192
msgid "Flexible Content (Pro only)"
msgstr "Joustava sisältö (vain Pro)"

#: includes/admin/admin-field-group.php:193
msgid "Clone (Pro only)"
msgstr "Klooni (vain Pro)"

#: includes/admin/admin-field-group.php:194
msgid "Gallery (Pro only)"
msgstr "Galleria (vain Pro)"

#: includes/admin/admin-field-group.php:234,
#: includes/admin/admin-field-groups.php:265
msgid "Location"
msgstr "Sijainti"

#: includes/admin/admin-field-group.php:235,
#: includes/admin/tools/class-acf-admin-tool-export.php:288
msgid "Settings"
msgstr "Asetukset"

#: includes/admin/admin-field-group.php:361
msgid "Field Keys"
msgstr "Kenttäavaimet"

#: includes/admin/admin-field-group.php:385
msgctxt "post status"
msgid "Active"
msgstr "Käytössä"

#: includes/admin/admin-field-group.php:752
msgid "Move Complete."
msgstr "Siirto valmis."

#: includes/admin/admin-field-group.php:754
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Kenttä %1$s löytyy nyt kenttäryhmästä %2$s"

#: includes/admin/admin-field-group.php:758
msgid "Close Window"
msgstr "Sulje ikkuna"

#: includes/admin/admin-field-group.php:797
msgid "Please select the destination for this field"
msgstr "Valitse kohde kentälle"

#: includes/admin/admin-field-group.php:804
msgid "Move Field"
msgstr "Siirrä kenttä"

#: includes/admin/admin-field-groups.php:116
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Käytössä <span class=\"count\">(%s)</span>"
msgstr[1] "Käytössä <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:196
msgid "Review local JSON changes"
msgstr "Tarkista paikalliset JSON-muutokset"

#: includes/admin/admin-field-groups.php:197
msgid "Loading diff"
msgstr "Ladataan diff"

#: includes/admin/admin-field-groups.php:198,
#: includes/admin/admin-field-groups.php:533
msgid "Sync changes"
msgstr "Synkronoi muutokset"

#: includes/admin/admin-field-groups.php:263,
#: pro/fields/class-acf-field-gallery.php:388,
#: includes/admin/views/field-group-options.php:127,
#: includes/admin/views/html-admin-page-upgrade-network.php:38,
#: includes/admin/views/html-admin-page-upgrade-network.php:49
msgid "Description"
msgstr "Kuvaus"

#: includes/admin/admin-field-groups.php:264,
#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Avain"

#: includes/admin/admin-field-groups.php:269
msgid "Local JSON"
msgstr "Paikallinen JSON"

#: includes/admin/admin-field-groups.php:419
msgid "Various"
msgstr "Sekalaisia"

#: includes/admin/admin-field-groups.php:449
msgid "Located in: %s"
msgstr "Sijaitsee: %s"

#: includes/admin/admin-field-groups.php:445
msgid "Located in plugin: %s"
msgstr "Lisäosalla: %s"

#: includes/admin/admin-field-groups.php:441
msgid "Located in theme: %s"
msgstr "Teemalla: %s"

#: includes/admin/admin-field-groups.php:484
msgid "Awaiting save"
msgstr "Odottaa tallentamista"

#: includes/admin/admin-field-groups.php:481
msgid "Saved"
msgstr "Tallennettu"

#: includes/admin/admin-field-groups.php:469,
#: includes/admin/admin-field-groups.php:687
msgid "Sync available"
msgstr "Synkronointi saatavissa"

#: includes/admin/admin-field-groups.php:477
msgid "Import"
msgstr "Tuo"

#: includes/admin/admin-field-groups.php:472
msgid "Sync"
msgstr "Synkronointi"

#: includes/admin/admin-field-groups.php:473
msgid "Review changes"
msgstr "Tarkista muutokset"

#: includes/admin/admin-field-groups.php:505
msgid "Duplicate this item"
msgstr "Monista tämä kohde"

#: includes/admin/admin-field-groups.php:505,
#: includes/admin/admin-field-groups.php:525,
#: pro/fields/class-acf-field-flexible-content.php:553,
#: includes/admin/views/field-group-field.php:57
msgid "Duplicate"
msgstr "Monista"

#: includes/admin/admin-field-groups.php:555
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Kenttäryhmä monistettu."
msgstr[1] "%s kenttäryhmää monistettu."

#: includes/admin/admin-field-groups.php:612
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Kenttäryhmä synkronoitu."
msgstr[1] "%s kenttäryhmää synkronoitu."

#: includes/admin/admin-field-groups.php:800
msgid "Select %s"
msgstr "Valitse %s"

#: includes/admin/admin-tools.php:119,
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Työkalut"

#: includes/admin/admin-upgrade.php:51, includes/admin/admin-upgrade.php:113,
#: includes/admin/admin-upgrade.php:114, includes/admin/admin-upgrade.php:177,
#: includes/admin/views/html-admin-page-upgrade-network.php:24,
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Päivitä tietokanta"

#: includes/admin/admin-upgrade.php:201
msgid "Review sites & upgrade"
msgstr "Tarkastele sivuja & päivitä"

#: includes/admin/admin.php:49,
#: includes/admin/views/field-group-options.php:142
msgid "Custom Fields"
msgstr "Lisäkentät"

#: includes/admin/admin.php:129, includes/admin/admin.php:131
msgid "Overview"
msgstr "Yleiskatsaus"

#: includes/admin/admin.php:132
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Advanced Custom Fields -lisäosa tarjoaa visuaalisen lomaketyökalun "
"WordPressin muokkausnäyttöjen mukauttamiseksi ylimääräisillä kentillä ja "
"intuitiivisen API:n mukautettujen kenttäarvojen näyttämiseksi missä tahansa "
"teeman mallitiedostossa."

#: includes/admin/admin.php:134
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Ennen kuin luot ensimmäisen kenttäryhmäsi, suosittelemme lukemaan <a href="
"\"%s\" target=\"_blank\">aloitusoppaamme</a>, jossa tutustutaan lisäosan "
"filosofiaan ja parhaisiin käytäntöihin."

#: includes/admin/admin.php:137
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Ota yhteyttä Ohjeet & tukipalvelut -välilehdessä, jos huomaat tarvitsevasi "
"apua."

#: includes/admin/admin.php:146, includes/admin/admin.php:148
msgid "Help & Support"
msgstr "Ohjeet & tukipalvelut"

#: includes/admin/admin.php:149
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Olemme fanaattisia tuen suhteen ja haluamme, että saat kaiken mahdollisen "
"irti verkkosivustostasi ACF:n avulla. Jos kohtaat ongelmia, apua löytyy "
"useista paikoista:"

#: includes/admin/admin.php:152
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Dokumentaatio</a>. Laaja dokumentaatiomme "
"sisältää viittauksia ja oppaita useimpiin kohtaamiisi tilanteisiin."

#: includes/admin/admin.php:156
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Keskustelut</a>. Yhteisöfoorumeillamme on "
"aktiivinen ja ystävällinen yhteisö, joka voi ehkä auttaa sinua selvittämään "
"ACF-maailman ihmeellisyyksiä."

#: includes/admin/admin.php:160
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Tukipalvelu</a>. Tukipalvelumme "
"ammattilaiset auttavat syvällisemmissä teknisissä haasteissasi."

#: includes/admin/admin.php:169
msgid "Information"
msgstr "Tiedot"

#: includes/admin/admin.php:170
msgid "Version %s"
msgstr "Versio %s"

#: includes/admin/admin.php:171
msgid "View details"
msgstr "Näytä tarkemmat tiedot"

#: includes/admin/admin.php:172
msgid "Visit website"
msgstr "Siirry verkkosivuille"

#: includes/admin/admin.php:201,
#: includes/admin/views/field-group-field-conditional-logic.php:157,
#: includes/admin/views/html-location-rule.php:92
msgid "and"
msgstr "ja"

#: includes/ajax/class-acf-ajax-local-json-diff.php:36
msgid "Invalid field group parameter(s)."
msgstr "Virheelliset kenttäryhmän parametrit."

#: includes/ajax/class-acf-ajax-local-json-diff.php:43
msgid "Invalid field group ID."
msgstr "Virheellinen kenttäryhmän tunnus."

#: includes/ajax/class-acf-ajax-local-json-diff.php:53
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr ""
"Tämä kenttäryhmä ei valitettavasti ole käytettävissä diff-vertailua varten."

#: includes/ajax/class-acf-ajax-local-json-diff.php:59
msgid "Last updated: %s"
msgstr "Päivitetty viimeksi: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:64
msgid "Original field group"
msgstr "Alkuperäinen kenttäryhmä"

#: includes/ajax/class-acf-ajax-local-json-diff.php:68
msgid "JSON field group (newer)"
msgstr "JSON-kenttäryhmä (uusi)"

#: includes/ajax/class-acf-ajax-upgrade.php:34,
#: includes/admin/views/html-admin-page-upgrade.php:94
msgid "No updates available."
msgstr "Päivityksiä ei ole saatavilla."

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Virheellinen nonce."

#: includes/api/api-helpers.php:821
msgid "Thumbnail"
msgstr "Pienoiskuva"

#: includes/api/api-helpers.php:822
msgid "Medium"
msgstr "Keskikokoinen"

#: includes/api/api-helpers.php:823
msgid "Large"
msgstr "Iso"

#: includes/api/api-helpers.php:864
msgid "Full Size"
msgstr "Täysikokoinen"

#: includes/api/api-helpers.php:1569, includes/api/api-term.php:147,
#: pro/fields/class-acf-field-clone.php:1005
msgid "(no title)"
msgstr "(ei otsikkoa)"

#: includes/api/api-helpers.php:3343
msgid "Image width must not exceed %dpx."
msgstr "Kuvan leveys ei saa ylittää %dpx."

#: includes/api/api-helpers.php:3338
msgid "Image width must be at least %dpx."
msgstr "Kuvan leveys täytyy olla vähintään %dpx."

#: includes/api/api-helpers.php:3362
msgid "Image height must not exceed %dpx."
msgstr "Kuvan korkeus ei saa ylittää %dpx."

#: includes/api/api-helpers.php:3357
msgid "Image height must be at least %dpx."
msgstr "Kuvan korkeus täytyy olla vähintään %dpx."

#: includes/api/api-helpers.php:3382
msgid "File size must not exceed %s."
msgstr "Tiedoston koko ei saa ylittää %s."

#: includes/api/api-helpers.php:3377
msgid "File size must be at least %s."
msgstr "Tiedoston koko täytyy olla vähintään %s."

#: includes/api/api-helpers.php:3413
msgid "File type must be %s."
msgstr "Tiedoston koko täytyy olla %s."

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Haitari (Accordion)"

#: includes/fields/class-acf-field-accordion.php:102
msgid "Open"
msgstr "Avoinna"

#: includes/fields/class-acf-field-accordion.php:103
msgid "Display this accordion as open on page load."
msgstr "Näytä tämä haitari avoimena sivun latautuessa."

#: includes/fields/class-acf-field-accordion.php:114
msgid "Multi-expand"
msgstr "Avaa useita"

#: includes/fields/class-acf-field-accordion.php:115
msgid "Allow this accordion to open without closing others."
msgstr "Salli tämän haitarin avautua sulkematta muita."

#: includes/fields/class-acf-field-accordion.php:126,
#: includes/fields/class-acf-field-tab.php:117
msgid "Endpoint"
msgstr "Päätepiste"

#: includes/fields/class-acf-field-accordion.php:127
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Määritä päätepiste aiemmalle haitarille. Tämä haitari ei tule näkyviin."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Painikeryhmä"

#: includes/fields/class-acf-field-button-group.php:147,
#: includes/fields/class-acf-field-checkbox.php:324,
#: includes/fields/class-acf-field-radio.php:191,
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "Valinnat"

#: includes/fields/class-acf-field-button-group.php:148,
#: includes/fields/class-acf-field-checkbox.php:325,
#: includes/fields/class-acf-field-radio.php:192,
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "Syötä jokainen valinta uudelle riville."

#: includes/fields/class-acf-field-button-group.php:148,
#: includes/fields/class-acf-field-checkbox.php:325,
#: includes/fields/class-acf-field-radio.php:192,
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr "Halutessasi voit määrittää sekä arvon että nimiön tähän tapaan:"

#: includes/fields/class-acf-field-button-group.php:148,
#: includes/fields/class-acf-field-checkbox.php:325,
#: includes/fields/class-acf-field-radio.php:192,
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "koira_istuu : Koira istuu"

#: includes/fields/class-acf-field-button-group.php:158,
#: includes/fields/class-acf-field-page_link.php:482,
#: includes/fields/class-acf-field-post_object.php:394,
#: includes/fields/class-acf-field-radio.php:202,
#: includes/fields/class-acf-field-select.php:386,
#: includes/fields/class-acf-field-taxonomy.php:748,
#: includes/fields/class-acf-field-user.php:68
msgid "Allow Null?"
msgstr "Salli tyhjä?"

#: includes/fields/class-acf-field-button-group.php:170,
#: includes/fields/class-acf-field-checkbox.php:366,
#: includes/fields/class-acf-field-color_picker.php:155,
#: includes/fields/class-acf-field-email.php:117,
#: includes/fields/class-acf-field-number.php:125,
#: includes/fields/class-acf-field-radio.php:245,
#: includes/fields/class-acf-field-range.php:163,
#: includes/fields/class-acf-field-select.php:375,
#: includes/fields/class-acf-field-text.php:97,
#: includes/fields/class-acf-field-textarea.php:103,
#: includes/fields/class-acf-field-true_false.php:148,
#: includes/fields/class-acf-field-url.php:101,
#: includes/fields/class-acf-field-wysiwyg.php:334
msgid "Default Value"
msgstr "Oletusarvo"

#: includes/fields/class-acf-field-button-group.php:171,
#: includes/fields/class-acf-field-email.php:118,
#: includes/fields/class-acf-field-number.php:126,
#: includes/fields/class-acf-field-radio.php:246,
#: includes/fields/class-acf-field-range.php:164,
#: includes/fields/class-acf-field-text.php:98,
#: includes/fields/class-acf-field-textarea.php:104,
#: includes/fields/class-acf-field-url.php:102,
#: includes/fields/class-acf-field-wysiwyg.php:335
msgid "Appears when creating a new post"
msgstr "Kentän oletusarvo"

#: includes/fields/class-acf-field-button-group.php:187,
#: includes/fields/class-acf-field-checkbox.php:384,
#: includes/fields/class-acf-field-radio.php:263
msgid "Horizontal"
msgstr "Vaakasuuntainen"

#: includes/fields/class-acf-field-button-group.php:188,
#: includes/fields/class-acf-field-checkbox.php:383,
#: includes/fields/class-acf-field-radio.php:262
msgid "Vertical"
msgstr "Pystysuuntainen"

#: includes/fields/class-acf-field-button-group.php:197,
#: includes/fields/class-acf-field-checkbox.php:405,
#: includes/fields/class-acf-field-file.php:227,
#: includes/fields/class-acf-field-link.php:170,
#: includes/fields/class-acf-field-radio.php:272,
#: includes/fields/class-acf-field-taxonomy.php:801
msgid "Return Value"
msgstr "Palauta arvo"

#: includes/fields/class-acf-field-button-group.php:198,
#: includes/fields/class-acf-field-checkbox.php:406,
#: includes/fields/class-acf-field-file.php:228,
#: includes/fields/class-acf-field-link.php:171,
#: includes/fields/class-acf-field-radio.php:273
msgid "Specify the returned value on front end"
msgstr "Määritä palautettu arvo front endiin"

#: includes/fields/class-acf-field-button-group.php:203,
#: includes/fields/class-acf-field-checkbox.php:411,
#: includes/fields/class-acf-field-radio.php:278,
#: includes/fields/class-acf-field-select.php:444
msgid "Value"
msgstr "Arvo"

#: includes/fields/class-acf-field-button-group.php:204,
#: includes/fields/class-acf-field-checkbox.php:412,
#: includes/fields/class-acf-field-radio.php:279,
#: includes/fields/class-acf-field-select.php:445,
#: pro/fields/class-acf-field-flexible-content.php:581,
#: includes/admin/views/field-group-fields.php:5
msgid "Label"
msgstr "Nimiö"

#: includes/fields/class-acf-field-button-group.php:205,
#: includes/fields/class-acf-field-checkbox.php:413,
#: includes/fields/class-acf-field-radio.php:280,
#: includes/fields/class-acf-field-select.php:446
msgid "Both (Array)"
msgstr "Molemmat (palautusmuoto on tällöin taulukko)"

#: includes/fields/class-acf-field-checkbox.php:25,
#: includes/fields/class-acf-field-taxonomy.php:733
msgid "Checkbox"
msgstr "Valintaruutu"

#: includes/fields/class-acf-field-checkbox.php:144
msgid "Toggle All"
msgstr "Valitse kaikki"

#: includes/fields/class-acf-field-checkbox.php:207
msgid "Add new choice"
msgstr "Lisää uusi valinta"

#: includes/fields/class-acf-field-checkbox.php:335
msgid "Allow Custom"
msgstr "Salli mukautettu"

#: includes/fields/class-acf-field-checkbox.php:340
msgid "Allow 'custom' values to be added"
msgstr "Salli käyttäjän syöttää omia arvojaan"

#: includes/fields/class-acf-field-checkbox.php:348
msgid "Save Custom"
msgstr "Tallenna mukautettu"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Save 'custom' values to the field's choices"
msgstr ""
"Tallenna 'Muu’-kentän arvo kentän valinta vaihtoehdoksi tulevaisuudessa"

#: includes/fields/class-acf-field-checkbox.php:367,
#: includes/fields/class-acf-field-select.php:376
msgid "Enter each default value on a new line"
msgstr "Syötä jokainen oletusarvo uudelle riville"

#: includes/fields/class-acf-field-checkbox.php:393
msgid "Toggle"
msgstr "Valitse"

#: includes/fields/class-acf-field-checkbox.php:394
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Näytetäänkö ”Valitse kaikki” -valintaruutu"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Värinvalitsin"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Clear"
msgstr "Tyhjennä"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Clear color"
msgstr "Tyhjennä väri"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Default"
msgstr "Oletus"

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Select default color"
msgstr "Valitse oletusväri"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Select Color"
msgstr "Valitse väri"

#: includes/fields/class-acf-field-color_picker.php:74
msgid "Color value"
msgstr "Väriarvo"

#: includes/fields/class-acf-field-color_picker.php:95,
#: includes/fields/class-acf-field-color_picker.php:185
msgid "Hex String"
msgstr "Heksamerkkijono"

#: includes/fields/class-acf-field-color_picker.php:96
msgid "RGBA String"
msgstr "RGBA-merkkijono"

#: includes/fields/class-acf-field-color_picker.php:167
msgid "Enable Transparency"
msgstr "Ota läpinäkyvyys käyttöön"

#: includes/fields/class-acf-field-color_picker.php:179,
#: includes/fields/class-acf-field-date_picker.php:216,
#: includes/fields/class-acf-field-date_time_picker.php:201,
#: includes/fields/class-acf-field-image.php:204,
#: includes/fields/class-acf-field-post_object.php:418,
#: includes/fields/class-acf-field-relationship.php:662,
#: includes/fields/class-acf-field-select.php:439,
#: includes/fields/class-acf-field-time_picker.php:131,
#: includes/fields/class-acf-field-user.php:90,
#: pro/fields/class-acf-field-gallery.php:573
msgid "Return Format"
msgstr "Palautusmuoto"

#: includes/fields/class-acf-field-color_picker.php:186
msgid "RGBA Array"
msgstr "RGBA-taulukko"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Päivämäärävalitsin"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Sulje"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Tänään"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Seuraava"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Edellinen"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Vk"

#: includes/fields/class-acf-field-date_picker.php:181,
#: includes/fields/class-acf-field-date_time_picker.php:182,
#: includes/fields/class-acf-field-time_picker.php:114
msgid "Display Format"
msgstr "Muokkausnäkymän muoto"

#: includes/fields/class-acf-field-date_picker.php:182,
#: includes/fields/class-acf-field-date_time_picker.php:183,
#: includes/fields/class-acf-field-time_picker.php:115
msgid "The format displayed when editing a post"
msgstr "Päivämäärän muoto muokkausnäkymässä"

#: includes/fields/class-acf-field-date_picker.php:190,
#: includes/fields/class-acf-field-date_picker.php:226,
#: includes/fields/class-acf-field-date_time_picker.php:192,
#: includes/fields/class-acf-field-date_time_picker.php:211,
#: includes/fields/class-acf-field-time_picker.php:122,
#: includes/fields/class-acf-field-time_picker.php:139
msgid "Custom:"
msgstr "Mukautettu:"

#: includes/fields/class-acf-field-date_picker.php:217,
#: includes/fields/class-acf-field-date_time_picker.php:202,
#: includes/fields/class-acf-field-time_picker.php:132
msgid "The format returned via template functions"
msgstr "Sivupohjan funktioiden palauttama päivämäärän muoto"

#: includes/fields/class-acf-field-date_picker.php:202
msgid "Save Format"
msgstr "Tallennusmuoto"

#: includes/fields/class-acf-field-date_picker.php:203
msgid "The format used when saving a value"
msgstr "Arvo tallennetaan tähän muotoon"

#: includes/fields/class-acf-field-date_picker.php:237,
#: includes/fields/class-acf-field-date_time_picker.php:220
msgid "Week Starts On"
msgstr "Viikon ensimmäinen päivä"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Päivämäärä- ja kellonaikavalitsin"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Valitse aika"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Aika"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Tunti"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuutti"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekunti"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisekunti"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekunti"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Aikavyöhyke"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Nyt"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Sulje"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Valitse"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Sähköposti"

#: includes/fields/class-acf-field-email.php:128,
#: includes/fields/class-acf-field-number.php:136,
#: includes/fields/class-acf-field-password.php:73,
#: includes/fields/class-acf-field-text.php:108,
#: includes/fields/class-acf-field-textarea.php:114,
#: includes/fields/class-acf-field-url.php:112
msgid "Placeholder Text"
msgstr "Täyteteksti"

#: includes/fields/class-acf-field-email.php:129,
#: includes/fields/class-acf-field-number.php:137,
#: includes/fields/class-acf-field-password.php:74,
#: includes/fields/class-acf-field-text.php:109,
#: includes/fields/class-acf-field-textarea.php:115,
#: includes/fields/class-acf-field-url.php:113
msgid "Appears within the input"
msgstr "Näkyy input-kentän sisällä"

#: includes/fields/class-acf-field-email.php:139,
#: includes/fields/class-acf-field-number.php:147,
#: includes/fields/class-acf-field-password.php:84,
#: includes/fields/class-acf-field-range.php:210,
#: includes/fields/class-acf-field-text.php:119
msgid "Prepend"
msgstr "Etuliite"

#: includes/fields/class-acf-field-email.php:140,
#: includes/fields/class-acf-field-number.php:148,
#: includes/fields/class-acf-field-password.php:85,
#: includes/fields/class-acf-field-range.php:211,
#: includes/fields/class-acf-field-text.php:120
msgid "Appears before the input"
msgstr "Näkyy ennen input-kenttää"

#: includes/fields/class-acf-field-email.php:150,
#: includes/fields/class-acf-field-number.php:158,
#: includes/fields/class-acf-field-password.php:95,
#: includes/fields/class-acf-field-range.php:221,
#: includes/fields/class-acf-field-text.php:130
msgid "Append"
msgstr "Loppuliite"

#: includes/fields/class-acf-field-email.php:151,
#: includes/fields/class-acf-field-number.php:159,
#: includes/fields/class-acf-field-password.php:96,
#: includes/fields/class-acf-field-range.php:222,
#: includes/fields/class-acf-field-text.php:131
msgid "Appears after the input"
msgstr "Näkyy input-kentän jälkeen"

#: includes/fields/class-acf-field-email.php:175
msgid "'%s' is not a valid email address"
msgstr "'%s' ei ole kelvollinen sähköpostiosoite"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Tiedosto"

#: includes/fields/class-acf-field-file.php:58,
#: includes/admin/tools/class-acf-admin-tool-import.php:55
msgid "Select File"
msgstr "Valitse tiedosto"

#: includes/fields/class-acf-field-file.php:59
msgid "Edit File"
msgstr "Muokkaa tiedostoa"

#: includes/fields/class-acf-field-file.php:60
msgid "Update File"
msgstr "Päivitä tiedosto"

#: includes/fields/class-acf-field-file.php:147
msgid "File name"
msgstr "Tiedoston nimi"

#: includes/fields/class-acf-field-file.php:151,
#: includes/fields/class-acf-field-file.php:264,
#: includes/fields/class-acf-field-file.php:277,
#: includes/fields/class-acf-field-image.php:276,
#: includes/fields/class-acf-field-image.php:313,
#: pro/fields/class-acf-field-gallery.php:682,
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr "Tiedoston koko"

#: includes/fields/class-acf-field-file.php:157,
#: includes/fields/class-acf-field-image.php:137,
#: includes/fields/class-acf-field-link.php:142,
#: pro/fields/class-acf-field-gallery.php:335,
#: includes/admin/views/field-group-field.php:56
msgid "Edit"
msgstr "Muokkaa"

#: includes/fields/class-acf-field-file.php:182,
#: includes/admin/tools/class-acf-admin-tool-import.php:89
msgid "No file selected"
msgstr "Ei valittua tiedostoa"

#: includes/fields/class-acf-field-file.php:182
msgid "Add File"
msgstr "Lisää tiedosto"

#: includes/fields/class-acf-field-file.php:233
msgid "File Array"
msgstr "Tiedosto"

#: includes/fields/class-acf-field-file.php:234
msgid "File URL"
msgstr "Tiedoston URL"

#: includes/fields/class-acf-field-file.php:235
msgid "File ID"
msgstr "Tiedoston ID"

#: includes/fields/class-acf-field-file.php:244,
#: includes/fields/class-acf-field-image.php:233,
#: pro/fields/class-acf-field-gallery.php:617
msgid "Library"
msgstr "Kirjasto"

#: includes/fields/class-acf-field-file.php:245,
#: includes/fields/class-acf-field-image.php:234,
#: pro/fields/class-acf-field-gallery.php:618
msgid "Limit the media library choice"
msgstr "Rajoita valintaa mediakirjastosta"

#: includes/fields/class-acf-field-file.php:250,
#: includes/fields/class-acf-field-image.php:239,
#: includes/locations/class-acf-location-attachment.php:73,
#: includes/locations/class-acf-location-comment.php:61,
#: includes/locations/class-acf-location-nav-menu.php:74,
#: includes/locations/class-acf-location-taxonomy.php:63,
#: includes/locations/class-acf-location-user-form.php:71,
#: includes/locations/class-acf-location-user-role.php:78,
#: includes/locations/class-acf-location-widget.php:65,
#: pro/fields/class-acf-field-gallery.php:623,
#: pro/locations/class-acf-location-block.php:66
msgid "All"
msgstr "Kaikki"

#: includes/fields/class-acf-field-file.php:251,
#: includes/fields/class-acf-field-image.php:240,
#: pro/fields/class-acf-field-gallery.php:624
msgid "Uploaded to post"
msgstr "Vain tähän artikkeliin ladatut"

#: includes/fields/class-acf-field-file.php:260,
#: includes/fields/class-acf-field-image.php:249,
#: pro/fields/class-acf-field-gallery.php:655
msgid "Minimum"
msgstr "Minimiarvo(t)"

#: includes/fields/class-acf-field-file.php:261,
#: includes/fields/class-acf-field-file.php:274
msgid "Restrict which files can be uploaded"
msgstr "Määritä tiedoston koko"

#: includes/fields/class-acf-field-file.php:273,
#: includes/fields/class-acf-field-image.php:286,
#: pro/fields/class-acf-field-gallery.php:692
msgid "Maximum"
msgstr "Maksimiarvo(t)"

#: includes/fields/class-acf-field-file.php:286,
#: includes/fields/class-acf-field-image.php:323,
#: pro/fields/class-acf-field-gallery.php:729
msgid "Allowed file types"
msgstr "Sallitut tiedostotyypit"

#: includes/fields/class-acf-field-file.php:287,
#: includes/fields/class-acf-field-image.php:324,
#: pro/fields/class-acf-field-gallery.php:730
msgid "Comma separated list. Leave blank for all types"
msgstr "Erota pilkulla. Jätä tyhjäksi, jos haluat sallia kaikki tiedostyypit"

#: includes/fields/class-acf-field-file.php:469
msgid "%s requires a valid attachment ID."
msgstr "%s edellyttää kelvollista liitetunnusta (ID)."

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google-kartta"

#: includes/fields/class-acf-field-google-map.php:60
msgid "Sorry, this browser does not support geolocation"
msgstr "Pahoittelut, tämä selain ei tue paikannusta"

#: includes/fields/class-acf-field-google-map.php:155,
#: includes/fields/class-acf-field-relationship.php:615
msgid "Search"
msgstr "Etsi"

#: includes/fields/class-acf-field-google-map.php:156
msgid "Clear location"
msgstr "Tyhjennä paikkatieto"

#: includes/fields/class-acf-field-google-map.php:157
msgid "Find current location"
msgstr "Etsi nykyinen sijainti"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Search for address..."
msgstr "Etsi osoite..."

#: includes/fields/class-acf-field-google-map.php:192,
#: includes/fields/class-acf-field-google-map.php:205
msgid "Center"
msgstr "Sijainti"

#: includes/fields/class-acf-field-google-map.php:193,
#: includes/fields/class-acf-field-google-map.php:206
msgid "Center the initial map"
msgstr "Kartan oletussijainti"

#: includes/fields/class-acf-field-google-map.php:219
msgid "Zoom"
msgstr "Zoomaus"

#: includes/fields/class-acf-field-google-map.php:220
msgid "Set the initial zoom level"
msgstr "Aseta oletuszoomaus"

#: includes/fields/class-acf-field-google-map.php:231,
#: includes/fields/class-acf-field-image.php:264,
#: includes/fields/class-acf-field-image.php:301,
#: includes/fields/class-acf-field-oembed.php:292,
#: pro/fields/class-acf-field-gallery.php:670,
#: pro/fields/class-acf-field-gallery.php:707
msgid "Height"
msgstr "Korkeus"

#: includes/fields/class-acf-field-google-map.php:232
msgid "Customize the map height"
msgstr "Kartan korkeuden mukauttaminen"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Ryhmä"

#: includes/fields/class-acf-field-group.php:446,
#: pro/fields/class-acf-field-repeater.php:381
msgid "Sub Fields"
msgstr "Alakentät"

#: includes/fields/class-acf-field-group.php:463,
#: pro/fields/class-acf-field-clone.php:851
msgid "Specify the style used to render the selected fields"
msgstr "Määritä tyyli, jota käytetään valittujen kenttien luomisessa"

#: includes/fields/class-acf-field-group.php:468,
#: pro/fields/class-acf-field-clone.php:856,
#: pro/fields/class-acf-field-flexible-content.php:617,
#: pro/fields/class-acf-field-repeater.php:459,
#: pro/locations/class-acf-location-block.php:22
msgid "Block"
msgstr "Lohko"

#: includes/fields/class-acf-field-group.php:469,
#: pro/fields/class-acf-field-clone.php:857,
#: pro/fields/class-acf-field-flexible-content.php:616,
#: pro/fields/class-acf-field-repeater.php:458
msgid "Table"
msgstr "Taulukko"

#: includes/fields/class-acf-field-group.php:470,
#: pro/fields/class-acf-field-clone.php:858,
#: pro/fields/class-acf-field-flexible-content.php:618,
#: pro/fields/class-acf-field-repeater.php:460
msgid "Row"
msgstr "Rivi"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Kuva"

#: includes/fields/class-acf-field-image.php:64
msgid "Select Image"
msgstr "Valitse kuva"

#: includes/fields/class-acf-field-image.php:65
msgid "Edit Image"
msgstr "Muokkaa kuvaa"

#: includes/fields/class-acf-field-image.php:66
msgid "Update Image"
msgstr "Päivitä kuva"

#: includes/fields/class-acf-field-image.php:158
msgid "No image selected"
msgstr "Ei kuvia valittu"

#: includes/fields/class-acf-field-image.php:158
msgid "Add Image"
msgstr "Lisää kuva"

#: includes/fields/class-acf-field-image.php:210,
#: pro/fields/class-acf-field-gallery.php:579
msgid "Image Array"
msgstr "Kuva"

#: includes/fields/class-acf-field-image.php:211,
#: pro/fields/class-acf-field-gallery.php:580
msgid "Image URL"
msgstr "Kuvan URL"

#: includes/fields/class-acf-field-image.php:212,
#: pro/fields/class-acf-field-gallery.php:581
msgid "Image ID"
msgstr "Kuvan ID"

#: includes/fields/class-acf-field-image.php:221,
#: pro/fields/class-acf-field-gallery.php:590
msgid "Preview Size"
msgstr "Esikatselukuvan koko"

#: includes/fields/class-acf-field-image.php:250,
#: includes/fields/class-acf-field-image.php:287,
#: pro/fields/class-acf-field-gallery.php:656,
#: pro/fields/class-acf-field-gallery.php:693
msgid "Restrict which images can be uploaded"
msgstr "Määritä millaisia kuvia voidaan ladata"

#: includes/fields/class-acf-field-image.php:253,
#: includes/fields/class-acf-field-image.php:290,
#: includes/fields/class-acf-field-oembed.php:279,
#: pro/fields/class-acf-field-gallery.php:659,
#: pro/fields/class-acf-field-gallery.php:696
msgid "Width"
msgstr "Leveys"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Linkki"

#: includes/fields/class-acf-field-link.php:137
msgid "Select Link"
msgstr "Valitse linkki"

#: includes/fields/class-acf-field-link.php:142
msgid "Opens in a new window/tab"
msgstr "Avaa uuteen ikkunaan/välilehteen"

#: includes/fields/class-acf-field-link.php:176
msgid "Link Array"
msgstr "Linkkitaulukko (array)"

#: includes/fields/class-acf-field-link.php:177
msgid "Link URL"
msgstr "Linkin URL-osoite"

#: includes/fields/class-acf-field-message.php:26,
#: includes/fields/class-acf-field-message.php:100,
#: includes/fields/class-acf-field-true_false.php:137
msgid "Message"
msgstr "Viesti"

#: includes/fields/class-acf-field-message.php:111,
#: includes/fields/class-acf-field-textarea.php:148
msgid "New Lines"
msgstr "Uudet rivit"

#: includes/fields/class-acf-field-message.php:112,
#: includes/fields/class-acf-field-textarea.php:149
msgid "Controls how new lines are rendered"
msgstr "Määrittää kuinka uudet rivit muotoillaan"

#: includes/fields/class-acf-field-message.php:116,
#: includes/fields/class-acf-field-textarea.php:153
msgid "Automatically add paragraphs"
msgstr "Lisää automaattisesti kappale"

#: includes/fields/class-acf-field-message.php:117,
#: includes/fields/class-acf-field-textarea.php:154
msgid "Automatically add &lt;br&gt;"
msgstr "Lisää automaattisesti &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:118,
#: includes/fields/class-acf-field-textarea.php:155
msgid "No Formatting"
msgstr "Ei muotoilua"

#: includes/fields/class-acf-field-message.php:127
msgid "Escape HTML"
msgstr "Escape HTML"

#: includes/fields/class-acf-field-message.php:128
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Salli HTML-muotoilun näkyminen tekstinä renderöinnin sijaan"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Numero"

#: includes/fields/class-acf-field-number.php:169,
#: includes/fields/class-acf-field-range.php:174
msgid "Minimum Value"
msgstr "Minimiarvo"

#: includes/fields/class-acf-field-number.php:180,
#: includes/fields/class-acf-field-range.php:186
msgid "Maximum Value"
msgstr "Maksimiarvo"

#: includes/fields/class-acf-field-number.php:191,
#: includes/fields/class-acf-field-range.php:198
msgid "Step Size"
msgstr "Askelluksen koko"

#: includes/fields/class-acf-field-number.php:229
msgid "Value must be a number"
msgstr "Arvon täytyy olla numero"

#: includes/fields/class-acf-field-number.php:244
msgid "Value must be equal to or higher than %d"
msgstr "Arvon täytyy olla sama tai suurempi kuin %d"

#: includes/fields/class-acf-field-number.php:251
msgid "Value must be equal to or lower than %d"
msgstr "Arvon täytyy olla sama tai pienempi kuin %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:230
msgid "Enter URL"
msgstr "Syötä URL"

#: includes/fields/class-acf-field-oembed.php:276,
#: includes/fields/class-acf-field-oembed.php:289
msgid "Embed Size"
msgstr "Upotuksen koko"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Sivun URL"

#: includes/fields/class-acf-field-page_link.php:165
msgid "Archives"
msgstr "Arkistot"

#: includes/fields/class-acf-field-page_link.php:249,
#: includes/fields/class-acf-field-post_object.php:250,
#: includes/fields/class-acf-field-taxonomy.php:948
msgid "Parent"
msgstr "Vanhempi"

#: includes/fields/class-acf-field-page_link.php:450,
#: includes/fields/class-acf-field-post_object.php:362,
#: includes/fields/class-acf-field-relationship.php:578
msgid "Filter by Post Type"
msgstr "Suodata tyypin mukaan"

#: includes/fields/class-acf-field-page_link.php:458,
#: includes/fields/class-acf-field-post_object.php:370,
#: includes/fields/class-acf-field-relationship.php:586
msgid "All post types"
msgstr "Kaikki artikkelityypit"

#: includes/fields/class-acf-field-page_link.php:466,
#: includes/fields/class-acf-field-post_object.php:378,
#: includes/fields/class-acf-field-relationship.php:594
msgid "Filter by Taxonomy"
msgstr "Suodata taksonomian mukaan"

#: includes/fields/class-acf-field-page_link.php:474,
#: includes/fields/class-acf-field-post_object.php:386,
#: includes/fields/class-acf-field-relationship.php:602
msgid "All taxonomies"
msgstr "Kaikki taksonomiat"

#: includes/fields/class-acf-field-page_link.php:494
msgid "Allow Archives URLs"
msgstr "Salli arkistojen URL-osoitteita"

#: includes/fields/class-acf-field-page_link.php:506,
#: includes/fields/class-acf-field-post_object.php:406,
#: includes/fields/class-acf-field-select.php:398,
#: includes/fields/class-acf-field-user.php:79
msgid "Select multiple values?"
msgstr "Valitse useita arvoja?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Salasana"

#: includes/fields/class-acf-field-post_object.php:25,
#: includes/fields/class-acf-field-post_object.php:423,
#: includes/fields/class-acf-field-relationship.php:667
msgid "Post Object"
msgstr "Artikkeliolio"

#: includes/fields/class-acf-field-post_object.php:424,
#: includes/fields/class-acf-field-relationship.php:668
msgid "Post ID"
msgstr "Artikkelin ID"

#: includes/fields/class-acf-field-post_object.php:642
msgid "%1$s must have a valid post ID."
msgstr "%1$s:lla on oltava kelvollinen artikkelitunnus (post ID)."

#: includes/fields/class-acf-field-post_object.php:651
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s pitää olla artikkelityyppiä %2$s."
msgstr[1] "%1$s pitää olla joku seuraavista artikkelityypeistä: %2$s"

#: includes/fields/class-acf-field-post_object.php:667
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s:lla pitää olla termi %2$s."
msgstr[1] "%1$s:lla pitää olla jokin seuraavista termeistä: %2$s"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Valintanappi"

#: includes/fields/class-acf-field-radio.php:214
msgid "Other"
msgstr "Muu"

#: includes/fields/class-acf-field-radio.php:219
msgid "Add 'other' choice to allow for custom values"
msgstr "Lisää 'muu' vaihtoehto salliaksesi mukautettuja arvoja"

#: includes/fields/class-acf-field-radio.php:227
msgid "Save Other"
msgstr "Tallenna Muu"

#: includes/fields/class-acf-field-radio.php:232
msgid "Save 'other' values to the field's choices"
msgstr "Tallenna 'muu'-kentän arvo kentän valinnaksi"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Liukusäädin"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Suodata artikkeleita"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Maximum values reached ( {max} values )"
msgstr "Maksimiarvo saavutettu ( {max} artikkelia )"

#: includes/fields/class-acf-field-relationship.php:64
msgid "Loading"
msgstr "Ladataan"

#: includes/fields/class-acf-field-relationship.php:65
msgid "No matches found"
msgstr "Ei yhtään osumaa"

#: includes/fields/class-acf-field-relationship.php:390
msgid "Select post type"
msgstr "Valitse artikkelityyppi"

#: includes/fields/class-acf-field-relationship.php:399
msgid "Select taxonomy"
msgstr "Valitse taksonomia"

#: includes/fields/class-acf-field-relationship.php:469
msgid "Search..."
msgstr "Etsi..."

#: includes/fields/class-acf-field-relationship.php:610
msgid "Filters"
msgstr "Suodattimet"

#: includes/fields/class-acf-field-relationship.php:616,
#: includes/locations/class-acf-location-post-type.php:22
msgid "Post Type"
msgstr "Artikkelityyppi"

#: includes/fields/class-acf-field-relationship.php:617,
#: includes/fields/class-acf-field-taxonomy.php:28,
#: includes/fields/class-acf-field-taxonomy.php:714,
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taksonomia"

#: includes/fields/class-acf-field-relationship.php:626
msgid "Elements"
msgstr "Elementit"

#: includes/fields/class-acf-field-relationship.php:627
msgid "Selected elements will be displayed in each result"
msgstr "Valitut elementit näytetään jokaisessa tuloksessa"

#: includes/fields/class-acf-field-relationship.php:631,
#: includes/admin/views/field-group-options.php:150
msgid "Featured Image"
msgstr "Artikkelikuva"

#: includes/fields/class-acf-field-relationship.php:640
msgid "Minimum posts"
msgstr "Vähimmäismäärä artikkeleita"

#: includes/fields/class-acf-field-relationship.php:651
msgid "Maximum posts"
msgstr "Maksimimäärä artikkeleita"

#: includes/fields/class-acf-field-relationship.php:752,
#: pro/fields/class-acf-field-gallery.php:832
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s vaatii vähintään %2$s valinnan"
msgstr[1] "%1$s vaatii vähintään %2$s valintaa"

#: includes/fields/class-acf-field-select.php:25,
#: includes/fields/class-acf-field-taxonomy.php:738
msgctxt "noun"
msgid "Select"
msgstr "Valintalista"

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Yksi tulos on saatavilla. Valitse se painamalla enter-näppäintä."

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d tulosta on saatavilla. Voit navigoida tuloksian välillä käyttämällä "
"”ylös” ja ”alas” -näppäimiä."

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Osumia ei löytynyt"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Kirjoita yksi tai useampi merkki"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Kirjoita %d tai useampi merkkiä"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Poista 1 merkki"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Poista %d merkkiä"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Voit valita vain yhden kohteen"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Voit valita vain %d kohdetta"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Lataa lisää tuloksia &hellip;"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Etsii&hellip;"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Lataus epäonnistui"

#: includes/fields/class-acf-field-select.php:410,
#: includes/fields/class-acf-field-true_false.php:159
msgid "Stylised UI"
msgstr "Tyylikäs käyttöliittymä"

#: includes/fields/class-acf-field-select.php:422
msgid "Use AJAX to lazy load choices?"
msgstr "Haluatko ladata valinnat laiskasti (käyttää AJAXia)?"

#: includes/fields/class-acf-field-select.php:440
msgid "Specify the value returned"
msgstr "Määritä palautetun arvon muoto"

#: includes/fields/class-acf-field-select.php:663
msgid "%1$s is not one of %2$s"
msgstr "%1$s ei ole yksi näistä: %2$s"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Erotusmerkki"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Välilehti"

#: includes/fields/class-acf-field-tab.php:103
msgid "Placement"
msgstr "Sijainti"

#: includes/fields/class-acf-field-tab.php:107,
#: includes/admin/views/field-group-options.php:87
msgid "Top aligned"
msgstr "Tasaa ylös"

#: includes/fields/class-acf-field-tab.php:108,
#: includes/admin/views/field-group-options.php:88
msgid "Left aligned"
msgstr "Tasaa vasemmalle"

#: includes/fields/class-acf-field-tab.php:118
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Määritä päätepiste, johon edelliset välilehdet lopetetaan. Tämä aloittaa "
"uuden välilehtiryhmän."

#: includes/fields/class-acf-field-taxonomy.php:673
msgctxt "No terms"
msgid "No %s"
msgstr "Ei %s"

#: includes/fields/class-acf-field-taxonomy.php:715
msgid "Select the taxonomy to be displayed"
msgstr "Valitse taksonomia, joka näytetään"

#: includes/fields/class-acf-field-taxonomy.php:726
msgid "Appearance"
msgstr "Ulkoasu"

#: includes/fields/class-acf-field-taxonomy.php:727
msgid "Select the appearance of this field"
msgstr "Valitse ulkoasu tälle kenttälle"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "Multiple Values"
msgstr "Useita arvoja"

#: includes/fields/class-acf-field-taxonomy.php:734
msgid "Multi Select"
msgstr "Valitse useita"

#: includes/fields/class-acf-field-taxonomy.php:736
msgid "Single Value"
msgstr "Yksi arvo"

#: includes/fields/class-acf-field-taxonomy.php:737
msgid "Radio Buttons"
msgstr "Valintanappi"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Create Terms"
msgstr "Uusien ehtojen luominen"

#: includes/fields/class-acf-field-taxonomy.php:766
msgid "Allow new terms to be created whilst editing"
msgstr "Salli uusien ehtojen luominen samalla kun muokataan"

#: includes/fields/class-acf-field-taxonomy.php:777
msgid "Save Terms"
msgstr "Tallenna ehdot"

#: includes/fields/class-acf-field-taxonomy.php:778
msgid "Connect selected terms to the post"
msgstr "Yhdistä valitut ehdot artikkeliin"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "Load Terms"
msgstr "Lataa ehdot"

#: includes/fields/class-acf-field-taxonomy.php:790
msgid "Load value from posts terms"
msgstr "Lataa arvo artikkelin ehdoista"

#: includes/fields/class-acf-field-taxonomy.php:806
msgid "Term Object"
msgstr "Ehto"

#: includes/fields/class-acf-field-taxonomy.php:807
msgid "Term ID"
msgstr "Ehdon ID"

#: includes/fields/class-acf-field-taxonomy.php:862
msgid "User unable to add new %s"
msgstr "Käyttäjä ei voi lisätä uutta %s"

#: includes/fields/class-acf-field-taxonomy.php:874
msgid "%s already exists"
msgstr "%s on jo olemassa"

#: includes/fields/class-acf-field-taxonomy.php:910
msgid "%s added"
msgstr "%s lisättiin"

#: includes/fields/class-acf-field-taxonomy.php:926,
#: pro/fields/class-acf-field-flexible-content.php:597,
#: includes/admin/views/field-group-fields.php:6
msgid "Name"
msgstr "Nimi"

#: includes/fields/class-acf-field-taxonomy.php:961,
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Lisää"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Teksti"

#: includes/fields/class-acf-field-text.php:141,
#: includes/fields/class-acf-field-textarea.php:125
msgid "Character Limit"
msgstr "Merkkirajoitus"

#: includes/fields/class-acf-field-text.php:142,
#: includes/fields/class-acf-field-textarea.php:126
msgid "Leave blank for no limit"
msgstr "Jos et halua rajoittaa, jätä tyhjäksi"

#: includes/fields/class-acf-field-text.php:168,
#: includes/fields/class-acf-field-textarea.php:221
msgid "Value must not exceed %d characters"
msgstr "Arvo ei saa olla suurempi kuin %d merkkiä"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Tekstialue"

#: includes/fields/class-acf-field-textarea.php:136
msgid "Rows"
msgstr "Rivit"

#: includes/fields/class-acf-field-textarea.php:137
msgid "Sets the textarea height"
msgstr "Aseta tekstialueen koko"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Kellonaikavalitsin"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "”Tosi / Epätosi” -valinta"

#: includes/fields/class-acf-field-true_false.php:138
msgid "Displays text alongside the checkbox"
msgstr "Näytä teksti valintaruudun rinnalla"

#: includes/fields/class-acf-field-true_false.php:172
msgid "On Text"
msgstr "Päällä -teksti"

#: includes/fields/class-acf-field-true_false.php:173
msgid "Text shown when active"
msgstr "Teksti, joka näytetään kun valinta on aktiivinen"

#: includes/fields/class-acf-field-true_false.php:189
msgid "Off Text"
msgstr "Pois päältä -teksti"

#: includes/fields/class-acf-field-true_false.php:190
msgid "Text shown when inactive"
msgstr "Teksti, joka näytetään kun valinta ei ole aktiivinen"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-url.php:154
msgid "Value must be a valid URL"
msgstr "Arvon täytyy olla validi URL"

#: includes/fields/class-acf-field-user.php:53
msgid "Filter by role"
msgstr "Suodata roolin mukaan"

#: includes/fields/class-acf-field-user.php:61
msgid "All user roles"
msgstr "Kaikki käyttäjäroolit"

#: includes/fields/class-acf-field-user.php:95
msgid "User Array"
msgstr "Käyttäjätaulukko"

#: includes/fields/class-acf-field-user.php:96
msgid "User Object"
msgstr "Käyttäjäobjekti"

#: includes/fields/class-acf-field-user.php:97
msgid "User ID"
msgstr "Käyttäjätunnus"

#: includes/fields/class-acf-field-user.php:350
msgid "Error loading field."
msgstr "Virhe ladattaessa kenttää."

#: includes/fields/class-acf-field-user.php:355
msgid "Invalid request."
msgstr "Virheellinen pyyntö."

#: includes/fields/class-acf-field-user.php:517
msgid "%1$s must have a valid user ID."
msgstr "%1$s:lla on oltava kelvollinen käyttäjätunnus."

#: includes/fields/class-acf-field-user.php:526
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s:lla pitää olla käyttäjä roolilla %2$s."
msgstr[1] "%1$s:lla pitää olla käyttäjä jollakin näistä rooleista: %2$s"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Wysiwyg-editori"

#: includes/fields/class-acf-field-wysiwyg.php:282
msgid "Visual"
msgstr "Graafinen"

#: includes/fields/class-acf-field-wysiwyg.php:283
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Teksti"

#: includes/fields/class-acf-field-wysiwyg.php:289
msgid "Click to initialize TinyMCE"
msgstr "Klikkaa ottaaksesi käyttöön graafisen editorin"

#: includes/fields/class-acf-field-wysiwyg.php:345
msgid "Tabs"
msgstr "Välilehdet"

#: includes/fields/class-acf-field-wysiwyg.php:350
msgid "Visual & Text"
msgstr "Graafinen ja teksti"

#: includes/fields/class-acf-field-wysiwyg.php:351
msgid "Visual Only"
msgstr "Vain graafinen"

#: includes/fields/class-acf-field-wysiwyg.php:352
msgid "Text Only"
msgstr "Vain teksti"

#: includes/fields/class-acf-field-wysiwyg.php:361
msgid "Toolbar"
msgstr "Työkalupalkki"

#: includes/fields/class-acf-field-wysiwyg.php:378
msgid "Show Media Upload Buttons?"
msgstr "Näytä Lisää media -painike?"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Delay initialization?"
msgstr "Viivytä alustusta?"

#: includes/fields/class-acf-field-wysiwyg.php:391
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE:tä ei alusteta ennen kuin kenttää napsautetaan"

#: includes/forms/form-front.php:40, pro/fields/class-acf-field-gallery.php:352
msgid "Title"
msgstr "Otsikko"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "Validoi sähköposti"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "Artikkeli päivitetty"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "Roskapostia havaittu"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Virhe</strong>: %s"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "on sama kuin"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "ei ole sama kuin"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Liite"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Kaikki %s muodot"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Kommentti"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Nykyinen käyttäjärooli"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super pääkäyttäjä"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Nykyinen käyttäjä"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Kirjautunut sisään"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Käyttää front endiä"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Käyttää back endiä"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Valikkokohde"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Valikko"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Valikkosijainnit"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Sivun vanhempi"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Sivupohja"

#: includes/locations/class-acf-location-page-template.php:73,
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Oletus sivupohja"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Sivun tyyppi"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Etusivu"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Artikkelit -sivu"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Ylätason sivu (sivu, jolla ei ole vanhempia)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Vanhempi sivu (sivu, jolla on alasivuja)"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Lapsisivu (sivu, jolla on vanhempi)"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Artikkelin kategoria"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Artikkelin muoto"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Artikkelin tila"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Artikkelin taksonomia"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Sivupohja"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Käyttäjälomake"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Lisää / Muokkaa"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Rekisteröi"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Käyttäjän rooli"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Vimpain"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Julkaistu"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Yhtään lisäkenttäryhmää ei löytynyt tälle asetussivulle. <a href=\"%s\">Luo "
"lisäkenttäryhmä</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Virhe</b>. Ei voitu yhdistää päivityspalvelimeen"

#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Päivitykset"

#: pro/admin/admin-updates.php:209
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Virhe</b>. Päivityspakettia ei voitu todentaa. Tarkista uudelleen tai "
"poista käytöstä ACF PRO -lisenssi ja aktivoi se uudelleen."

#: pro/admin/admin-updates.php:196
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Virhe</b>. Lisenssisi on umpeutunut tai poistettu käytöstä. Aktivoi ACF "
"PRO -lisenssisi uudelleen."

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Klooni"

#: pro/fields/class-acf-field-clone.php:815
msgid "Select one or more fields you wish to clone"
msgstr "Valitse kentät, jotka haluat kopioida"

#: pro/fields/class-acf-field-clone.php:834
msgid "Display"
msgstr "Näytä"

#: pro/fields/class-acf-field-clone.php:835
msgid "Specify the style used to render the clone field"
msgstr "Määritä tyyli, jota käytetään kloonikentän luomisessa"

#: pro/fields/class-acf-field-clone.php:840
msgid "Group (displays selected fields in a group within this field)"
msgstr "Ryhmä (valitut kentät näytetään ryhmänä tämän klooni-kentän sisällä)"

#: pro/fields/class-acf-field-clone.php:841
msgid "Seamless (replaces this field with selected fields)"
msgstr "Saumaton (korvaa tämä klooni-kenttä valituilla kentillä)"

#: pro/fields/class-acf-field-clone.php:864
msgid "Labels will be displayed as %s"
msgstr "Kentän nimiö näytetään seuraavassa muodossa: %s"

#: pro/fields/class-acf-field-clone.php:869
msgid "Prefix Field Labels"
msgstr "Kentän nimiön etuliite"

#: pro/fields/class-acf-field-clone.php:880
msgid "Values will be saved as %s"
msgstr "Arvot tallennetaan muodossa: %s"

#: pro/fields/class-acf-field-clone.php:885
msgid "Prefix Field Names"
msgstr "Kentän nimen etuliite"

#: pro/fields/class-acf-field-clone.php:1001
msgid "Unknown field"
msgstr "Tuntematon kenttä"

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field group"
msgstr "Tuntematon kenttäryhmä"

#: pro/fields/class-acf-field-clone.php:1042
msgid "All fields from %s field group"
msgstr "Kaikki kentät kenttäryhmästä %s"

#: pro/fields/class-acf-field-flexible-content.php:25,
#: includes/admin/views/html-notice-upgrade.php:12
msgid "Flexible Content"
msgstr "Joustava sisältö"

#: pro/fields/class-acf-field-flexible-content.php:31,
#: pro/fields/class-acf-field-repeater.php:182,
#: pro/fields/class-acf-field-repeater.php:473
msgid "Add Row"
msgstr "Lisää rivi"

#: pro/fields/class-acf-field-flexible-content.php:71,
#: pro/fields/class-acf-field-flexible-content.php:917,
#: pro/fields/class-acf-field-flexible-content.php:996
msgid "layout"
msgid_plural "layouts"
msgstr[0] "asettelu"
msgstr[1] "asettelut"

#: pro/fields/class-acf-field-flexible-content.php:72
msgid "layouts"
msgstr "asettelua"

#: pro/fields/class-acf-field-flexible-content.php:75,
#: pro/fields/class-acf-field-flexible-content.php:916,
#: pro/fields/class-acf-field-flexible-content.php:995
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Tämä kenttä vaatii vähintään {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:76
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Tämän kentän yläraja on {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} saatavilla (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:80
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} vaadittu (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:83
msgid "Flexible Content requires at least 1 layout"
msgstr "Vaaditaan vähintään yksi asettelu"

#: pro/fields/class-acf-field-flexible-content.php:276
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Klikkaa ”%s” -painiketta luodaksesi oman asettelun"

#: pro/fields/class-acf-field-flexible-content.php:410,
#: pro/fields/class-acf-field-repeater.php:295,
#: includes/admin/views/field-group-field.php:49
msgid "Drag to reorder"
msgstr "Muuta järjestystä vetämällä ja pudottamalla"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Lisää asettelu"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Duplicate layout"
msgstr "Monista asettelu"

#: pro/fields/class-acf-field-flexible-content.php:415
msgid "Remove layout"
msgstr "Poista asettelu"

#: pro/fields/class-acf-field-flexible-content.php:416,
#: pro/fields/class-acf-field-repeater.php:297
msgid "Click to toggle"
msgstr "Piilota/Näytä"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder Layout"
msgstr "Järjestä asettelu uudelleen"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder"
msgstr "Järjestä uudelleen"

#: pro/fields/class-acf-field-flexible-content.php:552
msgid "Delete Layout"
msgstr "Poista asettelu"

#: pro/fields/class-acf-field-flexible-content.php:552,
#: includes/admin/views/field-group-field.php:59
msgid "Delete"
msgstr "Poista"

#: pro/fields/class-acf-field-flexible-content.php:553
msgid "Duplicate Layout"
msgstr "Monista asettelu"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New Layout"
msgstr "Lisää uusi asettelu"

#: pro/fields/class-acf-field-flexible-content.php:635
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:650
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:679,
#: pro/fields/class-acf-field-repeater.php:469
msgid "Button Label"
msgstr "Painikkeen teksti"

#: pro/fields/class-acf-field-flexible-content.php:690
msgid "Minimum Layouts"
msgstr "Asetteluita vähintään"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "Asetteluita enintään"

#: pro/fields/class-acf-field-flexible-content.php:1704,
#: pro/fields/class-acf-field-repeater.php:1108
msgid "%s must be of type array or null."
msgstr "%s tyypin on oltava matriisi tai tyhjä."

#: pro/fields/class-acf-field-flexible-content.php:1715
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "%1$s täytyy sisältää vähintään %2$s %3$s asettelu."
msgstr[1] "%1$s täytyy sisältää vähintään %2$s %3$s asettelua."

#: pro/fields/class-acf-field-flexible-content.php:1731
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "%1$s täytyy sisältää korkeintaan %2$s %3$s asettelu."
msgstr[1] "%1$s täytyy sisältää korkeintaan %2$s %3$s asettelua."

#: pro/fields/class-acf-field-gallery.php:25,
#: includes/admin/views/html-notice-upgrade.php:15
msgid "Gallery"
msgstr "Galleria"

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "Lisää kuva galleriaan"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "Et voi valita enempää kuvia"

#: pro/fields/class-acf-field-gallery.php:320
msgid "Length"
msgstr "Pituus"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Kuvateksti"

#: pro/fields/class-acf-field-gallery.php:376
msgid "Alt Text"
msgstr "Vaihtoehtoinen teksti"

#: pro/fields/class-acf-field-gallery.php:500
msgid "Add to gallery"
msgstr "Lisää galleriaan"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Bulk actions"
msgstr "Massatoiminnot"

#: pro/fields/class-acf-field-gallery.php:505
msgid "Sort by date uploaded"
msgstr "Lajittele latauksen päivämäärän mukaan"

#: pro/fields/class-acf-field-gallery.php:506
msgid "Sort by date modified"
msgstr "Lajittele viimeisimmän muokkauksen päivämäärän mukaan"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Sort by title"
msgstr "Lajittele otsikon mukaan"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Reverse current order"
msgstr "Käännän nykyinen järjestys"

#: pro/fields/class-acf-field-gallery.php:520
msgid "Close"
msgstr "Sulje"

#: pro/fields/class-acf-field-gallery.php:602
msgid "Insert"
msgstr "Lisää"

#: pro/fields/class-acf-field-gallery.php:603
msgid "Specify where new attachments are added"
msgstr "Määritä mihin uudet liitteet lisätään"

#: pro/fields/class-acf-field-gallery.php:607
msgid "Append to the end"
msgstr "Lisää loppuun"

#: pro/fields/class-acf-field-gallery.php:608
msgid "Prepend to the beginning"
msgstr "Lisää alkuun"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Minimum Selection"
msgstr "Pienin määrä kuvia"

#: pro/fields/class-acf-field-gallery.php:644
msgid "Maximum Selection"
msgstr "Suurin määrä kuvia"

#: pro/fields/class-acf-field-repeater.php:25,
#: includes/admin/views/html-notice-upgrade.php:9
msgid "Repeater"
msgstr "Toista rivejä"

#: pro/fields/class-acf-field-repeater.php:64,
#: pro/fields/class-acf-field-repeater.php:659
msgid "Minimum rows reached ({min} rows)"
msgstr "Pienin määrä rivejä saavutettu ({min} riviä)"

#: pro/fields/class-acf-field-repeater.php:65
msgid "Maximum rows reached ({max} rows)"
msgstr "Suurin määrä rivejä saavutettu ({max} riviä)"

#: pro/fields/class-acf-field-repeater.php:334
msgid "Add row"
msgstr "Lisää rivi"

#: pro/fields/class-acf-field-repeater.php:335
msgid "Duplicate row"
msgstr "Monista rivi"

#: pro/fields/class-acf-field-repeater.php:336
msgid "Remove row"
msgstr "Poista rivi"

#: pro/fields/class-acf-field-repeater.php:414
msgid "Collapsed"
msgstr "Piilotettu"

#: pro/fields/class-acf-field-repeater.php:415
msgid "Select a sub field to show when row is collapsed"
msgstr "Valitse alakenttä, joka näytetään, kun rivi on piilotettu"

#: pro/fields/class-acf-field-repeater.php:427
msgid "Minimum Rows"
msgstr "Pienin määrä rivejä"

#: pro/fields/class-acf-field-repeater.php:439
msgid "Maximum Rows"
msgstr "Suurin määrä rivejä"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "Lohkotyyppejä ei ole"

#: pro/locations/class-acf-location-options-page.php:22,
#: includes/admin/views/html-notice-upgrade.php:18
msgid "Options Page"
msgstr "Asetukset-sivu"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Yhtään asetussivua ei ole olemassa"

#: tests/basic/test-blocks.php:456
msgid "Hero"
msgstr "Nosto"

#: tests/basic/test-blocks.php:457
msgid "Display a random hero image."
msgstr "Näytä satunnainen nostokuva."

#: tests/basic/test-blocks.php:630
msgid "Test JS"
msgstr "Testaa JS"

#: tests/basic/test-blocks.php:631
msgid "A block for testing JS."
msgstr "Lohko JS:n testaamiseen."

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Vie kenttäryhmiä"

#: includes/admin/tools/class-acf-admin-tool-export.php:39,
#: includes/admin/tools/class-acf-admin-tool-export.php:335,
#: includes/admin/tools/class-acf-admin-tool-export.php:364
msgid "Generate PHP"
msgstr "Luo PHP-koodi"

#: includes/admin/tools/class-acf-admin-tool-export.php:96,
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "Ei kenttäryhmää valittu"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Viety yksi kenttäryhmä."
msgstr[1] "Viety %s kenttäryhmää."

#: includes/admin/tools/class-acf-admin-tool-export.php:233,
#: includes/admin/tools/class-acf-admin-tool-export.php:262
msgid "Select Field Groups"
msgstr "Valitse kenttäryhmät"

#: includes/admin/tools/class-acf-admin-tool-export.php:329
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Valitse kenttäryhmät, jotka haluat viedä ja valitse sitten vientimetodisi. "
"Käytä Lataa-painiketta viedäksesi .json-tiedoston, jonka voit sitten tuoda "
"toisessa ACF asennuksessa. Käytä Generoi-painiketta luodaksesi PHP koodia, "
"jonka voit sijoittaa teemaasi."

#: includes/admin/tools/class-acf-admin-tool-export.php:334
msgid "Export File"
msgstr "Vie tiedosto"

#: includes/admin/tools/class-acf-admin-tool-export.php:405
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Tällä koodilla voit rekisteröidä valitut kenttäryhmät paikallisesti. "
"Paikallinen kenttäryhmä tarjoaa monia etuja, kuten nopeammat latausajat, "
"versionhallinnan & dynaamiset kentät/asetukset. Kopioi ja liitä koodi "
"teemasi functions.php tiedostoon tai sisällytä se ulkoisen tiedoston avulla."

#: includes/admin/tools/class-acf-admin-tool-export.php:435
msgid "Copy to clipboard"
msgstr "Kopioi leikepöydälle"

#: includes/admin/tools/class-acf-admin-tool-export.php:472
msgid "Copied"
msgstr "Kopioitu"

#: includes/admin/tools/class-acf-admin-tool-import.php:28
msgid "Import Field Groups"
msgstr "Tuo kenttäryhmiä"

#: includes/admin/tools/class-acf-admin-tool-import.php:49
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Valitse JSON-tiedosto, jonka haluat tuoda. Kenttäryhmät tuodaan, kun "
"klikkaat Tuo-painiketta."

#: includes/admin/tools/class-acf-admin-tool-import.php:66
msgid "Import File"
msgstr "Tuo tiedosto"

#: includes/admin/tools/class-acf-admin-tool-import.php:97
msgid "Error uploading file. Please try again"
msgstr "Virhe tiedostoa ladattaessa. Yritä uudelleen"

#: includes/admin/tools/class-acf-admin-tool-import.php:102
msgid "Incorrect file type"
msgstr "Virheellinen tiedostomuoto"

#: includes/admin/tools/class-acf-admin-tool-import.php:111
msgid "Import file empty"
msgstr "Tuotu tiedosto on tyhjä"

#: includes/admin/tools/class-acf-admin-tool-import.php:142
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Tuotu yksi kenttäryhmä"
msgstr[1] "Tuotu %s kenttäryhmää"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Ehdollinen logiikka"

#: includes/admin/views/field-group-field-conditional-logic.php:60
msgid "Show this field if"
msgstr "Näytä tämä kenttä, jos"

#: includes/admin/views/field-group-field-conditional-logic.php:172,
#: includes/admin/views/field-group-locations.php:38
msgid "Add rule group"
msgstr "Lisää sääntöryhmä"

#: includes/admin/views/field-group-field.php:53,
#: includes/admin/views/field-group-field.php:56
msgid "Edit field"
msgstr "Muokkaa kenttää"

#: includes/admin/views/field-group-field.php:57
msgid "Duplicate field"
msgstr "Monista kenttä"

#: includes/admin/views/field-group-field.php:58
msgid "Move field to another group"
msgstr "Siirrä kenttä toiseen ryhmään"

#: includes/admin/views/field-group-field.php:58
msgid "Move"
msgstr "Siirrä"

#: includes/admin/views/field-group-field.php:59
msgid "Delete field"
msgstr "Poista kenttä"

#: includes/admin/views/field-group-field.php:78
msgid "Field Label"
msgstr "Kentän nimiö"

#: includes/admin/views/field-group-field.php:79
msgid "This is the name which will appear on the EDIT page"
msgstr "Tätä nimeä käytetään MUOKKAA-sivulla"

#: includes/admin/views/field-group-field.php:92
msgid "Field Name"
msgstr "Kentän nimi"

#: includes/admin/views/field-group-field.php:93
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Yksi sana, ei välilyöntejä. Alaviivat ja ajatusviivat sallitaan"

#: includes/admin/views/field-group-field.php:106
msgid "Field Type"
msgstr "Kenttätyyppi"

#: includes/admin/views/field-group-field.php:121
msgid "Instructions"
msgstr "Ohjeet"

#: includes/admin/views/field-group-field.php:122
msgid "Instructions for authors. Shown when submitting data"
msgstr "Ohjeet kirjoittajille. Näytetään tietojen lähettämisen yhteydessä"

#: includes/admin/views/field-group-field.php:135
msgid "Required?"
msgstr "Pakollinen?"

#: includes/admin/views/field-group-field.php:161
msgid "Wrapper Attributes"
msgstr "Kääreen määritteet"

#: includes/admin/views/field-group-field.php:167
msgid "width"
msgstr "leveys"

#: includes/admin/views/field-group-field.php:185
msgid "class"
msgstr "class"

#: includes/admin/views/field-group-field.php:201
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:215,
#: includes/admin/views/field-group-field.php:215
msgid "Close Field"
msgstr "Sulje kenttä"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Järjestys"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tyyppi"

#: includes/admin/views/field-group-fields.php:19
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Ei kenttiä. Klikkaa <strong>+ Lisää kenttä</strong> -painiketta luodaksesi "
"ensimmäisen kenttäsi."

#: includes/admin/views/field-group-fields.php:44
msgid "+ Add Field"
msgstr "+ Lisää kenttä"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Säännöt"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Tästä voit määrittää, missä muokkausnäkymässä tämä kenttäryhmä näytetään"

#: includes/admin/views/field-group-options.php:10
msgid "Active"
msgstr "Käytössä"

#: includes/admin/views/field-group-options.php:27
msgid "Show in REST API"
msgstr "Näytä REST API:ssa"

#: includes/admin/views/field-group-options.php:44
msgid "Style"
msgstr "Tyyli"

#: includes/admin/views/field-group-options.php:51
msgid "Standard (WP metabox)"
msgstr "Standardi (WP-metalaatikko)"

#: includes/admin/views/field-group-options.php:52
msgid "Seamless (no metabox)"
msgstr "Saumaton (ei metalaatikkoa)"

#: includes/admin/views/field-group-options.php:61
msgid "Position"
msgstr "Sijainti"

#: includes/admin/views/field-group-options.php:68
msgid "High (after title)"
msgstr "Korkea (otsikon jälkeen)"

#: includes/admin/views/field-group-options.php:69
msgid "Normal (after content)"
msgstr "Normaali (sisällön jälkeen)"

#: includes/admin/views/field-group-options.php:70
msgid "Side"
msgstr "Reuna"

#: includes/admin/views/field-group-options.php:80
msgid "Label placement"
msgstr "Nimiön sijainti"

#: includes/admin/views/field-group-options.php:97
msgid "Instruction placement"
msgstr "Ohjeen sijainti"

#: includes/admin/views/field-group-options.php:104
msgid "Below labels"
msgstr "Tasaa nimiön alapuolelle"

#: includes/admin/views/field-group-options.php:105
msgid "Below fields"
msgstr "Tasaa kentän alapuolelle"

#: includes/admin/views/field-group-options.php:114
msgid "Order No."
msgstr "Järjestysnro."

#: includes/admin/views/field-group-options.php:115
msgid "Field groups with a lower order will appear first"
msgstr ""
"Kenttäryhmät, joilla on pienempi järjestysnumero, tulostetaan ensimmäisenä"

#: includes/admin/views/field-group-options.php:128
msgid "Shown in field group list"
msgstr "Näytetään kenttäryhmien listauksessa"

#: includes/admin/views/field-group-options.php:139
msgid "Permalink"
msgstr "Kestolinkki"

#: includes/admin/views/field-group-options.php:140
msgid "Content Editor"
msgstr "Sisältöeditori"

#: includes/admin/views/field-group-options.php:141
msgid "Excerpt"
msgstr "Katkelma"

#: includes/admin/views/field-group-options.php:143
msgid "Discussion"
msgstr "Keskustelu"

#: includes/admin/views/field-group-options.php:145
msgid "Revisions"
msgstr "Tarkastettu"

#: includes/admin/views/field-group-options.php:146
msgid "Slug"
msgstr "Polkutunnus (slug)"

#: includes/admin/views/field-group-options.php:147
msgid "Author"
msgstr "Kirjoittaja"

#: includes/admin/views/field-group-options.php:148
msgid "Format"
msgstr "Muoto"

#: includes/admin/views/field-group-options.php:149
msgid "Page Attributes"
msgstr "Sivun attribuutit"

#: includes/admin/views/field-group-options.php:151
msgid "Categories"
msgstr "Kategoriat"

#: includes/admin/views/field-group-options.php:152
msgid "Tags"
msgstr "Avainsanat"

#: includes/admin/views/field-group-options.php:153
msgid "Send Trackbacks"
msgstr "Lähetä paluuviitteet"

#: includes/admin/views/field-group-options.php:161
msgid "Hide on screen"
msgstr "Piilota näytöltä"

#: includes/admin/views/field-group-options.php:162
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Valitse</b> kohteita <b>piilottaaksesi</b> ne muokkausnäkymästä."

#: includes/admin/views/field-group-options.php:162
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Jos muokkausnäkymässä on useita kenttäryhmiä, käytetään ensimmäisen (pienin "
"järjestysnumero) kenttäryhmän asetuksia"

#: includes/admin/views/html-admin-navigation.php:89
msgid "Upgrade to Pro"
msgstr "Päivitä Pro-versioon"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Seuraavat sivustot vaativat tietokantapäivityksen. Valitse ne, jotka haluat "
"päivittää ja klikkaa %s."

#: includes/admin/views/html-admin-page-upgrade-network.php:26,
#: includes/admin/views/html-admin-page-upgrade-network.php:27,
#: includes/admin/views/html-admin-page-upgrade-network.php:96
msgid "Upgrade Sites"
msgstr "Päivitä sivustot"

#: includes/admin/views/html-admin-page-upgrade-network.php:36,
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Sivusto"

#: includes/admin/views/html-admin-page-upgrade-network.php:80
msgid "Site is up to date"
msgstr "Sivusto on ajan tasalla"

#: includes/admin/views/html-admin-page-upgrade-network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Sivusto edellyttää tietokannan päivityksen (%1$s -> %2$s)"

#: includes/admin/views/html-admin-page-upgrade-network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Tietokanta on päivitetty. <a href=\"%s\">Palaa verkon hallinnan "
"ohjausnäkymään</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
msgid "Please select at least one site to upgrade."
msgstr "Valitse vähintään yksi päivitettävä sivusto."

#: includes/admin/views/html-admin-page-upgrade-network.php:121,
#: includes/admin/views/html-notice-upgrade.php:45
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Tietokannan varmuuskopio on erittäin suositeltavaa ennen kuin jatkat. Oletko "
"varma, että haluat jatkaa päivitystä nyt?"

#: includes/admin/views/html-admin-page-upgrade-network.php:148,
#: includes/admin/views/html-admin-page-upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "Päivitetään data versioon %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:162
msgid "Upgrade complete."
msgstr "Päivitys valmis."

#: includes/admin/views/html-admin-page-upgrade-network.php:165,
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "Päivitys epäonnistui."

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Luetaan päivitystehtäviä..."

#: includes/admin/views/html-admin-page-upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Tietokannan päivitys on valmis. <a href=\"%s\">Katso mikä on uutta</a>"

#: includes/admin/views/html-admin-tools.php:24
msgid "Back to all tools"
msgstr "Takaisin kaikkiin työkaluihin"

#: includes/admin/views/html-notice-upgrade.php:28
msgid "Database Upgrade Required"
msgstr "Tietokanta on päivitettävä"

#: includes/admin/views/html-notice-upgrade.php:29
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Kiitos päivityksestä: %1$s v%2$s!"

#: includes/admin/views/html-notice-upgrade.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Tämä versio sisältää parannuksia tietokantaan ja edellyttää päivitystä."

#: includes/admin/views/html-notice-upgrade.php:31
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Varmista myös, että kaikki premium-lisäosat (%s) on päivitetty uusimpaan "
"versioon."

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Poista lisenssi käytöstä"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Aktivoi lisenssi"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Näytä lisenssitiedot"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Ottaaksesi käyttöön päivitykset, syötä alle lisenssiavaimesi. Jos sinulla ei "
"ole lisenssiavainta, katso <a href=\"%s\" target=\"_blank\">tarkemmat tiedot "
"ja hinnoittelu</a>."

#: pro/admin/views/html-settings-updates.php:41
msgid "License Key"
msgstr "Lisenssiavain"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "Käyttöoikeusavain on määritelty wp-config.php:ssa."

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr "Yritä aktivointia uudelleen"

#: pro/admin/views/html-settings-updates.php:76
msgid "Update Information"
msgstr "Päivitä tiedot"

#: pro/admin/views/html-settings-updates.php:83
msgid "Current Version"
msgstr "Nykyinen versio"

#: pro/admin/views/html-settings-updates.php:91
msgid "Latest Version"
msgstr "Uusin versio"

#: pro/admin/views/html-settings-updates.php:99
msgid "Update Available"
msgstr "Päivitys saatavilla"

#: pro/admin/views/html-settings-updates.php:111
msgid "Please enter your license key above to unlock updates"
msgstr "Syötä lisenssiavain saadaksesi päivityksiä"

#: pro/admin/views/html-settings-updates.php:109
msgid "Update Plugin"
msgstr "Päivitä lisäosa"

#: pro/admin/views/html-settings-updates.php:107
msgid "Please reactivate your license to unlock updates"
msgstr "Aktivoi käyttöoikeus saadaksesi päivityksiä"

#: pro/admin/views/html-settings-updates.php:124
msgid "Changelog"
msgstr "Muutosloki"

#: pro/admin/views/html-settings-updates.php:134
msgid "Upgrade Notice"
msgstr "Päivitys Ilmoitus"

#~ msgid "Inactive"
#~ msgstr "Ei käytössä"

#~ msgid "Elliot Condon"
#~ msgstr "Elliot Condon"

#, php-format
#~ msgid "Inactive <span class=\"count\">(%s)</span>"
#~ msgid_plural "Inactive <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Ei käytössä <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Ei käytössä <span class=\"count\">(%s)</span>"

#~ msgid "Status"
#~ msgstr "Status"

#, php-format
#~ msgid "See what's new in <a href=\"%s\">version %s</a>."
#~ msgstr "Katso mitä uutta <a href=\"%s\">versiossa %s</a>."

#~ msgid "Resources"
#~ msgstr "Resurssit"

#~ msgid "Documentation"
#~ msgstr "﻿Dokumentaatio"

#~ msgid "Pro"
#~ msgstr "Pro"

#, php-format
#~ msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
#~ msgstr "Kiitos, että luot sisältöä <a href=\"%s\">ACF:llä</a>."

#~ msgid "Synchronise field group"
#~ msgstr "Synkronoi kenttäryhmä"

#~ msgid "Apply"
#~ msgstr "﻿Käytä"

#~ msgid "Bulk Actions"
#~ msgstr "Massatoiminnot"

#~ msgid "Info"
#~ msgstr "Info"

#~ msgid "What's New"
#~ msgstr "Katso mitä uutta"

#~ msgid "Welcome to Advanced Custom Fields"
#~ msgstr "Tervetuloa Advanced Custom Fields -lisäosaan"

#, php-format
#~ msgid ""
#~ "Thank you for updating! ACF %s is bigger and better than ever before. We "
#~ "hope you like it."
#~ msgstr ""
#~ "Kiitos, että päivitit! ACF %s on suurempi ja parempi kuin koskaan ennen. "
#~ "Toivomme, että pidät siitä."

#~ msgid "A Smoother Experience"
#~ msgstr "Sujuvampi kokemus"

#~ msgid "Improved Usability"
#~ msgstr "Käytettävyyttä parannettu"

#~ msgid ""
#~ "Including the popular Select2 library has improved both usability and "
#~ "speed across a number of field types including post object, page link, "
#~ "taxonomy and select."
#~ msgstr ""
#~ "Mukaan otettu Select2-kirjasto on parantanut sekä käytettävyyttä että "
#~ "nopeutta erilaisissa kenttätyypeissä kuten artikkelioliossa, sivun "
#~ "linkissä, taksonomiassa ja valinnassa."

#~ msgid "Improved Design"
#~ msgstr "Parantunut muotoilu"

#~ msgid ""
#~ "Many fields have undergone a visual refresh to make ACF look better than "
#~ "ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
#~ "(new) fields!"
#~ msgstr ""
#~ "Monet kentät ovat käyneet läpi visuaalisen uudistuksen ja ACF näyttää "
#~ "paremmalta kuin koskaan ennen! Huomattavat muutokset ovat nähtävissä "
#~ "kentissä galleria, suodata artikkeleita ja oEmbed (uusi)!"

#~ msgid "Improved Data"
#~ msgstr "Parannettu data"

#~ msgid ""
#~ "Redesigning the data architecture has allowed sub fields to live "
#~ "independently from their parents. This allows you to drag and drop fields "
#~ "in and out of parent fields!"
#~ msgstr ""
#~ "Data-arkkitehtuurin uudelleensuunnittelu mahdollisti alakenttien "
#~ "riippumattomuuden vanhemmistaan. Tämän muutoksen myötä voit vetää ja "
#~ "pudottaa kenttiä riippumatta kenttähierarkiasta!"

#~ msgid "Goodbye Add-ons. Hello PRO"
#~ msgstr "Hyvästi lisäosat. Tervetuloa PRO"

#~ msgid "Introducing ACF PRO"
#~ msgstr "Esittelyssä ACF PRO"

#~ msgid ""
#~ "We're changing the way premium functionality is delivered in an exciting "
#~ "way!"
#~ msgstr ""
#~ "Olemme muuttamassa tapaa, jolla Premium-toiminnallisuus toimitetaan "
#~ "jännittävällä tavalla!"

#, php-format
#~ msgid ""
#~ "All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
#~ "version of ACF</a>. With both personal and developer licenses available, "
#~ "premium functionality is more affordable and accessible than ever before!"
#~ msgstr ""
#~ "Kaikki neljä premium-lisäosaa on yhdistetty uuteen <a href=\"%s\">ACF PRO "
#~ "-versioon</a>. Lisensseistä saatavilla on sekä henkilökohtaisia että "
#~ "kehittäjien lisenssejä, joten korkealuokkaiset toiminnallisuudet ovat nyt "
#~ "edullisimpia ja saavutettavampia kuin koskaan ennen!"

#~ msgid "Powerful Features"
#~ msgstr "Tehokkaat ominaisuudet"

#~ msgid ""
#~ "ACF PRO contains powerful features such as repeatable data, flexible "
#~ "content layouts, a beautiful gallery field and the ability to create "
#~ "extra admin options pages!"
#~ msgstr ""
#~ "ACF PRO sisältää tehokkaita ominaisuuksia, kuten toistuva data, joustavat "
#~ "sisältö-layoutit, kaunis galleriakenttä sekä mahdollisuus luoda "
#~ "ylimääräisiä ylläpitäjän asetussivuja!"

#, php-format
#~ msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
#~ msgstr "Lue lisää <a href=\"%s\">ACF PRO:n ominaisuuksista</a>."

#~ msgid "Easy Upgrading"
#~ msgstr "Helppo päivitys"

#~ msgid ""
#~ "Upgrading to ACF PRO is easy. Simply purchase a license online and "
#~ "download the plugin!"
#~ msgstr ""
#~ "Päivittäminen ACF PRO-versioon on helppoa. Osta lisenssi verkossa ja "
#~ "lataa lisäosa!"

#, php-format
#~ msgid ""
#~ "We also wrote an <a href=\"%s\">upgrade guide</a> to answer any "
#~ "questions, but if you do have one, please contact our support team via "
#~ "the <a href=\"%s\">help desk</a>."
#~ msgstr ""
#~ "Kirjoitimme myös <a href=\"%s\">päivitysoppaan</a> vastataksemme "
#~ "kysymyksiin. Jos jokin asia vielä vaivaa mieltäsi, ota yhteyttä "
#~ "asiakaspalveluumme <a href=\"%s\">neuvontapalvelun</a> kautta."

#~ msgid "New Features"
#~ msgstr "Uudet ominaisuudet"

#~ msgid "Link Field"
#~ msgstr "Linkki-kenttä"

#~ msgid ""
#~ "The Link field provides a simple way to select or define a link (url, "
#~ "title, target)."
#~ msgstr ""
#~ "Linkki-kentässä on yksinkertainen tapa valita tai määrittää linkki (URL, "
#~ "otsikko, kohde)."

#~ msgid "Group Field"
#~ msgstr "Ryhmä-kenttä"

#~ msgid "The Group field provides a simple way to create a group of fields."
#~ msgstr "Ryhmä-kentässä on yksinkertainen tapa luoda kenttäryhmiä."

#~ msgid "oEmbed Field"
#~ msgstr "oEmbed-kenttä"

#~ msgid ""
#~ "The oEmbed field allows an easy way to embed videos, images, tweets, "
#~ "audio, and other content."
#~ msgstr ""
#~ "oEmbed-kentän avulla voit helposti upottaa videoita, kuvia, twiittejä, "
#~ "ääntä ja muuta sisältöä."

#~ msgid "Clone Field"
#~ msgstr "Klooni-kenttä"

#~ msgid "The clone field allows you to select and display existing fields."
#~ msgstr ""
#~ "Klooni-kentän avulla voit valita ja näyttää aiemmin luotuja kenttiä."

#~ msgid "More AJAX"
#~ msgstr "Enemmän AJAXia"

#~ msgid "More fields use AJAX powered search to speed up page loading."
#~ msgstr ""
#~ "Useammat kentät käyttävät AJAX-käyttöistä hakua ja näin sivujen lataus "
#~ "nopeutuu."

#~ msgid ""
#~ "New auto export to JSON feature improves speed and allows for "
#~ "syncronisation."
#~ msgstr ""
#~ "Uusi automaattinen vienti JSON:iin parantaa nopeutta ja mahdollistaa "
#~ "synkronoinnin."

#~ msgid "Easy Import / Export"
#~ msgstr "Helppo tuonti / vienti"

#~ msgid "Both import and export can easily be done through a new tools page."
#~ msgstr ""
#~ "Sekä tuonti että vienti voidaan tehdä helposti uudella työkalut-sivulla."

#~ msgid "New Form Locations"
#~ msgstr "Uudet valikkosijainnit"

#~ msgid ""
#~ "Fields can now be mapped to menus, menu items, comments, widgets and all "
#~ "user forms!"
#~ msgstr ""
#~ "Kentät voidaan nyt linkittää valikkoihin, valikkokohteisiin, "
#~ "kommentteihin, vimpaimiin ja kaikkiin käyttäjälomakkeisiin!"

#~ msgid "More Customization"
#~ msgstr "Lisää muokkauksia"

#~ msgid ""
#~ "New PHP (and JS) actions and filters have been added to allow for more "
#~ "customization."
#~ msgstr ""
#~ "Uudet PHP (ja JS) -toiminnot ja -suodattimet on lisätty mahdollistamaan "
#~ "kattavamman mukautuksen."

#~ msgid "Fresh UI"
#~ msgstr "Tuore UI"

#~ msgid ""
#~ "The entire plugin has had a design refresh including new field types, "
#~ "settings and design!"
#~ msgstr ""
#~ "Koko lisäosa on saanut design-päivityksen, mukaan lukien uudet "
#~ "kenttätyypit, asetukset ja suunnittelu!"

#~ msgid "New Settings"
#~ msgstr "Uudet asetukset"

#~ msgid ""
#~ "Field group settings have been added for Active, Label Placement, "
#~ "Instructions Placement and Description."
#~ msgstr ""
#~ "Kenttäryhmän asetukset on lisätty ominaisuuksiin Aktiivinen, Nimiön "
#~ "sijoittelu, Ohjeiden sijoittelu ja Kuvaus."

#~ msgid "Better Front End Forms"
#~ msgstr "Paremmat front endin lomakkeet"

#~ msgid ""
#~ "acf_form() can now create a new post on submission with lots of new "
#~ "settings."
#~ msgstr ""
#~ "acf_form() voi nyt luoda uuden artikkelin pyydettäessä useilla uusilla "
#~ "asetuksilla."

#~ msgid "Form validation is now done via PHP + AJAX in favour of only JS."
#~ msgstr ""
#~ "Lomakkeen validointi tehdään nyt yhdistelmällä PHP + AJAX pelkän JS:n "
#~ "sijaan."

#~ msgid "Moving Fields"
#~ msgstr "Kenttien siirtäminen"

#~ msgid ""
#~ "New field group functionality allows you to move a field between groups & "
#~ "parents."
#~ msgstr ""
#~ "Uusi kenttäryhmien toiminnallisuus sallii kenttien siirtämisen ryhmien ja "
#~ "vanhempien välillä."

#, php-format
#~ msgid "We think you'll love the changes in %s."
#~ msgstr "Uskomme, että tulet rakastamaan muutoksia %s:ssa."

#~ msgid "Current Color"
#~ msgstr "Nykyinen väri"

#~ msgid "Normal"
#~ msgstr "Normaali"

#~ msgid "Fancy"
#~ msgstr "Hieno"

#~ msgid "Parent fields"
#~ msgstr "Yläkentät"

#~ msgid "Sibling fields"
#~ msgstr "Sisaruskentät"

#, php-format
#~ msgid "%s field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "%s kenttäryhmä synkronoitu."
#~ msgstr[1] "%s kenttäryhmät synkronoitu."

#~ msgid "Error validating request"
#~ msgstr "Virhe pyynnön käsittelyssä"

#~ msgid "Add-ons"
#~ msgstr "Lisäosat"

#~ msgid "<b>Error</b>. Could not load add-ons list"
#~ msgstr "<b>Virhe</b>. Lisäosa luetteloa ei voitu ladata"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Advanced Custom Fields -tietokantapäivitys"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Ennen kuin alat käyttämään uusia mahtavia ominaisuuksia, ole hyvä ja "
#~ "päivitä tietokantasi uuteen versioon."

#~ msgid "Download & Install"
#~ msgstr "Lataa ja asenna"

#~ msgid "Installed"
#~ msgstr "Asennettu"

#, php-format
#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Tehdäksesi päivityksen helpoksi, <a href=\"%s\">kirjaudu kauppaan</a> ja "
#~ "lataa ilmainen kopio ACF PRO:sta!"

#~ msgid "Under the Hood"
#~ msgstr "Konepellin alla"

#~ msgid "Smarter field settings"
#~ msgstr "Älykkäämmät kenttäasetukset"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr "ACF tallentaa nyt kenttäasetukset yksittäisenä artikkelioliona"

#~ msgid "Better version control"
#~ msgstr "Parempi versionhallinta"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "Uusi automaattinen JSON-vienti sallii kenttäasetuksia versionhallinnan"

#~ msgid "Swapped XML for JSON"
#~ msgstr "XML vaihdettu JSON:iin"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "Tuonti / Vienti käyttää nyt JSONia XML:n sijaan"

#~ msgid "New Forms"
#~ msgstr "Uudet lomakkeet"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "Lisättiin uusi kenttä sisällön upottamiseen"

#~ msgid "New Gallery"
#~ msgstr "Uusi galleria"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "Galleriakenttä on käynyt läpi suuresti tarvitun kasvojenkohotuksen"

#~ msgid "Relationship Field"
#~ msgstr "Suodata artikkeleita -kenttä"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr ""
#~ "Uudet Suodata artikkeleita -kentän asetukset 'Suodattamille' (Etsi, "
#~ "Artikkelityyppi, Taksonomia)"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "Uusi arkistoryhmä page_link -kentän valintana"

#~ msgid "Better Options Pages"
#~ msgstr "Paremmat asetukset-sivut"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Uusi toiminnallisuus asetukset-sivulle, joka sallii sekä vanhempi että "
#~ "lapsi menu-sivujen luomisen"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Vie kenttäryhmä PHP:llä"

#~ msgid "Download export file"
#~ msgstr "Lataa vientitiedosto"

#~ msgid "Generate export code"
#~ msgstr "Generoi vientikoodi"

#~ msgid "Locating"
#~ msgstr "Paikannus"

#~ msgid "Shown when entering data"
#~ msgstr "Näytetään muokkausnäkymässä"

#~ msgid "Error."
#~ msgstr "Virhe."

#~ msgid "No embed found for the given URL."
#~ msgstr "Upotettavaa ei löytynyt annetusta URL-osoitteesta."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Pienin määrä arvoja saavutettu ({min} arvoa)"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Välilehtikentän ulkoasu rikkoutuu, jos lisätään taulukko-tyyli toistin "
#~ "kenttä tai joustava sisältö kenttä asettelu"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "Ryhmittele kenttiä käyttämällä ”välilehtikenttiä”. Näin saat selkeämmän "
#~ "muokkausnäkymän."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Kaikki kentät, jotka seuraavat tätä \"välilehtikenttää\" (tai kunnes "
#~ "toinen \"välilehtikenttä\" määritellään) ryhmitellään yhteen ja "
#~ "välilehden otsikoksi tulee tämän kentän nimiö."

#~ msgid "None"
#~ msgstr "Ei mitään"

#~ msgid "Taxonomy Term"
#~ msgstr "Taksonomian ehto"

#~ msgid "remove {layout}?"
#~ msgstr "poista {layout}?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "Tämä kenttä vaatii vähintään {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "Maksimi {label} saavutettu ({max} {identifier})"

#~ msgid "http://www.elliotcondon.com/"
#~ msgstr "http://www.elliotcondon.com/"

#~ msgid "Getting Started"
#~ msgstr "Miten pääset alkuun"

#~ msgid "Field Types"
#~ msgstr "Kenttätyypit"

#~ msgid "Functions"
#~ msgstr "Funktiot"

#~ msgid "Actions"
#~ msgstr "Toiminnot"

#~ msgid "'How to' guides"
#~ msgstr "\"Miten\" oppaat"

#~ msgid "Tutorials"
#~ msgstr "Oppaat"

#~ msgid "FAQ"
#~ msgstr "UKK"

#~ msgid "Created by"
#~ msgstr "Tekijä"

#~ msgid "Error"
#~ msgstr "Virhe"

#~ msgid "See what's new"
#~ msgstr "Katso mitä uutta"

#~ msgid "eg. Show extra content"
#~ msgstr "Esim. näytä ylimääräinen sisältö"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "Yksi kenttä vaatii huomiota"
#~ msgstr[1] "%d kenttää vaatii huomiota."

#~ msgid ""
#~ "Error validating license URL (website does not match). Please re-activate "
#~ "your license"
#~ msgstr ""
#~ "Virhe lisenssin URL:n validoinnissa (websivu ei täsmää). Ole hyvä ja "
#~ "aktivoi lisenssisi uudelleen"

#~ msgid "See what's new in"
#~ msgstr "Katso mitä uutta löytyy"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Onnistui!</b> Tuontityökalu lisäsi %s kenttäryhmään: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Varoitus!</b> Tuontityökalu havaitsi %s kenttäryhmää on jo olemassa ja "
#~ "siksi ne jätettiin huomiotta: %s\t"

#~ msgid "Upgrade ACF"
#~ msgstr "Päivitä ACF"

#~ msgid "Upgrade"
#~ msgstr "Päivitä"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Vedä ja pudota muuttaaksesi järjestystä"

#~ msgid "Show a different month"
#~ msgstr "Näytä eri kuuakusi"

#~ msgid "Return format"
#~ msgstr "Palautusmuoto"

#~ msgid "uploaded to this post"
#~ msgstr "ladattu tähän artikkeliin"

#~ msgid "File Size"
#~ msgstr "Tiedoston koko"

#~ msgid "No File selected"
#~ msgstr "Ei tiedostoa valittu"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr "Huomioithan, että teksti syötetään aina funktiolle  "

#~ msgid "Warning"
#~ msgstr "Varoitus"

#~ msgid "Add new %s "
#~ msgstr "Lisää uusi %s "

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr ""
#~ "Olemme pahoillamme, mutta tapahtui <b>Yhteysvirhe</b>. Ole hyvä ja yritä "
#~ "uudelleen"

#~ msgid "Save Options"
#~ msgstr "Tallenna asetukset"

#~ msgid "License"
#~ msgstr "lisenssi"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Saadaksesi mahdollisuuden päivityksiin, syötä lisenssiavain. Jos sinulla "
#~ "ei ole lisenssiavainta, katso"

#~ msgid "details & pricing"
#~ msgstr "lisätiedot & hinnoittelu"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "Show Field Keys"
#~ msgstr "Näytä kenttäavain"

#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr ""
#~ "Kenttäryhmät luodaan järjestyksessä alkaen pienimmästä järjestysnumerosta"

#~ msgid "Upgrading data to "
#~ msgstr "Päivitetään data versioon %s"

#~ msgid "Hide / Show All"
#~ msgstr "Piilota / Näytä kaikki"

#~ msgid "Pending Review"
#~ msgstr "Odottaa tarkistusta"

#~ msgid "Draft"
#~ msgstr "Luonnos"

#~ msgid "Future"
#~ msgstr "Tuleva"

#~ msgid "Private"
#~ msgstr "Yksityinen"

#~ msgid "Revision"
#~ msgstr "Tarkastettu"

#~ msgid "Trash"
#~ msgstr "Roskakori"

#~ msgid "ACF PRO Required"
#~ msgstr "Vaaditaan ACF PRO"

#~ msgid ""
#~ "We have detected an issue which requires your attention: This website "
#~ "makes use of premium add-ons (%s) which are no longer compatible with ACF."
#~ msgstr ""
#~ "Olemme havainneet ongelman, joka vaatii huomiotasi: Tämä websivu käyttää "
#~ "premium lisäosia (%s), jotka eivät enää ole yhteensopivia ACF:n kanssa."

#~ msgid ""
#~ "Don't panic, you can simply roll back the plugin and continue using ACF "
#~ "as you know it!"
#~ msgstr ""
#~ "Ei kuitenkaan hätää! Voit helposti palata ja jatkaa ACF:n käyttöä "
#~ "sellaisena kuin sen tunnet!"

#~ msgid "Roll back to ACF v%s"
#~ msgstr "Palaa takaisin ACF v%s:ään"

#~ msgid "Learn why ACF PRO is required for my site"
#~ msgstr "Lue miksi ACF PRO vaaditaan sivustollani"

#~ msgid "Data Upgrade"
#~ msgstr "Tietojen päivitys"

#~ msgid "Data upgraded successfully."
#~ msgstr "Tietojen päivitys onnistui!"

#~ msgid "Data is at the latest version."
#~ msgstr "Tiedot ovat ajan tasalla."

#~ msgid "1 required field below is empty"
#~ msgid_plural "%s required fields below are empty"
#~ msgstr[0] "Yksi vaadittu kenttä on tyhjä"
#~ msgstr[1] "%s valittua kenttää ovat tyhjiä"

#~ msgid "Load & Save Terms to Post"
#~ msgstr "Lataa & tallenna taksonomian ehdot artikkeliin"

#~ msgid ""
#~ "Load value based on the post's terms and update the post's terms on save"
#~ msgstr ""
#~ "Lataa arvo perustuen artikkelin ehtoihin ja päivitä artikkelin ehdot "
#~ "tallennettaessa"
