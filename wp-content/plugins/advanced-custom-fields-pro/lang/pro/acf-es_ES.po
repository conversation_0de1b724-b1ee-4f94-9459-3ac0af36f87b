# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: \n"
"PO-Revision-Date: 2022-08-24 21:37-0400\n"
"Last-Translator: WP Overnight <<EMAIL>>\n"
"Language-Team: \n"
"Language: es_ES\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.1.1\n"

#: acf.php:497
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields y Advanced Custom Fields PRO no deberían estar "
"activos al mismo tiempo. Hemos desactivado automáticamente Advanced Custom "
"Fields PRO."

#: acf.php:495
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields y Advanced Custom Fields PRO no deberían estar "
"activos al mismo tiempo. Hemos desactivado automáticamente Advanced Custom "
"Fields."

#: includes/acf-value-functions.php:374
msgid ""
"<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
"field values before ACF has been initialized. This is not supported and can "
"result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
"\">Learn how to fix this</a>."
msgstr ""
"<strong>%1$s</strong> - Hemos detectado una o más llamadas para obtener "
"valores de campo de ACF antes de que ACF se haya iniciado. Esto no es "
"compatible y puede ocasionar datos mal formados o faltantes. <a href=\"%2$s"
"\" target=\"_blank\">Aprende cómo corregirlo</a>."

#: includes/fields/class-acf-field-user.php:526
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s debe tener un usuario con el perfil %2$s."
msgstr[1] "%1$s debe tener un usuario con uno de los siguientes perfiles: %2$s"

#: includes/fields/class-acf-field-user.php:517
msgid "%1$s must have a valid user ID."
msgstr "%1$s debe tener un ID de usuario válido."

#: includes/fields/class-acf-field-user.php:355
msgid "Invalid request."
msgstr "Petición no válida."

#: includes/fields/class-acf-field-select.php:663
msgid "%1$s is not one of %2$s"
msgstr "%1$s no es ninguna de las siguientes %2$s"

#: includes/fields/class-acf-field-post_object.php:667
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s debe tener un término %2$s."
msgstr[1] "%1$s debe tener uno de los siguientes términos: %2$s"

#: includes/fields/class-acf-field-post_object.php:651
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s debe ser del tipo de contenido %2$s."
msgstr[1] "%1$s debe ser de uno de los siguientes tipos de contenido: %2$s"

#: includes/fields/class-acf-field-post_object.php:642
msgid "%1$s must have a valid post ID."
msgstr "%1$s debe tener un ID de entrada válido."

#: includes/fields/class-acf-field-file.php:470
msgid "%s requires a valid attachment ID."
msgstr "%s necesita un ID de adjunto válido."

#: includes/admin/views/field-group-options.php:27
msgid "Show in REST API"
msgstr "Mostrar en la API REST"

#: includes/fields/class-acf-field-color_picker.php:167
msgid "Enable Transparency"
msgstr "Activar la transparencia"

#: includes/fields/class-acf-field-color_picker.php:186
msgid "RGBA Array"
msgstr "Array RGBA"

#: includes/fields/class-acf-field-color_picker.php:96
msgid "RGBA String"
msgstr "Cadena RGBA"

#: includes/fields/class-acf-field-color_picker.php:95
#: includes/fields/class-acf-field-color_picker.php:185
msgid "Hex String"
msgstr "Cadena hexadecimal"

#: includes/admin/views/html-admin-navigation.php:89
msgid "Upgrade to Pro"
msgstr "Actualizar a la versión Pro"

#: includes/admin/admin-field-group.php:194
#: assets/build/js/acf-field-group.js:745
#: assets/build/js/acf-field-group.js:908
msgid "Gallery (Pro only)"
msgstr "Galería (solo Pro)"

#: includes/admin/admin-field-group.php:193
#: assets/build/js/acf-field-group.js:742
#: assets/build/js/acf-field-group.js:898
msgid "Clone (Pro only)"
msgstr "Clon (solo Pro)"

#: includes/admin/admin-field-group.php:192
#: assets/build/js/acf-field-group.js:742
#: assets/build/js/acf-field-group.js:895
msgid "Flexible Content (Pro only)"
msgstr "Contenido flexible (solo Pro)"

#: includes/admin/admin-field-group.php:191
#: assets/build/js/acf-field-group.js:742
#: assets/build/js/acf-field-group.js:892
msgid "Repeater (Pro only)"
msgstr "Repetidor (solo Pro)"

#. Author of the plugin
msgid "Delicious Brains"
msgstr "Delicious Brains"

#: includes/admin/admin-field-group.php:385
msgctxt "post status"
msgid "Active"
msgstr "Activo"

#: includes/fields/class-acf-field-email.php:175
msgid "'%s' is not a valid email address"
msgstr "«%s» no es una dirección de correo electrónico válida"

#: includes/fields/class-acf-field-color_picker.php:74
msgid "Color value"
msgstr "Valor del color"

#: includes/fields/class-acf-field-color_picker.php:72
msgid "Select default color"
msgstr "Seleccionar el color por defecto"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Clear color"
msgstr "Vaciar el color"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Bloques"

#: includes/acf-wp-functions.php:83
msgid "Options"
msgstr "Opciones"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Usuarios"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Elementos del menú"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Widgets"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Adjuntos"

#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "Taxonomías"

#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "Entradas"

#: includes/ajax/class-acf-ajax-local-json-diff.php:68
msgid "JSON field group (newer)"
msgstr "Grupo de campos JSON (más nuevo)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:64
msgid "Original field group"
msgstr "Grupo de campos original"

#: includes/ajax/class-acf-ajax-local-json-diff.php:59
msgid "Last updated: %s"
msgstr "Última actualización: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:53
msgid "Sorry, this field group is unavailable for diff comparison."
msgstr ""
"Lo siento, este grupo de campos no está disponible para la comparacion diff."

#: includes/ajax/class-acf-ajax-local-json-diff.php:43
msgid "Invalid field group ID."
msgstr "ID de grupo de campos no válido."

#: includes/ajax/class-acf-ajax-local-json-diff.php:36
msgid "Invalid field group parameter(s)."
msgstr "Parámetro(s) de grupo de campos no válido(s)"

#: includes/admin/admin-field-groups.php:484
msgid "Awaiting save"
msgstr "Esperando el guardado"

#: includes/admin/admin-field-groups.php:481
msgid "Saved"
msgstr "Guardado"

#: includes/admin/admin-field-groups.php:477
msgid "Import"
msgstr "Importar"

#: includes/admin/admin-field-groups.php:473
msgid "Review changes"
msgstr "Revisar los cambios"

#: includes/admin/admin-field-groups.php:449
msgid "Located in: %s"
msgstr "Localizado en: %s"

#: includes/admin/admin-field-groups.php:445
msgid "Located in plugin: %s"
msgstr "Localizado en el plugin: %s"

#: includes/admin/admin-field-groups.php:441
msgid "Located in theme: %s"
msgstr "Localizado en el tema: %s"

#: includes/admin/admin-field-groups.php:419
msgid "Various"
msgstr "Varios"

#: includes/admin/admin-field-groups.php:198
#: includes/admin/admin-field-groups.php:533
msgid "Sync changes"
msgstr "Sincronizar cambios"

#: includes/admin/admin-field-groups.php:197
msgid "Loading diff"
msgstr "Cargando diff"

#: includes/admin/admin-field-groups.php:196
msgid "Review local JSON changes"
msgstr "Revisar cambios de JSON local"

#: includes/admin/admin.php:172
msgid "Visit website"
msgstr "Visitar web"

#: includes/admin/admin.php:171
msgid "View details"
msgstr "Ver detalles"

#: includes/admin/admin.php:170
msgid "Version %s"
msgstr "Versión %s"

#: includes/admin/admin.php:169
msgid "Information"
msgstr "Información"

#: includes/admin/admin.php:160
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Centro de ayuda</a>. Los profesionales de "
"soporte de nuestro centro de ayuda te ayudarán más en profundidad con los "
"retos técnicos."

#: includes/admin/admin.php:156
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the ‘how-tos’ of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Debates</a>. Tenemos una comunidad activa y "
"amistosa, en nuestros foros de la comunidad, que pueden ayudarte a descubrir "
"cómo hacer todo en el mundo de ACF."

#: includes/admin/admin.php:152
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Documentación</a>. Nuestra amplia "
"documentación contiene referencias y guías para la mayoría de situaciones en "
"las que puedas encontrarte."

#: includes/admin/admin.php:149
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Somos fanáticos del soporte, y queremos que consigas el máximo en tu web con "
"ACF. Si te encuentras con alguna dificultad, hay varios lugares donde puedes "
"encontrar ayuda:"

#: includes/admin/admin.php:146 includes/admin/admin.php:148
msgid "Help & Support"
msgstr "Ayuda y soporte"

#: includes/admin/admin.php:137
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Por favor, usa la pestaña de ayuda y soporte para contactar si descubres que "
"necesitas ayuda."

#: includes/admin/admin.php:134
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Antes de crear tu primer grupo de campos te recomendamos que primero leas "
"nuestra <a href=\"%s\" target=\"_blank\">guía de primeros pasos</a> para "
"familiarizarte con la filosofía y buenas prácticas del plugin."

#: includes/admin/admin.php:132
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"El plugin Advanced Custom Fields ofrece un constructor visual con el que "
"personalizar las pantallas de WordPress con campos adicionales, y una API "
"intuitiva parra mostrar valores de campos personalizados en cualquier "
"archivo de plantilla de cualquier tema."

#: includes/admin/admin.php:129 includes/admin/admin.php:131
msgid "Overview"
msgstr "Resumen"

#: includes/locations.php:36
msgid "Location type \"%s\" is already registered."
msgstr "El tipo de ubicación «%s» ya está registrado."

#: includes/locations.php:25
msgid "Class \"%s\" does not exist."
msgstr "La clase «%s» no existe."

#: acf.php:440 includes/admin/admin-field-group.php:385
#: includes/admin/admin-field-groups.php:230
msgctxt "post status"
msgid "Disabled"
msgstr "Desactivado"

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nonce no válido."

#: includes/fields/class-acf-field-user.php:350
msgid "Error loading field."
msgstr "Error al cargar el campo."

#: assets/build/js/acf-input.js:2596 assets/build/js/acf-input.js:2657
#: assets/build/js/acf-input.js:2904 assets/build/js/acf-input.js:2978
msgid "Location not found: %s"
msgstr "Ubicación no encontrada: %s"

#: includes/forms/form-user.php:353
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Error</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Widget"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Perfil de usuario"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Comentario"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Formato de entrada"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Elemento de menú"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Estado de entrada"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menús"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Ubicaciones de menú"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menú"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Taxonomía de entrada"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Página hija (tiene superior)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Página superior (con hijos)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Página de nivel superior (sin padres)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Página de entradas"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Página de inicio"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Tipo de página"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Viendo el escritorio"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Viendo la web"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Conectado"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Usuario actual"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Plantilla de página"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Registro"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Añadir / Editar"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Formulario de usuario"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Página superior"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Super administrador"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Rol del usuario actual"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Plantilla predeterminada"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Plantilla de entrada"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Categoría de entrada"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Todo los formatos de %s"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Adjunto"

#: includes/validation.php:364
msgid "%s value is required"
msgstr "El valor de %s es obligatorio"

#: includes/admin/views/field-group-field-conditional-logic.php:60
msgid "Show this field if"
msgstr "Mostrar este campo si"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Lógica condicional"

#: includes/admin/admin.php:201
#: includes/admin/views/field-group-field-conditional-logic.php:157
#: includes/admin/views/html-location-rule.php:92
msgid "and"
msgstr "y"

#: includes/admin/admin-field-groups.php:269
msgid "Local JSON"
msgstr "JSON Local"

#: includes/admin/views/html-notice-upgrade.php:31
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Por favor, comprueba también que todas las extensiones premium (%s) estén "
"actualizados a la última versión."

#: includes/admin/views/html-notice-upgrade.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Esta versión contiene mejoras en su base de datos y requiere una "
"actualización."

#: includes/admin/views/html-notice-upgrade.php:29
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "¡Gracias por actualizar a %1$s v%2$s!"

#: includes/admin/views/html-notice-upgrade.php:28
msgid "Database Upgrade Required"
msgstr "Es necesario actualizar la base de datos"

#: includes/admin/views/html-notice-upgrade.php:18
msgid "Options Page"
msgstr "Página de opciones"

#: includes/admin/views/html-notice-upgrade.php:15
msgid "Gallery"
msgstr "Galería"

#: includes/admin/views/html-notice-upgrade.php:12
msgid "Flexible Content"
msgstr "Contenido flexible"

#: includes/admin/views/html-notice-upgrade.php:9
msgid "Repeater"
msgstr "Repetidor"

#: includes/admin/views/html-admin-tools.php:24
msgid "Back to all tools"
msgstr "Volver a todas las herramientas"

#: includes/admin/views/field-group-options.php:162
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Si aparecen múltiples grupos de campos en una pantalla de edición, se "
"utilizarán las opciones del primer grupo (el que tenga el número de orden "
"menor)"

#: includes/admin/views/field-group-options.php:162
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Selecciona</b> los elementos que <b>ocultar</b> de la pantalla de edición."

#: includes/admin/views/field-group-options.php:161
msgid "Hide on screen"
msgstr "Ocultar en pantalla"

#: includes/admin/views/field-group-options.php:153
msgid "Send Trackbacks"
msgstr "Enviar trackbacks"

#: includes/admin/views/field-group-options.php:152
msgid "Tags"
msgstr "Etiquetas"

#: includes/admin/views/field-group-options.php:151
msgid "Categories"
msgstr "Categorías"

#: includes/admin/views/field-group-options.php:149
msgid "Page Attributes"
msgstr "Atributos de página"

#: includes/admin/views/field-group-options.php:148
msgid "Format"
msgstr "Formato"

#: includes/admin/views/field-group-options.php:147
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:146
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:145
msgid "Revisions"
msgstr "Revisiones"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/field-group-options.php:144
msgid "Comments"
msgstr "Comentarios"

#: includes/admin/views/field-group-options.php:143
msgid "Discussion"
msgstr "Discusión"

#: includes/admin/views/field-group-options.php:141
msgid "Excerpt"
msgstr "Extracto"

#: includes/admin/views/field-group-options.php:140
msgid "Content Editor"
msgstr "Editor de contenido"

#: includes/admin/views/field-group-options.php:139
msgid "Permalink"
msgstr "Enlace permanente"

#: includes/admin/views/field-group-options.php:128
msgid "Shown in field group list"
msgstr "Mostrado en lista de grupos de campos"

#: includes/admin/views/field-group-options.php:115
msgid "Field groups with a lower order will appear first"
msgstr "Los grupos de campos con menor orden aparecerán primero"

#: includes/admin/views/field-group-options.php:114
msgid "Order No."
msgstr "N.º de orden"

#: includes/admin/views/field-group-options.php:105
msgid "Below fields"
msgstr "Debajo de los campos"

#: includes/admin/views/field-group-options.php:104
msgid "Below labels"
msgstr "Debajo de las etiquetas"

#: includes/admin/views/field-group-options.php:97
msgid "Instruction placement"
msgstr "Ubicación de la instrucción"

#: includes/admin/views/field-group-options.php:80
msgid "Label placement"
msgstr "Ubicación de la etiqueta"

#: includes/admin/views/field-group-options.php:70
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/field-group-options.php:69
msgid "Normal (after content)"
msgstr "Normal (después del contenido)"

#: includes/admin/views/field-group-options.php:68
msgid "High (after title)"
msgstr "Alta (después del título)"

#: includes/admin/views/field-group-options.php:61
msgid "Position"
msgstr "Posición"

#: includes/admin/views/field-group-options.php:52
msgid "Seamless (no metabox)"
msgstr "Directo (sin caja meta)"

#: includes/admin/views/field-group-options.php:51
msgid "Standard (WP metabox)"
msgstr "Estándar (caja meta de WP)"

#: includes/admin/views/field-group-options.php:44
msgid "Style"
msgstr "Estilo"

#: includes/admin/views/field-group-fields.php:44
msgid "+ Add Field"
msgstr "+ Añadir campo"

#: includes/admin/views/field-group-fields.php:19
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"No hay campos. Haz clic en el botón <strong>+ Añadir campo</strong> para "
"crear tu primer campo."

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tipo"

#: includes/admin/admin-field-groups.php:264
#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Clave"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Orden"

#: includes/admin/views/field-group-field.php:215
msgid "Close Field"
msgstr "Cerrar campo"

#: includes/admin/views/field-group-field.php:201
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:185
msgid "class"
msgstr "class"

#: includes/admin/views/field-group-field.php:167
msgid "width"
msgstr "ancho"

#: includes/admin/views/field-group-field.php:161
msgid "Wrapper Attributes"
msgstr "Atributos del contenedor"

#: includes/admin/views/field-group-field.php:135
msgid "Required?"
msgstr "¿Obligatorio?"

#: includes/admin/views/field-group-field.php:122
msgid "Instructions for authors. Shown when submitting data"
msgstr ""
"Instrucciones para los autores. Se muestra a la hora de enviar los datos"

#: includes/admin/views/field-group-field.php:121
msgid "Instructions"
msgstr "Instrucciones"

#: includes/admin/views/field-group-field.php:106
msgid "Field Type"
msgstr "Tipo de campo"

#: includes/admin/views/field-group-field.php:93
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Una sola palabra, sin espacios. Se permiten guiones y guiones bajos"

#: includes/admin/views/field-group-field.php:92
msgid "Field Name"
msgstr "Nombre del campo"

#: includes/admin/views/field-group-field.php:79
msgid "This is the name which will appear on the EDIT page"
msgstr "Este es el nombre que aparecerá en la página EDITAR"

#: includes/admin/views/field-group-field.php:78
msgid "Field Label"
msgstr "Etiqueta del campo"

#: includes/admin/views/field-group-field.php:59
msgid "Delete"
msgstr "Borrar"

#: includes/admin/views/field-group-field.php:59
msgid "Delete field"
msgstr "Borrar campo"

#: includes/admin/views/field-group-field.php:58
msgid "Move"
msgstr "Mover"

#: includes/admin/views/field-group-field.php:58
msgid "Move field to another group"
msgstr "Mover campo a otro grupo"

#: includes/admin/views/field-group-field.php:57
msgid "Duplicate field"
msgstr "Duplicar campo"

#: includes/admin/views/field-group-field.php:53
#: includes/admin/views/field-group-field.php:56
msgid "Edit field"
msgstr "Editar campo"

#: includes/admin/views/field-group-field.php:49
msgid "Drag to reorder"
msgstr "Arrastra para reordenar"

#: includes/admin/admin-field-group.php:175
#: includes/admin/views/html-location-group.php:3
#: assets/build/js/acf-field-group.js:1694
#: assets/build/js/acf-field-group.js:2011
msgid "Show this field group if"
msgstr "Mostrar este grupo de campos si"

#: includes/admin/views/html-admin-page-upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "No hay actualizaciones disponibles."

#: includes/admin/views/html-admin-page-upgrade.php:33
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Actualización de la base de datos completa. <a href=\"%s\">Ver las "
"novedades</a>"

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Leyendo tareas de actualización..."

#: includes/admin/views/html-admin-page-upgrade-network.php:165
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "Fallo al actualizar."

#: includes/admin/views/html-admin-page-upgrade-network.php:162
msgid "Upgrade complete."
msgstr "Actualización completa."

#: includes/admin/views/html-admin-page-upgrade-network.php:148
#: includes/admin/views/html-admin-page-upgrade.php:31
msgid "Upgrading data to version %s"
msgstr "Actualizando datos a la versión %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:121
#: includes/admin/views/html-notice-upgrade.php:45
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Es muy recomendable que hagas una copia de seguridad de tu base de datos "
"antes de continuar. ¿Estás seguro que quieres ejecutar ya la actualización?"

#: includes/admin/views/html-admin-page-upgrade-network.php:117
msgid "Please select at least one site to upgrade."
msgstr "Por favor, selecciona al menos un sitio para actualizarlo."

#: includes/admin/views/html-admin-page-upgrade-network.php:97
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Actualización de base de datos completa. <a href=\"%s\">Volver al escritorio "
"de red</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:80
msgid "Site is up to date"
msgstr "El sitio está actualizado"

#: includes/admin/views/html-admin-page-upgrade-network.php:78
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "El sitio necesita actualizar la base de datos de %1$s a %2$s"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Sitio"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:96
msgid "Upgrade Sites"
msgstr "Actualizar los sitios"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Es necesario actualizar la base de datos de los siguientes sitios. Marca los "
"que quieras actualizar y haz clic en %s."

#: includes/admin/views/field-group-field-conditional-logic.php:172
#: includes/admin/views/field-group-locations.php:38
msgid "Add rule group"
msgstr "Añadir grupo de reglas"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crea un conjunto de reglas para determinar qué pantallas de edición "
"utilizarán estos campos personalizados"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Reglas"

#: includes/admin/tools/class-acf-admin-tool-export.php:472
msgid "Copied"
msgstr "Copiado"

#: includes/admin/tools/class-acf-admin-tool-export.php:435
msgid "Copy to clipboard"
msgstr "Copiar al portapapeles"

#: includes/admin/tools/class-acf-admin-tool-export.php:405
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"El siguiente código puede ser utilizado para registrar una versión local del "
"o los grupos seleccionados. Un grupo de campos local puede brindar muchos "
"beneficios como tiempos de carga más cortos, control de versiones y campos/"
"ajustes dinámicos. Simplemente copia y pega el siguiente código en el "
"archivo functions.php de tu tema o inclúyelo como un archivo externo."

#: includes/admin/tools/class-acf-admin-tool-export.php:334
msgid "Export File"
msgstr "Exportar archivo"

#: includes/admin/tools/class-acf-admin-tool-export.php:329
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Selecciona los grupos de campos que te gustaría exportar y luego elige tu "
"método de exportación. Utiliza el botón de descarga para exportar a un "
"archivo .json que puedes importar en otra instalación de ACF. Utiliza el "
"botón generar para exportar a código PHP que puedes incluir en tu tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:233
#: includes/admin/tools/class-acf-admin-tool-export.php:262
msgid "Select Field Groups"
msgstr "Selecciona grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-export.php:167
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Exportado 1 grupo de campos."
msgstr[1] "Exportado %s grupos de campos."

#: includes/admin/tools/class-acf-admin-tool-export.php:96
#: includes/admin/tools/class-acf-admin-tool-export.php:131
msgid "No field groups selected"
msgstr "Ningún grupo de campos seleccionado"

#: includes/admin/tools/class-acf-admin-tool-export.php:39
#: includes/admin/tools/class-acf-admin-tool-export.php:335
#: includes/admin/tools/class-acf-admin-tool-export.php:364
msgid "Generate PHP"
msgstr "Generar PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:35
msgid "Export Field Groups"
msgstr "Exportar grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-import.php:142
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Se ha importado un grupo de campos"
msgstr[1] "Se han importado %s grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-import.php:111
msgid "Import file empty"
msgstr "Archivo de imporación vacío"

#: includes/admin/tools/class-acf-admin-tool-import.php:102
msgid "Incorrect file type"
msgstr "Tipo de campo incorrecto"

#: includes/admin/tools/class-acf-admin-tool-import.php:97
msgid "Error uploading file. Please try again"
msgstr "Error al subir el archivo. Por favor, inténtalo de nuevo"

#: includes/admin/tools/class-acf-admin-tool-import.php:66
msgid "Import File"
msgstr "Importar archivo"

#: includes/admin/tools/class-acf-admin-tool-import.php:49
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Selecciona el archivo JSON de Advanced Custom Fields que te gustaría "
"importar.  Cuando hagas clic en el botón importar de abajo, ACF importará "
"los grupos de campos."

#: includes/admin/tools/class-acf-admin-tool-import.php:28
msgid "Import Field Groups"
msgstr "Importar grupo de campos"

#: includes/admin/admin-field-groups.php:472
msgid "Sync"
msgstr "Sincronizar"

#: includes/admin/admin-field-groups.php:800
msgid "Select %s"
msgstr "Selecciona %s"

#: includes/admin/admin-field-groups.php:505
#: includes/admin/admin-field-groups.php:525
#: includes/admin/views/field-group-field.php:57
msgid "Duplicate"
msgstr "Duplicar"

#: includes/admin/admin-field-groups.php:505
msgid "Duplicate this item"
msgstr "Duplicar este elemento"

#: includes/admin/admin-field-groups.php:263
#: includes/admin/views/field-group-options.php:127
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
msgid "Description"
msgstr "Descripción"

#: includes/admin/admin-field-groups.php:469
#: includes/admin/admin-field-groups.php:687
msgid "Sync available"
msgstr "Sincronización disponible"

#: includes/admin/admin-field-groups.php:612
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Grupo de campos sincronizado."
msgstr[1] "%s grupos de campos sincronizados."

#: includes/admin/admin-field-groups.php:555
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Grupo de campos duplicado."
msgstr[1] "%s grupos de campos duplicados."

#: includes/admin/admin-field-groups.php:116
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Activo <span class=\"count\">(%s)</span>"
msgstr[1] "Activos <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:201
msgid "Review sites & upgrade"
msgstr "Revisar sitios y actualizar"

#: includes/admin/admin-upgrade.php:51 includes/admin/admin-upgrade.php:113
#: includes/admin/admin-upgrade.php:114 includes/admin/admin-upgrade.php:177
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Actualizar base de datos"

#: includes/admin/admin.php:49 includes/admin/views/field-group-options.php:142
msgid "Custom Fields"
msgstr "Campos personalizados"

#: includes/admin/admin-field-group.php:804
msgid "Move Field"
msgstr "Mover campo"

#: includes/admin/admin-field-group.php:797
msgid "Please select the destination for this field"
msgstr "Por favor, selecciona el destino para este campo"

#: includes/admin/admin-field-group.php:758
msgid "Close Window"
msgstr "Cerrar ventana"

#: includes/admin/admin-field-group.php:754
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "El campo %1$s ahora se puede encontrar en el grupo de campos %2$s"

#: includes/admin/admin-field-group.php:752
msgid "Move Complete."
msgstr "Movimiento completo."

#: includes/admin/views/field-group-options.php:10
msgid "Active"
msgstr "Activo"

#: includes/admin/admin-field-group.php:361
msgid "Field Keys"
msgstr "Claves de campo"

#: includes/admin/admin-field-group.php:235
#: includes/admin/tools/class-acf-admin-tool-export.php:288
msgid "Settings"
msgstr "Ajustes"

#: includes/admin/admin-field-group.php:234
#: includes/admin/admin-field-groups.php:265
msgid "Location"
msgstr "Ubicación"

#: includes/admin/admin-field-group.php:176 assets/build/js/acf-input.js:963
#: assets/build/js/acf-input.js:1075
msgid "Null"
msgstr "Null"

#: includes/acf-field-group-functions.php:846
#: includes/admin/admin-field-group.php:173
#: assets/build/js/acf-field-group.js:994
#: assets/build/js/acf-field-group.js:1228
msgid "copy"
msgstr "copiar"

#: includes/admin/admin-field-group.php:172
#: assets/build/js/acf-field-group.js:318
#: assets/build/js/acf-field-group.js:383
msgid "(this field)"
msgstr "(este campo)"

#: includes/admin/admin-field-group.php:170 assets/build/js/acf-input.js:900
#: assets/build/js/acf-input.js:924 assets/build/js/acf-input.js:1002
#: assets/build/js/acf-input.js:1030
msgid "Checked"
msgstr "Seleccionado"

#: includes/admin/admin-field-group.php:169
#: assets/build/js/acf-field-group.js:1075
#: assets/build/js/acf-field-group.js:1326
msgid "Move Custom Field"
msgstr "Mover campo personalizado"

#: includes/admin/admin-field-group.php:168
#: assets/build/js/acf-field-group.js:341
#: assets/build/js/acf-field-group.js:409
msgid "No toggle fields available"
msgstr "No hay campos de conmutación disponibles"

#: includes/admin/admin-field-group.php:167
#: assets/build/js/acf-field-group.js:2056
#: assets/build/js/acf-field-group.js:2425
msgid "Move to trash. Are you sure?"
msgstr "Mover a papelera. ¿Estás seguro?"

#: includes/admin/admin-field-group.php:166
#: assets/build/js/acf-field-group.js:2047
#: assets/build/js/acf-field-group.js:2413
msgid "Field group title is required"
msgstr "El título del grupo de campos es obligatorio"

#: includes/admin/admin-field-group.php:165
#: assets/build/js/acf-field-group.js:1063
#: assets/build/js/acf-field-group.js:1312
msgid "This field cannot be moved until its changes have been saved"
msgstr "Este campo se puede mover hasta que sus cambios se hayan guardado"

#: includes/admin/admin-field-group.php:164
#: assets/build/js/acf-field-group.js:893
#: assets/build/js/acf-field-group.js:1110
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""
"La cadena \"field_\" no se debe utilizar al comienzo de un nombre de campo"

#: includes/admin/admin-field-group.php:93
msgid "Field group draft updated."
msgstr "Borrador del grupo de campos actualizado."

#: includes/admin/admin-field-group.php:92
msgid "Field group scheduled for."
msgstr "Grupo de campos programado."

#: includes/admin/admin-field-group.php:91
msgid "Field group submitted."
msgstr "Grupo de campos enviado."

#: includes/admin/admin-field-group.php:90
msgid "Field group saved."
msgstr "Grupo de campos guardado."

#: includes/admin/admin-field-group.php:89
msgid "Field group published."
msgstr "Grupo de campos publicado."

#: includes/admin/admin-field-group.php:86
msgid "Field group deleted."
msgstr "Grupo de campos eliminado."

#: includes/admin/admin-field-group.php:84
#: includes/admin/admin-field-group.php:85
#: includes/admin/admin-field-group.php:87
msgid "Field group updated."
msgstr "Grupo de campos actualizado."

#: includes/admin/admin-tools.php:119
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Herramientas"

#: includes/locations/abstract-acf-location.php:106
msgid "is not equal to"
msgstr "no es igual a"

#: includes/locations/abstract-acf-location.php:105
msgid "is equal to"
msgstr "es igual a"

#: includes/locations.php:102
msgid "Forms"
msgstr "Formularios"

#: includes/locations.php:100 includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Página"

#: includes/locations.php:99 includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Entrada"

#: includes/fields.php:358
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:357
msgid "Relational"
msgstr "Relación"

#: includes/fields.php:356
msgid "Choice"
msgstr "Elección"

#: includes/fields.php:354
msgid "Basic"
msgstr "Básico"

#: includes/fields.php:313
msgid "Unknown"
msgstr "Desconocido"

#: includes/fields.php:313
msgid "Field type does not exist"
msgstr "El tipo de campo no existe"

#: includes/forms/form-front.php:236
msgid "Spam Detected"
msgstr "Spam detectado"

#: includes/forms/form-front.php:107
msgid "Post updated"
msgstr "Publicación actualizada"

#: includes/forms/form-front.php:106
msgid "Update"
msgstr "Actualizar"

#: includes/forms/form-front.php:57
msgid "Validate Email"
msgstr "Validar correo electrónico"

#: includes/fields.php:355 includes/forms/form-front.php:49
msgid "Content"
msgstr "Contenido"

#: includes/forms/form-front.php:40
msgid "Title"
msgstr "Título"

#: includes/assets.php:371 includes/forms/form-comment.php:160
#: assets/build/js/acf-input.js:6870 assets/build/js/acf-input.js:7822
msgid "Edit field group"
msgstr "Editar grupo de campos"

#: includes/admin/admin-field-group.php:188 assets/build/js/acf-input.js:1102
#: assets/build/js/acf-input.js:1230
msgid "Selection is less than"
msgstr "La selección es menor que"

#: includes/admin/admin-field-group.php:187 assets/build/js/acf-input.js:1084
#: assets/build/js/acf-input.js:1202
msgid "Selection is greater than"
msgstr "La selección es mayor que"

#: includes/admin/admin-field-group.php:186 assets/build/js/acf-input.js:1051
#: assets/build/js/acf-input.js:1170
msgid "Value is less than"
msgstr "El valor es menor que"

#: includes/admin/admin-field-group.php:185 assets/build/js/acf-input.js:1020
#: assets/build/js/acf-input.js:1139
msgid "Value is greater than"
msgstr "El valor es mayor que"

#: includes/admin/admin-field-group.php:184 assets/build/js/acf-input.js:871
#: assets/build/js/acf-input.js:960
msgid "Value contains"
msgstr "El valor contiene"

#: includes/admin/admin-field-group.php:183 assets/build/js/acf-input.js:846
#: assets/build/js/acf-input.js:926
msgid "Value matches pattern"
msgstr "El valor coincide con el patrón"

#: includes/admin/admin-field-group.php:182 assets/build/js/acf-input.js:825
#: assets/build/js/acf-input.js:999 assets/build/js/acf-input.js:903
#: assets/build/js/acf-input.js:1116
msgid "Value is not equal to"
msgstr "El valor no es igual a"

#: includes/admin/admin-field-group.php:181 assets/build/js/acf-input.js:796
#: assets/build/js/acf-input.js:944 assets/build/js/acf-input.js:864
#: assets/build/js/acf-input.js:1053
msgid "Value is equal to"
msgstr "El valor es igual a"

#: includes/admin/admin-field-group.php:180 assets/build/js/acf-input.js:775
#: assets/build/js/acf-input.js:841
msgid "Has no value"
msgstr "No tiene ningún valor"

#: includes/admin/admin-field-group.php:179 assets/build/js/acf-input.js:744
#: assets/build/js/acf-input.js:783
msgid "Has any value"
msgstr "No tiene algún valor"

#: includes/assets.php:352 assets/build/js/acf.js:1489
#: assets/build/js/acf.js:1550
msgid "Cancel"
msgstr "Cancelar"

#: includes/assets.php:348 assets/build/js/acf.js:1641
#: assets/build/js/acf.js:1747
msgid "Are you sure?"
msgstr "¿Estás seguro?"

#: includes/assets.php:368 assets/build/js/acf-input.js:8793
#: assets/build/js/acf-input.js:10110
msgid "%d fields require attention"
msgstr "%d campos requieren atención"

#: includes/assets.php:367 assets/build/js/acf-input.js:8791
#: assets/build/js/acf-input.js:10106
msgid "1 field requires attention"
msgstr "1 campo requiere atención"

#: includes/assets.php:366 includes/validation.php:286
#: includes/validation.php:296 assets/build/js/acf-input.js:8784
#: assets/build/js/acf-input.js:10101
msgid "Validation failed"
msgstr "Validación fallida"

#: includes/assets.php:365 assets/build/js/acf-input.js:8939
#: assets/build/js/acf-input.js:10284
msgid "Validation successful"
msgstr "Validación correcta"

#: includes/media.php:54 assets/build/js/acf-input.js:6699
#: assets/build/js/acf-input.js:7626
msgid "Restricted"
msgstr "Restringido"

#: includes/media.php:53 assets/build/js/acf-input.js:6529
#: assets/build/js/acf-input.js:7390
msgid "Collapse Details"
msgstr "Contraer detalles"

#: includes/media.php:52 assets/build/js/acf-input.js:6529
#: assets/build/js/acf-input.js:7387
msgid "Expand Details"
msgstr "Ampliar detalles"

#: includes/media.php:51 assets/build/js/acf-input.js:6401
#: assets/build/js/acf-input.js:7235
msgid "Uploaded to this post"
msgstr "Subido a esta publicación"

#: includes/media.php:50 assets/build/js/acf-input.js:6437
#: assets/build/js/acf-input.js:7274
msgctxt "verb"
msgid "Update"
msgstr "Actualizar"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Editar"

#: includes/assets.php:362 assets/build/js/acf-input.js:8561
#: assets/build/js/acf-input.js:9872
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Los cambios que has realizado se perderán si navegas hacia otra página"

#: includes/api/api-helpers.php:3413
msgid "File type must be %s."
msgstr "El tipo de archivo debe ser %s."

#: includes/admin/admin-field-group.php:174
#: includes/admin/views/field-group-field-conditional-logic.php:60
#: includes/admin/views/field-group-field-conditional-logic.php:170
#: includes/admin/views/field-group-locations.php:36
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3409 assets/build/js/acf-field-group.js:447
#: assets/build/js/acf-field-group.js:1727
#: assets/build/js/acf-field-group.js:538
#: assets/build/js/acf-field-group.js:2055
msgid "or"
msgstr "o"

#: includes/api/api-helpers.php:3382
msgid "File size must not exceed %s."
msgstr "El tamaño del archivo no debe ser mayor de %s."

#: includes/api/api-helpers.php:3377
msgid "File size must be at least %s."
msgstr "El tamaño de archivo debe ser al menos %s."

#: includes/api/api-helpers.php:3362
msgid "Image height must not exceed %dpx."
msgstr "La altura de la imagen no debe exceder %dpx."

#: includes/api/api-helpers.php:3357
msgid "Image height must be at least %dpx."
msgstr "La altura de la imagen debe ser al menos %dpx."

#: includes/api/api-helpers.php:3343
msgid "Image width must not exceed %dpx."
msgstr "El ancho de la imagen no debe exceder %dpx."

#: includes/api/api-helpers.php:3338
msgid "Image width must be at least %dpx."
msgstr "El ancho de la imagen debe ser al menos %dpx."

#: includes/api/api-helpers.php:1569 includes/api/api-term.php:147
msgid "(no title)"
msgstr "(sin título)"

#: includes/api/api-helpers.php:864
msgid "Full Size"
msgstr "Tamaño completo"

#: includes/api/api-helpers.php:823
msgid "Large"
msgstr "Grande"

#: includes/api/api-helpers.php:822
msgid "Medium"
msgstr "Mediano"

#: includes/api/api-helpers.php:821
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/acf-field-functions.php:841
#: includes/admin/admin-field-group.php:171
#: assets/build/js/acf-field-group.js:709
#: assets/build/js/acf-field-group.js:846
msgid "(no label)"
msgstr "(sin etiqueta)"

#: includes/fields/class-acf-field-textarea.php:137
msgid "Sets the textarea height"
msgstr "Establece la altura del área de texto"

#: includes/fields/class-acf-field-textarea.php:136
msgid "Rows"
msgstr "Filas"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Área de texto"

#: includes/fields/class-acf-field-checkbox.php:394
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Anteponer una casilla de verificación extra para cambiar todas las opciones"

#: includes/fields/class-acf-field-checkbox.php:393
msgid "Toggle"
msgstr "Cambiar"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Save 'custom' values to the field's choices"
msgstr "Guardar los valores «personalizados» a las opciones del campo"

#: includes/fields/class-acf-field-checkbox.php:348
msgid "Save Custom"
msgstr "Guardar personalizados"

#: includes/fields/class-acf-field-checkbox.php:340
msgid "Allow 'custom' values to be added"
msgstr "Permite añadir valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:335
msgid "Allow Custom"
msgstr "Permitir personalizados"

#: includes/fields/class-acf-field-checkbox.php:207
msgid "Add new choice"
msgstr "Añadir nueva opción"

#: includes/fields/class-acf-field-checkbox.php:144
msgid "Toggle All"
msgstr "Invertir todos"

#: includes/fields/class-acf-field-page_link.php:494
msgid "Allow Archives URLs"
msgstr "Permitir las URLs de los archivos"

#: includes/fields/class-acf-field-page_link.php:165
msgid "Archives"
msgstr "Archivo"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Enlace a página"

#: includes/fields/class-acf-field-taxonomy.php:961
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Añadir"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:926
msgid "Name"
msgstr "Nombre"

#: includes/fields/class-acf-field-taxonomy.php:910
msgid "%s added"
msgstr "%s añadido/s"

#: includes/fields/class-acf-field-taxonomy.php:874
msgid "%s already exists"
msgstr "%s ya existe"

#: includes/fields/class-acf-field-taxonomy.php:862
msgid "User unable to add new %s"
msgstr "El usuario no puede añadir nuevos %s"

#: includes/fields/class-acf-field-taxonomy.php:807
msgid "Term ID"
msgstr "ID de término"

#: includes/fields/class-acf-field-taxonomy.php:806
msgid "Term Object"
msgstr "Objeto de término"

#: includes/fields/class-acf-field-taxonomy.php:790
msgid "Load value from posts terms"
msgstr "Cargar el valor de los términos de la publicación"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "Load Terms"
msgstr "Cargar términos"

#: includes/fields/class-acf-field-taxonomy.php:778
msgid "Connect selected terms to the post"
msgstr "Conectar los términos seleccionados con la publicación"

#: includes/fields/class-acf-field-taxonomy.php:777
msgid "Save Terms"
msgstr "Guardar términos"

#: includes/fields/class-acf-field-taxonomy.php:766
msgid "Allow new terms to be created whilst editing"
msgstr "Permitir la creación de nuevos términos mientras se edita"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Create Terms"
msgstr "Crear términos"

#: includes/fields/class-acf-field-taxonomy.php:737
msgid "Radio Buttons"
msgstr "Botones de radio"

#: includes/fields/class-acf-field-taxonomy.php:736
msgid "Single Value"
msgstr "Valor único"

#: includes/fields/class-acf-field-taxonomy.php:734
msgid "Multi Select"
msgstr "Selección múltiple"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:733
msgid "Checkbox"
msgstr "Casilla de verificación"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "Multiple Values"
msgstr "Valores múltiples"

#: includes/fields/class-acf-field-taxonomy.php:727
msgid "Select the appearance of this field"
msgstr "Selecciona la apariencia de este campo"

#: includes/fields/class-acf-field-taxonomy.php:726
msgid "Appearance"
msgstr "Apariencia"

#: includes/fields/class-acf-field-taxonomy.php:715
msgid "Select the taxonomy to be displayed"
msgstr "Selecciona la taxonomía a mostrar"

#: includes/fields/class-acf-field-taxonomy.php:673
msgctxt "No terms"
msgid "No %s"
msgstr "Ningún %s"

#: includes/fields/class-acf-field-number.php:251
msgid "Value must be equal to or lower than %d"
msgstr "El valor debe ser menor o igual a %d"

#: includes/fields/class-acf-field-number.php:244
msgid "Value must be equal to or higher than %d"
msgstr "El valor debe ser mayor o igual a %d"

#: includes/fields/class-acf-field-number.php:229
msgid "Value must be a number"
msgstr "El valor debe ser un número"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Número"

#: includes/fields/class-acf-field-radio.php:232
msgid "Save 'other' values to the field's choices"
msgstr "Guardar los valores de 'otros' en las opciones del campo"

#: includes/fields/class-acf-field-radio.php:227
msgid "Save Other"
msgstr "Guardar otros"

#: includes/fields/class-acf-field-radio.php:219
msgid "Add 'other' choice to allow for custom values"
msgstr "Añade la opción 'otros' para permitir valores personalizados"

#: includes/fields/class-acf-field-radio.php:214
msgid "Other"
msgstr "Otros"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Botón de radio"

#: includes/fields/class-acf-field-accordion.php:127
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Define un punto final para que el acordeón anterior se detenga. Este "
"acordeón no será visible."

#: includes/fields/class-acf-field-accordion.php:115
msgid "Allow this accordion to open without closing others."
msgstr "Permita que este acordeón se abra sin cerrar otros."

#: includes/fields/class-acf-field-accordion.php:114
msgid "Multi-expand"
msgstr "Multi-expansión"

#: includes/fields/class-acf-field-accordion.php:103
msgid "Display this accordion as open on page load."
msgstr "Muestra este acordeón como abierto en la carga de la página."

#: includes/fields/class-acf-field-accordion.php:102
msgid "Open"
msgstr "Abrir"

#: includes/fields/class-acf-field-accordion.php:25
msgid "Accordion"
msgstr "Acordeón"

#: includes/fields/class-acf-field-file.php:262
#: includes/fields/class-acf-field-file.php:275
msgid "Restrict which files can be uploaded"
msgstr "Restringir qué archivos que se pueden subir"

#: includes/fields/class-acf-field-file.php:236
msgid "File ID"
msgstr "ID del archivo"

#: includes/fields/class-acf-field-file.php:235
msgid "File URL"
msgstr "URL del archivo"

#: includes/fields/class-acf-field-file.php:234
msgid "File Array"
msgstr "Array del archivo"

#: includes/fields/class-acf-field-file.php:183
msgid "Add File"
msgstr "Añadir archivo"

#: includes/admin/tools/class-acf-admin-tool-import.php:89
#: includes/fields/class-acf-field-file.php:183
msgid "No file selected"
msgstr "Ningún archivo seleccionado"

#: includes/fields/class-acf-field-file.php:147
msgid "File name"
msgstr "Nombre del archivo"

#: includes/fields/class-acf-field-file.php:60
#: assets/build/js/acf-input.js:2334 assets/build/js/acf-input.js:2603
msgid "Update File"
msgstr "Actualizar archivo"

#: includes/fields/class-acf-field-file.php:59
#: assets/build/js/acf-input.js:2333 assets/build/js/acf-input.js:2602
msgid "Edit File"
msgstr "Editar archivo"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:58
#: assets/build/js/acf-input.js:2308 assets/build/js/acf-input.js:2575
msgid "Select File"
msgstr "Seleccionar archivo"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Archivo"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Contraseña"

#: includes/fields/class-acf-field-select.php:440
msgid "Specify the value returned"
msgstr "Especifica el valor devuelto"

#: includes/fields/class-acf-field-select.php:422
msgid "Use AJAX to lazy load choices?"
msgstr "¿Usar AJAX para hacer cargar las opciones de forma asíncrona?"

#: includes/fields/class-acf-field-checkbox.php:367
#: includes/fields/class-acf-field-select.php:376
msgid "Enter each default value on a new line"
msgstr "Añade cada valor en una nueva línea"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:48
#: assets/build/js/acf-input.js:6311 assets/build/js/acf-input.js:7120
msgctxt "verb"
msgid "Select"
msgstr "Selecciona"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Error al cargar"

#: includes/fields/class-acf-field-select.php:117
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Buscando&hellip;"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Cargando más resultados&hellip;"

#: includes/fields/class-acf-field-select.php:115
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Solo puedes seleccionar %d elementos"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Solo puedes seleccionar 1 elemento"

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Por favor, borra %d caracteres"

#: includes/fields/class-acf-field-select.php:112
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Por favor, borra 1 carácter"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Por favor, introduce %d o más caracteres"

#: includes/fields/class-acf-field-select.php:110
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Por favor, introduce 1 o más caracteres"

#: includes/fields/class-acf-field-select.php:109
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "No se han encontrado coincidencias"

#: includes/fields/class-acf-field-select.php:108
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d resultados disponibles, utiliza las flechas arriba y abajo para navegar "
"por los resultados."

#: includes/fields/class-acf-field-select.php:107
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Hay un resultado disponible, pulsa enter para seleccionarlo."

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:738
msgctxt "noun"
msgid "Select"
msgstr "Selección"

#: includes/fields/class-acf-field-user.php:97
msgid "User ID"
msgstr "ID del usuario"

#: includes/fields/class-acf-field-user.php:96
msgid "User Object"
msgstr "Grupo de objetos"

#: includes/fields/class-acf-field-user.php:95
msgid "User Array"
msgstr "Grupo de usuarios"

#: includes/fields/class-acf-field-user.php:61
msgid "All user roles"
msgstr "Todos los roles de usuario"

#: includes/fields/class-acf-field-user.php:53
msgid "Filter by role"
msgstr "Filtrar por perfil"

#: includes/fields/class-acf-field-user.php:20 includes/locations.php:101
msgid "User"
msgstr "Usuario"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Separador"

#: includes/fields/class-acf-field-color_picker.php:73
msgid "Select Color"
msgstr "Seleccionar color"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Default"
msgstr "Por defecto"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Clear"
msgstr "Vaciar"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Selector de color"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seleccionar"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Hecho"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Ahora"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Zona horaria"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsegundo"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisegundo"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Segundo"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuto"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Elegir hora"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Selector de fecha y hora"

#: includes/fields/class-acf-field-tab.php:118
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Definir un punto final para detener las pestañas anteriores. Esto iniciará "
"un nuevo grupo de pestañas."

#: includes/fields/class-acf-field-accordion.php:126
#: includes/fields/class-acf-field-tab.php:117
msgid "Endpoint"
msgstr "Variable"

#: includes/admin/views/field-group-options.php:88
#: includes/fields/class-acf-field-tab.php:108
msgid "Left aligned"
msgstr "Alineada a la izquierda"

#: includes/admin/views/field-group-options.php:87
#: includes/fields/class-acf-field-tab.php:107
msgid "Top aligned"
msgstr "Alineada arriba"

#: includes/fields/class-acf-field-tab.php:103
msgid "Placement"
msgstr "Ubicación"

#: includes/fields/class-acf-field-tab.php:26
msgid "Tab"
msgstr "Pestaña"

#: includes/fields/class-acf-field-url.php:154
msgid "Value must be a valid URL"
msgstr "El valor debe ser una URL válida"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-link.php:177
msgid "Link URL"
msgstr "URL del enlace"

#: includes/fields/class-acf-field-link.php:176
msgid "Link Array"
msgstr "Array de enlaces"

#: includes/fields/class-acf-field-link.php:142
msgid "Opens in a new window/tab"
msgstr "Abrir en una nueva ventana/pestaña"

#: includes/fields/class-acf-field-link.php:137
msgid "Select Link"
msgstr "Elige el enlace"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Enlace"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Correo electrónico"

#: includes/fields/class-acf-field-number.php:191
#: includes/fields/class-acf-field-range.php:198
msgid "Step Size"
msgstr "Tamaño de paso"

#: includes/fields/class-acf-field-number.php:180
#: includes/fields/class-acf-field-range.php:186
msgid "Maximum Value"
msgstr "Valor máximo"

#: includes/fields/class-acf-field-number.php:169
#: includes/fields/class-acf-field-range.php:174
msgid "Minimum Value"
msgstr "Valor mínimo"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Rango"

#: includes/fields/class-acf-field-button-group.php:205
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-radio.php:280
#: includes/fields/class-acf-field-select.php:446
msgid "Both (Array)"
msgstr "Ambos (Array)"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:279
#: includes/fields/class-acf-field-select.php:445
msgid "Label"
msgstr "Etiqueta"

#: includes/fields/class-acf-field-button-group.php:203
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:278
#: includes/fields/class-acf-field-select.php:444
msgid "Value"
msgstr "Valor"

#: includes/fields/class-acf-field-button-group.php:188
#: includes/fields/class-acf-field-checkbox.php:383
#: includes/fields/class-acf-field-radio.php:262
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:187
#: includes/fields/class-acf-field-checkbox.php:384
#: includes/fields/class-acf-field-radio.php:263
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:325
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "rojo : Rojo"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:325
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Para más control, puedes especificar tanto un valor como una etiqueta, así:"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-checkbox.php:325
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "Añade cada opción en una nueva línea."

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:324
#: includes/fields/class-acf-field-radio.php:191
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "Opciones"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Grupo de botones"

#: includes/fields/class-acf-field-page_link.php:506
#: includes/fields/class-acf-field-post_object.php:406
#: includes/fields/class-acf-field-select.php:398
#: includes/fields/class-acf-field-user.php:79
msgid "Select multiple values?"
msgstr "¿Seleccionar múltiples valores?"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:482
#: includes/fields/class-acf-field-post_object.php:394
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-taxonomy.php:748
#: includes/fields/class-acf-field-user.php:68
msgid "Allow Null?"
msgstr "¿Permitir Null?"

#: includes/fields/class-acf-field-page_link.php:249
#: includes/fields/class-acf-field-post_object.php:250
#: includes/fields/class-acf-field-taxonomy.php:948
msgid "Parent"
msgstr "Superior"

#: includes/fields/class-acf-field-wysiwyg.php:391
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE no se inicializará hasta que se haga clic en el campo"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Delay initialization?"
msgstr "¿Retrasar el inicio?"

#: includes/fields/class-acf-field-wysiwyg.php:378
msgid "Show Media Upload Buttons?"
msgstr "¿Mostrar botones de subida de medios?"

#: includes/fields/class-acf-field-wysiwyg.php:361
msgid "Toolbar"
msgstr "Barra de herramientas"

#: includes/fields/class-acf-field-wysiwyg.php:352
msgid "Text Only"
msgstr "Sólo texto"

#: includes/fields/class-acf-field-wysiwyg.php:351
msgid "Visual Only"
msgstr "Sólo visual"

#: includes/fields/class-acf-field-wysiwyg.php:350
msgid "Visual & Text"
msgstr "Visual y Texto"

#: includes/fields/class-acf-field-wysiwyg.php:345
msgid "Tabs"
msgstr "Pestañas"

#: includes/fields/class-acf-field-wysiwyg.php:289
msgid "Click to initialize TinyMCE"
msgstr "Haz clic para iniciar TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:283
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-wysiwyg.php:282
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Editor Wysiwyg"

#: includes/fields/class-acf-field-text.php:168
#: includes/fields/class-acf-field-textarea.php:221
msgid "Value must not exceed %d characters"
msgstr "El valor no debe exceder los %d caracteres"

#: includes/fields/class-acf-field-text.php:142
#: includes/fields/class-acf-field-textarea.php:126
msgid "Leave blank for no limit"
msgstr "Déjalo en blanco para ilimitado"

#: includes/fields/class-acf-field-text.php:141
#: includes/fields/class-acf-field-textarea.php:125
msgid "Character Limit"
msgstr "Límite de caracteres"

#: includes/fields/class-acf-field-email.php:151
#: includes/fields/class-acf-field-number.php:159
#: includes/fields/class-acf-field-password.php:96
#: includes/fields/class-acf-field-range.php:222
#: includes/fields/class-acf-field-text.php:131
msgid "Appears after the input"
msgstr "Aparece después del campo"

#: includes/fields/class-acf-field-email.php:150
#: includes/fields/class-acf-field-number.php:158
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:221
#: includes/fields/class-acf-field-text.php:130
msgid "Append"
msgstr "Anexar"

#: includes/fields/class-acf-field-email.php:140
#: includes/fields/class-acf-field-number.php:148
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:211
#: includes/fields/class-acf-field-text.php:120
msgid "Appears before the input"
msgstr "Aparece antes del campo"

#: includes/fields/class-acf-field-email.php:139
#: includes/fields/class-acf-field-number.php:147
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:210
#: includes/fields/class-acf-field-text.php:119
msgid "Prepend"
msgstr "Anteponer"

#: includes/fields/class-acf-field-email.php:129
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:109
#: includes/fields/class-acf-field-textarea.php:115
#: includes/fields/class-acf-field-url.php:113
msgid "Appears within the input"
msgstr "Aparece en el campo"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:73
#: includes/fields/class-acf-field-text.php:108
#: includes/fields/class-acf-field-textarea.php:114
#: includes/fields/class-acf-field-url.php:112
msgid "Placeholder Text"
msgstr "Marcador de posición"

#: includes/fields/class-acf-field-button-group.php:171
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:126
#: includes/fields/class-acf-field-radio.php:246
#: includes/fields/class-acf-field-range.php:164
#: includes/fields/class-acf-field-text.php:98
#: includes/fields/class-acf-field-textarea.php:104
#: includes/fields/class-acf-field-url.php:102
#: includes/fields/class-acf-field-wysiwyg.php:335
msgid "Appears when creating a new post"
msgstr "Aparece cuando se está creando una nueva entrada"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-relationship.php:752
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s necesita al menos %2$s selección"
msgstr[1] "%1$s necesita al menos %2$s selecciones"

#: includes/fields/class-acf-field-post_object.php:424
#: includes/fields/class-acf-field-relationship.php:668
msgid "Post ID"
msgstr "ID de publicación"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:423
#: includes/fields/class-acf-field-relationship.php:667
msgid "Post Object"
msgstr "Objeto de publicación"

#: includes/fields/class-acf-field-relationship.php:651
msgid "Maximum posts"
msgstr "Publicaciones máximas"

#: includes/fields/class-acf-field-relationship.php:640
msgid "Minimum posts"
msgstr "Publicaciones mínimas"

#: includes/admin/views/field-group-options.php:150
#: includes/fields/class-acf-field-relationship.php:631
msgid "Featured Image"
msgstr "Imagen destacada"

#: includes/fields/class-acf-field-relationship.php:627
msgid "Selected elements will be displayed in each result"
msgstr "Los elementos seleccionados se mostrarán en cada resultado"

#: includes/fields/class-acf-field-relationship.php:626
msgid "Elements"
msgstr "Elementos"

#: includes/fields/class-acf-field-relationship.php:617
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:714
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Taxonomía"

#: includes/fields/class-acf-field-relationship.php:616
#: includes/locations/class-acf-location-post-type.php:22
msgid "Post Type"
msgstr "Tipo de contenido"

#: includes/fields/class-acf-field-relationship.php:610
msgid "Filters"
msgstr "Filtros"

#: includes/fields/class-acf-field-page_link.php:474
#: includes/fields/class-acf-field-post_object.php:386
#: includes/fields/class-acf-field-relationship.php:602
msgid "All taxonomies"
msgstr "Todas las taxonomías"

#: includes/fields/class-acf-field-page_link.php:466
#: includes/fields/class-acf-field-post_object.php:378
#: includes/fields/class-acf-field-relationship.php:594
msgid "Filter by Taxonomy"
msgstr "Filtrar por taxonomía"

#: includes/fields/class-acf-field-page_link.php:458
#: includes/fields/class-acf-field-post_object.php:370
#: includes/fields/class-acf-field-relationship.php:586
msgid "All post types"
msgstr "Todos los tipos de contenido"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:362
#: includes/fields/class-acf-field-relationship.php:578
msgid "Filter by Post Type"
msgstr "Filtrar por tipo de contenido"

#: includes/fields/class-acf-field-relationship.php:469
msgid "Search..."
msgstr "Buscar..."

#: includes/fields/class-acf-field-relationship.php:399
msgid "Select taxonomy"
msgstr "Selecciona taxonomía"

#: includes/fields/class-acf-field-relationship.php:390
msgid "Select post type"
msgstr "Seleccionar tipo de contenido"

#: includes/fields/class-acf-field-relationship.php:65
#: assets/build/js/acf-input.js:3686 assets/build/js/acf-input.js:4168
msgid "No matches found"
msgstr "No se han encontrado coincidencias"

#: includes/fields/class-acf-field-relationship.php:64
#: assets/build/js/acf-input.js:3670 assets/build/js/acf-input.js:4147
msgid "Loading"
msgstr "Cargando"

#: includes/fields/class-acf-field-relationship.php:63
#: assets/build/js/acf-input.js:3593 assets/build/js/acf-input.js:4051
msgid "Maximum values reached ( {max} values )"
msgstr "Valores máximos alcanzados ( {max} valores )"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relación"

#: includes/fields/class-acf-field-file.php:288
#: includes/fields/class-acf-field-image.php:325
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista separada por comas. Déjalo en blanco para todos los tipos"

#: includes/fields/class-acf-field-file.php:287
#: includes/fields/class-acf-field-image.php:324
msgid "Allowed file types"
msgstr "Tipos de archivos permitidos"

#: includes/fields/class-acf-field-file.php:274
#: includes/fields/class-acf-field-image.php:287
msgid "Maximum"
msgstr "Máximo"

#: includes/fields/class-acf-field-file.php:151
#: includes/fields/class-acf-field-file.php:265
#: includes/fields/class-acf-field-file.php:278
#: includes/fields/class-acf-field-image.php:277
#: includes/fields/class-acf-field-image.php:314
msgid "File size"
msgstr "Tamaño del archivo"

#: includes/fields/class-acf-field-image.php:251
#: includes/fields/class-acf-field-image.php:288
msgid "Restrict which images can be uploaded"
msgstr "Restringir qué imágenes se pueden subir"

#: includes/fields/class-acf-field-file.php:261
#: includes/fields/class-acf-field-image.php:250
msgid "Minimum"
msgstr "Mínimo"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:241
msgid "Uploaded to post"
msgstr "Subidos al contenido"

#: includes/fields/class-acf-field-file.php:251
#: includes/fields/class-acf-field-image.php:240
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Todos"

#: includes/fields/class-acf-field-file.php:246
#: includes/fields/class-acf-field-image.php:235
msgid "Limit the media library choice"
msgstr "Limitar las opciones de la biblioteca de medios"

#: includes/fields/class-acf-field-file.php:245
#: includes/fields/class-acf-field-image.php:234
msgid "Library"
msgstr "Biblioteca"

#: includes/fields/class-acf-field-image.php:222
msgid "Preview Size"
msgstr "Tamaño de vista previa"

#: includes/fields/class-acf-field-image.php:213
msgid "Image ID"
msgstr "ID de imagen"

#: includes/fields/class-acf-field-image.php:212
msgid "Image URL"
msgstr "URL de imagen"

#: includes/fields/class-acf-field-image.php:211
msgid "Image Array"
msgstr "Array de imágenes"

#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:406
#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-link.php:171
#: includes/fields/class-acf-field-radio.php:273
msgid "Specify the returned value on front end"
msgstr "Especificar el valor devuelto en la web"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-file.php:228
#: includes/fields/class-acf-field-link.php:170
#: includes/fields/class-acf-field-radio.php:272
#: includes/fields/class-acf-field-taxonomy.php:801
msgid "Return Value"
msgstr "Valor de retorno"

#: includes/fields/class-acf-field-image.php:159
msgid "Add Image"
msgstr "Añadir imagen"

#: includes/fields/class-acf-field-image.php:159
msgid "No image selected"
msgstr "No hay ninguna imagen seleccionada"

#: includes/assets.php:351 includes/fields/class-acf-field-file.php:159
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:142 assets/build/js/acf.js:1488
#: assets/build/js/acf.js:1549
msgid "Remove"
msgstr "Quitar"

#: includes/admin/views/field-group-field.php:56
#: includes/fields/class-acf-field-file.php:157
#: includes/fields/class-acf-field-image.php:137
#: includes/fields/class-acf-field-link.php:142
msgid "Edit"
msgstr "Editar"

#: includes/fields/class-acf-field-image.php:67 includes/media.php:55
#: assets/build/js/acf-input.js:6354 assets/build/js/acf-input.js:7174
msgid "All images"
msgstr "Todas las imágenes"

#: includes/fields/class-acf-field-image.php:66
#: assets/build/js/acf-input.js:2997 assets/build/js/acf-input.js:3377
msgid "Update Image"
msgstr "Actualizar imagen"

#: includes/fields/class-acf-field-image.php:65
#: assets/build/js/acf-input.js:2996 assets/build/js/acf-input.js:3376
msgid "Edit Image"
msgstr "Editar imagen"

#: includes/fields/class-acf-field-image.php:64
#: assets/build/js/acf-input.js:2974 assets/build/js/acf-input.js:3351
msgid "Select Image"
msgstr "Seleccionar imagen"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Imagen"

#: includes/fields/class-acf-field-message.php:128
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Permitir que el maquetado HTML se muestre como texto visible en vez de "
"interpretarlo"

#: includes/fields/class-acf-field-message.php:127
msgid "Escape HTML"
msgstr "Escapar HTML"

#: includes/fields/class-acf-field-message.php:118
#: includes/fields/class-acf-field-textarea.php:155
msgid "No Formatting"
msgstr "Sin formato"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:154
msgid "Automatically add &lt;br&gt;"
msgstr "Añadir &lt;br&gt; automáticamente"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:153
msgid "Automatically add paragraphs"
msgstr "Añadir párrafos automáticamente"

#: includes/fields/class-acf-field-message.php:112
#: includes/fields/class-acf-field-textarea.php:149
msgid "Controls how new lines are rendered"
msgstr "Controla cómo se muestran los saltos de línea"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:148
msgid "New Lines"
msgstr "Nuevas líneas"

#: includes/fields/class-acf-field-date_picker.php:237
#: includes/fields/class-acf-field-date_time_picker.php:220
msgid "Week Starts On"
msgstr "La semana comienza el"

#: includes/fields/class-acf-field-date_picker.php:203
msgid "The format used when saving a value"
msgstr "El formato utilizado cuando se guarda un valor"

#: includes/fields/class-acf-field-date_picker.php:202
msgid "Save Format"
msgstr "Guardar formato"

#: includes/fields/class-acf-field-date_picker.php:64
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sem"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Siguiente"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Hoy"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Listo"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Selector de fecha"

#: includes/fields/class-acf-field-image.php:254
#: includes/fields/class-acf-field-image.php:291
#: includes/fields/class-acf-field-oembed.php:279
msgid "Width"
msgstr "Ancho"

#: includes/fields/class-acf-field-oembed.php:276
#: includes/fields/class-acf-field-oembed.php:289
msgid "Embed Size"
msgstr "Tamaño de incrustación"

#: includes/fields/class-acf-field-oembed.php:230
msgid "Enter URL"
msgstr "Introduce la URL"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:190
msgid "Text shown when inactive"
msgstr "Texto mostrado cuando está inactivo"

#: includes/fields/class-acf-field-true_false.php:189
msgid "Off Text"
msgstr "Texto desactivado"

#: includes/fields/class-acf-field-true_false.php:173
msgid "Text shown when active"
msgstr "Texto mostrado cuando está activo"

#: includes/fields/class-acf-field-true_false.php:172
msgid "On Text"
msgstr "Texto activado"

#: includes/fields/class-acf-field-select.php:410
#: includes/fields/class-acf-field-true_false.php:159
msgid "Stylised UI"
msgstr "UI estilizada"

#: includes/fields/class-acf-field-button-group.php:170
#: includes/fields/class-acf-field-checkbox.php:366
#: includes/fields/class-acf-field-color_picker.php:155
#: includes/fields/class-acf-field-email.php:117
#: includes/fields/class-acf-field-number.php:125
#: includes/fields/class-acf-field-radio.php:245
#: includes/fields/class-acf-field-range.php:163
#: includes/fields/class-acf-field-select.php:375
#: includes/fields/class-acf-field-text.php:97
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-true_false.php:148
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:334
msgid "Default Value"
msgstr "Valor por defecto"

#: includes/fields/class-acf-field-true_false.php:138
msgid "Displays text alongside the checkbox"
msgstr "Muestra el texto junto a la casilla de verificación"

#: includes/fields/class-acf-field-message.php:26
#: includes/fields/class-acf-field-message.php:100
#: includes/fields/class-acf-field-true_false.php:137
msgid "Message"
msgstr "Mensaje"

#: includes/assets.php:350 includes/fields/class-acf-field-true_false.php:83
#: includes/fields/class-acf-field-true_false.php:193
#: assets/build/js/acf.js:1645 assets/build/js/acf.js:1749
msgid "No"
msgstr "No"

#: includes/assets.php:349 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:176
#: assets/build/js/acf.js:1643 assets/build/js/acf.js:1748
msgid "Yes"
msgstr "Sí"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Verdadero / Falso"

#: includes/fields/class-acf-field-group.php:470
msgid "Row"
msgstr "Fila"

#: includes/fields/class-acf-field-group.php:469
msgid "Table"
msgstr "Tabla"

#: includes/fields/class-acf-field-group.php:468
msgid "Block"
msgstr "Bloque"

#: includes/fields/class-acf-field-group.php:463
msgid "Specify the style used to render the selected fields"
msgstr ""
"Especifica el estilo utilizado para representar los campos seleccionados"

#: includes/fields.php:359 includes/fields/class-acf-field-button-group.php:181
#: includes/fields/class-acf-field-checkbox.php:377
#: includes/fields/class-acf-field-group.php:462
#: includes/fields/class-acf-field-radio.php:256
msgid "Layout"
msgstr "Estructura"

#: includes/fields/class-acf-field-group.php:446
msgid "Sub Fields"
msgstr "Subcampos"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Grupo"

#: includes/fields/class-acf-field-google-map.php:232
msgid "Customize the map height"
msgstr "Personalizar la altura del mapa"

#: includes/fields/class-acf-field-google-map.php:231
#: includes/fields/class-acf-field-image.php:265
#: includes/fields/class-acf-field-image.php:302
#: includes/fields/class-acf-field-oembed.php:292
msgid "Height"
msgstr "Altura"

#: includes/fields/class-acf-field-google-map.php:220
msgid "Set the initial zoom level"
msgstr "Establecer el nivel inicial de zoom"

#: includes/fields/class-acf-field-google-map.php:219
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:193
#: includes/fields/class-acf-field-google-map.php:206
msgid "Center the initial map"
msgstr "Centrar inicialmente el mapa"

#: includes/fields/class-acf-field-google-map.php:192
#: includes/fields/class-acf-field-google-map.php:205
msgid "Center"
msgstr "Centro"

#: includes/fields/class-acf-field-google-map.php:160
msgid "Search for address..."
msgstr "Buscar dirección..."

#: includes/fields/class-acf-field-google-map.php:157
msgid "Find current location"
msgstr "Encontrar ubicación actual"

#: includes/fields/class-acf-field-google-map.php:156
msgid "Clear location"
msgstr "Borrar ubicación"

#: includes/fields/class-acf-field-google-map.php:155
#: includes/fields/class-acf-field-relationship.php:615
msgid "Search"
msgstr "Buscar"

#: includes/fields/class-acf-field-google-map.php:60
#: assets/build/js/acf-input.js:2673 assets/build/js/acf-input.js:3004
msgid "Sorry, this browser does not support geolocation"
msgstr "Lo siento, este navegador no es compatible con la geolocalización"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Mapa de Google"

#: includes/fields/class-acf-field-date_picker.php:217
#: includes/fields/class-acf-field-date_time_picker.php:202
#: includes/fields/class-acf-field-time_picker.php:132
msgid "The format returned via template functions"
msgstr "El formato devuelto por de las funciones del tema"

#: includes/fields/class-acf-field-color_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:216
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-image.php:205
#: includes/fields/class-acf-field-post_object.php:418
#: includes/fields/class-acf-field-relationship.php:662
#: includes/fields/class-acf-field-select.php:439
#: includes/fields/class-acf-field-time_picker.php:131
#: includes/fields/class-acf-field-user.php:90
msgid "Return Format"
msgstr "Formato de retorno"

#: includes/fields/class-acf-field-date_picker.php:190
#: includes/fields/class-acf-field-date_picker.php:226
#: includes/fields/class-acf-field-date_time_picker.php:192
#: includes/fields/class-acf-field-date_time_picker.php:211
#: includes/fields/class-acf-field-time_picker.php:122
#: includes/fields/class-acf-field-time_picker.php:139
msgid "Custom:"
msgstr "Personalizado:"

#: includes/fields/class-acf-field-date_picker.php:182
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:115
msgid "The format displayed when editing a post"
msgstr "El formato mostrado cuando se edita una publicación"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-time_picker.php:114
msgid "Display Format"
msgstr "Formato de visualización"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Selector de hora"

#: acf.php:445
msgid "Disabled <span class=\"count\">(%s)</span>"
msgid_plural "Disabled <span class=\"count\">(%s)</span>"
msgstr[0] "Desactivado <span class=\"count\">(%s)</span>"
msgstr[1] "Desactivados <span class=\"count\">(%s)</span>"

#: acf.php:402
msgid "No Fields found in Trash"
msgstr "No se han encontrado campos en la papelera"

#: acf.php:401
msgid "No Fields found"
msgstr "No se han encontrado campos"

#: acf.php:400
msgid "Search Fields"
msgstr "Buscar campos"

#: acf.php:399
msgid "View Field"
msgstr "Ver campo"

#: acf.php:398 includes/admin/views/field-group-fields.php:56
msgid "New Field"
msgstr "Nuevo campo"

#: acf.php:397
msgid "Edit Field"
msgstr "Editar campo"

#: acf.php:396
msgid "Add New Field"
msgstr "Añadir nuevo campo"

#: acf.php:394
msgid "Field"
msgstr "Campo"

#: acf.php:393 includes/admin/admin-field-group.php:233
#: includes/admin/admin-field-groups.php:266
msgid "Fields"
msgstr "Campos"

#: acf.php:368
msgid "No Field Groups found in Trash"
msgstr "No se han encontrado grupos de campos en la papelera"

#: acf.php:367
msgid "No Field Groups found"
msgstr "No se han encontrado grupos de campos"

#: acf.php:366
msgid "Search Field Groups"
msgstr "Buscar grupo de campos"

#: acf.php:365
msgid "View Field Group"
msgstr "Ver grupo de campos"

#: acf.php:364
msgid "New Field Group"
msgstr "Nuevo grupo de campos"

#: acf.php:363
msgid "Edit Field Group"
msgstr "Editar grupo de campos"

#: acf.php:362
msgid "Add New Field Group"
msgstr "Añadir nuevo grupo de campos"

#: acf.php:361 acf.php:395 includes/admin/admin.php:51
msgid "Add New"
msgstr "Añadir nuevo"

#: acf.php:360
msgid "Field Group"
msgstr "Grupo de campos"

#: acf.php:359 includes/admin/admin.php:50
msgid "Field Groups"
msgstr "Grupos de campos"

#. Description of the plugin
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Personaliza WordPress con campos potentes, profesionales e intuitivos."

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php:70
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:166
msgid "Block type name is required."
msgstr "El nombre del tipo de bloque es requerido."

#: pro/blocks.php:173
msgid "Block type \"%s\" is already registered."
msgstr "El tipo de bloque \" %s\" ya está registrado."

#: pro/blocks.php:731
msgid "Switch to Edit"
msgstr "Cambiar a Editar"

#: pro/blocks.php:732
msgid "Switch to Preview"
msgstr "Cambiar a vista previa"

#: pro/blocks.php:733
msgid "Change content alignment"
msgstr "Cambiar la alineación del contenido"

#. translators: %s: Block type title
#: pro/blocks.php:736
msgid "%s settings"
msgstr "%s ajustes"

#: pro/blocks.php:949
msgid "This block contains no editable fields."
msgstr "Este bloque no contiene campos editables."

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:955
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr ""
"Asigna un <a href=\"%s\" target=\"_blank\">grupo de campos</a> para añadir "
"campos a este bloque."

#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Opciones Actualizadas"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a href=\"%1$s"
"\">Updates</a> page. If you don't have a licence key, please see <a href="
"\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Para habilitar las actualizaciones, introduzca su clave de licencia en la "
"página <a href=\"%1$s\">Actualizaciones.</a> Si no tiene una clave de "
"licencia, consulte los <a href=\"%2$s\" target=\"_blank\">detalles y los "
"precios.</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>Error de activación de ACF</b>. La clave de licencia definida ha "
"cambiado, pero se ha producido un error al desactivar la licencia anterior"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>Error de activación de ACF</b>. La clave de licencia definida ha "
"cambiado, pero se ha producido un error al conectarse al servidor de "
"activación"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr "<b>Error de activación de ACF</b>"

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""
"<b>Error</b>. No se ha podido conectar con el servidor de actualización"

#: pro/updates.php:279, pro/admin/views/html-settings-updates.php:117
msgid "Check Again"
msgstr "Comprobar de nuevo"

#: pro/updates.php:561
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""
"<b>Error</b>. No se ha podido conectar con el servidor de actualización"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Publicar"

#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"No se encontraron grupos de campos personalizados para esta página de "
"opciones. <a href=\"%s\">Crear Grupo de Campos Personalizados</a>"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr ""
"<b>Error</b>. No se ha podido conectar con el servidor de actualización"

#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Actualizaciones"

#: pro/admin/admin-updates.php:209
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Error</b>. No se pudo autenticar el paquete de actualización. Compruebe "
"de nuevo o desactive y reactive su licencia ACF PRO."

#: pro/admin/admin-updates.php:196
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Error</b>. Su licencia para este sitio ha caducado o ha sido desactivada. "
"Por favor, reactive su licencia ACF PRO."

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clonar"

#: pro/fields/class-acf-field-clone.php:815
msgid "Select one or more fields you wish to clone"
msgstr "Elige uno o más campos que quieras clonar"

#: pro/fields/class-acf-field-clone.php:834
msgid "Display"
msgstr "Mostrar"

#: pro/fields/class-acf-field-clone.php:835
msgid "Specify the style used to render the clone field"
msgstr "Especifique el estilo utilizado para procesar el campo de clonación"

#: pro/fields/class-acf-field-clone.php:840
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Grupo (muestra los campos seleccionados en un grupo dentro de este campo)"

#: pro/fields/class-acf-field-clone.php:841
msgid "Seamless (replaces this field with selected fields)"
msgstr "Transparente (reemplaza este campo con los campos seleccionados)"

#: pro/fields/class-acf-field-clone.php:864
msgid "Labels will be displayed as %s"
msgstr "Las etiquetas se mostrarán como %s"

#: pro/fields/class-acf-field-clone.php:869
msgid "Prefix Field Labels"
msgstr "Etiquetas del prefijo de campo"

#: pro/fields/class-acf-field-clone.php:880
msgid "Values will be saved as %s"
msgstr "Los valores se guardarán como %s"

#: pro/fields/class-acf-field-clone.php:885
msgid "Prefix Field Names"
msgstr "Nombres de prefijos de campos"

#: pro/fields/class-acf-field-clone.php:1001
msgid "Unknown field"
msgstr "Campo desconocido"

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field group"
msgstr "Grupo de campos desconocido"

#: pro/fields/class-acf-field-clone.php:1042
msgid "All fields from %s field group"
msgstr "Todos los campos del grupo de campo %s"

#: pro/fields/class-acf-field-flexible-content.php:31,
#: pro/fields/class-acf-field-repeater.php:79,
#: pro/fields/class-acf-field-repeater.php:263
msgid "Add Row"
msgstr "Agregar Fila"

#: pro/fields/class-acf-field-flexible-content.php:71,
#: pro/fields/class-acf-field-flexible-content.php:917,
#: pro/fields/class-acf-field-flexible-content.php:996
msgid "layout"
msgid_plural "layouts"
msgstr[0] "diseño"
msgstr[1] "esquema"

#: pro/fields/class-acf-field-flexible-content.php:72
msgid "layouts"
msgstr "diseños"

#: pro/fields/class-acf-field-flexible-content.php:75,
#: pro/fields/class-acf-field-flexible-content.php:916,
#: pro/fields/class-acf-field-flexible-content.php:995
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Este campo requiere al menos {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:76
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""
"Este campo tiene un límite de la etiqueta de la etiqueta de la etiqueta de "
"la etiqueta."

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponible (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:80
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} requerido (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:83
msgid "Flexible Content requires at least 1 layout"
msgstr "El Contenido Flexible requiere por lo menos 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:276
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Haz click en el botón \"%s\" debajo para comenzar a crear tu esquema"

#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Agregar Esquema"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Duplicate layout"
msgstr "Duplicar Diseño"

#: pro/fields/class-acf-field-flexible-content.php:415
msgid "Remove layout"
msgstr "Remover esquema"

#: pro/fields/class-acf-field-flexible-content.php:416,
#: pro/fields/class-acf-repeater-table.php:369
msgid "Click to toggle"
msgstr "Clic para mostrar"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder Layout"
msgstr "Reordenar Esquema"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder"
msgstr "Reordenar"

#: pro/fields/class-acf-field-flexible-content.php:552
msgid "Delete Layout"
msgstr "Eliminar Esquema"

#: pro/fields/class-acf-field-flexible-content.php:553
msgid "Duplicate Layout"
msgstr "Duplicar Esquema"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New Layout"
msgstr "Agregar Nuevo Esquema"

#: pro/fields/class-acf-field-flexible-content.php:635
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:650
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:679,
#: pro/fields/class-acf-field-repeater.php:259
msgid "Button Label"
msgstr "Etiqueta del Botón"

#: pro/fields/class-acf-field-flexible-content.php:690
msgid "Minimum Layouts"
msgstr "Esquemas Mínimos"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "Esquemas Máximos"

#: pro/fields/class-acf-field-flexible-content.php:1704,
#: pro/fields/class-acf-field-repeater.php:861
msgid "%s must be of type array or null."
msgstr "%s debe ser de tipo matriz o null."

#: pro/fields/class-acf-field-flexible-content.php:1715
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "%1$s debe contener al menos %2$s %3$s diseño."
msgstr[1] "%1$s debe contener al menos %2$s %3$s diseños."

#: pro/fields/class-acf-field-flexible-content.php:1731
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "%1$s debe contener como máximo %2$s %3$s diseño."
msgstr[1] "%1$s debe contener como máximo %2$s %3$s diseños."

#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "Agregar Imagen a Galería"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "Selección máxima alcanzada"

#: pro/fields/class-acf-field-gallery.php:320
msgid "Length"
msgstr "Longitud"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Leyenda"

#: pro/fields/class-acf-field-gallery.php:376
msgid "Alt Text"
msgstr "Texto Alt"

#: pro/fields/class-acf-field-gallery.php:500
msgid "Add to gallery"
msgstr "Agregar a galería"

#: pro/fields/class-acf-field-gallery.php:504
msgid "Bulk actions"
msgstr "Acciones en lote"

#: pro/fields/class-acf-field-gallery.php:505
msgid "Sort by date uploaded"
msgstr "Ordenar por fecha de subida"

#: pro/fields/class-acf-field-gallery.php:506
msgid "Sort by date modified"
msgstr "Ordenar por fecha de modificación"

#: pro/fields/class-acf-field-gallery.php:507
msgid "Sort by title"
msgstr "Ordenar por título"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Reverse current order"
msgstr "Invertir orden actual"

#: pro/fields/class-acf-field-gallery.php:520
msgid "Close"
msgstr "Cerrar"

#: pro/fields/class-acf-field-gallery.php:602
msgid "Insert"
msgstr "Insertar"

#: pro/fields/class-acf-field-gallery.php:603
msgid "Specify where new attachments are added"
msgstr "Especificar dónde se agregan nuevos adjuntos"

#: pro/fields/class-acf-field-gallery.php:607
msgid "Append to the end"
msgstr "Añadir al final"

#: pro/fields/class-acf-field-gallery.php:608
msgid "Prepend to the beginning"
msgstr "Adelantar hasta el principio"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Minimum Selection"
msgstr "Selección Mínima"

#: pro/fields/class-acf-field-gallery.php:644
msgid "Maximum Selection"
msgstr "Selección Máxima"

#: pro/fields/class-acf-field-repeater.php:53,
#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum rows reached ({min} rows)"
msgstr "Mínimo de filas alcanzado ({min} rows)"

#: pro/fields/class-acf-field-repeater.php:54
msgid "Maximum rows reached ({max} rows)"
msgstr "Máximo de filas alcanzado ({max} rows)"

#: pro/fields/class-acf-field-repeater.php:55
msgid "Error loading page"
msgstr "Error al cargar la página"

#: pro/fields/class-acf-field-repeater.php:174
msgid "Collapsed"
msgstr "Colapsado"

#: pro/fields/class-acf-field-repeater.php:175
msgid "Select a sub field to show when row is collapsed"
msgstr "Elige un subcampo para indicar cuándo se colapsa la fila"

#: pro/fields/class-acf-field-repeater.php:187
msgid "Minimum Rows"
msgstr "Mínimo de Filas"

#: pro/fields/class-acf-field-repeater.php:199
msgid "Maximum Rows"
msgstr "Máximo de Filas"

#: pro/fields/class-acf-field-repeater.php:228
msgid "Pagination"
msgstr "Paginación"

#: pro/fields/class-acf-field-repeater.php:229
msgid "Useful for fields with a large number of rows."
msgstr "Útil para campos con un gran número de filas."

#: pro/fields/class-acf-field-repeater.php:240
msgid "Rows Per Page"
msgstr "Filas por página"

#: pro/fields/class-acf-field-repeater.php:241
msgid "Set the number of rows to be displayed on a page."
msgstr "Establece el número de filas que se mostrarán en una página."

#: pro/fields/class-acf-field-repeater.php:959
msgid "Invalid field key."
msgstr "Clave de campo no válida."

#: pro/fields/class-acf-field-repeater.php:968
msgid "There was an error retrieving the field."
msgstr "Ha habido un error al recuperar el campo."

#: pro/fields/class-acf-repeater-table.php:389
msgid "Add row"
msgstr "Agregar fila"

#: pro/fields/class-acf-repeater-table.php:390
msgid "Duplicate row"
msgstr "Duplicar fila"

#: pro/fields/class-acf-repeater-table.php:391
msgid "Remove row"
msgstr "Remover fila"

#: pro/fields/class-acf-repeater-table.php:435,
#: pro/fields/class-acf-repeater-table.php:452
msgid "Current Page"
msgstr "Página actual"

#: pro/fields/class-acf-repeater-table.php:444
msgid "First page"
msgstr "Primera página"

#: pro/fields/class-acf-repeater-table.php:448
msgid "Previous page"
msgstr "Página anterior"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:457
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr "%1$s de %2$s"

#: pro/fields/class-acf-repeater-table.php:465
msgid "Next page"
msgstr "Página siguiente"

#: pro/fields/class-acf-repeater-table.php:469
msgid "Last page"
msgstr "Última página"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "No existen tipos de bloques"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "No existen páginas de opciones"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Desactivar Licencia"

#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Activar Licencia"

#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Información de la licencia"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Para desbloquear las actualizaciones, por favor a continuación introduce tu "
"clave de licencia. Si no tienes una clave de licencia, consulta <a href=\"%s"
"\" target=\"_blank\">detalles y precios</a>."

#: pro/admin/views/html-settings-updates.php:41
msgid "License Key"
msgstr "Clave de Licencia"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "La clave de licencia se define en wp-config.php."

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr "Reintentar activación"

#: pro/admin/views/html-settings-updates.php:76
msgid "Update Information"
msgstr "Información de Actualización"

#: pro/admin/views/html-settings-updates.php:83
msgid "Current Version"
msgstr "Versión Actual"

#: pro/admin/views/html-settings-updates.php:91
msgid "Latest Version"
msgstr "Última Versión"

#: pro/admin/views/html-settings-updates.php:99
msgid "Update Available"
msgstr "Actualización Disponible"

#: pro/admin/views/html-settings-updates.php:111
msgid "Please enter your license key above to unlock updates"
msgstr "Por favor ingresa tu clave de licencia para habilitar actualizaciones"

#: pro/admin/views/html-settings-updates.php:109
msgid "Update Plugin"
msgstr "Actualizar Plugin"

#: pro/admin/views/html-settings-updates.php:107
msgid "Please reactivate your license to unlock updates"
msgstr "Reactive su licencia para desbloquear actualizaciones"

#: pro/admin/views/html-settings-updates.php:124
msgid "Changelog"
msgstr "Registro de cambios"

#: pro/admin/views/html-settings-updates.php:134
msgid "Upgrade Notice"
msgstr "Notificación de Actualización"

#~ msgid "Inactive"
#~ msgstr "Inactivo"

#, php-format
#~ msgid "Inactive <span class=\"count\">(%s)</span>"
#~ msgid_plural "Inactive <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Activo <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Activos <span class=\"count\">(%s)</span>"

#~ msgid "Parent fields"
#~ msgstr "Campos Padre"

#~ msgid "Sibling fields"
#~ msgstr "Campos de mismo nivel"

#, php-format
#~ msgid "%s field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "%s grupo de campos sincronizado."
#~ msgstr[1] "%s grupos de campos sincronizado."

#~ msgid "Status"
#~ msgstr "Estado"

#, php-format
#~ msgid "See what's new in <a href=\"%s\">version %s</a>."
#~ msgstr "Ver las novedades de la <a href=\"% s \">versión %s</a>."

#~ msgid "Resources"
#~ msgstr "Recursos"

#~ msgid "Website"
#~ msgstr "Sitio web"

#~ msgid "Documentation"
#~ msgstr "Documentación"

#~ msgid "Pro"
#~ msgstr "Pro"

#, php-format
#~ msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
#~ msgstr "Gracias por crear con <a href=\" %s \">ACF</a>."

#~ msgid "Synchronise field group"
#~ msgstr "Sincronizar grupo de campos"

#~ msgid "Apply"
#~ msgstr "Aplicar"

#~ msgid "Bulk Actions"
#~ msgstr "Acciones en lote"

#~ msgid "Add-ons"
#~ msgstr "Agregados"

#~ msgid "<b>Error</b>. Could not load add-ons list"
#~ msgstr "<b>Error</b>. No se pudo cargar la lista de agregados"

#~ msgid "Info"
#~ msgstr "Info"

#~ msgid "What's New"
#~ msgstr "Qué hay de nuevo"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Actualización de Base de Datos de Advanced Custom Fields"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Antes de comenzar a utilizar las nuevas y excelentes características, por "
#~ "favor actualizar tu base de datos a la versión más nueva."

#~ msgid "Download & Install"
#~ msgstr "Descargar e Instalar"

#~ msgid "Installed"
#~ msgstr "Instalado"

#~ msgid "Welcome to Advanced Custom Fields"
#~ msgstr "Bienvenido a Advanced Custom Fields"

#, php-format
#~ msgid ""
#~ "Thank you for updating! ACF %s is bigger and better than ever before. We "
#~ "hope you like it."
#~ msgstr ""
#~ "Gracias por actualizar! ACF %s es más grande y poderoso que nunca.  "
#~ "Esperamos que te guste."

#~ msgid "A smoother custom field experience"
#~ msgstr "Una experiencia de campos personalizados más fluida"

#~ msgid "Improved Usability"
#~ msgstr "Usabilidad Mejorada"

#~ msgid ""
#~ "Including the popular Select2 library has improved both usability and "
#~ "speed across a number of field types including post object, page link, "
#~ "taxonomy and select."
#~ msgstr ""
#~ "Incluir la popular librería Select2 ha mejorado tanto la usabilidad como "
#~ "la velocidad a través de varios tipos de campos, incluyendo el objeto "
#~ "post , link a página, taxonomía y selección."

#~ msgid "Improved Design"
#~ msgstr "Diseño Mejorado"

#~ msgid ""
#~ "Many fields have undergone a visual refresh to make ACF look better than "
#~ "ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
#~ "(new) fields!"
#~ msgstr ""
#~ "Muchos campos han experimentado un mejorado visual para hacer que ACF "
#~ "luzca mejor que nunca! Hay cambios notables en los campos de galería, "
#~ "relación y oEmbed (nuevo)!"

#~ msgid "Improved Data"
#~ msgstr "Datos Mejorados"

#~ msgid ""
#~ "Redesigning the data architecture has allowed sub fields to live "
#~ "independently from their parents. This allows you to drag and drop fields "
#~ "in and out of parent fields!"
#~ msgstr ""
#~ "Rediseñar la arquitectura de datos ha permitido que los sub campos vivan "
#~ "independientemente de sus padres.  Esto permite arrastrar y soltar campos "
#~ "desde y hacia otros campos padres!"

#~ msgid "Goodbye Add-ons. Hello PRO"
#~ msgstr "Adiós Agregados.  Hola PRO"

#~ msgid "Introducing ACF PRO"
#~ msgstr "Presentando ACF PRO"

#~ msgid ""
#~ "We're changing the way premium functionality is delivered in an exciting "
#~ "way!"
#~ msgstr ""
#~ "Estamos cambiando la manera en que las funcionalidades premium son "
#~ "brindadas de un modo muy interesante!"

#, php-format
#~ msgid ""
#~ "All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
#~ "version of ACF</a>. With both personal and developer licenses available, "
#~ "premium functionality is more affordable and accessible than ever before!"
#~ msgstr ""
#~ "Todos los 4 agregados premium han sido combinados en una nueva <a href="
#~ "\"%s\">version Pro de ACF</a>. Con lincencias personales y para "
#~ "desarrolladores disponibles, la funcionalidad premium es más acequible "
#~ "que nunca!"

#~ msgid "Powerful Features"
#~ msgstr "Características Poderosas"

#~ msgid ""
#~ "ACF PRO contains powerful features such as repeatable data, flexible "
#~ "content layouts, a beautiful gallery field and the ability to create "
#~ "extra admin options pages!"
#~ msgstr ""
#~ "ACF PRO contiene poderosas características como campo de repetición, "
#~ "contenido con disposición flexible, un hermoso campo de galería y la "
#~ "habilidad de crear páginas de opciones extra en el panel de "
#~ "administración."

#, php-format
#~ msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
#~ msgstr ""
#~ "Lee más acerca de las <a href=\"%s\">características de ACF PRO</a>."

#~ msgid "Easy Upgrading"
#~ msgstr "Fácil Actualización"

#, php-format
#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Para facilitar la actualización, <a href=\"%s\">accede a tu cuenta en "
#~ "nuestra tienda</a> y solicita una copia gratis de ACF PRO!"

#, php-format
#~ msgid ""
#~ "We also wrote an <a href=\"%s\">upgrade guide</a> to answer any "
#~ "questions, but if you do have one, please contact our support team via "
#~ "the <a href=\"%s\">help desk</a>"
#~ msgstr ""
#~ "Nosotros también escribimos una <a href=\"%s\">guía de actualización</a> "
#~ "para responder cualquier pregunta, pero si tienes una, por favor contacta "
#~ "nuestro equipo de soporte via <a href=\"%s\">mesa de ayuda</a>"

#~ msgid "Under the Hood"
#~ msgstr "Debajo del Capó"

#~ msgid "Smarter field settings"
#~ msgstr "Ajustes de campos más inteligentes"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr ""
#~ "ACF ahora guarda los ajustes de los campos como objetos post individuales"

#~ msgid "More AJAX"
#~ msgstr "Más AJAX"

#~ msgid "More fields use AJAX powered search to speed up page loading"
#~ msgstr ""
#~ "Más campos utilizan búsqueda manejada por AJAX para acelerar la carga de "
#~ "página"

#~ msgid "New auto export to JSON feature improves speed"
#~ msgstr "La nueva funcionalidad de exportar a JSON mejora la velocidad"

#~ msgid "Better version control"
#~ msgstr "Mejor Control por Versiones"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "La nueva funcionalidad de exporta a JSON permite que los ajustes de los "
#~ "campos se controlen por versiones"

#~ msgid "Swapped XML for JSON"
#~ msgstr "Reemplazamos XML por JSON"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "Importar / Exportar ahora utilizan JSON en vez de XML"

#~ msgid "New Forms"
#~ msgstr "Nuevos Formularios"

#~ msgid "Fields can now be mapped to comments, widgets and all user forms!"
#~ msgstr ""
#~ "Los campos ahora pueden ser mapeados a comentarios, widgets y todos los "
#~ "formularios de usuario!"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "Se agregó un nuevo campo para embeber contenido."

#~ msgid "New Gallery"
#~ msgstr "Nueva Galería"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "El campo galería ha experimentado un muy necesario lavado de cara"

#~ msgid "New Settings"
#~ msgstr "Nuevos Ajustes"

#~ msgid ""
#~ "Field group settings have been added for label placement and instruction "
#~ "placement"
#~ msgstr ""
#~ "Se agregaron ajustes de grupos de campos para posicionamiento de las "
#~ "etiquetas y las instrucciones"

#~ msgid "Better Front End Forms"
#~ msgstr "Mejores formularios para Front End"

#~ msgid "acf_form() can now create a new post on submission"
#~ msgstr "acf_form() ahora puede crear nuevos post"

#~ msgid "Better Validation"
#~ msgstr "Mejor Validación"

#~ msgid "Form validation is now done via PHP + AJAX in favour of only JS"
#~ msgstr ""
#~ "La validación de los formularios es ahora realizada via PHP + AJAX en vez "
#~ "de sólo JS"

#~ msgid "Relationship Field"
#~ msgstr "Campod de Relación"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr ""
#~ "Nuevos ajustes para 'Filtros' en camp de Relación (Búsqueda, Tipo de "
#~ "Post, Taxonomía)"

#~ msgid "Moving Fields"
#~ msgstr "Moviendo Campos"

#~ msgid ""
#~ "New field group functionality allows you to move a field between groups & "
#~ "parents"
#~ msgstr ""
#~ "Nueva funcionalidad de grupos permite mover campos entre grupos y padres"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "Nuevo grupo archivos en el campo de selección de page_link"

#~ msgid "Better Options Pages"
#~ msgstr "Mejores Páginas de Opciones"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Nuevas funciones para las páginas de opciones permiten crear tanto "
#~ "páginas de menú hijas como superiores."

#, php-format
#~ msgid "We think you'll love the changes in %s."
#~ msgstr "Creemos que te encantarán los cambios en %s."

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Exportar Field Groups a PHP"

#~ msgid "Download export file"
#~ msgstr "Descargar archivo de exportación"

#~ msgid "Generate export code"
#~ msgstr "Generar código de exportación"

#~ msgid "Current Color"
#~ msgstr "Color actual"

#~ msgid "Locating"
#~ msgstr "Ubicando"

#~ msgid "Shown when entering data"
#~ msgstr "Mostrado cuando se ingresan datos"

#~ msgid "Error."
#~ msgstr "Error."

#~ msgid "No embed found for the given URL."
#~ msgstr "No se encontró embed para la URL proporcionada."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Valores mínimos alcanzados ( {min} valores )"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "El campo pestaña se visualizará incorrectamente cuando sea agregado a un "
#~ "campo de repetición con estilo Tabla o a un layout de contenido flexible"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "Usa \"Campos Pestaña\" para organizar mejor tu pantalla de edición "
#~ "agrupando campos."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Todos los campos que siguen de este \"campo pestaña\" (o hasta que otro "
#~ "\"campo pestaña\" sea definido) serán agrepados la etiqueta de este campo "
#~ "como título de la pestaña."

#~ msgid "None"
#~ msgstr "Ninguno"

#~ msgid "Taxonomy Term"
#~ msgstr "Término de Taxonomía"

#~ msgid "remove {layout}?"
#~ msgstr "remover {layout}?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "Este campo requiere al menos {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "Límite máximo de {label} alcanzado. ({max} {identifier})"

#~ msgid "Elliot Condon"
#~ msgstr "Elliot Condon"

#~ msgid "http://www.elliotcondon.com/"
#~ msgstr "http://www.elliotcondon.com/"

#~ msgid "See what's new in"
#~ msgstr "Que hay de nuevo en"

#~ msgid "Getting Started"
#~ msgstr "Comenzando"

#~ msgid "Field Types"
#~ msgstr "Tipos de Campos"

#~ msgid "Functions"
#~ msgstr "Funciones"

#~ msgid "Actions"
#~ msgstr "Acciones"

#~ msgid "'How to' guides"
#~ msgstr "Guías 'Cómo hacer'"

#~ msgid "Tutorials"
#~ msgstr "Tutoriales"

#~ msgid "Created by"
#~ msgstr "Creado por"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr ""
#~ "<b>Perfecto</b>. La herramienta de importación agregó %s grupos de "
#~ "campos: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Alerta</b>. La herramienta de importación detectó que %s grupos de "
#~ "campos ya existen y han sido ignorados: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Actualizar ACF"

#~ msgid "Upgrade"
#~ msgstr "Actualizar"

#~ msgid "Error"
#~ msgstr "Error"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Arrastra y suelta para reordenar"

#~ msgid "Upgrading data to"
#~ msgstr "Actualizando datos a"

#~ msgid "See what's new"
#~ msgstr "Mira qué hay de nuevo"

#~ msgid "Show a different month"
#~ msgstr "Mostrar un mes diferente"

#~ msgid "Return format"
#~ msgstr "Formato de Retorno"

#~ msgid "uploaded to this post"
#~ msgstr "subidos a este post"

#~ msgid "File Size"
#~ msgstr "Tamaño de Archivo"

#~ msgid "No File selected"
#~ msgstr "No hay ningún archivo seleccionado"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "Por favor toma en cuenta que todo el texto será pasado primero por la "
#~ "función wp"

#~ msgid "Warning"
#~ msgstr "Alerta"

#~ msgid "Add new %s "
#~ msgstr "Agregar nuevo %s"

#~ msgid "eg. Show extra content"
#~ msgstr "ej. Mostrar contenido extra"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Error de Conección</b>. Disculpa, por favor intenta nuevamente"

#~ msgid "Save Options"
#~ msgstr "Guardar Opciones"

#~ msgid "License"
#~ msgstr "Licencia"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Para desbloquear las actualizaciones, por favor ingresa tu clabe de "
#~ "licencia debajo.  Si no tienes una clave de licencia, por favor mira"

#~ msgid "details & pricing"
#~ msgstr "detalles y precios"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "Fallo en la validación. Uno o más campos son requeridos."

#~ msgid "Error: Field Type does not exist!"
#~ msgstr "Error: El tipo de campo no existe!"

#~ msgid "No ACF groups selected"
#~ msgstr "No hay grupos de ACF seleccionados"

#~ msgid "Field Order"
#~ msgstr "Orden de los campos"

#~ msgid "Docs"
#~ msgstr "Docs"

#~ msgid "Field Instructions"
#~ msgstr "Instrucciones del campo"

#~ msgid "Save Field"
#~ msgstr "Guardar Field"

#~ msgid "Hide this edit screen"
#~ msgstr "Ocultar esta pantalla de edición"

#~ msgid "continue editing ACF"
#~ msgstr "continuar editando ACF"

#~ msgid "match"
#~ msgstr "coincide"

#~ msgid "of the above"
#~ msgstr "de los superiores"

#~ msgid "Field groups are created in order <br />from lowest to highest."
#~ msgstr "Los Field Groups son creados en orden <br /> de menor a mayor."

#~ msgid "Show on page"
#~ msgstr "Mostrar en página"

#~ msgid "Deselect items to hide them on the edit page"
#~ msgstr "Deselecciona items para esconderlos en la página de edición"

#~ msgid ""
#~ "If multiple ACF groups appear on an edit page, the first ACF group's "
#~ "options will be used. The first ACF group is the one with the lowest "
#~ "order number."
#~ msgstr ""
#~ "Si aparecen multiples grupos de ACF en una página de edición, se usarán "
#~ "las opciones del primer grupo. Se considera primer grupo de ACF al que "
#~ "cuenta con el número de orden más bajo."

#~ msgid ""
#~ "Read documentation, learn the functions and find some tips &amp; tricks "
#~ "for your next web project."
#~ msgstr ""
#~ "Lee la documentación, aprende sobre las funciones y encuentra algunos "
#~ "trucos y consejos para tu siguiente proyecto web."

#~ msgid "Vote"
#~ msgstr "Vota"

#~ msgid "Follow"
#~ msgstr "Sígueme"

#~ msgid "Advanced Custom Fields Settings"
#~ msgstr "Ajustes de Advanced Custom Fields"

#~ msgid "Activate Add-ons."
#~ msgstr "Activar Add-ons."

#~ msgid "Flexible Content Field"
#~ msgstr "Flexible Content Field"

#~ msgid ""
#~ "Add-ons can be unlocked by purchasing a license key. Each key can be used "
#~ "on multiple sites."
#~ msgstr ""
#~ "Las Add-ons pueden desbloquearse comprando una clave de licencia. Cada "
#~ "clave puede usarse en multiple sites."

#~ msgid "Find Add-ons"
#~ msgstr "Buscar Add-ons"

#~ msgid "Export Field Groups to XML"
#~ msgstr "Exportar Field Groups a XML"

#~ msgid ""
#~ "ACF will create a .xml export file which is compatible with the native WP "
#~ "import plugin."
#~ msgstr ""
#~ "ACF creará un archivo .xml que es compatible con el plugin de importación "
#~ "nativo de WP."

#~ msgid "Export XML"
#~ msgstr "Exportar XML"

#~ msgid "Navigate to the"
#~ msgstr "Navegar a"

#~ msgid "and select WordPress"
#~ msgstr "y selecciona WordPress"

#~ msgid "Install WP import plugin if prompted"
#~ msgstr "Instalar el plugin de importación de WP si se pide"

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "Subir e importar tu archivo .xml exportado"

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "Selecciona tu usuario e ignora Import Attachments"

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "¡Eso es todo! Feliz WordPressing"

#~ msgid "ACF will create the PHP code to include in your theme"
#~ msgstr "ACF creará el código PHP para incluir en tu tema"

#~ msgid "Register Field Groups with PHP"
#~ msgstr "Registrar Field Groups con PHP"

#~ msgid "Copy the PHP code generated"
#~ msgstr "Copia el código PHP generado"

#~ msgid "Paste into your functions.php file"
#~ msgstr "Pegalo en tu archivo functions.php"

#~ msgid ""
#~ "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr ""
#~ "Para activar cualquier Add-on, edita y usa el código en las primeras "
#~ "pocas lineas."

#~ msgid ""
#~ "/**\n"
#~ " * Activate Add-ons\n"
#~ " * Here you can enter your activation codes to unlock Add-ons to use in "
#~ "your theme. \n"
#~ " * Since all activation codes are multi-site licenses, you are allowed to "
#~ "include your key in premium themes. \n"
#~ " * Use the commented out code to update the database with your activation "
#~ "code. \n"
#~ " * You may place this code inside an IF statement that only runs on theme "
#~ "activation.\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Activar Add-ons\n"
#~ " * Aquí puedes introducir tus códigos de activación para desbloquear Add-"
#~ "ons y utilizarlos en tu tema. \n"
#~ " * Ya que todos los códigos de activación tiene licencia multi-site, se "
#~ "te permite incluir tu clave en temas premium. \n"
#~ " * Utiliza el código comentado para actualizar la base de datos con tu "
#~ "código de activación. \n"
#~ " * Puedes colocar este código dentro de una instrucción IF para que sólo "
#~ "funcione en la activación del tema.\n"
#~ " */"

#~ msgid ""
#~ "/**\n"
#~ " * Register field groups\n"
#~ " * The register_field_group function accepts 1 array which holds the "
#~ "relevant data to register a field group\n"
#~ " * You may edit the array as you see fit. However, this may result in "
#~ "errors if the array is not compatible with ACF\n"
#~ " * This code must run every time the functions.php file is read\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Registrar field groups\n"
#~ " * La función register_field_group acepta un 1 array que contiene los "
#~ "datos pertinentes para registrar un Field Group\n"
#~ " * Puedes editar el array como mejor te parezca. Sin embargo, esto puede "
#~ "dar lugar a errores si la matriz no es compatible con ACF\n"
#~ " * Este código debe ejecutarse cada vez que se lee el archivo functions."
#~ "php\n"
#~ " */"

#~ msgid "No field groups were selected"
#~ msgstr "No hay ningún Field Group seleccionado"

#~ msgid "No choices to choose from"
#~ msgstr "No hay opciones para escojer"

#~ msgid ""
#~ "Enter your choices one per line<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tRed<br />\n"
#~ "\t\t\t\tBlue<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tor<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tred : Red<br />\n"
#~ "\t\t\t\tblue : Blue"
#~ msgstr ""
#~ "Introduce tus opciones, una por línea<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tRojo<br />\n"
#~ "\t\t\t\tAzul<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\to<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tred : Rojo<br />\n"
#~ "\t\t\t\tblue : Azul"

#~ msgid "eg. dd/mm/yy. read more about"
#~ msgstr "ej. dd/mm/yy. leer más sobre"

#~ msgid "Remove File"
#~ msgstr "Eliminar Archivo"

#~ msgid "Click the \"add row\" button below to start creating your layout"
#~ msgstr ""
#~ "Haz click sobre el botón \"añadir fila\" para empezar a crear tu Layout"

#~ msgid "+ Add Row"
#~ msgstr "+ Añadir fila"

#~ msgid ""
#~ "No fields. Click the \"+ Add Field button\" to create your first field."
#~ msgstr ""
#~ "No hay campos. Haz click en el botón \"+ Añadir Campo\" para crear tu "
#~ "primer campo."

#~ msgid ""
#~ "Filter posts by selecting a post type<br />\n"
#~ "\t\t\t\tTip: deselect all post types to show all post type's posts"
#~ msgstr ""
#~ "Filtrar posts seleccionando un post type<br />\n"
#~ "\t\t\t\tConsejo: deselecciona todos los post type para mostrar todos los "
#~ "tipos de post"

#~ msgid "Filter from Taxonomy"
#~ msgstr "Filtrar por Taxonomía"

#~ msgid "Set to -1 for inifinit"
#~ msgstr "Se establece en -1 para inifinito"

#~ msgid "Repeater Fields"
#~ msgstr "Repeater Fields"

#~ msgid "Row Limit"
#~ msgstr "Limite de filas"

#~ msgid "Formatting"
#~ msgstr "Formato"

#~ msgid "Define how to render html tags"
#~ msgstr "Define como renderizar las etiquetas html"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "Define como renderizar los tags html / nuevas lineas"
