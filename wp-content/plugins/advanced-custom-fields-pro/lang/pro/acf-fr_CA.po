msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.8.8\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2020-04-23 20:16-0400\n"
"PO-Revision-Date: 2020-04-23 21:07-0400\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: fr_CA\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 2.3\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

# @ acf
#: acf.php:68
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

# @ acf
#: acf.php:349 includes/admin/admin.php:54
msgid "Field Groups"
msgstr "Groupes de champs"

# @ acf
#: acf.php:350
msgid "Field Group"
msgstr "Groupe de champs"

# @ acf
#: acf.php:351 acf.php:383 includes/admin/admin.php:55
#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Add New"
msgstr "Ajouter"

# @ acf
#: acf.php:352
msgid "Add New Field Group"
msgstr "Ajouter un nouveau groupe de champs"

# @ acf
#: acf.php:353
msgid "Edit Field Group"
msgstr "Modifier le groupe de champs"

# @ acf
#: acf.php:354
msgid "New Field Group"
msgstr "Nouveau groupe de champs"

# @ default
#: acf.php:355
msgid "View Field Group"
msgstr "Voir le groupe de champs"

# @ default
#: acf.php:356
msgid "Search Field Groups"
msgstr "Rechercher des groupes de champs"

# @ default
#: acf.php:357
msgid "No Field Groups found"
msgstr "Aucun groupe de champs trouvé"

# @ default
#: acf.php:358
msgid "No Field Groups found in Trash"
msgstr "Aucun groupe de champs trouvé dans la corbeille"

# @ acf
#: acf.php:381 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:260
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Champs"

# @ acf
#: acf.php:382
msgid "Field"
msgstr "Champ"

# @ acf
#: acf.php:384
msgid "Add New Field"
msgstr "Ajouter un champ"

# @ acf
#: acf.php:385
msgid "Edit Field"
msgstr "Modifier le champ"

# @ acf
#: acf.php:386 includes/admin/views/field-group-fields.php:41
msgid "New Field"
msgstr "Nouveau champ"

# @ acf
#: acf.php:387
msgid "View Field"
msgstr "Voir le champ"

# @ default
#: acf.php:388
msgid "Search Fields"
msgstr "Rechercher des champs"

# @ default
#: acf.php:389
msgid "No Fields found"
msgstr "Aucun champ trouvé"

# @ default
#: acf.php:390
msgid "No Fields found in Trash"
msgstr "Aucun champ trouvé dans la corbeille"

#: acf.php:425
msgid "Disabled"
msgstr "Désactivé"

#: acf.php:430
#, php-format
msgid "Disabled <span class=\"count\">(%s)</span>"
msgid_plural "Disabled <span class=\"count\">(%s)</span>"
msgstr[0] "Désactivé <span class=\"count\">(%s)</span>"
msgstr[1] "Désactivés <span class=\"count\">(%s)</span>"

#: includes/acf-field-functions.php:831
#: includes/admin/admin-field-group.php:178
msgid "(no label)"
msgstr "(aucun label)"

#: includes/acf-field-group-functions.php:820
#: includes/admin/admin-field-group.php:180
msgid "copy"
msgstr "copie"

# @ acf
#: includes/acf-wp-functions.php:41
msgid "Posts"
msgstr "Articles"

# @ acf
#: includes/acf-wp-functions.php:54
msgid "Taxonomies"
msgstr "Taxonomies"

#: includes/acf-wp-functions.php:59
msgid "Attachments"
msgstr "Fichiers attachés"

#: includes/acf-wp-functions.php:63
#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr "Commentaires"

#: includes/acf-wp-functions.php:67
msgid "Widgets"
msgstr "Widgets"

#: includes/acf-wp-functions.php:71
#: includes/locations/class-acf-location-nav-menu.php:87
msgid "Menus"
msgstr "Menus"

#: includes/acf-wp-functions.php:75
msgid "Menu items"
msgstr "Éléments de menu"

#: includes/acf-wp-functions.php:79
msgid "Users"
msgstr "Utilisateurs"

# @ acf
#: includes/acf-wp-functions.php:83 pro/options-page.php:51
msgid "Options"
msgstr "Options"

#: includes/acf-wp-functions.php:87
msgid "Blocks"
msgstr "Blocs"

# @ default
#: includes/admin/admin-field-group.php:86
#: includes/admin/admin-field-group.php:87
#: includes/admin/admin-field-group.php:89
msgid "Field group updated."
msgstr "Groupe de champs mis à jour."

# @ default
#: includes/admin/admin-field-group.php:88
msgid "Field group deleted."
msgstr "Groupe de champs supprimé."

# @ default
#: includes/admin/admin-field-group.php:91
msgid "Field group published."
msgstr "Groupe de champ publié."

# @ default
#: includes/admin/admin-field-group.php:92
msgid "Field group saved."
msgstr "Groupe de champ enregistré."

# @ default
#: includes/admin/admin-field-group.php:93
msgid "Field group submitted."
msgstr "Groupe de champ enregistré."

#: includes/admin/admin-field-group.php:94
msgid "Field group scheduled for."
msgstr "Groupe de champs programmé pour."

#: includes/admin/admin-field-group.php:95
msgid "Field group draft updated."
msgstr "Brouillon du groupe de champs mis à jour."

#: includes/admin/admin-field-group.php:171
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Le nom d’un champ ne peut pas commencer par « field_ »"

#: includes/admin/admin-field-group.php:172
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Ce champ ne peut pas être déplacé tant que ses modifications n’ont pas été "
"enregistrées"

# @ default
#: includes/admin/admin-field-group.php:173
msgid "Field group title is required"
msgstr "Veuillez indiquer un titre pour le groupe de champs"

# @ acf
#: includes/admin/admin-field-group.php:174
msgid "Move to trash. Are you sure?"
msgstr "Mettre à la corbeille. Êtes-vous sûr?"

#: includes/admin/admin-field-group.php:175
msgid "No toggle fields available"
msgstr "Aucun champ de sélection disponible"

# @ acf
#: includes/admin/admin-field-group.php:176
msgid "Move Custom Field"
msgstr "Déplacer le champ personnalisé"

#: includes/admin/admin-field-group.php:177
msgid "Checked"
msgstr "Coché"

#: includes/admin/admin-field-group.php:179
msgid "(this field)"
msgstr "(ce champ)"

#: includes/admin/admin-field-group.php:181
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3649
msgid "or"
msgstr "ou"

#: includes/admin/admin-field-group.php:182
msgid "Null"
msgstr "Vide"

# @ acf
#: includes/admin/admin-field-group.php:221
#: includes/admin/admin-field-groups.php:259
msgid "Location"
msgstr "Emplacement"

#: includes/admin/admin-field-group.php:222
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Réglages"

#: includes/admin/admin-field-group.php:372
msgid "Field Keys"
msgstr "Identifiants des champs"

#: includes/admin/admin-field-group.php:402
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Actif"

#: includes/admin/admin-field-group.php:402
msgid "Inactive"
msgstr "Inactif"

#: includes/admin/admin-field-group.php:763
msgid "Move Complete."
msgstr "Déplacement effectué."

#: includes/admin/admin-field-group.php:764
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Le champ %s a été déplacé dans le groupe %s"

# @ acf
#: includes/admin/admin-field-group.php:765
msgid "Close Window"
msgstr "Fermer la fenêtre"

# @ acf
#: includes/admin/admin-field-group.php:806
msgid "Please select the destination for this field"
msgstr "Choisissez la destination de ce champ"

# @ acf
#: includes/admin/admin-field-group.php:813
msgid "Move Field"
msgstr "Déplacer le champ"

#: includes/admin/admin-field-groups.php:102
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Actif <span class=\"count\">(%s)</span>"
msgstr[1] "Actifs <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:135
msgid "Overview"
msgstr "Aperçu"

#: includes/admin/admin-field-groups.php:224
msgctxt "post status"
msgid "Disabled"
msgstr "Désactivé"

# @ acf
#: includes/admin/admin-field-groups.php:257
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
#: pro/fields/class-acf-field-gallery.php:380
msgid "Description"
msgstr "Description"

#: includes/admin/admin-field-groups.php:258
#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Identifiant"

#: includes/admin/admin-field-groups.php:263
#: includes/admin/views/settings-info.php:91
msgid "Local JSON"
msgstr "JSON Local"

#: includes/admin/admin-field-groups.php:413
msgid "Various"
msgstr "Variable"

#: includes/admin/admin-field-groups.php:435
#, php-format
msgid "Located in theme: %s"
msgstr "Localisé dans le thème : %s"

#: includes/admin/admin-field-groups.php:439
#, php-format
msgid "Located in plugin: %s"
msgstr "Localisé dans le plugin : %s"

# @ acf
#: includes/admin/admin-field-groups.php:443
#, php-format
msgid "Located in: %s"
msgstr "Localisé dans : %s"

# @ acf
#: includes/admin/admin-field-groups.php:463
#: includes/admin/admin-field-groups.php:672
msgid "Sync available"
msgstr "Synchronisation disponible"

#: includes/admin/admin-field-groups.php:466
msgid "Sync"
msgstr "Synchroniser"

#: includes/admin/admin-field-groups.php:467
msgid "Review changes"
msgstr "Réviser les changements"

#: includes/admin/admin-field-groups.php:471
msgid "Import"
msgstr "Importer"

#: includes/admin/admin-field-groups.php:475
msgid "Saved"
msgstr "Sauvegardé"

#: includes/admin/admin-field-groups.php:478
msgid "Awaiting save"
msgstr "En attente de sauvegarde"

# @ acf
#: includes/admin/admin-field-groups.php:499
msgid "Duplicate this item"
msgstr "Dupliquer cet élément"

#: includes/admin/admin-field-groups.php:499
#: includes/admin/admin-field-groups.php:519
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Duplicate"
msgstr "Dupliquer"

#: includes/admin/admin-field-groups.php:524
msgid "Sync changes"
msgstr "Synchroniser les changements"

# @ default
#: includes/admin/admin-field-groups.php:546
#, php-format
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Groupe de champs dupliqué."
msgstr[1] "%s groupes de champs dupliqués."

# @ default
#: includes/admin/admin-field-groups.php:603
#, php-format
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Groupe de champs synchronisé."
msgstr[1] "%s groupes de champs synchronisés."

# @ acf
#: includes/admin/admin-field-groups.php:776
#, php-format
msgid "Select %s"
msgstr "Choisir %s"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Outils"

# @ acf
#: includes/admin/admin-upgrade.php:49 includes/admin/admin-upgrade.php:111
#: includes/admin/admin-upgrade.php:112 includes/admin/admin-upgrade.php:175
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Mise à niveau de la base de données"

#: includes/admin/admin-upgrade.php:199
msgid "Review sites & upgrade"
msgstr "Examiner les sites et mettre à niveau"

# @ acf
#: includes/admin/admin.php:53 includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr "ACF"

#: includes/admin/admin.php:59
msgid "Info"
msgstr "Informations"

#: includes/admin/admin.php:124
msgid "What's New"
msgstr "Nouveautés"

# @ acf
#: includes/admin/admin.php:125 pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Liste des modifications"

#: includes/admin/admin.php:182
#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:86
msgid "and"
msgstr "et"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Exporter les groupes de champs"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Générer le PHP"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "Aucun groupe de champs sélectionné"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Un groupe de champ a été exporté."
msgstr[1] "%s groupes de champs ont été exportés."

# @ default
#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Sélectionnez les groupes de champs"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Sélectionnez les groupes de champs que vous souhaitez exporter puis "
"choisissez la méthode d’export. Utilisez le bouton « télécharger » pour "
"exporter un fichier JSON que vous pourrez importer dans une autre "
"installation ACF. Utilisez le « générer » pour exporter le code PHP que vous "
"pourrez ajouter à votre thème."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Exporter le fichier"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Le code suivant peut être utilisé pour enregistrer une version locale du/des "
"groupe(s) de champs sélectionné(s). Un groupe de champ local apporte de "
"nombreux bénéfices comme des temps de chargement plus rapide, la gestion de "
"versions, ou des champs/paramètres dynamiques. Copiez-collez le code suivant "
"dans le fichier functions.php de votre thème ou incluez-le depuis un autre "
"fichier."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "Copier dans le presse-papiers"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "Copié"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importer les groupes de champs"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Sélectionnez le fichier JSON ACF que vous souhaitez importer et cliquez sur "
"Importer. ACF importera les groupes de champs."

# @ acf
#: includes/admin/tools/class-acf-admin-tool-import.php:52
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "Sélectionner un fichier"

#: includes/admin/tools/class-acf-admin-tool-import.php:62
msgid "Import File"
msgstr "Importer le fichier"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-import.php:85
#: includes/fields/class-acf-field-file.php:170
msgid "No file selected"
msgstr "Aucun fichier sélectionné"

#: includes/admin/tools/class-acf-admin-tool-import.php:93
msgid "Error uploading file. Please try again"
msgstr "Échec de l’import du fichier. Merci d’essayer à nouveau"

#: includes/admin/tools/class-acf-admin-tool-import.php:98
msgid "Incorrect file type"
msgstr "Type de fichier incorrect"

#: includes/admin/tools/class-acf-admin-tool-import.php:107
msgid "Import file empty"
msgstr "Le fichier à importer est vide"

#: includes/admin/tools/class-acf-admin-tool-import.php:138
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Un groupe de champs importé"
msgstr[1] "%s groupes de champs importés"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Logique conditionnelle"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "Montrer ce champ si"

# @ acf
#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Ajouter une règle"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:410
#: pro/fields/class-acf-field-repeater.php:299
msgid "Drag to reorder"
msgstr "Faites glisser pour réorganiser"

# @ acf
#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "Modifier ce champ"

# @ acf
#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:138
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:337
msgid "Edit"
msgstr "Modifier"

# @ acf
#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "Dupliquer ce champ"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "Déplacer le champ dans un autre groupe"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "Déplacer"

# @ acf
#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "Supprimer ce champ"

# @ acf
#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Delete"
msgstr "Supprimer"

# @ acf
#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "Titre du champ"

# @ acf
#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "Ce nom apparaîtra sur la page d‘édition"

# @ acf
#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "Nom du champ"

# @ acf
#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Un seul mot, sans espace. Les « _ » et « - » sont autorisés"

# @ acf
#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "Type de champ"

# @ acf
#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "Instructions"

# @ acf
#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instructions pour les auteurs. Affichées lors de la saisie du contenu"

# @ acf
#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Requis?"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Attributs du conteneur"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "largeur"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "classe"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id"

# @ acf
#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "Fermer le champ"

# @ acf
#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Ordre"

# @ acf
#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:433
#: pro/fields/class-acf-field-flexible-content.php:582
msgid "Label"
msgstr "Intitulé"

# @ acf
#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:936
#: pro/fields/class-acf-field-flexible-content.php:596
msgid "Name"
msgstr "Nom"

# @ acf
#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Type"

# @ acf
#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Aucun champ. Cliquez sur le bouton <strong>+ Ajouter un champ</strong> pour "
"créer votre premier champ."

# @ acf
#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Ajouter un champ"

# @ acf
#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Règles"

# @ acf
#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Créez une série de règles pour déterminer les écrans sur lesquels ce groupe "
"de champs sera utilisé"

# @ acf
#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Style"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Standard (boîte WP)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Sans contour (directement dans la page)"

# @ acf
#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Position"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Haute (après le titre)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normal (après le contenu)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Sur le côté"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Emplacement de l’intitulé"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Aligné en haut"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Aligné à gauche"

# @ acf
#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Emplacement des instructions"

# @ acf
#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Sous les intitulés"

# @ acf
#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Sous les champs"

# @ acf
#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Ordre"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr ""
"Le groupe de champs qui a l’ordre le plus petit sera affiché en premier"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Affiché dans la liste des groupes de champs"

#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr "Permalien"

#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr "Éditeur de contenu"

#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr "Extrait"

#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr "Discussion"

#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr "Révisions"

#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr "Identifiant (slug)"

#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr "Auteur"

# @ acf
#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr "Attributs de page"

# @ acf
#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:607
msgid "Featured Image"
msgstr "Image à la Une"

#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr "Catégories"

#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr "Mots-clés"

#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr "Envoyer des rétroliens"

#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr "Masquer"

# @ acf
#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Sélectionnez</b> les champs que vous souhaitez <b>masquer</b> sur la page "
"d‘édition."

# @ acf
#: includes/admin/views/field-group-options.php:129
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Si plusieurs groupes ACF sont présents sur une page d‘édition, le groupe "
"portant le numéro le plus bas sera affiché en premier"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Les sites suivants nécessites une mise à niveau de la base de données. "
"Sélectionnez ceux que vous voulez mettre à jour et cliquez sur %s."

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr "Mettre à niveau les sites"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Site"

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Le site requiert une mise à niveau de la base données de %s à %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:76
msgid "Site is up to date"
msgstr "Le site est à jour"

#: includes/admin/views/html-admin-page-upgrade-network.php:93
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Mise à niveau de la base de données effectuée. <a href=\"%s\">Retourner au "
"panneau d’administration du réseau</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:113
msgid "Please select at least one site to upgrade."
msgstr "Merci de sélectionner au moins un site à mettre à niveau."

#: includes/admin/views/html-admin-page-upgrade-network.php:117
#: includes/admin/views/html-notice-upgrade.php:38
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Il est fortement recommandé de faire une sauvegarde de votre base de données "
"avant de continuer. Êtes-vous sûr de vouloir lancer la mise à niveau "
"maintenant?"

#: includes/admin/views/html-admin-page-upgrade-network.php:144
#: includes/admin/views/html-admin-page-upgrade.php:31
#, php-format
msgid "Upgrading data to version %s"
msgstr "Migration des données vers la version %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:158
msgid "Upgrade complete."
msgstr "Mise à niveau terminée."

#: includes/admin/views/html-admin-page-upgrade-network.php:161
#: includes/admin/views/html-admin-page-upgrade.php:65
msgid "Upgrade failed."
msgstr "Mise à niveau échouée."

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Lecture des instructions de mise à niveau…"

#: includes/admin/views/html-admin-page-upgrade.php:33
#, php-format
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Mise à niveau de la base de données terminée. <a href=\"%s\">Consulter les "
"nouveautés</a>"

# @ acf
#: includes/admin/views/html-admin-page-upgrade.php:94
#: includes/ajax/class-acf-ajax-upgrade.php:32
msgid "No updates available."
msgstr "Aucune mise à jour disponible."

#: includes/admin/views/html-admin-tools.php:21
msgid "Back to all tools"
msgstr "Retour aux outils"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Montrer ce groupe si"

# @ acf
#: includes/admin/views/html-notice-upgrade.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Répéteur"

# @ acf
#: includes/admin/views/html-notice-upgrade.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Contenu flexible"

# @ acf
#: includes/admin/views/html-notice-upgrade.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galerie"

# @ acf
#: includes/admin/views/html-notice-upgrade.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Page d‘options"

#: includes/admin/views/html-notice-upgrade.php:21
msgid "Database Upgrade Required"
msgstr "Mise à jour de la base de données nécessaire"

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Merci d’avoir mis à jour %s v%s!"

#: includes/admin/views/html-notice-upgrade.php:22
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Cette version contient des améliorations de la base de données et nécessite "
"une mise à niveau."

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Veuillez également vérifier que tous les modules d’extension premium (%s) "
"soient à jour à leur dernière version disponible."

# @ acf
#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Bienvenue sur Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Merci d’avoir mis à jour! ACF %s est plus performant que jamais. Nous "
"espérons que vous l’apprécierez."

#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr "Une expérience plus fluide"

#: includes/admin/views/settings-info.php:18
msgid "Improved Usability"
msgstr "Convivialité améliorée"

#: includes/admin/views/settings-info.php:19
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"ACF inclue désormais la librairie populaire Select2, qui améliore "
"l’ergonomie et la vitesse sur plusieurs types de champs dont l’objet "
"article, lien vers page, taxonomie, et sélection."

#: includes/admin/views/settings-info.php:22
msgid "Improved Design"
msgstr "Design amélioré"

#: includes/admin/views/settings-info.php:23
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Plusieurs champs ont reçu une refonte graphique pour qu’ACF apparaisse sous "
"son plus beau jour! Les améliorations sont notamment visibles sur la "
"galerie, le champ relationnel et le petit nouveau : oEmbed (champ de contenu "
"embarqué)!"

#: includes/admin/views/settings-info.php:26
msgid "Improved Data"
msgstr "Données améliorées"

#: includes/admin/views/settings-info.php:27
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"L’architecture des données a été complètement revue et permet dorénavant aux "
"sous-champs de vivre indépendamment de leurs parents. Cela permet de "
"déplacer les champs en dehors de leurs parents!"

#: includes/admin/views/settings-info.php:35
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Au revoir modules d’extension. Bonjour ACF Pro"

#: includes/admin/views/settings-info.php:38
msgid "Introducing ACF PRO"
msgstr "Découvrez ACF PRO"

#: includes/admin/views/settings-info.php:39
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""
"Nous avons changé la façon dont les fonctionnalités premium sont délivrées!"

#: includes/admin/views/settings-info.php:40
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Les 4 modules d’extension premium (répéteur, galerie, contenu flexible et "
"pages d’options) ont été combinés en une toute nouvelle <a href=\"%s"
"\">version PRO d’ACF</a>. Avec des licences personnelles et développeur "
"disponibles, les fonctionnalités premium sont encore plus accessibles que "
"jamais!"

#: includes/admin/views/settings-info.php:44
msgid "Powerful Features"
msgstr "Nouvelles fonctionnalités surpuissantes"

#: includes/admin/views/settings-info.php:45
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO contient de nouvelles super fonctionnalités telles que les champs "
"répéteurs, les dispositions flexibles, une superbe galerie et la possibilité "
"de créer des pages d’options!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr ""
"En savoir plus à propos des <a href=\"%s\">fonctionnalités d’ACF PRO</a>."

# @ wp3i
#: includes/admin/views/settings-info.php:50
msgid "Easy Upgrading"
msgstr "Mise à niveau facile"

#: includes/admin/views/settings-info.php:51
msgid ""
"Upgrading to ACF PRO is easy. Simply purchase a license online and download "
"the plugin!"
msgstr ""
"La mise à niveau vers ACF PRO est facile. Achetez simplement une licence en "
"ligne et téléchargez l’extension!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>."
msgstr ""
"Nous avons également écrit un <a href=\"%s\">guide de mise à niveau</a> pour "
"répondre aux questions habituelles, mais si vous avez une question "
"spécifique, veuillez contacter notre équipe de support via le <a href=\"%s"
"\">support technique</a>."

#: includes/admin/views/settings-info.php:61
msgid "New Features"
msgstr "Nouvelles Fonctionnalités"

#: includes/admin/views/settings-info.php:66
msgid "Link Field"
msgstr "Champ Lien"

#: includes/admin/views/settings-info.php:67
msgid ""
"The Link field provides a simple way to select or define a link (url, title, "
"target)."
msgstr ""
"Le champ Lien permet de sélectionner ou définir un lien en toute simplicité "
"(URL, titre, cible)."

#: includes/admin/views/settings-info.php:71
msgid "Group Field"
msgstr "Champ Groupe"

#: includes/admin/views/settings-info.php:72
msgid "The Group field provides a simple way to create a group of fields."
msgstr ""
"Le champ Groupe permet de créer un groupe de champs en toute simplicité."

#: includes/admin/views/settings-info.php:76
msgid "oEmbed Field"
msgstr "Champ de Contenu Embarqué (oEmbed)"

#: includes/admin/views/settings-info.php:77
msgid ""
"The oEmbed field allows an easy way to embed videos, images, tweets, audio, "
"and other content."
msgstr ""
"Le champ oEmbed vous permet d’embarquer des vidéos, des images, des tweets, "
"de l’audio ou encore d’autres média en toute simplicité."

#: includes/admin/views/settings-info.php:81
msgid "Clone Field"
msgstr "Champ Clone"

#: includes/admin/views/settings-info.php:81
msgid "Pro"
msgstr "Pro"

#: includes/admin/views/settings-info.php:82
msgid "The clone field allows you to select and display existing fields."
msgstr ""
"Le champ Clone vous permet de sélectionner et afficher des champs existants."

#: includes/admin/views/settings-info.php:86
msgid "More AJAX"
msgstr "Plus d’AJAX"

#: includes/admin/views/settings-info.php:87
msgid "More fields use AJAX powered search to speed up page loading."
msgstr ""
"Plus de champs utilisent maintenant la recherche via AJAX afin d’améliorer "
"le temps de chargement des pages."

#: includes/admin/views/settings-info.php:92
msgid ""
"New auto export to JSON feature improves speed and allows for syncronisation."
msgstr ""
"La nouvelle fonctionnalité d’export automatique en JSON améliore la rapidité "
"et simplifie la synchronisation."

#: includes/admin/views/settings-info.php:96
msgid "Easy Import / Export"
msgstr "Import / Export Facile"

#: includes/admin/views/settings-info.php:97
msgid "Both import and export can easily be done through a new tools page."
msgstr ""
"Les imports et exports de données d’ACF sont encore plus simples à réaliser "
"via notre nouvelle page d’outils."

#: includes/admin/views/settings-info.php:101
msgid "New Form Locations"
msgstr "Nouveaux Emplacements de Champs"

#: includes/admin/views/settings-info.php:102
msgid ""
"Fields can now be mapped to menus, menu items, comments, widgets and all "
"user forms!"
msgstr ""
"Les champs peuvent désormais être intégrés dans les menus, éléments de menu, "
"commentaires, widgets et tous les formulaires utilisateurs!"

#: includes/admin/views/settings-info.php:106
msgid "More Customization"
msgstr "Encore plus de Personnalisation"

#: includes/admin/views/settings-info.php:107
msgid ""
"New PHP (and JS) actions and filters have been added to allow for more "
"customization."
msgstr ""
"De nouveaux filtres et actions PHP (et JS) ont été ajoutés afin de vous "
"permettre plus de personnalisation."

#: includes/admin/views/settings-info.php:111
msgid "Fresh UI"
msgstr "Interface Améliorée"

#: includes/admin/views/settings-info.php:112
msgid ""
"The entire plugin has had a design refresh including new field types, "
"settings and design!"
msgstr ""
"Toute l’extension a été améliorée et inclut de nouveaux types de champs, "
"réglages ainsi qu’un nouveau design!"

#: includes/admin/views/settings-info.php:116
msgid "New Settings"
msgstr "Nouveaux Paramètres"

#: includes/admin/views/settings-info.php:117
msgid ""
"Field group settings have been added for Active, Label Placement, "
"Instructions Placement and Description."
msgstr ""
"De nouveaux réglages font leur apparition pour Actif, Emplacement du Label, "
"Emplacement des Instructions et Description."

#: includes/admin/views/settings-info.php:121
msgid "Better Front End Forms"
msgstr "De meilleurs formulaires côté public"

#: includes/admin/views/settings-info.php:122
msgid ""
"acf_form() can now create a new post on submission with lots of new settings."
msgstr ""
"acf_form() peut maintenant créer un nouvel article lors de la soumission et "
"propose de nombreux réglages."

#: includes/admin/views/settings-info.php:126
msgid "Better Validation"
msgstr "Meilleure validation"

#: includes/admin/views/settings-info.php:127
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr ""
"La validation des formulaires est maintenant faite via PHP + AJAX au lieu "
"d’être seulement faite en JS."

# @ acf
#: includes/admin/views/settings-info.php:131
msgid "Moving Fields"
msgstr "Champs amovibles"

#: includes/admin/views/settings-info.php:132
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents."
msgstr ""
"La nouvelle fonctionnalité de Groupe de Champ vous permet de déplacer un "
"champ entre différents groupes et parents."

#: includes/admin/views/settings-info.php:143
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Nous pensons que vous allez adorer les nouveautés de la version %s."

#: includes/ajax/class-acf-ajax-local-json-diff.php:34
#: includes/ajax/class-acf-ajax-local-json-diff.php:41
#: includes/ajax/class-acf-ajax-local-json-diff.php:51
msgid "Error."
msgstr "Erreur."

# @ acf
#: includes/ajax/class-acf-ajax-local-json-diff.php:57
#, php-format
msgid "Last updated: %s"
msgstr "Dernière mise à jour : %s"

# @ acf
#: includes/ajax/class-acf-ajax-local-json-diff.php:62
msgid "Current field group"
msgstr "Groupe de champs actuel"

#: includes/ajax/class-acf-ajax-local-json-diff.php:66
msgid "Local JSON field group (newer)"
msgstr "Groupe de champs local JSON (plus récent)"

#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Nonce invalide."

#: includes/api/api-helpers.php:827
msgid "Thumbnail"
msgstr "Miniature"

#: includes/api/api-helpers.php:828
msgid "Medium"
msgstr "Moyen"

#: includes/api/api-helpers.php:829
msgid "Large"
msgstr "Grande"

#: includes/api/api-helpers.php:878
msgid "Full Size"
msgstr "Taille originale"

# @ acf
#: includes/api/api-helpers.php:1599 includes/api/api-term.php:147
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(sans titre)"

#: includes/api/api-helpers.php:3570
#, php-format
msgid "Image width must be at least %dpx."
msgstr "L’image doit mesurer au moins %dpx de largeur."

#: includes/api/api-helpers.php:3575
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "L’image ne doit pas dépasser %dpx de largeur."

#: includes/api/api-helpers.php:3591
#, php-format
msgid "Image height must be at least %dpx."
msgstr "L’image doit mesurer au moins %dpx de hauteur."

#: includes/api/api-helpers.php:3596
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "L’image ne doit pas dépasser %dpx de hauteur."

#: includes/api/api-helpers.php:3614
#, php-format
msgid "File size must be at least %s."
msgstr "Le poids de l’image doit être d’au moins %s."

#: includes/api/api-helpers.php:3619
#, php-format
msgid "File size must must not exceed %s."
msgstr "Le poids de l’image ne doit pas dépasser %s."

# @ acf
#: includes/api/api-helpers.php:3653
#, php-format
msgid "File type must be %s."
msgstr "Le type de fichier doit être %s."

#: includes/assets.php:184
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Les modifications seront perdues si vous quittez cette page"

#: includes/assets.php:187 includes/fields/class-acf-field-select.php:259
msgctxt "verb"
msgid "Select"
msgstr "Choisir"

#: includes/assets.php:188
msgctxt "verb"
msgid "Edit"
msgstr "Modifier"

#: includes/assets.php:189
msgctxt "verb"
msgid "Update"
msgstr "Mettre à jour"

#: includes/assets.php:190
msgid "Uploaded to this post"
msgstr "Lié(s) à cet article"

#: includes/assets.php:191
msgid "Expand Details"
msgstr "Afficher les détails"

#: includes/assets.php:192
msgid "Collapse Details"
msgstr "Masquer les détails"

#: includes/assets.php:193
msgid "Restricted"
msgstr "Limité"

# @ acf
#: includes/assets.php:194 includes/fields/class-acf-field-image.php:66
msgid "All images"
msgstr "Toutes les images"

#: includes/assets.php:197
msgid "Validation successful"
msgstr "Validé avec succès"

#: includes/assets.php:198 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Échec de la validation"

#: includes/assets.php:199
msgid "1 field requires attention"
msgstr "1 champ requiert votre attention"

#: includes/assets.php:200
#, php-format
msgid "%d fields require attention"
msgstr "%d champs requièrent votre attention"

# @ acf
#: includes/assets.php:203
msgid "Are you sure?"
msgstr "Êtes-vous sûr(e)?"

#: includes/assets.php:204 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Oui"

#: includes/assets.php:205 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Non"

# @ acf
#: includes/assets.php:206 includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-image.php:140
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:338
#: pro/fields/class-acf-field-gallery.php:478
msgid "Remove"
msgstr "Enlever"

#: includes/assets.php:207
msgid "Cancel"
msgstr "Annuler"

#: includes/assets.php:210
msgid "Has any value"
msgstr "A n’importe quelle valeur"

#: includes/assets.php:211
msgid "Has no value"
msgstr "N’a pas de valeur"

#: includes/assets.php:212
msgid "Value is equal to"
msgstr "La valeur est égale à"

#: includes/assets.php:213
msgid "Value is not equal to"
msgstr "La valeur est différente de"

#: includes/assets.php:214
msgid "Value matches pattern"
msgstr "La valeur correspond au modèle"

#: includes/assets.php:215
msgid "Value contains"
msgstr "La valeur contient"

#: includes/assets.php:216
msgid "Value is greater than"
msgstr "La valeur est supérieure à"

#: includes/assets.php:217
msgid "Value is less than"
msgstr "La valeur est inférieure à"

#: includes/assets.php:218
msgid "Selection is greater than"
msgstr "La sélection est supérieure à"

#: includes/assets.php:219
msgid "Selection is less than"
msgstr "La sélection est inférieure à"

# @ acf
#: includes/assets.php:222 includes/forms/form-comment.php:166
#: pro/admin/admin-options-page.php:325
msgid "Edit field group"
msgstr "Modifier le groupe de champs"

# @ acf
#: includes/fields.php:308
msgid "Field type does not exist"
msgstr "Ce type de champ n‘existe pas"

#: includes/fields.php:308
msgid "Unknown"
msgstr "Inconnu"

#: includes/fields.php:349
msgid "Basic"
msgstr "Commun"

#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr "Contenu"

# @ acf
#: includes/fields.php:351
msgid "Choice"
msgstr "Choix"

# @ acf
#: includes/fields.php:352
msgid "Relational"
msgstr "Relationnel"

#: includes/fields.php:353
msgid "jQuery"
msgstr "jQuery"

# @ acf
#: includes/fields.php:354 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:553
#: pro/fields/class-acf-field-flexible-content.php:602
#: pro/fields/class-acf-field-repeater.php:448
msgid "Layout"
msgstr "Mise en page"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Accordéon"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Ouvert"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Ouvrir l’accordéon au chargement de la page."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Ouverture multiple"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "Permettre à cet accordéon de s’ouvrir sans refermer les autres."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Extrémité"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Définir comme extrémité de l’accordéon précédent. Cet accordéon ne sera pas "
"visible."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Groupe de boutons"

# @ acf
#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "Choix"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "Indiquez une valeur par ligne."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Pour plus de contrôle, vous pouvez spécifier la valeur et le label de cette "
"manière :"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "rouge : Rouge"

# @ acf
#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:382
#: includes/fields/class-acf-field-taxonomy.php:781
#: includes/fields/class-acf-field-user.php:63
msgid "Allow Null?"
msgstr "Autoriser une valeur vide?"

# @ acf
#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:155
#: includes/fields/class-acf-field-select.php:373
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:381
msgid "Default Value"
msgstr "Valeur par défaut"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:156
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Appears when creating a new post"
msgstr "Valeur donnée lors de la création d’un nouvel article"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "Vertical"

# @ acf
#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:826
msgid "Return Value"
msgstr "Valeur renvoyée"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "Spécifier la valeur retournée dans le code"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:432
msgid "Value"
msgstr "Valeur"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:434
msgid "Both (Array)"
msgstr "Les deux (tableau/Array)"

# @ acf
#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:768
msgid "Checkbox"
msgstr "Case à cocher"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Tout (dé)sélectionner"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Ajouter un choix"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Autoriser des valeurs personnalisées"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Permettre l’ajout de valeurs personnalisées"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Enregistrer les valeurs personnalisées"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "Enregistrer les valeurs personnalisées dans les choix du champs"

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:374
msgid "Enter each default value on a new line"
msgstr "Entrez chaque valeur par défaut sur une nouvelle ligne"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Tout (dé)sélectionner"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Ajouter une case à cocher au début pour tout sélectionner/désélectionner"

# @ acf
#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Sélecteur de couleur"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Effacer"

# @ acf
#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Valeur par défaut"

# @ acf
#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Choisir une couleur"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Couleur actuelle"

# @ acf
#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Sélecteur de date"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Valider"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Aujourd’hui"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Suiv."

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Préc."

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sem."

# @ acf
#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Format d’affichage"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "Format affiché lors de l’édition d’un article"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Personnalisé :"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "Enregistrer le format"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "Le format enregistré"

# @ acf
#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-image.php:204
#: includes/fields/class-acf-field-post_object.php:431
#: includes/fields/class-acf-field-relationship.php:634
#: includes/fields/class-acf-field-select.php:427
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:79
#: pro/fields/class-acf-field-gallery.php:557
msgid "Return Format"
msgstr "Format de la valeur retournée"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "Valeur retournée dans le code"

#: includes/fields/class-acf-field-date_picker.php:227
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "La semaine commence le"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Sélecteur de date et heure"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Choisir l’heure"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Heure"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Heure"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minute"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Seconde"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milliseconde"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microseconde"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fuseau horaire"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Maintenant"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Valider"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Sélectionner"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Adresse courriel"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:104
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Texte indicatif"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:105
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Apparait dans le champ"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:194
#: includes/fields/class-acf-field-text.php:113
msgid "Prepend"
msgstr "Préfixe"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:195
#: includes/fields/class-acf-field-text.php:114
msgid "Appears before the input"
msgstr "Apparait avant le champ"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:203
#: includes/fields/class-acf-field-text.php:122
msgid "Append"
msgstr "Suffixe"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:204
#: includes/fields/class-acf-field-text.php:123
msgid "Appears after the input"
msgstr "Apparait après le champ"

# @ acf
#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Fichier"

# @ acf
#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr "Modifier le fichier"

# @ acf
#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr "Mettre à jour le fichier"

# @ acf
#: includes/fields/class-acf-field-file.php:141
msgid "File name"
msgstr "Nom du fichier"

# @ acf
#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:264
#: includes/fields/class-acf-field-image.php:293
#: pro/fields/class-acf-field-gallery.php:642
#: pro/fields/class-acf-field-gallery.php:671
msgid "File size"
msgstr "Taille du fichier"

# @ acf
#: includes/fields/class-acf-field-file.php:170
msgid "Add File"
msgstr "Ajouter un fichier"

#: includes/fields/class-acf-field-file.php:221
msgid "File Array"
msgstr "Données du fichier (tableau/Array)"

# @ acf
#: includes/fields/class-acf-field-file.php:222
msgid "File URL"
msgstr "URL du fichier"

# @ acf
#: includes/fields/class-acf-field-file.php:223
msgid "File ID"
msgstr "ID du Fichier"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:229
#: pro/fields/class-acf-field-gallery.php:592
msgid "Library"
msgstr "Média"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:230
#: pro/fields/class-acf-field-gallery.php:593
msgid "Limit the media library choice"
msgstr "Limiter le choix dans la médiathèque"

#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:235
#: includes/locations/class-acf-location-attachment.php:71
#: includes/locations/class-acf-location-comment.php:59
#: includes/locations/class-acf-location-nav-menu.php:72
#: includes/locations/class-acf-location-taxonomy.php:61
#: includes/locations/class-acf-location-user-form.php:64
#: includes/locations/class-acf-location-user-role.php:76
#: includes/locations/class-acf-location-widget.php:63
#: pro/fields/class-acf-field-gallery.php:598
#: pro/locations/class-acf-location-block.php:80
msgid "All"
msgstr "Tous"

#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-image.php:236
#: pro/fields/class-acf-field-gallery.php:599
msgid "Uploaded to post"
msgstr "Liés à cet article"

# @ acf
#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:243
#: pro/fields/class-acf-field-gallery.php:621
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:245
#: includes/fields/class-acf-field-file.php:256
msgid "Restrict which files can be uploaded"
msgstr "Restreindre l’import de fichiers"

# @ acf
#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:272
#: pro/fields/class-acf-field-gallery.php:650
msgid "Maximum"
msgstr "Maximum"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:301
#: pro/fields/class-acf-field-gallery.php:678
msgid "Allowed file types"
msgstr "Types de fichiers autorisés"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:302
#: pro/fields/class-acf-field-gallery.php:679
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Extensions autorisées séparées par une virgule. Laissez vide pour autoriser "
"toutes les extensions"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr "Désolé, ce navigateur ne prend pas en charge la géolocalisation"

#: includes/fields/class-acf-field-google-map.php:146
#: includes/fields/class-acf-field-relationship.php:593
msgid "Search"
msgstr "Rechercher"

# @ acf
#: includes/fields/class-acf-field-google-map.php:147
msgid "Clear location"
msgstr "Effacer la position"

#: includes/fields/class-acf-field-google-map.php:148
msgid "Find current location"
msgstr "Trouver l’emplacement actuel"

#: includes/fields/class-acf-field-google-map.php:151
msgid "Search for address..."
msgstr "Chercher une adresse…"

#: includes/fields/class-acf-field-google-map.php:181
#: includes/fields/class-acf-field-google-map.php:192
msgid "Center"
msgstr "Centre"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:193
msgid "Center the initial map"
msgstr "Position initiale du centre de la carte"

#: includes/fields/class-acf-field-google-map.php:204
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:205
msgid "Set the initial zoom level"
msgstr "Niveau de zoom initial"

#: includes/fields/class-acf-field-google-map.php:214
#: includes/fields/class-acf-field-image.php:255
#: includes/fields/class-acf-field-image.php:284
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:633
#: pro/fields/class-acf-field-gallery.php:662
msgid "Height"
msgstr "Hauteur"

#: includes/fields/class-acf-field-google-map.php:215
msgid "Customize the map height"
msgstr "Hauteur de la carte"

# @ acf
#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Groupe"

# @ acf
#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:384
msgid "Sub Fields"
msgstr "Sous-champs"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "Style utilisé pour générer les champs sélectionnés"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:613
#: pro/fields/class-acf-field-repeater.php:456
#: pro/locations/class-acf-location-block.php:27
msgid "Block"
msgstr "Bloc"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:612
#: pro/fields/class-acf-field-repeater.php:455
msgid "Table"
msgstr "Tableau"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:614
#: pro/fields/class-acf-field-repeater.php:457
msgid "Row"
msgstr "Rangée"

# @ acf
#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Image"

# acf
#: includes/fields/class-acf-field-image.php:63
msgid "Select Image"
msgstr "Sélectionner une image"

# @ acf
#: includes/fields/class-acf-field-image.php:64
msgid "Edit Image"
msgstr "Modifier l’image"

# @ acf
#: includes/fields/class-acf-field-image.php:65
msgid "Update Image"
msgstr "Mettre à jour"

# @ acf
#: includes/fields/class-acf-field-image.php:156
msgid "No image selected"
msgstr "Aucune image sélectionnée"

# @ acf
#: includes/fields/class-acf-field-image.php:156
msgid "Add Image"
msgstr "Ajouter une image"

# @ acf
#: includes/fields/class-acf-field-image.php:210
#: pro/fields/class-acf-field-gallery.php:563
msgid "Image Array"
msgstr "Données de l’image (tableau/Array)"

# @ acf
#: includes/fields/class-acf-field-image.php:211
#: pro/fields/class-acf-field-gallery.php:564
msgid "Image URL"
msgstr "URL de l‘image"

# @ acf
#: includes/fields/class-acf-field-image.php:212
#: pro/fields/class-acf-field-gallery.php:565
msgid "Image ID"
msgstr "ID de l‘image"

# @ acf
#: includes/fields/class-acf-field-image.php:219
#: pro/fields/class-acf-field-gallery.php:571
msgid "Preview Size"
msgstr "Taille de prévisualisation"

#: includes/fields/class-acf-field-image.php:244
#: includes/fields/class-acf-field-image.php:273
#: pro/fields/class-acf-field-gallery.php:622
#: pro/fields/class-acf-field-gallery.php:651
msgid "Restrict which images can be uploaded"
msgstr "Restreindre les images envoyées"

#: includes/fields/class-acf-field-image.php:247
#: includes/fields/class-acf-field-image.php:276
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:625
#: pro/fields/class-acf-field-gallery.php:654
msgid "Width"
msgstr "Largeur"

# @ acf
#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Lien"

# @ acf
#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Sélectionner un lien"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Ouvrir dans un nouvel onglet/fenêtre"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Tableau de données (Array)"

# @ acf
#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "URL du Lien"

# @ acf
#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Message"

# @ acf
#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Nouvelles lignes"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Comment sont interprétés les sauts de lignes"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Ajouter des paragraphes automatiquement"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Ajouter &lt;br&gt; automatiquement"

# @ acf
#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Pas de formatage"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Afficher le code HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Permettre l’affichage du code HTML à l’écran au lieu de l’interpréter"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Nombre"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:164
msgid "Minimum Value"
msgstr "Valeur minimum"

# @ acf
#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:174
msgid "Maximum Value"
msgstr "Valeur maximum"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:184
msgid "Step Size"
msgstr "Pas"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "La valeur doit être un nombre"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "La valeur doit être être supérieure ou égale à %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "La valeur doit être inférieure ou égale à %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "Entrez l’URL"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "Dimensions"

# @ acf
#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Lien vers page ou article"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Archives"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:267
#: includes/fields/class-acf-field-taxonomy.php:958
msgid "Parent"
msgstr "Parent"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:383
#: includes/fields/class-acf-field-relationship.php:560
msgid "Filter by Post Type"
msgstr "Filtrer par type de publication"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:568
msgid "All post types"
msgstr "Tous les types de publication"

# @ acf
#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:397
#: includes/fields/class-acf-field-relationship.php:574
msgid "Filter by Taxonomy"
msgstr "Filtrer par taxonomie"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:405
#: includes/fields/class-acf-field-relationship.php:582
msgid "All taxonomies"
msgstr "Toutes les taxonomies"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Afficher les pages d’archives"

# @ acf
#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:392
#: includes/fields/class-acf-field-user.php:71
msgid "Select multiple values?"
msgstr "Autoriser la sélection multiple?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Mot de passe"

# @ acf
#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:436
#: includes/fields/class-acf-field-relationship.php:639
msgid "Post Object"
msgstr "Objet Article"

# @ acf
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:640
msgid "Post ID"
msgstr "ID de l’article"

# @ acf
#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Bouton radio"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Autre"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Ajouter un choix « Autre » pour autoriser des valeurs personnalisées"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Enregistrer les valeurs personnalisées"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Enregistrer les valeurs personnalisées « Autre » en tant que choix"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Plage de valeurs"

# @ acf
#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relation"

#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr "Nombre maximal de valeurs atteint ({max} valeurs)"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr "Chargement en cours"

#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr "Aucun résultat"

#: includes/fields/class-acf-field-relationship.php:411
msgid "Select post type"
msgstr "Choisissez le type de publication"

# @ acf
#: includes/fields/class-acf-field-relationship.php:420
msgid "Select taxonomy"
msgstr "Choisissez la taxonomie"

#: includes/fields/class-acf-field-relationship.php:477
msgid "Search..."
msgstr "Rechercher…"

#: includes/fields/class-acf-field-relationship.php:588
msgid "Filters"
msgstr "Filtres"

# @ acf
#: includes/fields/class-acf-field-relationship.php:594
#: includes/locations/class-acf-location-post-type.php:20
msgid "Post Type"
msgstr "Type de publication"

# @ acf
#: includes/fields/class-acf-field-relationship.php:595
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:751
#: includes/locations/class-acf-location-taxonomy.php:20
msgid "Taxonomy"
msgstr "Taxonomie"

#: includes/fields/class-acf-field-relationship.php:602
msgid "Elements"
msgstr "Éléments"

#: includes/fields/class-acf-field-relationship.php:603
msgid "Selected elements will be displayed in each result"
msgstr "Les éléments sélectionnés seront affichés dans chaque résultat"

# @ acf
#: includes/fields/class-acf-field-relationship.php:614
msgid "Minimum posts"
msgstr "Minimum d’articles sélectionnables"

# @ acf
#: includes/fields/class-acf-field-relationship.php:623
msgid "Maximum posts"
msgstr "Maximum d’articles sélectionnables"

#: includes/fields/class-acf-field-relationship.php:727
#: pro/fields/class-acf-field-gallery.php:779
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s requiert au moins %s sélection"
msgstr[1] "%s requiert au moins %s sélections"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:773
msgctxt "noun"
msgid "Select"
msgstr "Sélection"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Un résultat est disponible, appuyez sur Entrée pour le sélectionner."

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d résultats sont disponibles, utilisez les flèches haut et bas pour "
"naviguer parmi les résultats."

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Aucun résultat trouvé"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Veuillez saisir au minimum 1 caractère"

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Veuillez saisir au minimum %d caractères"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Veuillez retirer 1 caractère"

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Veuillez retirer %d caractères"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Vous ne pouvez sélectionner qu’un seul élément"

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Vous ne pouvez sélectionner que %d éléments"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Chargement de résultats supplémentaires&hellip;"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Recherche en cours&hellip;"

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Échec du chargement"

# @ acf
#: includes/fields/class-acf-field-select.php:402
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Interface stylisée"

#: includes/fields/class-acf-field-select.php:412
msgid "Use AJAX to lazy load choices?"
msgstr "Utiliser AJAX pour charger les choix dynamiquement?"

#: includes/fields/class-acf-field-select.php:428
msgid "Specify the value returned"
msgstr "Définit la valeur retournée"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Séparateur"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Onglet"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Emplacement"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Définit une extrémité pour fermer les précédents onglets. Cela va commencer "
"un nouveau groupe d’onglets."

#: includes/fields/class-acf-field-taxonomy.php:711
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Pas de %s"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:752
msgid "Select the taxonomy to be displayed"
msgstr "Choisissez la taxonomie à afficher"

#: includes/fields/class-acf-field-taxonomy.php:761
msgid "Appearance"
msgstr "Apparence"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:762
msgid "Select the appearance of this field"
msgstr "Apparence de ce champ"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:767
msgid "Multiple Values"
msgstr "Valeurs multiples"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:769
msgid "Multi Select"
msgstr "Sélecteur multiple"

#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Single Value"
msgstr "Valeur unique"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Radio Buttons"
msgstr "Boutons radio"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:796
msgid "Create Terms"
msgstr "Créer des termes"

#: includes/fields/class-acf-field-taxonomy.php:797
msgid "Allow new terms to be created whilst editing"
msgstr "Autoriser la création de nouveaux termes pendant l’édition"

#: includes/fields/class-acf-field-taxonomy.php:806
msgid "Save Terms"
msgstr "Enregistrer les termes"

#: includes/fields/class-acf-field-taxonomy.php:807
msgid "Connect selected terms to the post"
msgstr "Lier les termes sélectionnés à l’article"

#: includes/fields/class-acf-field-taxonomy.php:816
msgid "Load Terms"
msgstr "Charger les termes"

#: includes/fields/class-acf-field-taxonomy.php:817
msgid "Load value from posts terms"
msgstr "Charger une valeur depuis les termes de l’article"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:831
msgid "Term Object"
msgstr "Objet Terme"

#: includes/fields/class-acf-field-taxonomy.php:832
msgid "Term ID"
msgstr "ID du terme"

#: includes/fields/class-acf-field-taxonomy.php:882
#, php-format
msgid "User unable to add new %s"
msgstr "Utilisateur incapable d’ajouter un nouveau %s"

#: includes/fields/class-acf-field-taxonomy.php:892
#, php-format
msgid "%s already exists"
msgstr "%s existe déjà"

#: includes/fields/class-acf-field-taxonomy.php:924
#, php-format
msgid "%s added"
msgstr "%s ajouté"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:970
#: includes/locations/class-acf-location-user-form.php:65
msgid "Add"
msgstr "Ajouter"

# @ acf
#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Texte"

#: includes/fields/class-acf-field-text.php:131
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Limite de caractères"

#: includes/fields/class-acf-field-text.php:132
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Laisser vide ne pas donner de limite"

#: includes/fields/class-acf-field-text.php:157
#: includes/fields/class-acf-field-textarea.php:215
#, php-format
msgid "Value must not exceed %d characters"
msgstr "La valeur ne doit pas dépasser %d caractères"

# @ acf
#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Zone de texte"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Lignes"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Hauteur du champ"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Sélecteur d’heure"

# @ acf
#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Oui / Non"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Affiche le texte à côté de la case à cocher"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Texte côté « Actif »"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Text affiché lorsque le bouton est actif"

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr "Texte côté « Inactif »"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "Texte affiché lorsque le bouton est désactivé"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "URL"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "La valeur doit être une URL valide"

#: includes/fields/class-acf-field-user.php:20 includes/locations-new.php:99
#: includes/locations.php:95
msgid "User"
msgstr "Utilisateur"

#: includes/fields/class-acf-field-user.php:51
msgid "Filter by role"
msgstr "Filtrer par rôle"

#: includes/fields/class-acf-field-user.php:59
msgid "All user roles"
msgstr "Tous les rôles utilisateurs"

#: includes/fields/class-acf-field-user.php:84
msgid "User Array"
msgstr "Tableau Utilisateur (Array)"

#: includes/fields/class-acf-field-user.php:85
msgid "User Object"
msgstr "Objet Utilisateur"

#: includes/fields/class-acf-field-user.php:86
msgid "User ID"
msgstr "ID de l’Utilisateur"

#: includes/fields/class-acf-field-user.php:334
msgid "Error loading field."
msgstr "Échec du chargement du champ."

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Éditeur WYSIWYG"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual"
msgstr "Visuel"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:331
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Texte"

#: includes/fields/class-acf-field-wysiwyg.php:337
msgid "Click to initialize TinyMCE"
msgstr "Cliquez pour initialiser TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Tabs"
msgstr "Onglets"

#: includes/fields/class-acf-field-wysiwyg.php:395
msgid "Visual & Text"
msgstr "Visuel & Texte brut"

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Visual Only"
msgstr "Visuel seulement"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "Text Only"
msgstr "Texte brut seulement"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:404
msgid "Toolbar"
msgstr "Barre d‘outils"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Show Media Upload Buttons?"
msgstr "Afficher le bouton d‘ajout de média?"

#: includes/fields/class-acf-field-wysiwyg.php:429
msgid "Delay initialization?"
msgstr "Retarder l’initialisation?"

#: includes/fields/class-acf-field-wysiwyg.php:430
msgid "TinyMCE will not be initialized until field is clicked"
msgstr ""
"TinyMCE ne sera pas initialisé avant que l’utilisateur clique sur le champ"

#: includes/forms/form-front.php:38 pro/fields/class-acf-field-gallery.php:353
msgid "Title"
msgstr "Titre"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Valider l’adresse courriel"

# @ acf
#: includes/forms/form-front.php:104 pro/fields/class-acf-field-gallery.php:510
#: pro/options-page.php:81
msgid "Update"
msgstr "Mise à jour"

# @ acf
#: includes/forms/form-front.php:105
msgid "Post updated"
msgstr "Article mis à jour"

#: includes/forms/form-front.php:231
msgid "Spam Detected"
msgstr "Pourriel repéré"

#: includes/forms/form-user.php:336
#, php-format
msgid "<strong>ERROR</strong>: %s"
msgstr "<strong>ERREUR</strong> : %s"

# @ acf
#: includes/locations-new.php:23
#, php-format
msgid "Class \"%s\" does not exist."
msgstr "La classe \"%s\" n’existe pas."

#: includes/locations-new.php:34
#, php-format
msgid "Location type \"%s\" is already registered."
msgstr "Le type d’emplacement \"%s\" est déjà enregistré."

# @ acf
#: includes/locations-new.php:97 includes/locations.php:93
#: includes/locations/class-acf-location-post.php:20
msgid "Post"
msgstr "Article"

# @ acf
#: includes/locations-new.php:98 includes/locations.php:94
#: includes/locations/class-acf-location-page.php:20
msgid "Page"
msgstr "Page"

# @ acf
#: includes/locations-new.php:100 includes/locations.php:96
msgid "Forms"
msgstr "Formulaires"

#: includes/locations-new.php:172 includes/locations.php:243
#: includes/locations/abstract-acf-location.php:95
msgid "is equal to"
msgstr "est égal à"

#: includes/locations-new.php:173 includes/locations.php:244
#: includes/locations/abstract-acf-location.php:96
msgid "is not equal to"
msgstr "n‘est pas égal à"

#: includes/locations/class-acf-location-attachment.php:20
msgid "Attachment"
msgstr "Fichier attaché"

#: includes/locations/class-acf-location-attachment.php:82
#, php-format
msgid "All %s formats"
msgstr "Tous les formats %s"

#: includes/locations/class-acf-location-comment.php:20
msgid "Comment"
msgstr "Commentaire"

# @ acf
#: includes/locations/class-acf-location-current-user-role.php:20
msgid "Current User Role"
msgstr "Rôle de l’utilisateur courant"

#: includes/locations/class-acf-location-current-user-role.php:75
msgid "Super Admin"
msgstr "Super Administrateur"

#: includes/locations/class-acf-location-current-user.php:20
msgid "Current User"
msgstr "Utilisateur courant"

#: includes/locations/class-acf-location-current-user.php:69
msgid "Logged in"
msgstr "Connecté"

#: includes/locations/class-acf-location-current-user.php:70
msgid "Viewing front end"
msgstr "Est dans le site"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Viewing back end"
msgstr "Est dans l’interface d’administration"

#: includes/locations/class-acf-location-nav-menu-item.php:20
msgid "Menu Item"
msgstr "Élément de menu"

#: includes/locations/class-acf-location-nav-menu.php:20
msgid "Menu"
msgstr "Menu"

# @ acf
#: includes/locations/class-acf-location-nav-menu.php:78
msgid "Menu Locations"
msgstr "Emplacement de menu"

# @ acf
#: includes/locations/class-acf-location-page-parent.php:20
msgid "Page Parent"
msgstr "Page parente"

#: includes/locations/class-acf-location-page-template.php:20
msgid "Page Template"
msgstr "Modèle de page"

# @ acf
#: includes/locations/class-acf-location-page-template.php:71
#: includes/locations/class-acf-location-post-template.php:83
msgid "Default Template"
msgstr "Modèle de base"

# @ acf
#: includes/locations/class-acf-location-page-type.php:20
msgid "Page Type"
msgstr "Type de page"

#: includes/locations/class-acf-location-page-type.php:105
msgid "Front Page"
msgstr "Page d’accueil"

#: includes/locations/class-acf-location-page-type.php:106
msgid "Posts Page"
msgstr "Page des articles"

#: includes/locations/class-acf-location-page-type.php:107
msgid "Top Level Page (no parent)"
msgstr "Page de haut niveau (sans parent)"

#: includes/locations/class-acf-location-page-type.php:108
msgid "Parent Page (has children)"
msgstr "Page parente (avec page(s) enfant)"

#: includes/locations/class-acf-location-page-type.php:109
msgid "Child Page (has parent)"
msgstr "Page enfant (avec parent)"

#: includes/locations/class-acf-location-post-category.php:20
msgid "Post Category"
msgstr "Catégorie"

# @ acf
#: includes/locations/class-acf-location-post-format.php:20
msgid "Post Format"
msgstr "Format d‘article"

# @ acf
#: includes/locations/class-acf-location-post-status.php:20
msgid "Post Status"
msgstr "Statut de l’article"

# @ acf
#: includes/locations/class-acf-location-post-taxonomy.php:20
msgid "Post Taxonomy"
msgstr "Taxonomie"

#: includes/locations/class-acf-location-post-template.php:20
msgid "Post Template"
msgstr "Modèle d’article"

# @ acf
#: includes/locations/class-acf-location-user-form.php:20
msgid "User Form"
msgstr "Formulaire utilisateur"

#: includes/locations/class-acf-location-user-form.php:66
msgid "Add / Edit"
msgstr "Ajouter / Modifier"

#: includes/locations/class-acf-location-user-form.php:67
msgid "Register"
msgstr "Inscription"

# @ acf
#: includes/locations/class-acf-location-user-role.php:22
msgid "User Role"
msgstr "Rôle utilisateur"

#: includes/locations/class-acf-location-widget.php:20
msgid "Widget"
msgstr "Widget"

# @ default
#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "La valeur %s est requise"

# @ acf
#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:198
msgid "Publish"
msgstr "Publier"

# @ default
#: pro/admin/admin-options-page.php:204
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Aucun groupe de champs trouvé pour cette page d’options. <a href=\"%s"
"\">Créer un groupe de champs</a>"

#: pro/admin/admin-updates.php:49
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Erreur</b>. Impossible de joindre le serveur"

# @ acf
#: pro/admin/admin-updates.php:118 pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Mises à jour"

#: pro/admin/admin-updates.php:191
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Erreur</b>. Impossible d’authentifier la mise à jour. Merci d’essayer à "
"nouveau et si le problème persiste, désactivez et réactivez votre licence "
"ACF PRO."

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Désactiver la licence"

# @ acf
#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Activer votre licence"

# @ acf
#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Informations sur la licence"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Pour débloquer les mises à jour, veuillez entrer votre clé de licence ci-"
"dessous. Si vous n’en avez pas, rendez-vous sur nos <a href=\"%s\" target="
"\"_blank\">détails & tarifs</a>."

# @ acf
#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Code de licence"

# @ acf
#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informations concernant les mises à jour"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Version installée"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Version disponible"

# @ acf
#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Mise à jour disponible"

# @ acf
#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Mettre à jour l’extension"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Entrez votre clé de licence ci-dessus pour activer les mises à jour"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Vérifier à nouveau"

# @ wp3i
#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Informations de mise à niveau"

# @ default
#: pro/blocks.php:36
msgid "Block type name is required."
msgstr "Le nom de type de bloc est requis."

#: pro/blocks.php:43
#, php-format
msgid "Block type \"%s\" is already registered."
msgstr "Le type de bloc \"%s\" est déjà enregistré."

#: pro/blocks.php:393
msgid "Switch to Edit"
msgstr "Passer en Édition"

#: pro/blocks.php:394
msgid "Switch to Preview"
msgstr "Passer en Prévisualisation"

#: pro/blocks.php:397
#, php-format
msgid "%s settings"
msgstr "Réglages de %s"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clone"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Sélectionnez un ou plusieurs champs à cloner"

# @ acf
#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Format d’affichage"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Définit le style utilisé pour générer le champ dupliqué"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Groupe (affiche les champs sélectionnés dans un groupe à l’intérieur de ce "
"champ)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Remplace ce champ par les champs sélectionnés"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Les labels seront affichés en tant que %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Préfixer les labels de champs"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Les valeurs seront enregistrées en tant que %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Préfixer les noms de champs"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Champ inconnu"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Groupe de champ inconnu"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Tous les champs du groupe %s"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:468
msgid "Add Row"
msgstr "Ajouter un élément"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:924
#: pro/fields/class-acf-field-flexible-content.php:1006
msgid "layout"
msgid_plural "layouts"
msgstr[0] "mise-en-forme"
msgstr[1] "mises-en-forme"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "mises-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:923
#: pro/fields/class-acf-field-flexible-content.php:1005
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Ce champ requiert au moins {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Ce champ a une limite de {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponible (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} requis (min {min})"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "Le contenu flexible nécessite au moins une mise-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:287
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""
"Cliquez sur le bouton « %s » ci-dessous pour créer votre première mise-en-"
"forme"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Ajouter une mise-en-forme"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Remove layout"
msgstr "Retirer la mise-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:415
#: pro/fields/class-acf-field-repeater.php:301
msgid "Click to toggle"
msgstr "Cliquer pour intervertir"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder Layout"
msgstr "Réorganiser la mise-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Reorder"
msgstr "Réorganiser"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Delete Layout"
msgstr "Supprimer la mise-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Duplicate Layout"
msgstr "Dupliquer la mise-en-forme"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Add New Layout"
msgstr "Ajouter une nouvelle mise-en-forme"

#: pro/fields/class-acf-field-flexible-content.php:629
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:642
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:669
#: pro/fields/class-acf-field-repeater.php:464
msgid "Button Label"
msgstr "Intitulé du bouton"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:678
msgid "Minimum Layouts"
msgstr "Nombre minimum de mises-en-forme"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:687
msgid "Maximum Layouts"
msgstr "Nombre maximum de mises-en-forme"

# @ acf
#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "Ajouter l’image à la galerie"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "Nombre de sélections maximales atteint"

#: pro/fields/class-acf-field-gallery.php:322
msgid "Length"
msgstr "Longueur"

#: pro/fields/class-acf-field-gallery.php:362
msgid "Caption"
msgstr "Légende"

#: pro/fields/class-acf-field-gallery.php:371
msgid "Alt Text"
msgstr "Texte alternatif"

#: pro/fields/class-acf-field-gallery.php:487
msgid "Add to gallery"
msgstr "Ajouter à la galerie"

# @ acf
#: pro/fields/class-acf-field-gallery.php:491
msgid "Bulk actions"
msgstr "Actions de groupe"

#: pro/fields/class-acf-field-gallery.php:492
msgid "Sort by date uploaded"
msgstr "Ordonner par date d’import"

#: pro/fields/class-acf-field-gallery.php:493
msgid "Sort by date modified"
msgstr "Ranger par date de modification"

# @ acf
#: pro/fields/class-acf-field-gallery.php:494
msgid "Sort by title"
msgstr "Ranger par titre"

#: pro/fields/class-acf-field-gallery.php:495
msgid "Reverse current order"
msgstr "Inverser l’ordre actuel"

# @ acf
#: pro/fields/class-acf-field-gallery.php:507
msgid "Close"
msgstr "Fermer"

#: pro/fields/class-acf-field-gallery.php:580
msgid "Insert"
msgstr "Insérer"

#: pro/fields/class-acf-field-gallery.php:581
msgid "Specify where new attachments are added"
msgstr "Définir où les nouveaux fichiers attachés sont ajoutés"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Append to the end"
msgstr "Ajouter à la fin"

#: pro/fields/class-acf-field-gallery.php:586
msgid "Prepend to the beginning"
msgstr "Insérer au début"

# @ acf
#: pro/fields/class-acf-field-gallery.php:605
msgid "Minimum Selection"
msgstr "Nombre minimum"

# @ acf
#: pro/fields/class-acf-field-gallery.php:613
msgid "Maximum Selection"
msgstr "Nombre maximum"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:661
msgid "Minimum rows reached ({min} rows)"
msgstr "Nombre minimum d’éléments atteint ({min} éléments)"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "Nombre maximum d’éléments atteint ({max} éléments)"

# @ acf
#: pro/fields/class-acf-field-repeater.php:338
msgid "Add row"
msgstr "Ajouter un élément"

# @ acf
#: pro/fields/class-acf-field-repeater.php:339
msgid "Remove row"
msgstr "Retirer l’élément"

#: pro/fields/class-acf-field-repeater.php:417
msgid "Collapsed"
msgstr "Replié"

#: pro/fields/class-acf-field-repeater.php:418
msgid "Select a sub field to show when row is collapsed"
msgstr "Choisir un sous champ à afficher lorsque l’élément est replié"

# @ acf
#: pro/fields/class-acf-field-repeater.php:428
msgid "Minimum Rows"
msgstr "Nombre minimum d’éléments"

# @ acf
#: pro/fields/class-acf-field-repeater.php:438
msgid "Maximum Rows"
msgstr "Nombre maximum d’éléments"

#: pro/locations/class-acf-location-options-page.php:80
msgid "No options pages exist"
msgstr "Aucune page d’option n’existe"

# @ acf
#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Options mises à jours"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Pour activer les mises à jour, veuillez entrer votre clé de licence sur la "
"page <a href=\"%s\">Mises à jour</a>. Si vous n’en avez pas, rendez-vous sur "
"nos <a href=\"%s\">détails & tarifs</a>."

# @ acf
#: tests/basic/test-blocks.php:114
msgid "Normal"
msgstr "Normal"

#: tests/basic/test-blocks.php:115
msgid "Fancy"
msgstr "Élaboré"

#. Plugin URI of the plugin/theme
#. Author URI of the plugin/theme
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Description of the plugin/theme
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Personnalisez WordPress avec des champs intuitifs, puissants et "
"professionnels."

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"
