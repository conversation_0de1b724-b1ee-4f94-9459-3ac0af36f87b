msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro\n"
"Report-Msgid-Bugs-To: https://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2022-08-03 11:47+0000\n"
"PO-Revision-Date: 2022-08-03 13:03+0100\n"
"Last-Translator: Delicious Brains <<EMAIL>>\n"
"Language-Team: Français\n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.1.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Loco-Version: 2.6.2; wp-6.0\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

# @ acf
#: pro/acf-pro.php:27
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/blocks.php:166
msgid "Block type name is required."
msgstr "Le nom du type de bloc est obligatoire."

#: pro/blocks.php:173
msgid "Block type \"%s\" is already registered."
msgstr "Le type de bloc \"%s\" est déjà déclaré."

#: pro/blocks.php:731
msgid "Switch to Edit"
msgstr "Passer en mode Édition"

#: pro/blocks.php:732
msgid "Switch to Preview"
msgstr "Passer en mode Aperçu"

#: pro/blocks.php:733
msgid "Change content alignment"
msgstr "Modifier l’alignement du contenu"

#. translators: %s: Block type title
#: pro/blocks.php:736
msgid "%s settings"
msgstr "Réglages de %s "

#: pro/blocks.php:949
msgid "This block contains no editable fields."
msgstr "Ce bloc ne contient aucun champ éditable."

#. translators: %s: an admin URL to the field group edit screen
#: pro/blocks.php:955
msgid ""
"Assign a <a href=\"%s\" target=\"_blank\">field group</a> to add fields to "
"this block."
msgstr "Assignez un <a href=\"%s\" target=\"_blank\">groupe de champs</a> pour ajouter des champs à "
"ce bloc."

# @ acf
#: pro/options-page.php:47
msgid "Options"
msgstr "Options"

# @ acf
#: pro/options-page.php:77, pro/fields/class-acf-field-gallery.php:523
msgid "Update"
msgstr "Mise à jour"

# @ acf
#: pro/options-page.php:78
msgid "Options Updated"
msgstr "Options mises à jour"

#: pro/updates.php:99
msgid ""
"To enable updates, please enter your license key on the <a href=\"%1$s"
"\">Updates</a> page. If you don't have a licence key, please see <a href="
"\"%2$s\" target=\"_blank\">details & pricing</a>."
msgstr ""
"Pour activer les mises à jour, veuillez indiquer votre clé de licence sur la "
"page <a href=\"%1$s\">Mises à jour</a>. Si vous n’en possédez pas encore "
"une, jetez un oeil à nos <a href=\"%2$s\" target=\"_blank\">détails & "
"tarifs</a>."

#: pro/updates.php:159
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when deactivating your old licence"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Votre clé de licence a été modifiée, mais "
"une erreur est survenue lors de la désactivation de votre précédente licence"

#: pro/updates.php:154
msgid ""
"<b>ACF Activation Error</b>. Your defined license key has changed, but an "
"error occurred when connecting to activation server"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Votre clé de licence définie a été "
"modifiée, mais une erreur est survenue lors de la connexion au serveur "
"d’activation"

#: pro/updates.php:192
msgid "<b>ACF Activation Error</b>"
msgstr "<b>Erreur d’activation d’ACF</b>"

#: pro/updates.php:187
msgid ""
"<b>ACF Activation Error</b>. An error occurred when connecting to activation "
"server"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Une erreur est survenue lors de la "
"connexion au serveur d’activation"

#: pro/updates.php:279, pro/admin/views/html-settings-updates.php:117
msgid "Check Again"
msgstr "Vérifier à nouveau"

#: pro/updates.php:561
msgid "<b>ACF Activation Error</b>. Could not connect to activation server"
msgstr ""
"<b>Erreur d’activation d’ACF</b>. Impossible de se connecter au serveur "
"d’activation"

#: pro/admin/admin-options-page.php:195
msgid "Publish"
msgstr "Publier"

# @ default
#: pro/admin/admin-options-page.php:199
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Aucun groupe de champs trouvé pour cette page options. <a href=\"%s\">Créer "
"un groupe de champs</a>"

# @ acf
#: pro/admin/admin-options-page.php:309
msgid "Edit field group"
msgstr "Modifier le groupe de champs"

#: pro/admin/admin-updates.php:52
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Erreur</b>. Impossible de joindre le serveur"

# @ acf
#: pro/admin/admin-updates.php:122,
#: pro/admin/views/html-settings-updates.php:12
msgid "Updates"
msgstr "Mises à jour"

#: pro/admin/admin-updates.php:209
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Erreur</b>. Impossible d’authentifier la mise à jour. Merci d’essayer à "
"nouveau et si le problème persiste, désactivez et réactivez votre licence "
"ACF PRO."

#: pro/admin/admin-updates.php:196
msgid ""
"<b>Error</b>. Your license for this site has expired or been deactivated. "
"Please reactivate your ACF PRO license."
msgstr ""
"<b>Erreur</b>. La licence pour ce site a expiré ou a été désactivée. "
"Veuillez réactiver votre licence ACF PRO."

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clone"

# @ acf
#: pro/fields/class-acf-field-clone.php:814
msgid "Fields"
msgstr "Champs"

#: pro/fields/class-acf-field-clone.php:815
msgid "Select one or more fields you wish to clone"
msgstr "Sélectionnez un ou plusieurs champs à cloner"

# @ acf
#: pro/fields/class-acf-field-clone.php:834
msgid "Display"
msgstr "Format d’affichage"

#: pro/fields/class-acf-field-clone.php:835
msgid "Specify the style used to render the clone field"
msgstr "Définit le style utilisé pour générer le champ dupliqué"

#: pro/fields/class-acf-field-clone.php:840
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Groupe (affiche les champs sélectionnés dans un groupe à l’intérieur de ce "
"champ)"

#: pro/fields/class-acf-field-clone.php:841
msgid "Seamless (replaces this field with selected fields)"
msgstr "Remplace ce champ par les champs sélectionnés"

# @ acf
#: pro/fields/class-acf-field-clone.php:850,
#: pro/fields/class-acf-field-flexible-content.php:549,
#: pro/fields/class-acf-field-flexible-content.php:604,
#: pro/fields/class-acf-field-repeater.php:211
msgid "Layout"
msgstr "Disposition"

#: pro/fields/class-acf-field-clone.php:851
msgid "Specify the style used to render the selected fields"
msgstr "Définit le style utilisé pour générer les champs sélectionnés"

#: pro/fields/class-acf-field-clone.php:856,
#: pro/fields/class-acf-field-flexible-content.php:617,
#: pro/fields/class-acf-field-repeater.php:219,
#: pro/locations/class-acf-location-block.php:22
msgid "Block"
msgstr "Bloc"

#: pro/fields/class-acf-field-clone.php:857,
#: pro/fields/class-acf-field-flexible-content.php:616,
#: pro/fields/class-acf-field-repeater.php:218
msgid "Table"
msgstr "Tableau"

#: pro/fields/class-acf-field-clone.php:858,
#: pro/fields/class-acf-field-flexible-content.php:618,
#: pro/fields/class-acf-field-repeater.php:220
msgid "Row"
msgstr "Rangée"

#: pro/fields/class-acf-field-clone.php:864
msgid "Labels will be displayed as %s"
msgstr "Les libellés seront affichés en tant que %s"

#: pro/fields/class-acf-field-clone.php:869
msgid "Prefix Field Labels"
msgstr "Préfixer les libellés de champs"

#: pro/fields/class-acf-field-clone.php:880
msgid "Values will be saved as %s"
msgstr "Les valeurs seront enregistrées en tant que %s"

#: pro/fields/class-acf-field-clone.php:885
msgid "Prefix Field Names"
msgstr "Préfixer les noms de champs"

#: pro/fields/class-acf-field-clone.php:1001
msgid "Unknown field"
msgstr "Champ inconnu"

# @ acf
#: pro/fields/class-acf-field-clone.php:1005
msgid "(no title)"
msgstr "(aucun titre)"

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field group"
msgstr "Groupe de champ inconnu"

#: pro/fields/class-acf-field-clone.php:1042
msgid "All fields from %s field group"
msgstr "Tous les champs du groupe %s"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Contenu flexible"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:31,
#: pro/fields/class-acf-field-repeater.php:79,
#: pro/fields/class-acf-field-repeater.php:263
msgid "Add Row"
msgstr "Ajouter un élément"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:71,
#: pro/fields/class-acf-field-flexible-content.php:917,
#: pro/fields/class-acf-field-flexible-content.php:996
msgid "layout"
msgid_plural "layouts"
msgstr[0] "disposition"
msgstr[1] "dispositions"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:72
msgid "layouts"
msgstr "dispositions"

#: pro/fields/class-acf-field-flexible-content.php:75,
#: pro/fields/class-acf-field-flexible-content.php:916,
#: pro/fields/class-acf-field-flexible-content.php:995
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Ce champ requiert au moins {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:76
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Ce champ a une limite de {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:79
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponible (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:80
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} required (min {min})"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:83
msgid "Flexible Content requires at least 1 layout"
msgstr "Le contenu flexible nécessite au moins une disposition"

#: pro/fields/class-acf-field-flexible-content.php:276
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""
"Cliquez sur le bouton \"%s\" ci-dessous pour créer votre première disposition"

#: pro/fields/class-acf-field-flexible-content.php:410,
#: pro/fields/class-acf-repeater-table.php:354
msgid "Drag to reorder"
msgstr "Glisser pour réorganiser"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:413
msgid "Add layout"
msgstr "Ajouter une disposition"

#: pro/fields/class-acf-field-flexible-content.php:414
msgid "Duplicate layout"
msgstr "Dupliquer la disposition"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:415
msgid "Remove layout"
msgstr "Retirer la disposition"

#: pro/fields/class-acf-field-flexible-content.php:416,
#: pro/fields/class-acf-repeater-table.php:369
msgid "Click to toggle"
msgstr "Cliquer pour afficher/cacher"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder Layout"
msgstr "Réorganiser la disposition"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Reorder"
msgstr "Réorganiser"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:552
msgid "Delete Layout"
msgstr "Supprimer la disposition"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:552
msgid "Delete"
msgstr "Supprimer"

#: pro/fields/class-acf-field-flexible-content.php:553
msgid "Duplicate Layout"
msgstr "Dupliquer la disposition"

#: pro/fields/class-acf-field-flexible-content.php:553
msgid "Duplicate"
msgstr "Dupliquer"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New Layout"
msgstr "Ajouter une disposition"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Add New"
msgstr "Ajouter"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:581
msgid "Label"
msgstr "Intitulé"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:597
msgid "Name"
msgstr "Nom"

#: pro/fields/class-acf-field-flexible-content.php:635
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:650
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:679,
#: pro/fields/class-acf-field-repeater.php:259
msgid "Button Label"
msgstr "Intitulé du bouton"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:690
msgid "Minimum Layouts"
msgstr "Nombre minimum de dispositions"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "Nombre maximum de dispositions"

#: pro/fields/class-acf-field-flexible-content.php:1704,
#: pro/fields/class-acf-field-repeater.php:861
msgid "%s must be of type array or null."
msgstr "la valeur de %s doit être un tableau ou null."

#: pro/fields/class-acf-field-flexible-content.php:1715
msgid "%1$s must contain at least %2$s %3$s layout."
msgid_plural "%1$s must contain at least %2$s %3$s layouts."
msgstr[0] "Le champ %1$s doit contenir au moins %2$s %3$s disposition."
msgstr[1] "Le champ %1$s doit contenir au moins %2$s %3$s dispositions."

#: pro/fields/class-acf-field-flexible-content.php:1731
msgid "%1$s must contain at most %2$s %3$s layout."
msgid_plural "%1$s must contain at most %2$s %3$s layouts."
msgstr[0] "Le champ %1$s doit contenir au maximum %2$s %3$s disposition."
msgstr[1] "Le champ %1$s doit contenir au maximum %2$s %3$s dispositions."

# @ acf
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galerie"

# @ acf
#: pro/fields/class-acf-field-gallery.php:73
msgid "Add Image to Gallery"
msgstr "Ajouter l’image à la galerie"

#: pro/fields/class-acf-field-gallery.php:74
msgid "Maximum selection reached"
msgstr "Nombre de sélections maximales atteint"

#: pro/fields/class-acf-field-gallery.php:320
msgid "Length"
msgstr "Longueur"

# @ acf
#: pro/fields/class-acf-field-gallery.php:335
msgid "Edit"
msgstr "Modifier"

# @ acf
#: pro/fields/class-acf-field-gallery.php:336,
#: pro/fields/class-acf-field-gallery.php:491
msgid "Remove"
msgstr "Enlever"

#: pro/fields/class-acf-field-gallery.php:352
msgid "Title"
msgstr "Titre"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Légende"

#: pro/fields/class-acf-field-gallery.php:376
msgid "Alt Text"
msgstr "Texte alternatif"

# @ acf
#: pro/fields/class-acf-field-gallery.php:388
msgid "Description"
msgstr "Description"

#: pro/fields/class-acf-field-gallery.php:500
msgid "Add to gallery"
msgstr "Ajouter à la galerie"

# @ acf
#: pro/fields/class-acf-field-gallery.php:504
msgid "Bulk actions"
msgstr "Actions de groupe"

#: pro/fields/class-acf-field-gallery.php:505
msgid "Sort by date uploaded"
msgstr "Ranger par date d’import"

#: pro/fields/class-acf-field-gallery.php:506
msgid "Sort by date modified"
msgstr "Ranger par date de modification"

# @ acf
#: pro/fields/class-acf-field-gallery.php:507
msgid "Sort by title"
msgstr "Ranger par titre"

#: pro/fields/class-acf-field-gallery.php:508
msgid "Reverse current order"
msgstr "Inverser l’ordre actuel"

# @ acf
#: pro/fields/class-acf-field-gallery.php:520
msgid "Close"
msgstr "Appliquer"

# @ acf
#: pro/fields/class-acf-field-gallery.php:573
msgid "Return Format"
msgstr "Format dans le modèle"

# @ acf
#: pro/fields/class-acf-field-gallery.php:579
msgid "Image Array"
msgstr "Données de l’image (tableau)"

# @ acf
#: pro/fields/class-acf-field-gallery.php:580
msgid "Image URL"
msgstr "URL de l'image"

# @ acf
#: pro/fields/class-acf-field-gallery.php:581
msgid "Image ID"
msgstr "ID de l'image"

# @ acf
#: pro/fields/class-acf-field-gallery.php:590
msgid "Preview Size"
msgstr "Taille de prévisualisation"

#: pro/fields/class-acf-field-gallery.php:602
msgid "Insert"
msgstr "Insérer"

#: pro/fields/class-acf-field-gallery.php:603
msgid "Specify where new attachments are added"
msgstr "Définir comment les images sont insérées"

#: pro/fields/class-acf-field-gallery.php:607
msgid "Append to the end"
msgstr "Insérer à la fin"

#: pro/fields/class-acf-field-gallery.php:608
msgid "Prepend to the beginning"
msgstr "Insérer au début"

#: pro/fields/class-acf-field-gallery.php:617
msgid "Library"
msgstr "Médias"

#: pro/fields/class-acf-field-gallery.php:618
msgid "Limit the media library choice"
msgstr "Limiter le choix de la médiathèque"

#: pro/fields/class-acf-field-gallery.php:623,
#: pro/locations/class-acf-location-block.php:66
msgid "All"
msgstr "Tous"

#: pro/fields/class-acf-field-gallery.php:624
msgid "Uploaded to post"
msgstr "Liés à cette publication"

# @ acf
#: pro/fields/class-acf-field-gallery.php:633
msgid "Minimum Selection"
msgstr "Minimum d’images"

# @ acf
#: pro/fields/class-acf-field-gallery.php:644
msgid "Maximum Selection"
msgstr "Maximum d’images"

# @ acf
#: pro/fields/class-acf-field-gallery.php:655
msgid "Minimum"
msgstr "Minimum"

#: pro/fields/class-acf-field-gallery.php:656,
#: pro/fields/class-acf-field-gallery.php:693
msgid "Restrict which images can be uploaded"
msgstr "Restreindre les images téléversées"

#: pro/fields/class-acf-field-gallery.php:659,
#: pro/fields/class-acf-field-gallery.php:696
msgid "Width"
msgstr "Largeur"

#: pro/fields/class-acf-field-gallery.php:670,
#: pro/fields/class-acf-field-gallery.php:707
msgid "Height"
msgstr "Hauteur"

# @ acf
#: pro/fields/class-acf-field-gallery.php:682,
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr "Poids du fichier"

# @ acf
#: pro/fields/class-acf-field-gallery.php:692
msgid "Maximum"
msgstr "Maximum"

#: pro/fields/class-acf-field-gallery.php:729
msgid "Allowed file types"
msgstr "Types de fichiers autorisés"

#: pro/fields/class-acf-field-gallery.php:730
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Listez les extensions autorisées en les séparant par une virgule. Laissez "
"vide pour autoriser toutes les extensions"

#: pro/fields/class-acf-field-gallery.php:832
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "Vous devez sélectionner au moins %2$s élément pour le champ %1$s"
msgstr[1] "Vous devez sélectionner au moins %2$s éléments pour le champ %1$s"

# @ acf
#: pro/fields/class-acf-field-repeater.php:22
msgid "Repeater"
msgstr "Répéteur"

#: pro/fields/class-acf-field-repeater.php:53,
#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum rows reached ({min} rows)"
msgstr "Nombre minimal d’éléments atteint ({min} éléments)"

#: pro/fields/class-acf-field-repeater.php:54
msgid "Maximum rows reached ({max} rows)"
msgstr "Nombre maximal d’éléments atteint ({max} éléments)"

#: pro/fields/class-acf-field-repeater.php:55
msgid "Error loading page"
msgstr "Erreur de chargement de la page"

# @ acf
#: pro/fields/class-acf-field-repeater.php:141
msgid "Sub Fields"
msgstr "Sous-champs"

#: pro/fields/class-acf-field-repeater.php:174
msgid "Collapsed"
msgstr "Replié"

#: pro/fields/class-acf-field-repeater.php:175
msgid "Select a sub field to show when row is collapsed"
msgstr "Choisir un sous champ à montrer lorsque la ligne est refermée"

# @ acf
#: pro/fields/class-acf-field-repeater.php:187
msgid "Minimum Rows"
msgstr "Nombre minimum d’éléments"

# @ acf
#: pro/fields/class-acf-field-repeater.php:199
msgid "Maximum Rows"
msgstr "Nombre maximum d’éléments"

#: pro/fields/class-acf-field-repeater.php:228
msgid "Pagination"
msgstr "Pagination"

#: pro/fields/class-acf-field-repeater.php:229
msgid "Useful for fields with a large number of rows."
msgstr "Utile pour les champs avec un grand nombre de lignes."

#: pro/fields/class-acf-field-repeater.php:240
msgid "Rows Per Page"
msgstr "Lignes par Page"

#: pro/fields/class-acf-field-repeater.php:241
msgid "Set the number of rows to be displayed on a page."
msgstr "Définir le nombre de lignes à afficher sur une page."

#: pro/fields/class-acf-field-repeater.php:945
msgid "Invalid nonce."
msgstr "Nonce invalide."

#: pro/fields/class-acf-field-repeater.php:959
msgid "Invalid field key."
msgstr "Clé de champ invalide"

#: pro/fields/class-acf-field-repeater.php:968
msgid "There was an error retrieving the field."
msgstr "Il y a une erreur lors de la récupération du champ."

# @ acf
#: pro/fields/class-acf-repeater-table.php:389
msgid "Add row"
msgstr "Ajouter un élément"

#: pro/fields/class-acf-repeater-table.php:390
msgid "Duplicate row"
msgstr "Dupliquer la ligne"

# @ acf
#: pro/fields/class-acf-repeater-table.php:391
msgid "Remove row"
msgstr "Retirer l’élément"

#: pro/fields/class-acf-repeater-table.php:435,
#: pro/fields/class-acf-repeater-table.php:452
msgid "Current Page"
msgstr "Page actuelle"

#: pro/fields/class-acf-repeater-table.php:444
msgid "First page"
msgstr "Première page"

#: pro/fields/class-acf-repeater-table.php:448
msgid "Previous page"
msgstr "Page précédente"

#. translators: 1: Current page, 2: Total pages.
#: pro/fields/class-acf-repeater-table.php:457
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr "%1$s sur %2$s"

#: pro/fields/class-acf-repeater-table.php:465
msgid "Next page"
msgstr "Page suivante"

#: pro/fields/class-acf-repeater-table.php:469
msgid "Last page"
msgstr "Dernière page"

#: pro/locations/class-acf-location-block.php:71
msgid "No block types exist"
msgstr "Aucun type de blocs existant"

# @ acf
#: pro/locations/class-acf-location-options-page.php:22
msgid "Options Page"
msgstr "Page d'options"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Aucune page d’option créée"

#: pro/admin/views/html-settings-updates.php:6
msgid "Deactivate License"
msgstr "Désactiver la licence"

# @ acf
#: pro/admin/views/html-settings-updates.php:6
msgid "Activate License"
msgstr "Activer votre licence"

# @ acf
#: pro/admin/views/html-settings-updates.php:16
msgid "License Information"
msgstr "Informations sur la licence"

#: pro/admin/views/html-settings-updates.php:34
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Pour débloquer les mises à jour, veuillez entrer votre clé de licence ci-"
"dessous. Si vous n’en possédez pas encore une, jetez un oeil à nos <a href="
"\"%s\" target=\"_blank\">détails & tarifs</a>."

# @ acf
#: pro/admin/views/html-settings-updates.php:41
msgid "License Key"
msgstr "Clé de licence"

#: pro/admin/views/html-settings-updates.php:22
msgid "Your license key is defined in wp-config.php."
msgstr "Votre clé de licence est définie dans le fichier wp-config.php"

#: pro/admin/views/html-settings-updates.php:29
msgid "Retry Activation"
msgstr "Retenter l’activation"

# @ acf
#: pro/admin/views/html-settings-updates.php:76
msgid "Update Information"
msgstr "Informations de mise à jour"

#: pro/admin/views/html-settings-updates.php:83
msgid "Current Version"
msgstr "Version actuelle"

#: pro/admin/views/html-settings-updates.php:91
msgid "Latest Version"
msgstr "Dernière version"

# @ acf
#: pro/admin/views/html-settings-updates.php:99
msgid "Update Available"
msgstr "Mise à jour disponible"

#: pro/admin/views/html-settings-updates.php:116
msgid "No"
msgstr "Non"

#: pro/admin/views/html-settings-updates.php:104
msgid "Yes"
msgstr "Oui"

#: pro/admin/views/html-settings-updates.php:111
msgid "Please enter your license key above to unlock updates"
msgstr "Entrez votre clé de licence ci-dessous pour activer les mises à jour"

# @ acf
#: pro/admin/views/html-settings-updates.php:109
msgid "Update Plugin"
msgstr "Mettre à jour l’extension"

#: pro/admin/views/html-settings-updates.php:107
msgid "Please reactivate your license to unlock updates"
msgstr "Veuillez réactiver votre licence afin de débloquer les mises à jour"

# @ acf
#: pro/admin/views/html-settings-updates.php:124
msgid "Changelog"
msgstr "Améliorations"

# @ wp3i
#: pro/admin/views/html-settings-updates.php:134
msgid "Upgrade Notice"
msgstr "Améliorations"

#~ msgid "%1$s is not one of %2$s"
#~ msgstr "%1$s ne correspond pas à %2$s"

#~ msgid "%1$s must be of post type %2$s."
#~ msgid_plural "%1$s must be of one of the following post types: %2$s"
#~ msgstr[0] ""
#~ "Le type de publication sélectionné dans le champ %1$s doit impérativement "
#~ "être %2$s."
#~ msgstr[1] ""
#~ "Le type de publication sélectionné dans le champ %1$s doit correspondre à "
#~ "l’une des valeurs suivantes : %2$s"

#~ msgid "%1$s must have a user with the %2$s role."
#~ msgid_plural "%1$s must have a user with one of the following roles: %2$s"
#~ msgstr[0] ""
#~ "Le rôle de l’utilisateur sélectionné dans le champ %1$s doit "
#~ "impérativement être : %2$s"
#~ msgstr[1] ""
#~ "L’utilisateur sélectionné dans le champ %1$s doit avoir l’un des rôles "
#~ "suivants : %2$s"

#~ msgid "%1$s must have a valid post ID."
#~ msgstr "%1s doit contenir un ID de publication valide."

#~ msgid "%1$s must have a valid user ID."
#~ msgstr "%1s doit contenir un ID de compte valide."

#~ msgid "%1$s must have term %2$s."
#~ msgid_plural "%1$s must have one of the following terms: %2$s"
#~ msgstr[0] "%1$s doit correspondre au terme %2$s."
#~ msgstr[1] "%1$s doit correspondre à l’un des termes suivants : %2$s"

#, php-format
#~ msgid "%d fields require attention"
#~ msgstr "%d champs requièrent votre attention"

#, php-format
#~ msgid "%s added"
#~ msgstr "%s ajouté"

#, php-format
#~ msgid "%s already exists"
#~ msgstr "%s existe déjà"

#~ msgid "%s requires a valid attachment ID."
#~ msgstr "%s nécessite un ID de média attaché valide."

# @ default
#, php-format
#~ msgid "%s value is required"
#~ msgstr "La valeur %s est requise"

#~ msgid "'%s' is not a valid email address"
#~ msgstr "'%s' n’est pas une adresse e-mail valide"

#~ msgid "(no label)"
#~ msgstr "(aucun libellé)"

#~ msgid "(this field)"
#~ msgstr "(ce champ)"

# @ acf
#~ msgid "+ Add Field"
#~ msgstr "+ Ajouter un champ"

#~ msgid "1 field requires attention"
#~ msgstr "1 champ requiert votre attention"

#, php-format
#~ msgid ""
#~ "<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
#~ "friendly community on our Community Forums who may be able to help you "
#~ "figure out the ‘how-tos’ of the ACF world."
#~ msgstr ""
#~ "<a href=\"%s\" target=\"_blank\">Forums</a>. Nous avons une communauté "
#~ "active et amicale sur nos forums qui pourrait être capable de vous aider "
#~ "quant aux bonnes pratiques de l’univers ACF."

#, php-format
#~ msgid ""
#~ "<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
#~ "documentation contains references and guides for most situations you may "
#~ "encounter."
#~ msgstr ""
#~ "<a href=\"%s\" target=\"_blank\">Documentation</a>. Notre documentation "
#~ "est très complète et contient les ressources et exemples pour faire face "
#~ "à toutes les situations que vous devriez rencontrer."

#, php-format
#~ msgid ""
#~ "<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals "
#~ "on our Help Desk will assist with your more in depth, technical "
#~ "challenges."
#~ msgstr ""
#~ "<a href=\"%s\" target=\"_blank\">Support</a>. Nos professionnels du "
#~ "support vous assisteront en profondeur pour vos soucis les plus "
#~ "techniques."

# @ acf
#~ msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
#~ msgstr ""
#~ "<b>Sélectionnez </b> les champs que vous souhaitez <b>masquer</b> sur la "
#~ "page d‘édition."

#~ msgid ""
#~ "<strong>%1$s</strong> - We've detected one or more calls to retrieve ACF "
#~ "field values before ACF has been initialized. This is not supported and "
#~ "can result in malformed or missing data. <a href=\"%2$s\" target=\"_blank"
#~ "\">Learn how to fix this</a>."
#~ msgstr ""
#~ "<strong>%1$s</strong> - Nous avons détecté un ou plusieurs tentatives de "
#~ "récupération des valeurs de champs ACF avant que ACF n’ait été "
#~ "initialisé. Vous pourriez rencontrer des problème de données manquantes "
#~ "ou erronées. <a href=\"%2$s\" target=\"_blank\">Découvre comment résoudre "
#~ "ce problème</a>."

#, php-format
#~| msgid "<strong>ERROR</strong>: %s"
#~ msgid "<strong>Error</strong>: %s"
#~ msgstr "<strong>Erreur</strong> : %s"

#~ msgid "A block for testing JS."
#~ msgstr "Un bloc pour tester JS."

#~ msgid "Accordion"
#~ msgstr "Accordéon"

#~ msgid "Active"
#~ msgstr "Actif"

#, php-format
#~ msgid "Active <span class=\"count\">(%s)</span>"
#~ msgid_plural "Active <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Actif <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Actifs <span class=\"count\">(%s)</span>"

# @ acf
#~ msgid "Add"
#~ msgstr "Ajouter"

#~ msgid "Add 'other' choice to allow for custom values"
#~ msgstr "Ajouter « autre » pour autoriser une valeur personnalisée"

#~ msgid "Add / Edit"
#~ msgstr "Ajouter / Modifier"

# @ acf
#~ msgid "Add File"
#~ msgstr "Ajouter un fichier"

# @ acf
#~ msgid "Add Image"
#~ msgstr "Ajouter une image"

#~ msgid "Add new choice"
#~ msgstr "Ajouter un choix"

# @ acf
#~ msgid "Add New Field"
#~ msgstr "Ajouter un champ"

# @ acf
#~ msgid "Add New Field Group"
#~ msgstr "Ajouter un nouveau groupe de champs"

# @ acf
#~ msgid "Add rule group"
#~ msgstr "Ajouter une règle"

# @ acf
#~ msgid "Advanced Custom Fields"
#~ msgstr "Advanced Custom Fields"

#~ msgid ""
#~ "Advanced Custom Fields and Advanced Custom Fields PRO should not be "
#~ "active at the same time. We've automatically deactivated Advanced Custom "
#~ "Fields PRO."
#~ msgstr ""
#~ "Les extensions Advanced Custom Fields et Advanced Custom Fields PRO ne "
#~ "doivent pas être activés simultanément. Nous avons automatiquement "
#~ "désactivé Advanced Custom Fields PRO."

#~ msgid ""
#~ "Advanced Custom Fields and Advanced Custom Fields PRO should not be "
#~ "active at the same time. We've automatically deactivated Advanced Custom "
#~ "Fields."
#~ msgstr ""
#~ "Les extensions Advanced Custom Fields et Advanced Custom Fields PRO ne "
#~ "doivent pas être activés simultanément. Nous avons automatiquement "
#~ "désactivé Advanced Custom Fields."

#, php-format
#~ msgid "All %s formats"
#~ msgstr "Tous les formats %s"

# @ acf
#~ msgid "All images"
#~ msgstr "Toutes les images"

#~ msgid "All post types"
#~ msgstr "Tous les types de publication"

#~ msgid "All taxonomies"
#~ msgstr "Toutes les taxonomies"

#~ msgid "All user roles"
#~ msgstr "Tous les rôles utilisateurs"

#~ msgid "Allow 'custom' values to be added"
#~ msgstr "Permet l’ajout d’une valeur personnalisée"

#~ msgid "Allow Archives URLs"
#~ msgstr "Afficher les pages d’archives"

#~ msgid "Allow Custom"
#~ msgstr "Permettra une valeur personnalisée"

#~ msgid "Allow HTML markup to display as visible text instead of rendering"
#~ msgstr ""
#~ "Permettre l’affichage du code HTML à l’écran au lieu de l’interpréter"

#~ msgid "Allow new terms to be created whilst editing"
#~ msgstr "Autoriser la création de nouveaux termes pendant l’édition"

# @ acf
#~ msgid "Allow Null?"
#~ msgstr "Autoriser une valeur vide ?"

#~ msgid "Allow this accordion to open without closing others."
#~ msgstr "Permettre à cet accordéon de s'ouvrir sans refermer les autres."

#~ msgid "and"
#~ msgstr "et"

#~ msgid "Appearance"
#~ msgstr "Apparence"

#~ msgid "Appears after the input"
#~ msgstr "Apparait après le champ"

#~ msgid "Appears before the input"
#~ msgstr "Apparait avant le champ"

#~ msgid "Appears when creating a new post"
#~ msgstr "Valeur affichée à la création d’une publication"

#~ msgid "Appears within the input"
#~ msgstr "Apparait dans le champ (placeholder)"

#~ msgid "Append"
#~ msgstr "Suffixe"

#~ msgid "Archives"
#~ msgstr "Archives"

# @ acf
#~ msgid "Are you sure?"
#~ msgstr "Confirmez-vous cette action ?"

#~ msgid "Attachment"
#~ msgstr "Média (photo, fichier…)"

#~ msgid "Attachments"
#~ msgstr "Médias"

#~ msgid "Author"
#~ msgstr "Auteur"

#~ msgid "Automatically add &lt;br&gt;"
#~ msgstr "Ajouter &lt;br&gt; automatiquement"

#~ msgid "Automatically add paragraphs"
#~ msgstr "Ajouter des paragraphes automatiquement"

#~ msgid "Awaiting save"
#~ msgstr "En attente d’une sauvegarde"

#~ msgid "Back to all tools"
#~ msgstr "Retour aux outils"

#~ msgid "Basic"
#~ msgstr "Champs basiques"

#, php-format
#~ msgid ""
#~ "Before creating your first Field Group, we recommend first reading our <a "
#~ "href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
#~ "yourself with the plugin's philosophy and best practises."
#~ msgstr ""
#~ "Avant de créer votre premier Groupe de Champs, nous vous recommandons de "
#~ "lire notre <a href=\"%s\" target=\"_blank\">Guide de démarrage</a> afin "
#~ "de vous familiariser avec la philosophie et les bonnes pratiques de "
#~ "l’extension."

# @ acf
#~ msgid "Below fields"
#~ msgstr "Sous les champs"

# @ acf
#~ msgid "Below labels"
#~ msgstr "Sous les intitulés"

#~ msgid "Blocks"
#~ msgstr "Blocs"

#~ msgid "Both (Array)"
#~ msgstr "Les deux (tableau)"

#~ msgid "Button Group"
#~ msgstr "Groupe de boutons"

#~ msgid "Cancel"
#~ msgstr "Annuler"

#~ msgid "Categories"
#~ msgstr "Catégories"

#~ msgid "Center"
#~ msgstr "Centre"

#~ msgid "Center the initial map"
#~ msgstr "Position géographique du centre de la carte"

#~ msgid "Character Limit"
#~ msgstr "Limite de caractères"

# @ acf
#~ msgid "Checkbox"
#~ msgstr "Case à cocher"

#~ msgid "Checked"
#~ msgstr "Coché"

#~ msgid "Child Page (has parent)"
#~ msgstr "Page enfant (avec parent)"

# @ acf
#~ msgid "Choice"
#~ msgstr "Choix"

# @ acf
#~ msgid "Choices"
#~ msgstr "Choix"

#~ msgid "class"
#~ msgstr "classe"

# @ acf
#, php-format
#~ msgid "Class \"%s\" does not exist."
#~ msgstr "La classe \"%s\" n’existe pas"

#~ msgid "Clear"
#~ msgstr "Effacer"

#~ msgid "Clear color"
#~ msgstr "Effacer la couleur"

# @ acf
#~ msgid "Clear location"
#~ msgstr "Effacer la position"

#~ msgid "Click to initialize TinyMCE"
#~ msgstr "Cliquez pour initialiser TinyMCE"

#~ msgid "Clone (Pro only)"
#~ msgstr "Cloner (fonctionnalité Pro)"

# @ acf
#~ msgid "Close Field"
#~ msgstr "Fermer le champ"

# @ acf
#~ msgid "Close Window"
#~ msgstr "Fermer la fenêtre"

#~ msgid "Collapse Details"
#~ msgstr "Masquer les détails"

# @ acf
#~ msgid "Color Picker"
#~ msgstr "Couleur"

#~ msgid "Color value"
#~ msgstr "Valeur de la couleur"

#~ msgid "Comment"
#~ msgstr "Commentaire"

#~ msgid "Comments"
#~ msgstr "Commentaires"

#~ msgid "Conditional Logic"
#~ msgstr "Logique conditionnelle"

#~ msgid "Connect selected terms to the post"
#~ msgstr "Lier les termes sélectionnés à la publication"

#~ msgid "Content"
#~ msgstr "Contenu"

#~ msgid "Content Editor"
#~ msgstr "Éditeur de contenu"

#~ msgid "Controls how new lines are rendered"
#~ msgstr "Comment sont interprétés les sauts de lignes"

#~ msgid "Copied"
#~ msgstr "Copié"

#~ msgid "copy"
#~ msgstr "copie"

#~ msgid "Copy to clipboard"
#~ msgstr "Copier dans le presse-papiers"

# @ acf
#~ msgid ""
#~ "Create a set of rules to determine which edit screens will use these "
#~ "advanced custom fields"
#~ msgstr ""
#~ "Créez une série de règles pour déterminer les écrans sur lesquels ce "
#~ "groupe de champs sera utilisé"

# @ acf
#~ msgid "Create Terms"
#~ msgstr "Créer des termes"

#~ msgid "Current User"
#~ msgstr "Utilisateur actuel"

# @ acf
#~ msgid "Current User Role"
#~ msgstr "Rôle utilisateur actuel"

# @ acf
#~ msgid "Custom Fields"
#~ msgstr "ACF"

#~ msgid "Custom:"
#~ msgstr "Personnalisé :"

#~ msgid "Customize the map height"
#~ msgstr "Personnaliser la hauteur de la carte"

#~ msgid ""
#~ "Customize WordPress with powerful, professional and intuitive fields."
#~ msgstr ""
#~ "Personnalisez WordPress avec des champs intuitifs, puissants et "
#~ "professionnels."

#, php-format
#~ msgid ""
#~ "Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
#~ msgstr ""
#~ "Mise à niveau de la base de données effectuée. <a href=\"%s\">Retourner "
#~ "au panneau d’administration du réseau</a>"

#, php-format
#~ msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
#~ msgstr ""
#~ "Mise à niveau de la base de données terminée. <a href=\"%s\">Consulter "
#~ "les nouveautés</a>"

#~ msgid "Database Upgrade Required"
#~ msgstr "Mise à jour de la base de données nécessaire"

# @ acf
#~ msgid "Date Picker"
#~ msgstr "Date"

#~ msgctxt "Date Picker JS closeText"
#~ msgid "Done"
#~ msgstr "Valider"

#~ msgctxt "Date Picker JS currentText"
#~ msgid "Today"
#~ msgstr "Aujourd’hui"

#~ msgctxt "Date Picker JS nextText"
#~ msgid "Next"
#~ msgstr "Suivant"

#~ msgctxt "Date Picker JS prevText"
#~ msgid "Prev"
#~ msgstr "Précédent"

#~ msgctxt "Date Picker JS weekHeader"
#~ msgid "Wk"
#~ msgstr "Sem"

#~ msgid "Date Time Picker"
#~ msgstr "Date et Heure"

#~ msgctxt "Date Time Picker JS amText"
#~ msgid "AM"
#~ msgstr "AM"

#~ msgctxt "Date Time Picker JS amTextShort"
#~ msgid "A"
#~ msgstr "A"

#~ msgctxt "Date Time Picker JS closeText"
#~ msgid "Done"
#~ msgstr "Valider"

#~ msgctxt "Date Time Picker JS currentText"
#~ msgid "Now"
#~ msgstr "Maintenant"

#~ msgctxt "Date Time Picker JS hourText"
#~ msgid "Hour"
#~ msgstr "Heure"

#~ msgctxt "Date Time Picker JS microsecText"
#~ msgid "Microsecond"
#~ msgstr "Microseconde"

#~ msgctxt "Date Time Picker JS millisecText"
#~ msgid "Millisecond"
#~ msgstr "Milliseconde"

#~ msgctxt "Date Time Picker JS minuteText"
#~ msgid "Minute"
#~ msgstr "Minute"

#~ msgctxt "Date Time Picker JS pmText"
#~ msgid "PM"
#~ msgstr "PM"

#~ msgctxt "Date Time Picker JS pmTextShort"
#~ msgid "P"
#~ msgstr "P"

#~ msgctxt "Date Time Picker JS secondText"
#~ msgid "Second"
#~ msgstr "Seconde"

#~ msgctxt "Date Time Picker JS selectText"
#~ msgid "Select"
#~ msgstr "Valider"

#~ msgctxt "Date Time Picker JS timeOnlyTitle"
#~ msgid "Choose Time"
#~ msgstr "Choix de l’heure"

#~ msgctxt "Date Time Picker JS timeText"
#~ msgid "Time"
#~ msgstr "Heure"

#~ msgctxt "Date Time Picker JS timezoneText"
#~ msgid "Time Zone"
#~ msgstr "Fuseau horaire"

# @ acf
#~ msgid "Default"
#~ msgstr "Valeur par défaut"

# @ acf
#~ msgid "Default Template"
#~ msgstr "Modèle de base"

# @ acf
#~ msgid "Default Value"
#~ msgstr "Valeur par défaut"

#~ msgid ""
#~ "Define an endpoint for the previous accordion to stop. This accordion "
#~ "will not be visible."
#~ msgstr ""
#~ "Définir un point de terminaison pour arrêter l’accordéon. Cet accordéon "
#~ "ne sera pas visible."

#~ msgid ""
#~ "Define an endpoint for the previous tabs to stop. This will start a new "
#~ "group of tabs."
#~ msgstr ""
#~ "Définir un point de terminaison pour arrêter les précédents onglets. Cela "
#~ "va commencer un nouveau groupe d’onglets."

#~ msgid "Delay initialization?"
#~ msgstr "Retarder l’initialisation ?"

# @ acf
#~ msgid "Delete field"
#~ msgstr "Supprimer ce champ"

#~ msgid "Delicious Brains"
#~ msgstr "Delicious Brains"

#, php-format
#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Désactivé <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Désactivés <span class=\"count\">(%s)</span>"

#~ msgid "Discussion"
#~ msgstr "Discussion"

#~ msgid "Display a random hero image."
#~ msgstr "Affiche une bannière imagée"

# @ acf
#~ msgid "Display Format"
#~ msgstr "Format dans l’administration"

#~ msgid "Display this accordion as open on page load."
#~ msgstr "Ouvrir l’accordéon au chargement de la page."

#~ msgid "Displays text alongside the checkbox"
#~ msgstr "Affiche le texte à côté de la case à cocher"

# @ acf
#~ msgid "Duplicate field"
#~ msgstr "Dupliquer ce champ"

# @ acf
#~ msgid "Duplicate this item"
#~ msgstr "Dupliquer cet élément"

# @ acf
#~ msgid "Edit Field"
#~ msgstr "Modifier le champ"

# @ acf
#~ msgid "Edit field"
#~ msgstr "Modifier ce champ"

# @ acf
#~ msgid "Edit Field Group"
#~ msgstr "Modifier le groupe de champs"

# @ acf
#~ msgid "Edit File"
#~ msgstr "Modifier le fichier"

# @ acf
#~ msgid "Edit Image"
#~ msgstr "Modifier l’image"

#~ msgid "Elements"
#~ msgstr "Éléments"

#~ msgid "Email"
#~ msgstr "E-mail"

#~ msgid "Embed Size"
#~ msgstr "Dimensions"

#~ msgid "Enable Transparency"
#~ msgstr "Activer la transparence"

#~ msgid "Endpoint"
#~ msgstr "Point de terminaison"

#~ msgid "Enter each choice on a new line."
#~ msgstr "Indiquez une valeur par ligne."

#~ msgid "Enter each default value on a new line"
#~ msgstr "Entrez chaque valeur par défaut sur une nouvelle ligne"

#~ msgid "Enter URL"
#~ msgstr "Entrez l’URL"

#~ msgid "Error loading field."
#~ msgstr "Erreur pendant le chargement du champ."

#~ msgid "Error uploading file. Please try again"
#~ msgstr "Échec de l'import du fichier. Merci de réessayer"

#~ msgid "Escape HTML"
#~ msgstr "Autoriser le code HTML"

#~ msgid "Excerpt"
#~ msgstr "Extrait"

#~ msgid "Expand Details"
#~ msgstr "Afficher les détails"

# @ acf
#~ msgid "Export Field Groups"
#~ msgstr "Exporter les groupes de champs"

#~ msgid "Export File"
#~ msgstr "Exporter le fichier"

#, php-format
#~ msgid "Exported 1 field group."
#~ msgid_plural "Exported %s field groups."
#~ msgstr[0] "1 groupe de champ a été exporté."
#~ msgstr[1] "%s groupes de champs ont été exportés."

# @ acf
#~ msgid "Featured Image"
#~ msgstr "Image à la Une"

# @ acf
#~ msgid "Field"
#~ msgstr "Champ"

# @ acf
#~ msgid "Field Group"
#~ msgstr "Groupe de champs"

# @ default
#~ msgid "Field group deleted."
#~ msgstr "Groupe de champs supprimé."

#~ msgid "Field group draft updated."
#~ msgstr "Brouillon du groupe de champs mis à jour."

#, php-format
#~ msgid "Field group duplicated."
#~ msgid_plural "%s field groups duplicated."
#~ msgstr[0] "Groupe de champs dupliqué."
#~ msgstr[1] "%s groupes de champs dupliqués."

# @ default
#~ msgid "Field group published."
#~ msgstr "Groupe de champ publié."

# @ default
#~ msgid "Field group saved."
#~ msgstr "Groupe de champ enregistré."

#~ msgid "Field group scheduled for."
#~ msgstr "Groupe de champs programmé pour."

# @ default
#~ msgid "Field group submitted."
#~ msgstr "Groupe de champ enregistré."

#, php-format
#~ msgid "Field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "Groupe de champs synchronisé."
#~ msgstr[1] "%s groupes de champs synchronisés."

# @ default
#~ msgid "Field group title is required"
#~ msgstr "Veuillez indiquer un titre pour le groupe de champs"

# @ default
#~ msgid "Field group updated."
#~ msgstr "Groupe de champs mis à jour."

# @ acf
#~ msgid "Field Groups"
#~ msgstr "Groupes de champs"

#~ msgid "Field groups with a lower order will appear first"
#~ msgstr ""
#~ "Le groupe de champs qui a l’ordre le plus petit sera affiché en premier"

#~ msgid "Field Keys"
#~ msgstr "Identifiants des champs"

# @ acf
#~ msgid "Field Label"
#~ msgstr "Titre du champ"

# @ acf
#~ msgid "Field Name"
#~ msgstr "Nom du champ"

# @ acf
#~ msgid "Field Type"
#~ msgstr "Type de champ"

# @ acf
#~ msgid "Field type does not exist"
#~ msgstr "Ce type de champ n‘existe pas"

# @ acf
#~ msgid "File"
#~ msgstr "Fichier"

#~ msgid "File Array"
#~ msgstr "Données du fichier (tableau)"

# @ acf
#~ msgid "File ID"
#~ msgstr "ID du Fichier"

# @ acf
#~ msgid "File name"
#~ msgstr "Nom du fichier"

#, php-format
#~ msgid "File size must be at least %s."
#~ msgstr "Le poids de l’image doit être d’au moins %s."

#, php-format
#~ msgid "File size must not exceed %s."
#~ msgstr "Le poids du fichier ne doit pas dépasser %s."

# @ acf
#, php-format
#~ msgid "File type must be %s."
#~ msgstr "Le type de fichier doit être %s."

# @ acf
#~ msgid "File URL"
#~ msgstr "URL du fichier"

#~ msgid "Filter by Post Type"
#~ msgstr "Filtrer par type de publication"

#~ msgid "Filter by role"
#~ msgstr "Filtrer par rôle"

# @ acf
#~ msgid "Filter by Taxonomy"
#~ msgstr "Filtrer par taxonomie"

#~ msgid "Filters"
#~ msgstr "Filtres"

#~ msgid "Find current location"
#~ msgstr "Trouver l’emplacement actuel"

#~ msgid "Flexible Content (Pro only)"
#~ msgstr "Contenu flexible (fonctionnalité Pro)"

#~ msgid "For more control, you may specify both a value and label like this:"
#~ msgstr ""
#~ "Pour un contrôle plus poussé, vous pouvez spécifier la valeur et le "
#~ "libellé de cette manière :"

# @ acf
#~ msgid "Format"
#~ msgstr "Format"

# @ acf
#~ msgid "Forms"
#~ msgstr "Formulaires"

#~ msgid "Front Page"
#~ msgstr "Page d’accueil"

#~ msgid "Full Size"
#~ msgstr "Taille originale"

#~ msgid "Gallery (Pro only)"
#~ msgstr "Galerie (fonctionnalité Pro)"

#~ msgid "Generate PHP"
#~ msgstr "Générer le PHP"

#~ msgid "Google Map"
#~ msgstr "Google Map"

# @ acf
#~ msgid "Group"
#~ msgstr "Groupe"

#~ msgid "Has any value"
#~ msgstr "A n'importe quelle valeur"

#~ msgid "Has no value"
#~ msgstr "N'a pas de valeur"

#~ msgid "Help & Support"
#~ msgstr "Aide & Support"

#~ msgid "Hero"
#~ msgstr "Bannière"

#~ msgid "Hex String"
#~ msgstr "Chaîne hexadécimale"

#~ msgid "Hide on screen"
#~ msgstr "Masquer"

#~ msgid "High (after title)"
#~ msgstr "Haute (après le titre)"

#~ msgid "Horizontal"
#~ msgstr "Horizontal"

#~ msgid "https://www.advancedcustomfields.com"
#~ msgstr "https://www.advancedcustomfields.com"

#~ msgid "id"
#~ msgstr "id"

# @ acf
#~ msgid ""
#~ "If multiple field groups appear on an edit screen, the first field "
#~ "group's options will be used (the one with the lowest order number)"
#~ msgstr ""
#~ "Si plusieurs groupes ACF sont présents sur une page d‘édition, le groupe "
#~ "portant le numéro le plus bas sera affiché en premier."

# @ acf
#~ msgid "Image"
#~ msgstr "Image"

#, php-format
#~ msgid "Image height must be at least %dpx."
#~ msgstr "L’image doit mesurer au moins %dpx de hauteur."

#, php-format
#~ msgid "Image height must not exceed %dpx."
#~ msgstr "L’image ne doit pas dépasser %dpx de hauteur."

#, php-format
#~ msgid "Image width must be at least %dpx."
#~ msgstr "L'image doit mesurer au moins %dpx de largeur."

#, php-format
#~ msgid "Image width must not exceed %dpx."
#~ msgstr "L'image ne doit pas dépasser %dpx de largeur."

#~ msgid "Import"
#~ msgstr "Importer"

# @ acf
#~ msgid "Import Field Groups"
#~ msgstr "Importer les groupes de champs"

#~ msgid "Import File"
#~ msgstr "Importer le fichier"

#~ msgid "Import file empty"
#~ msgstr "Le fichier à importer est vide"

#, php-format
#~ msgid "Imported 1 field group"
#~ msgid_plural "Imported %s field groups"
#~ msgstr[0] "1 groupe de champs importé"
#~ msgstr[1] "%s groupes de champs importés"

#~ msgid "Incorrect file type"
#~ msgstr "Type de fichier incorrect"

# @ acf
#~ msgid "Information"
#~ msgstr "Informations"

# @ acf
#~ msgid "Instruction placement"
#~ msgstr "Emplacement des instructions"

# @ acf
#~ msgid "Instructions"
#~ msgstr "Instructions"

# @ acf
#~ msgid "Instructions for authors. Shown when submitting data"
#~ msgstr ""
#~ "Instructions pour les auteurs. Affichées lors de la saisie du contenu"

# @ acf
#~ msgid "Invalid field group ID."
#~ msgstr "ID du groupe de champs invalide."

# @ default
#~ msgid "Invalid field group parameter(s)."
#~ msgstr "Paramètre(s) du groupe de champs invalide."

#~ msgid "Invalid request."
#~ msgstr "Requête invalide."

#~ msgid "is equal to"
#~ msgstr "est égal à"

#~ msgid "is not equal to"
#~ msgstr "n‘est pas égal à"

#~ msgid ""
#~ "It is strongly recommended that you backup your database before "
#~ "proceeding. Are you sure you wish to run the updater now?"
#~ msgstr ""
#~ "Il est fortement recommandé de faire une sauvegarde de votre base de "
#~ "données avant de continuer. Êtes-vous sûr de vouloir lancer la mise à "
#~ "niveau maintenant ?"

#~ msgid "jQuery"
#~ msgstr "jQuery"

# @ acf
#~ msgid "JSON field group (newer)"
#~ msgstr "Groupe de champ JSON (plus récent)"

#~ msgid "Key"
#~ msgstr "Identifiant"

#~ msgid "Label placement"
#~ msgstr "Emplacement de l'intitulé"

#~ msgid "Large"
#~ msgstr "Grande"

# @ acf
#, php-format
#~ msgid "Last updated: %s"
#~ msgstr "Dernière mise à jour : %s"

#~ msgid "Leave blank for no limit"
#~ msgstr "Laisser vide pour illimité"

#~ msgid "Left aligned"
#~ msgstr "Aligné à gauche"

# @ acf
#~ msgid "Link"
#~ msgstr "Lien"

#~ msgid "Link Array"
#~ msgstr "Données de lien (tableau)"

# @ acf
#~ msgid "Link URL"
#~ msgstr "URL du Lien"

#~ msgid "Load Terms"
#~ msgstr "Charger les termes"

#~ msgid "Load value from posts terms"
#~ msgstr "Charger une valeur depuis les termes"

#~ msgid "Loading"
#~ msgstr "Chargement"

#~ msgid "Loading diff"
#~ msgstr "Chargement du différentiel"

#~ msgid "Local JSON"
#~ msgstr "JSON Local"

#, php-format
#~ msgid "Located in plugin: %s"
#~ msgstr "Situé dans l’extension : %s"

#, php-format
#~ msgid "Located in theme: %s"
#~ msgstr "Situé dans le thème : %s"

# @ acf
#, php-format
#~ msgid "Located in: %s"
#~ msgstr "Situé dans : %s"

# @ acf
#~ msgid "Location"
#~ msgstr "Emplacement"

#, php-format
#~ msgid "Location type \"%s\" is already registered."
#~ msgstr "Le type d’emplacement \"%s\" est déjà déclaré."

#~ msgid "Logged in"
#~ msgstr "Connecté"

# @ acf
#~ msgid "Maximum posts"
#~ msgstr "Maximum de publications"

# @ acf
#~ msgid "Maximum Value"
#~ msgstr "Valeur maximale"

#~ msgid "Maximum values reached ( {max} values )"
#~ msgstr "Nombre maximal de valeurs atteint ({max} valeurs)"

#~ msgid "Medium"
#~ msgstr "Moyen"

#~ msgid "Menu"
#~ msgstr "Menu"

#~ msgid "Menu Item"
#~ msgstr "Élément de menu"

#~ msgid "Menu items"
#~ msgstr "Éléments de menu"

# @ acf
#~ msgid "Menu Locations"
#~ msgstr "Emplacement de menu"

#~ msgid "Menus"
#~ msgstr "Menus"

# @ acf
#~ msgid "Message"
#~ msgstr "Message"

# @ acf
#~ msgid "Minimum posts"
#~ msgstr "Minimum de publications"

#~ msgid "Minimum Value"
#~ msgstr "Valeur minimale"

#~ msgid "Move"
#~ msgstr "Déplacer"

#~ msgid "Move Complete."
#~ msgstr "Déplacement effectué."

# @ acf
#~ msgid "Move Custom Field"
#~ msgstr "Déplacer le champ personnalisé"

# @ acf
#~ msgid "Move Field"
#~ msgstr "Déplacer le champ"

#~ msgid "Move field to another group"
#~ msgstr "Déplacer le champ dans un autre groupe"

# @ acf
#~ msgid "Move to trash. Are you sure?"
#~ msgstr "Mettre à la corbeille. Êtes-vous sûr ?"

# @ acf
#~ msgid "Multi Select"
#~ msgstr "Sélecteur multiple"

#~ msgid "Multi-expand"
#~ msgstr "Ouverture multiple"

# @ acf
#~ msgid "Multiple Values"
#~ msgstr "Valeurs multiples"

# @ acf
#~ msgctxt "Name for the Text editor tab (formerly HTML)"
#~ msgid "Text"
#~ msgstr "Texte"

# @ acf
#~ msgid "New Field"
#~ msgstr "Nouveau champ"

# @ acf
#~ msgid "New Field Group"
#~ msgstr "Nouveau groupe de champs"

# @ acf
#~ msgid "New Lines"
#~ msgstr "Nouvelles lignes"

# @ default
#~ msgid "No Field Groups found"
#~ msgstr "Aucun groupe de champs trouvé"

# @ default
#~ msgid "No Field Groups found in Trash"
#~ msgstr "Aucun groupe de champs trouvé dans la corbeille"

# @ acf
#~ msgid "No field groups selected"
#~ msgstr "Aucun groupe de champs n'est sélectionné"

# @ default
#~ msgid "No Fields found"
#~ msgstr "Aucun champ trouvé"

# @ default
#~ msgid "No Fields found in Trash"
#~ msgstr "Aucun champ trouvé dans la corbeille"

# @ acf
#~ msgid ""
#~ "No fields. Click the <strong>+ Add Field</strong> button to create your "
#~ "first field."
#~ msgstr ""
#~ "Aucun champ. Cliquez sur le bouton <strong>+ Ajouter un champ</strong> "
#~ "pour créer votre premier champ."

# @ acf
#~ msgid "No file selected"
#~ msgstr "Aucun fichier sélectionné"

# @ acf
#~ msgid "No Formatting"
#~ msgstr "Pas de formatage"

# @ acf
#~ msgid "No image selected"
#~ msgstr "Aucune image sélectionnée"

#~ msgid "No matches found"
#~ msgstr "Aucun résultat"

#, php-format
#~ msgctxt "No terms"
#~ msgid "No %s"
#~ msgstr "Pas de %s"

#~ msgid "No toggle fields available"
#~ msgstr "Ajoutez d’abord une case à cocher ou un champ sélection"

# @ acf
#~ msgid "No updates available."
#~ msgstr "Aucune mise à jour disponible."

#~ msgid "Normal (after content)"
#~ msgstr "Normal (après le contenu)"

#~ msgctxt "noun"
#~ msgid "Select"
#~ msgstr "Liste déroulante"

#~ msgid "Null"
#~ msgstr "Vide"

#~ msgid "Number"
#~ msgstr "Nombre"

#~ msgid "oEmbed"
#~ msgstr "oEmbed"

#~ msgid "Off Text"
#~ msgstr "Texte côté « Inactif »"

#~ msgid "On Text"
#~ msgstr "Texte côté « Actif »"

#~ msgid "Open"
#~ msgstr "Ouvert"

#~ msgid "Opens in a new window/tab"
#~ msgstr "Ouvrir dans un nouvel onglet"

#~ msgid "or"
#~ msgstr "ou"

# @ acf
#~ msgid "Order"
#~ msgstr "Ordre"

# @ acf
#~ msgid "Order No."
#~ msgstr "Numéro d’ordre"

# @ acf
#~ msgid "Original field group"
#~ msgstr "Groupe de champ original"

#~ msgid "Other"
#~ msgstr "Autre"

#~ msgid "Overview"
#~ msgstr "Aperçu"

# @ acf
#~ msgid "Page"
#~ msgstr "Page"

#~ msgid "Page Attributes"
#~ msgstr "Attributs de la page"

# @ acf
#~ msgid "Page Link"
#~ msgstr "Lien vers la publication"

# @ acf
#~ msgid "Page Parent"
#~ msgstr "Page parente"

#~ msgid "Page Template"
#~ msgstr "Modèle de page"

# @ acf
#~ msgid "Page Type"
#~ msgstr "Type de page"

#~ msgid "Parent"
#~ msgstr "Parent"

#~ msgid "Parent Page (has children)"
#~ msgstr "Page parente (avec page enfant)"

#~ msgid "Password"
#~ msgstr "Mot de passe"

#~ msgid "Permalink"
#~ msgstr "Permalien"

#~ msgid "Placeholder Text"
#~ msgstr "Texte d’exemple"

#~ msgid "Placement"
#~ msgstr "Emplacement"

#, php-format
#~ msgid ""
#~ "Please also check all premium add-ons (%s) are updated to the latest "
#~ "version."
#~ msgstr ""
#~ "Veuillez également vérifier que tous les add-ons premium (%s) sont à jour "
#~ "avec la dernière version disponible."

#~ msgid "Please select at least one site to upgrade."
#~ msgstr "Merci de sélectionner au moins un site à mettre à niveau."

# @ acf
#~ msgid "Please select the destination for this field"
#~ msgstr "Choisissez la destination de ce champ"

#~ msgid ""
#~ "Please use the Help & Support tab to get in touch should you find "
#~ "yourself requiring assistance."
#~ msgstr ""
#~ "Utilisez l’onglet Aide & Support afin de nous contacter dans le cas où "
#~ "vous auriez besoin d’assistance."

# @ acf
#~ msgid "Position"
#~ msgstr "Position"

# @ acf
#~ msgid "Post"
#~ msgstr "Publication"

#~ msgid "Post Category"
#~ msgstr "Catégorie"

# @ acf
#~ msgid "Post Format"
#~ msgstr "Format d‘article"

# @ acf
#~ msgid "Post ID"
#~ msgstr "ID de la publication"

# @ acf
#~ msgid "Post Object"
#~ msgstr "Objet Publication"

# @ acf
#~ msgid "Post Status"
#~ msgstr "Statut de l’article"

#~ msgctxt "post status"
#~ msgid "Active"
#~ msgstr "Actif"

#~ msgctxt "post status"
#~ msgid "Disabled"
#~ msgstr "Désactivé"

# @ acf
#~ msgid "Post Taxonomy"
#~ msgstr "Taxonomie"

#~ msgid "Post Template"
#~ msgstr "Modèle d’article"

# @ acf
#~ msgid "Post Type"
#~ msgstr "Type de publication"

# @ acf
#~ msgid "Post updated"
#~ msgstr "Publication mise à jour"

# @ acf
#~ msgid "Posts"
#~ msgstr "Publications"

#~ msgid "Posts Page"
#~ msgstr "Page des articles"

#~ msgid "Prepend"
#~ msgstr "Préfixe"

#~ msgid "Prepend an extra checkbox to toggle all choices"
#~ msgstr "Ajouter une case à cocher au début pour intervertir tous les choix"

# @ acf
#~ msgid "Radio Button"
#~ msgstr "Bouton radio"

# @ acf
#~ msgid "Radio Buttons"
#~ msgstr "Boutons radio"

#~ msgid "Range"
#~ msgstr "Curseur numérique"

#~ msgid "Reading upgrade tasks..."
#~ msgstr "Lecture des instructions de mise à niveau…"

#~ msgid "red : Red"
#~ msgstr "rouge : Rouge"

#~ msgid "Register"
#~ msgstr "Inscription"

# @ acf
#~ msgid "Relational"
#~ msgstr "Relationnel"

# @ acf
#~ msgid "Relationship"
#~ msgstr "Relation"

#~ msgid "Repeater (Pro only)"
#~ msgstr "Répéteur (fonctionnalité Pro)"

# @ acf
#~ msgid "Required?"
#~ msgstr "Requis ?"

#~ msgid "Restrict which files can be uploaded"
#~ msgstr "Restreindre l’import de fichiers"

#~ msgid "Restricted"
#~ msgstr "Limité"

# @ acf
#~ msgid "Return Value"
#~ msgstr "Valeur affichée dans le template"

#~ msgid "Review changes"
#~ msgstr "Voir les changements"

#~ msgid "Review local JSON changes"
#~ msgstr "Voir les modifications du JSON local"

#~ msgid "Review sites & upgrade"
#~ msgstr "Examiner les sites et mettre à niveau"

#~ msgid "Revisions"
#~ msgstr "Révisions"

#~ msgid "RGBA Array"
#~ msgstr "Tableau RGBA"

#~ msgid "RGBA String"
#~ msgstr "Chaîne RGBA"

#~ msgid "Rows"
#~ msgstr "Lignes"

# @ acf
#~ msgid "Rules"
#~ msgstr "Règles"

#~ msgid "Save 'custom' values to the field's choices"
#~ msgstr "Enregistre la valeur personnalisée dans les choix du champs"

#~ msgid "Save 'other' values to the field's choices"
#~ msgstr "Enregistrer « autre » en tant que choix"

#~ msgid "Save Custom"
#~ msgstr "Enregistrer la valeur personnalisée"

#~ msgid "Save Format"
#~ msgstr "Enregistrer le format"

#~ msgid "Save Other"
#~ msgstr "Enregistrer"

#~ msgid "Save Terms"
#~ msgstr "Enregistrer les termes"

#~ msgid "Saved"
#~ msgstr "Enregistré"

#~ msgid "Seamless (no metabox)"
#~ msgstr "Sans contour (directement dans la page)"

#~ msgid "Search"
#~ msgstr "Rechercher"

# @ default
#~ msgid "Search Field Groups"
#~ msgstr "Rechercher un groupe de champs"

# @ default
#~ msgid "Search Fields"
#~ msgstr "Rechercher des champs"

#~ msgid "Search for address..."
#~ msgstr "Rechercher une adresse…"

#~ msgid "Search..."
#~ msgstr "Rechercher…"

# @ acf
#, php-format
#~ msgid "Select %s"
#~ msgstr "Choisir %s"

# @ acf
#~ msgid "Select Color"
#~ msgstr "Couleur"

#~ msgid "Select default color"
#~ msgstr "Sélectionner la couleur par défaut"

# @ default
#~ msgid "Select Field Groups"
#~ msgstr "Sélectionnez les groupes de champs"

# @ acf
#~ msgid "Select File"
#~ msgstr "Sélectionner un fichier"

# acf
#~ msgid "Select Image"
#~ msgstr "Sélectionner l‘image"

# @ acf
#~ msgid "Select Link"
#~ msgstr "Sélectionner un lien"

# @ acf
#~ msgid "Select multiple values?"
#~ msgstr "Plusieurs valeurs possibles ?"

#~ msgid "Select post type"
#~ msgstr "Choisissez le type de publication"

# @ acf
#~ msgid "Select taxonomy"
#~ msgstr "Choisissez la taxonomie"

#~ msgid ""
#~ "Select the Advanced Custom Fields JSON file you would like to import. "
#~ "When you click the import button below, ACF will import the field groups."
#~ msgstr ""
#~ "Sélectionnez le fichier JSON que vous souhaitez importer et cliquez sur "
#~ "« Importer ». ACF s'occupe du reste."

# @ acf
#~ msgid "Select the appearance of this field"
#~ msgstr "Personnaliser l’apparence de champ"

#~ msgid ""
#~ "Select the field groups you would like to export and then select your "
#~ "export method. Use the download button to export to a .json file which "
#~ "you can then import to another ACF installation. Use the generate button "
#~ "to export to PHP code which you can place in your theme."
#~ msgstr ""
#~ "Sélectionnez les groupes de champs que vous souhaitez exporter puis "
#~ "choisissez ensuite la méthode d'export : le bouton télécharger vous "
#~ "permettra d’exporter un fichier JSON que vous pourrez importer dans une "
#~ "autre installation ACF alors que le bouton « générer » exportera le code "
#~ "PHP que vous pourrez ajouter dans votre thème."

# @ acf
#~ msgid "Select the taxonomy to be displayed"
#~ msgstr "Choisissez la taxonomie à afficher"

#~ msgctxt "Select2 JS input_too_long_1"
#~ msgid "Please delete 1 character"
#~ msgstr "Veuillez retirer 1 caractère"

#, php-format
#~ msgctxt "Select2 JS input_too_long_n"
#~ msgid "Please delete %d characters"
#~ msgstr "Veuillez retirer %d caractères"

#~ msgctxt "Select2 JS input_too_short_1"
#~ msgid "Please enter 1 or more characters"
#~ msgstr "Veuillez saisir au minimum 1 caractère"

#, php-format
#~ msgctxt "Select2 JS input_too_short_n"
#~ msgid "Please enter %d or more characters"
#~ msgstr "Veuillez saisir au minimum %d caractères"

#~ msgctxt "Select2 JS load_fail"
#~ msgid "Loading failed"
#~ msgstr "Échec du chargement"

#~ msgctxt "Select2 JS load_more"
#~ msgid "Loading more results&hellip;"
#~ msgstr "Chargement de résultats supplémentaires&hellip;"

#~ msgctxt "Select2 JS matches_0"
#~ msgid "No matches found"
#~ msgstr "Aucun résultat trouvé"

#~ msgctxt "Select2 JS matches_1"
#~ msgid "One result is available, press enter to select it."
#~ msgstr "Un résultat disponible, appuyez sur Entrée pour le sélectionner."

#, php-format
#~ msgctxt "Select2 JS matches_n"
#~ msgid "%d results are available, use up and down arrow keys to navigate."
#~ msgstr ""
#~ "%d résultats sont disponibles, utilisez les flèches haut et bas pour "
#~ "naviguer parmi les résultats."

#~ msgctxt "Select2 JS searching"
#~ msgid "Searching&hellip;"
#~ msgstr "Recherche en cours&hellip;"

#~ msgctxt "Select2 JS selection_too_long_1"
#~ msgid "You can only select 1 item"
#~ msgstr "Vous ne pouvez sélectionner qu’un seul élément"

#, php-format
#~ msgctxt "Select2 JS selection_too_long_n"
#~ msgid "You can only select %d items"
#~ msgstr "Vous ne pouvez sélectionner que %d éléments"

#~ msgid "Selected elements will be displayed in each result"
#~ msgstr "Les éléments sélectionnés seront affichés dans chaque résultat"

#~ msgid "Selection is greater than"
#~ msgstr "La sélection est supérieure à"

#~ msgid "Selection is less than"
#~ msgstr "La sélection est inférieure à"

#~ msgid "Send Trackbacks"
#~ msgstr "Envoyer des rétroliens"

#~ msgid "Separator"
#~ msgstr "Séparateur"

#~ msgid "Set the initial zoom level"
#~ msgstr "Définir le niveau de zoom (0 : monde ; 14 : ville ; 21 : rue)"

#~ msgid "Sets the textarea height"
#~ msgstr "Hauteur du champ"

#~ msgid "Settings"
#~ msgstr "Réglages"

#~ msgid "Show in REST API"
#~ msgstr "Afficher dans l’API REST"

# @ acf
#~ msgid "Show Media Upload Buttons?"
#~ msgstr "Afficher les boutons d‘ajout de médias ?"

#~ msgid "Show this field group if"
#~ msgstr "Montrer ce groupe quand"

#~ msgid "Show this field if"
#~ msgstr "Montrer ce champ si"

#~ msgid "Shown in field group list"
#~ msgstr "Affiché dans la liste des groupes de champs"

#~ msgid "Side"
#~ msgstr "Sur le côté"

#~ msgid "Single Value"
#~ msgstr "Valeur unique"

# @ acf
#~ msgid "Single word, no spaces. Underscores and dashes allowed"
#~ msgstr "Un seul mot, sans espace.<br />Les « _ » et « - » sont autorisés"

#~ msgid "Site"
#~ msgstr "Site"

#~ msgid "Site is up to date"
#~ msgstr "Le site est à jour"

#, php-format
#~| msgid "Site requires database upgrade from %s to %s"
#~ msgid "Site requires database upgrade from %1$s to %2$s"
#~ msgstr ""
#~ "Le site requiert une mise à niveau de la base de données de %1$s vers %2$s"

#~ msgid "Slug"
#~ msgstr "Identifiant (slug)"

#~ msgid "Sorry, this browser does not support geolocation"
#~ msgstr "Désolé, ce navigateur ne prend pas en charge la géolocalisation"

#~ msgid "Sorry, this field group is unavailable for diff comparison."
#~ msgstr ""
#~ "Désolé, ce groupe de champs n’est pas disponible pour une comparaison."

#~ msgid "Spam Detected"
#~ msgstr "Spam repéré"

#~ msgid "Specify the returned value on front end"
#~ msgstr "Spécifier la valeur retournée sur le site"

#~ msgid "Specify the value returned"
#~ msgstr "Définit la valeur retournée"

#~ msgid "Standard (WP metabox)"
#~ msgstr "Dans un bloc"

#~ msgid "Step Size"
#~ msgstr "Pas"

# @ acf
#~ msgid "Style"
#~ msgstr "Style"

# @ acf
#~ msgid "Stylised UI"
#~ msgstr "Interface avancée"

#~ msgid "Super Admin"
#~ msgstr "Super Administrateur"

#~ msgid "Sync"
#~ msgstr "Synchronisation"

# @ acf
#~ msgid "Sync available"
#~ msgstr "Synchronisation disponible"

#~ msgid "Sync changes"
#~ msgstr "Synchroniser"

#~ msgid "Tab"
#~ msgstr "Onglet"

#~ msgid "Tabs"
#~ msgstr "Onglets"

#~ msgid "Tags"
#~ msgstr "Mots-clés"

# @ acf
#~ msgid "Taxonomies"
#~ msgstr "Taxonomies"

# @ acf
#~ msgid "Taxonomy"
#~ msgstr "Taxonomie"

#~ msgid "Term ID"
#~ msgstr "ID du terme"

# @ acf
#~ msgid "Term Object"
#~ msgstr "Objet Terme"

#~ msgid "Test JS"
#~ msgstr "Test JS"

# @ acf
#~ msgid "Text"
#~ msgstr "Texte"

# @ acf
#~ msgid "Text Area"
#~ msgstr "Zone de texte"

# @ acf
#~ msgid "Text Only"
#~ msgstr "Texte brut seulement"

#~ msgid "Text shown when active"
#~ msgstr "Text affiché lorsqu’il est actif"

#~ msgid "Text shown when inactive"
#~ msgstr "Texte affiché lorsqu’il est désactivé"

#, php-format
#~| msgid "Thank you for updating to %s v%s!"
#~ msgid "Thank you for updating to %1$s v%2$s!"
#~ msgstr "Merci d’avoir mis à jour %1$s v%2$s !"

#, php-format
#~| msgid "The %s field can now be found in the %s field group"
#~ msgid "The %1$s field can now be found in the %2$s field group"
#~ msgstr "Le champ %1$s a été déplacé dans le groupe %2$s"

#~ msgid ""
#~ "The Advanced Custom Fields plugin provides a visual form builder to "
#~ "customize WordPress edit screens with extra fields, and an intuitive API "
#~ "to display custom field values in any theme template file."
#~ msgstr ""
#~ "Advanced Custom Fields fournit un constructeur visuel vous permettant de "
#~ "personnaliser les écrans de WordPress en ajoutant des champs "
#~ "additionnels, ainsi qu’une API intuitive pour afficher ces valeurs dans "
#~ "tous les modèles de votre thème."

#~ msgid ""
#~ "The changes you made will be lost if you navigate away from this page"
#~ msgstr "Les modifications seront perdues si vous quittez cette page"

#~ msgid ""
#~ "The following code can be used to register a local version of the "
#~ "selected field group(s). A local field group can provide many benefits "
#~ "such as faster load times, version control & dynamic fields/settings. "
#~ "Simply copy and paste the following code to your theme's functions.php "
#~ "file or include it within an external file."
#~ msgstr ""
#~ "Le code suivant peut être utilisé pour enregistrer une version locale du "
#~ "ou des groupes de champs sélectionnés. Un groupe de champ local apporte "
#~ "pas mal de bénéfices tels qu'un temps de chargement plus rapide, la "
#~ "gestion des versions et les champs/paramètres dynamiques. Copiez/collez "
#~ "simplement le code suivant dans le fichier functions.php de votre thème "
#~ "ou incluez-le depuis un autre fichier."

#, php-format
#~ msgid ""
#~ "The following sites require a DB upgrade. Check the ones you want to "
#~ "update and then click %s."
#~ msgstr ""
#~ "Les sites suivants nécessites une mise à niveau de la base de données. "
#~ "Sélectionnez ceux que vous voulez mettre à jour et cliquez sur %s."

#~ msgid "The format displayed when editing a post"
#~ msgstr ""
#~ "Format affiché lors de l’édition d’une publication depuis l’interface "
#~ "d’administration"

#~ msgid "The format returned via template functions"
#~ msgstr "Valeur retournée dans le modèle sur le site"

#~ msgid "The format used when saving a value"
#~ msgstr "Le format enregistré"

#~ msgid "The string \"field_\" may not be used at the start of a field name"
#~ msgstr "Le nom d’un champ ne peut pas commencer par \"field_\""

#~ msgid "This field cannot be moved until its changes have been saved"
#~ msgstr ""
#~ "Ce champ ne peut pas être déplacé tant que ses modifications n'ont pas "
#~ "été enregistrées"

# @ acf
#~ msgid "This is the name which will appear on the EDIT page"
#~ msgstr "Ce nom apparaîtra sur la page d‘édition"

#~ msgid ""
#~ "This version contains improvements to your database and requires an "
#~ "upgrade."
#~ msgstr ""
#~ "Cette version contient des améliorations de la base de données et "
#~ "nécessite une mise à niveau."

#~ msgid "Thumbnail"
#~ msgstr "Miniature"

#~ msgid "Time Picker"
#~ msgstr "Heure"

#~ msgid "TinyMCE will not be initialized until field is clicked"
#~ msgstr "TinyMCE ne sera pas initialisé tant que le champ n’est pas cliqué"

#~ msgid "Toggle"
#~ msgstr "Masquer/afficher"

#~ msgid "Toggle All"
#~ msgstr "Tout masquer/afficher"

# @ acf
#~ msgid "Toolbar"
#~ msgstr "Barre d‘outils"

#~ msgid "Tools"
#~ msgstr "Outils"

#~ msgid "Top aligned"
#~ msgstr "Aligné en haut"

#~ msgid "Top Level Page (no parent)"
#~ msgstr "Page de haut niveau (sans descendant)"

# @ acf
#~ msgid "True / False"
#~ msgstr "Vrai / Faux"

# @ acf
#~ msgid "Type"
#~ msgstr "Type"

#~ msgid "Unknown"
#~ msgstr "Inconnu"

# @ acf
#~ msgid "Update File"
#~ msgstr "Mettre à jour le fichier"

# @ acf
#~ msgid "Update Image"
#~ msgstr "Mettre à jour"

#~ msgid "Upgrade complete."
#~ msgstr "Mise à niveau terminée."

# @ acf
#~ msgid "Upgrade Database"
#~ msgstr "Mise à niveau de la base de données"

#~ msgid "Upgrade failed."
#~ msgstr "Mise à niveau échouée."

#~ msgid "Upgrade Sites"
#~ msgstr "Mettre à niveau les sites"

#~ msgid "Upgrade to Pro"
#~ msgstr "Mettre à niveau vers la version Pro"

#, php-format
#~ msgid "Upgrading data to version %s"
#~ msgstr "Migration des données vers la version %s"

#~ msgid "Uploaded to this post"
#~ msgstr "Liés à cette publication"

#~ msgid "Url"
#~ msgstr "URL"

#~ msgid "Use AJAX to lazy load choices?"
#~ msgstr "Utiliser AJAX pour charger les choix dynamiquement (lazy load) ?"

#~ msgid "User"
#~ msgstr "Utilisateur"

#~ msgid "User Array"
#~ msgstr "Données de l’Utilisateur (tableau)"

# @ acf
#~ msgid "User Form"
#~ msgstr "Formulaire utilisateur"

#~ msgid "User ID"
#~ msgstr "ID de l’utilisateur"

#~ msgid "User Object"
#~ msgstr "Objet Utilisateur"

# @ acf
#~ msgid "User Role"
#~ msgstr "Rôle utilisateur"

#, php-format
#~ msgid "User unable to add new %s"
#~ msgstr "Utilisateur incapable d’ajouter un nouveau %s"

#~ msgid "Users"
#~ msgstr "Utilisateurs"

#~ msgid "Validate Email"
#~ msgstr "Valider l’e-mail"

#~ msgid "Validation failed"
#~ msgstr "Échec de la validation"

#~ msgid "Validation successful"
#~ msgstr "Validé avec succès"

#~ msgid "Value"
#~ msgstr "Valeur"

#~ msgid "Value contains"
#~ msgstr "La valeur contient"

#~ msgid "Value is equal to"
#~ msgstr "La valeur est égale à"

#~ msgid "Value is greater than"
#~ msgstr "La valeur est supérieure à"

#~ msgid "Value is less than"
#~ msgstr "La valeur est inférieure à"

#~ msgid "Value is not equal to"
#~ msgstr "La valeur est différente de"

#~ msgid "Value matches pattern"
#~ msgstr "La valeur correspond au modèle"

#~ msgid "Value must be a number"
#~ msgstr "La valeur doit être un nombre"

#~ msgid "Value must be a valid URL"
#~ msgstr "La valeur doit être une URL valide"

#, php-format
#~ msgid "Value must be equal to or higher than %d"
#~ msgstr "La valeur doit être être supérieure ou égale à %d"

#, php-format
#~ msgid "Value must be equal to or lower than %d"
#~ msgstr "La valeur doit être inférieure ou égale à %d"

#, php-format
#~ msgid "Value must not exceed %d characters"
#~ msgstr "La valeur ne doit pas dépasser %d caractères."

#~ msgid "Various"
#~ msgstr "Divers"

#~ msgctxt "verb"
#~ msgid "Edit"
#~ msgstr "Modifier"

#~ msgctxt "verb"
#~ msgid "Select"
#~ msgstr "Choisir"

#~ msgctxt "verb"
#~ msgid "Update"
#~ msgstr "Mettre à jour"

#, php-format
#~ msgid "Version %s"
#~ msgstr "Version %s"

#~ msgid "Vertical"
#~ msgstr "Vertical"

# @ acf
#~ msgid "View details"
#~ msgstr "Voir les détails"

# @ acf
#~ msgid "View Field"
#~ msgstr "Voir le champ"

# @ default
#~ msgid "View Field Group"
#~ msgstr "Voir le groupe de champs"

#~ msgid "Viewing back end"
#~ msgstr "Depuis l’interface d’administration"

#~ msgid "Viewing front end"
#~ msgstr "Depuis le site"

#~ msgid "Visit website"
#~ msgstr "Visiter le site"

#~ msgid "Visual"
#~ msgstr "Visuel"

#~ msgid "Visual & Text"
#~ msgstr "Visuel & Texte brut"

#~ msgid "Visual Only"
#~ msgstr "Éditeur visuel seulement"

#~ msgid ""
#~ "We are fanatical about support, and want you to get the best out of your "
#~ "website with ACF. If you run into any difficulties, there are several "
#~ "places you can find help:"
#~ msgstr ""
#~ "Nous sommes des fanatiques du support et nous souhaitons que vous ayez la "
#~ "meilleure expérience avec ACF. Si vous avez des difficultés, voici "
#~ "plusieurs solutions pour obtenir de l’aide :"

#~ msgid "Week Starts On"
#~ msgstr "Les semaines commencent le"

#~ msgid "Widget"
#~ msgstr "Widget"

#~ msgid "Widgets"
#~ msgstr "Widgets"

#~ msgid "width"
#~ msgstr "largeur"

#~ msgid "Wrapper Attributes"
#~ msgstr "Attributs du conteneur"

# @ acf
#~ msgid "Wysiwyg Editor"
#~ msgstr "Éditeur de contenu"

#~ msgid "Zoom"
#~ msgstr "Zoom"
