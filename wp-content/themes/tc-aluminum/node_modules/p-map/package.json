{"name": "p-map", "version": "1.2.0", "description": "Map over promises concurrently", "license": "MIT", "repository": "sindresorhus/p-map", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "map", "resolved", "wait", "collection", "iterable", "iterator", "race", "fulfilled", "async", "await", "promises", "concurrently", "concurrency", "parallel", "bluebird"], "devDependencies": {"ava": "*", "delay": "^2.0.0", "in-range": "^1.0.0", "random-int": "^1.0.0", "time-span": "^2.0.0", "xo": "*"}}