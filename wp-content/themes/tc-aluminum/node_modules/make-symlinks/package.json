{"name": "make-symlinks", "version": "2.0.1", "description": "Create symbolic link (file symlink) using glob", "license": "MIT", "repository": "iguntur/make-symlinks", "main": "dist/index.js", "typings": "dist/index.d.ts", "files": ["dist/index.d.ts", "dist/index.js"], "engines": {"node": ">=6"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/iguntur"}, "scripts": {"prepublishOnly": "npm run build:release", "clean": "rm -rf dist", "build": "npm run clean && tsc", "build:release": "npm run test && rm -rf dist/*.test.*", "test": "npm run build && ava"}, "keywords": ["create", "glob", "link", "links", "ln", "make", "symbolic", "symlinks", "tool", "unlink", "utility", "wildcard"], "dependencies": {"del": "^3.0.0", "globby": "^8.0.1", "pify": "^4.0.1"}, "devDependencies": {"@guntur/tsconfig": "^1.0.0", "@types/node": "^10.12.15", "ava": "^1.0.1", "is-symbolic-link": "^2.0.3", "tempy": "^0.2.1", "typescript": "^3.2.2"}}