"use strict";var t=require("react"),e=require("@babel/runtime/helpers/inheritsLoose"),n=require("prop-types"),r=require("tiny-warning");function o(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var i=o(t),u=o(e),a=o(n),s=o(r),c="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{};function l(t){var e=[];return{on:function(t){e.push(t)},off:function(t){e=e.filter((function(e){return e!==t}))},get:function(){return t},set:function(n,r){t=n,e.forEach((function(e){return e(t,r)}))}}}var p=i.default.createContext||function(e,n){var r,o,i,p="__create-react-context-"+((c[i="__global_unique_id__"]=(c[i]||0)+1)+"__"),f=function(t){function e(){var e;return(e=t.apply(this,arguments)||this).emitter=l(e.props.value),e}u.default(e,t);var r=e.prototype;return r.getChildContext=function(){var t;return(t={})[p]=this.emitter,t},r.componentWillReceiveProps=function(t){if(this.props.value!==t.value){var e,r=this.props.value,o=t.value;((i=r)===(u=o)?0!==i||1/i==1/u:i!=i&&u!=u)?e=0:(e="function"==typeof n?n(r,o):**********,"production"!==process.env.NODE_ENV&&s.default((**********&e)===e,"calculateChangedBits: Expected the return value to be a 31-bit integer. Instead received: "+e),0!==(e|=0)&&this.emitter.set(t.value,e))}var i,u},r.render=function(){return this.props.children},e}(t.Component);f.childContextTypes=((r={})[p]=a.default.object.isRequired,r);var d=function(t){function n(){var e;return(e=t.apply(this,arguments)||this).state={value:e.getValue()},e.onUpdate=function(t,n){0!=((0|e.observedBits)&n)&&e.setState({value:e.getValue()})},e}u.default(n,t);var r=n.prototype;return r.componentWillReceiveProps=function(t){var e=t.observedBits;this.observedBits=null==e?**********:e},r.componentDidMount=function(){this.context[p]&&this.context[p].on(this.onUpdate);var t=this.props.observedBits;this.observedBits=null==t?**********:t},r.componentWillUnmount=function(){this.context[p]&&this.context[p].off(this.onUpdate)},r.getValue=function(){return this.context[p]?this.context[p].get():e},r.render=function(){return(t=this.props.children,Array.isArray(t)?t[0]:t)(this.state.value);var t},n}(t.Component);return d.contextTypes=((o={})[p]=a.default.object,o),{Provider:f,Consumer:d}};module.exports=p;