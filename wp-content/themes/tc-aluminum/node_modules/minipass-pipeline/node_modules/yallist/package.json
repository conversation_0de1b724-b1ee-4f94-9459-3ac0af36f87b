{"name": "yallist", "version": "4.0.0", "description": "Yet Another Linked List", "main": "yallist.js", "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "dependencies": {}, "devDependencies": {"tap": "^12.1.0"}, "scripts": {"test": "tap test/*.js --100", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC"}