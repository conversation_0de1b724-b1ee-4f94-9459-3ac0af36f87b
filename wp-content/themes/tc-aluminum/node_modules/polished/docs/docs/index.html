<!doctype html>
<html>
<head>
  <meta charset='utf-8' />
  <title>polished | Documentation</title>
  <meta name='viewport' content='width=device-width,initial-scale=1'>
  <meta name="description" content="A lightweight toolset for writing styles in JavaScript." />

  <!-- Twitter Card data -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:site" content="@b_hough">
  <meta name="twitter:title" content="✨ polished | Documentation">
  <meta name="twitter:description" content="A lightweight toolset for writing styles in JavaScript.">
  <meta name="twitter:creator" content="@b_hough">
  <meta name="twitter:image" content="/assets/meta.png">

  <!-- Open Graph data -->
  <meta property="og:title" content="✨ polished | Documentation" />
  <meta property="og:type" content="article" />
  <meta property="og:url" content="https://styled-components.com/color-schemer/" />
  <meta property="og:image" content="/assets/meta.png" />
  <meta property="og:description" content="A lightweight toolset for writing styles in JavaScript." />
  <meta property="og:site_name" content="✨ polished" />

  <link href='/assets/bass.css' type='text/css' rel='stylesheet' />
  <link href='/assets/github.css' type='text/css' rel='stylesheet' />
  <link href='/assets/style.css' type='text/css' rel='stylesheet' />
  <link rel="shortcut icon" href="/favicon.png" />
</head>
<body class='documentation'>
  
    <div class='max-width-4 mx-auto'>
  <div class='clearfix md-mxn2'>
    <div class='fixed xs-hide sm-hide fix-3 overflow-auto max-height-100'>
      <div class='py1 px2'>
        <h3 class='mb0 no-anchor'></h3>
        <input
          placeholder='Filter'
          id='filter-input'
          class='col12 block input'
          type='text' />
        <div id='toc'>
          <ul class='list-reset h5 py1-ul'>
            
              
              <li><a
                href='#installation'
                class="h5 bold black caps">
                Installation
                
              </a>
              
              </li>
            
              
              <li><a
                href='#usage'
                class="h5 bold black caps">
                Usage
                
              </a>
              
              </li>
            
              
              <li><a
                href='#mixins'
                class="h5 bold black caps">
                Mixins
                
              </a>
              
              </li>
            
              
              <li><a
                href='#between'
                class="">
                between
                
              </a>
              
              </li>
            
              
              <li><a
                href='#clearfix'
                class="">
                clearFix
                
              </a>
              
              </li>
            
              
              <li><a
                href='#cover'
                class="">
                cover
                
              </a>
              
              </li>
            
              
              <li><a
                href='#ellipsis'
                class="">
                ellipsis
                
              </a>
              
              </li>
            
              
              <li><a
                href='#fluidrange'
                class="">
                fluidRange
                
              </a>
              
              </li>
            
              
              <li><a
                href='#fontface'
                class="">
                fontFace
                
              </a>
              
              </li>
            
              
              <li><a
                href='#hidetext'
                class="">
                hideText
                
              </a>
              
              </li>
            
              
              <li><a
                href='#hidevisually'
                class="">
                hideVisually
                
              </a>
              
              </li>
            
              
              <li><a
                href='#hidpi'
                class="">
                hiDPI
                
              </a>
              
              </li>
            
              
              <li><a
                href='#lineargradient'
                class="">
                linearGradient
                
              </a>
              
              </li>
            
              
              <li><a
                href='#normalize'
                class="">
                normalize
                
              </a>
              
              </li>
            
              
              <li><a
                href='#radialgradient'
                class="">
                radialGradient
                
              </a>
              
              </li>
            
              
              <li><a
                href='#retinaimage'
                class="">
                retinaImage
                
              </a>
              
              </li>
            
              
              <li><a
                href='#timingfunctions'
                class="">
                timingFunctions
                
              </a>
              
              </li>
            
              
              <li><a
                href='#triangle'
                class="">
                triangle
                
              </a>
              
              </li>
            
              
              <li><a
                href='#wordwrap'
                class="">
                wordWrap
                
              </a>
              
              </li>
            
              
              <li><a
                href='#color'
                class="h5 bold black caps">
                Color
                
              </a>
              
              </li>
            
              
              <li><a
                href='#adjusthue'
                class="">
                adjustHue
                
              </a>
              
              </li>
            
              
              <li><a
                href='#complement'
                class="">
                complement
                
              </a>
              
              </li>
            
              
              <li><a
                href='#darken'
                class="">
                darken
                
              </a>
              
              </li>
            
              
              <li><a
                href='#desaturate'
                class="">
                desaturate
                
              </a>
              
              </li>
            
              
              <li><a
                href='#getcontrast'
                class="">
                getContrast
                
              </a>
              
              </li>
            
              
              <li><a
                href='#getluminance'
                class="">
                getLuminance
                
              </a>
              
              </li>
            
              
              <li><a
                href='#grayscale'
                class="">
                grayscale
                
              </a>
              
              </li>
            
              
              <li><a
                href='#hsl'
                class="">
                hsl
                
              </a>
              
              </li>
            
              
              <li><a
                href='#hsla'
                class="">
                hsla
                
              </a>
              
              </li>
            
              
              <li><a
                href='#hsltocolorstring'
                class="">
                hslToColorString
                
              </a>
              
              </li>
            
              
              <li><a
                href='#invert'
                class="">
                invert
                
              </a>
              
              </li>
            
              
              <li><a
                href='#lighten'
                class="">
                lighten
                
              </a>
              
              </li>
            
              
              <li><a
                href='#meetscontrastguidelines'
                class="">
                meetsContrastGuidelines
                
              </a>
              
              </li>
            
              
              <li><a
                href='#mix'
                class="">
                mix
                
              </a>
              
              </li>
            
              
              <li><a
                href='#opacify'
                class="">
                opacify
                
              </a>
              
              </li>
            
              
              <li><a
                href='#parsetohsl'
                class="">
                parseToHsl
                
              </a>
              
              </li>
            
              
              <li><a
                href='#parsetorgb'
                class="">
                parseToRgb
                
              </a>
              
              </li>
            
              
              <li><a
                href='#readablecolor'
                class="">
                readableColor
                
              </a>
              
              </li>
            
              
              <li><a
                href='#rgb'
                class="">
                rgb
                
              </a>
              
              </li>
            
              
              <li><a
                href='#rgba'
                class="">
                rgba
                
              </a>
              
              </li>
            
              
              <li><a
                href='#rgbtocolorstring'
                class="">
                rgbToColorString
                
              </a>
              
              </li>
            
              
              <li><a
                href='#saturate'
                class="">
                saturate
                
              </a>
              
              </li>
            
              
              <li><a
                href='#sethue'
                class="">
                setHue
                
              </a>
              
              </li>
            
              
              <li><a
                href='#setlightness'
                class="">
                setLightness
                
              </a>
              
              </li>
            
              
              <li><a
                href='#setsaturation'
                class="">
                setSaturation
                
              </a>
              
              </li>
            
              
              <li><a
                href='#shade'
                class="">
                shade
                
              </a>
              
              </li>
            
              
              <li><a
                href='#tint'
                class="">
                tint
                
              </a>
              
              </li>
            
              
              <li><a
                href='#tocolorstring'
                class="">
                toColorString
                
              </a>
              
              </li>
            
              
              <li><a
                href='#transparentize'
                class="">
                transparentize
                
              </a>
              
              </li>
            
              
              <li><a
                href='#math'
                class="h5 bold black caps">
                Math
                
              </a>
              
              </li>
            
              
              <li><a
                href='#math'
                class="">
                math
                
              </a>
              
              </li>
            
              
              <li><a
                href='#shorthands'
                class="h5 bold black caps">
                Shorthands
                
              </a>
              
              </li>
            
              
              <li><a
                href='#animation'
                class="">
                animation
                
              </a>
              
              </li>
            
              
              <li><a
                href='#backgroundimages'
                class="">
                backgroundImages
                
              </a>
              
              </li>
            
              
              <li><a
                href='#backgrounds'
                class="">
                backgrounds
                
              </a>
              
              </li>
            
              
              <li><a
                href='#border'
                class="">
                border
                
              </a>
              
              </li>
            
              
              <li><a
                href='#bordercolor'
                class="">
                borderColor
                
              </a>
              
              </li>
            
              
              <li><a
                href='#borderradius'
                class="">
                borderRadius
                
              </a>
              
              </li>
            
              
              <li><a
                href='#borderstyle'
                class="">
                borderStyle
                
              </a>
              
              </li>
            
              
              <li><a
                href='#borderwidth'
                class="">
                borderWidth
                
              </a>
              
              </li>
            
              
              <li><a
                href='#buttons'
                class="">
                buttons
                
              </a>
              
              </li>
            
              
              <li><a
                href='#margin'
                class="">
                margin
                
              </a>
              
              </li>
            
              
              <li><a
                href='#padding'
                class="">
                padding
                
              </a>
              
              </li>
            
              
              <li><a
                href='#position'
                class="">
                position
                
              </a>
              
              </li>
            
              
              <li><a
                href='#size'
                class="">
                size
                
              </a>
              
              </li>
            
              
              <li><a
                href='#textinputs'
                class="">
                textInputs
                
              </a>
              
              </li>
            
              
              <li><a
                href='#transitions'
                class="">
                transitions
                
              </a>
              
              </li>
            
              
              <li><a
                href='#helpers'
                class="h5 bold black caps">
                Helpers
                
              </a>
              
              </li>
            
              
              <li><a
                href='#cssvar'
                class="">
                cssVar
                
              </a>
              
              </li>
            
              
              <li><a
                href='#directionalproperty'
                class="">
                directionalProperty
                
              </a>
              
              </li>
            
              
              <li><a
                href='#em'
                class="">
                em
                
              </a>
              
              </li>
            
              
              <li><a
                href='#getvalueandunit'
                class="">
                getValueAndUnit
                
              </a>
              
              </li>
            
              
              <li><a
                href='#important'
                class="">
                important
                
              </a>
              
              </li>
            
              
              <li><a
                href='#modularscale'
                class="">
                modularScale
                
              </a>
              
              </li>
            
              
              <li><a
                href='#rem'
                class="">
                rem
                
              </a>
              
              </li>
            
              
              <li><a
                href='#remtopx'
                class="">
                remToPx
                
              </a>
              
              </li>
            
              
              <li><a
                href='#stripunit'
                class="">
                stripUnit
                
              </a>
              
              </li>
            
              
              <li><a
                href='#easings'
                class="h5 bold black caps">
                Easings
                
              </a>
              
              </li>
            
              
              <li><a
                href='#easein'
                class="">
                easeIn
                
              </a>
              
              </li>
            
              
              <li><a
                href='#easeinout'
                class="">
                easeInOut
                
              </a>
              
              </li>
            
              
              <li><a
                href='#easeout'
                class="">
                easeOut
                
              </a>
              
              </li>
            
              
              <li><a
                href='#types'
                class="h5 bold black caps">
                Types
                
              </a>
              
              </li>
            
              
              <li><a
                href='#fluidrangeconfiguration'
                class="">
                FluidRangeConfiguration
                
              </a>
              
              </li>
            
              
              <li><a
                href='#fontfaceconfiguration'
                class="">
                FontFaceConfiguration
                
              </a>
              
              </li>
            
              
              <li><a
                href='#hslcolor'
                class="">
                HslColor
                
              </a>
              
              </li>
            
              
              <li><a
                href='#hslacolor'
                class="">
                HslaColor
                
              </a>
              
              </li>
            
              
              <li><a
                href='#interactionstate'
                class="">
                InteractionState
                
              </a>
              
              </li>
            
              
              <li><a
                href='#modularscaleratio'
                class="">
                ModularScaleRatio
                
              </a>
              
              </li>
            
              
              <li><a
                href='#radialgradientconfiguration'
                class="">
                RadialGradientConfiguration
                
              </a>
              
              </li>
            
              
              <li><a
                href='#rgbacolor'
                class="">
                RgbaColor
                
              </a>
              
              </li>
            
              
              <li><a
                href='#rgbcolor'
                class="">
                RgbColor
                
              </a>
              
              </li>
            
              
              <li><a
                href='#sidekeyword'
                class="">
                SideKeyword
                
              </a>
              
              </li>
            
              
              <li><a
                href='#styles'
                class="">
                Styles
                
              </a>
              
              </li>
            
              
              <li><a
                href='#triangleconfiguration'
                class="">
                TriangleConfiguration
                
              </a>
              
              </li>
            
              
              <li><a
                href='#contrastscores'
                class="">
                ContrastScores
                
              </a>
              
              </li>
            
              
              <li><a
                href='#lineargradientconfiguration'
                class="">
                LinearGradientConfiguration
                
              </a>
              
              </li>
            
              
              <li><a
                href='#timingfunction'
                class="">
                TimingFunction
                
              </a>
              
              </li>
            
          </ul>
        </div>
        <div class='mt1 h6 quiet'>
          <a href='http://documentation.js.org/reading-documentation.html'>Need help reading this?</a>
        </div>
      </div>
    </div>
    <div class='fix-margin-3'>
      
        
          <div class='keyline-top-not py2'><section class='section py2 clearfix'>

  <h2 class="section__heading" id='installation' class='mt0'>
    Installation
  </h2>

  
    <div class='installation'><code class='command'>npm install --save polished</code></div>

  
</section>
</div>
        
      
        
          <div class='keyline-top-not py2'><section class='section py2 clearfix'>

  <h2 class="section__heading" id='usage' class='mt0'>
    Usage
  </h2>

  
    <div class='usage'><code class='javascript'>import { lighten, modularScale } from 'polished'</code></div>

  
</section>
</div>
        
      
        
          <div class='keyline-top-not py2'><section class='section py2 clearfix'>

  <h2 class="section__heading" id='mixins' class='mt0'>
    Mixins
  </h2>

  
    

  
</section>
</div>
        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='between'>
      between
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns a CSS calc formula for linear interpolation of a property between two values. Accepts optional minScreen (defaults to '320px') and maxScreen (defaults to '1200px').</p>


  <div class='pre p1 fill-light mt0'>between(fromSize: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), toSize: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), minScreen: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, maxScreen: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>fromSize</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>toSize</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>minScreen</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&#39;320px&#39;</code>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>maxScreen</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&#39;1200px&#39;</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  fontSize: between('20px', '100px', '400px', '<span class="hljs-number">1000</span>px'),
  fontSize: between('20px', '100px')
}

<span class="hljs-comment">// styled-components usage</span>
const div = styled.div`
  fontSize: ${between('20px', '100px', '400px', '<span class="hljs-number">1000</span>px')};
  fontSize: ${between('20px', '100px')}
`

<span class="hljs-comment">// CSS as JS Output</span>

h1: {
  'fontSize': 'calc(-33.<span class="hljs-number">33333333333334</span>px + 13.<span class="hljs-number">33333333333333</span>4vw)',
  'fontSize': 'calc(-9.<span class="hljs-number">09090909090909</span>3px + 9.<span class="hljs-number">09090909090909</span>2vw)'
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='clearfix'>
      clearFix
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>CSS to contain a float (credit to CSSMojo).</p>


  <div class='pre p1 fill-light mt0'>clearFix(parent: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>parent</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&#39;&amp;&#39;</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
   ...clearFix(),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  <span class="hljs-subst">${clearFix()}</span>
`</span>

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-string">'&amp;::after'</span>: {
  <span class="hljs-string">'clear'</span>: <span class="hljs-string">'both'</span>,
  <span class="hljs-string">'content'</span>: <span class="hljs-string">'""'</span>,
  <span class="hljs-string">'display'</span>: <span class="hljs-string">'table'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='cover'>
      cover
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>CSS to fully cover an area. Can optionally be passed an offset to act as a "padding".</p>


  <div class='pre p1 fill-light mt0'>cover(offset: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>offset</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)
            = <code>0</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  ...cover()
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  ${cover()}
`

<span class="hljs-comment">// CSS as JS Output</span>

div: {
  <span class="hljs-symbol">'position</span>': <span class="hljs-symbol">'absolute</span>',
  <span class="hljs-symbol">'top</span>': <span class="hljs-string">'0'</span>,
  <span class="hljs-symbol">'right</span>: <span class="hljs-string">'0'</span>,
  <span class="hljs-symbol">'bottom</span>': <span class="hljs-string">'0'</span>,
  <span class="hljs-symbol">'left</span>: <span class="hljs-string">'0'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='ellipsis'>
      ellipsis
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>CSS to represent truncated text with an ellipsis. You can optionally pass a max-width and number of lines before truncating.</p>


  <div class='pre p1 fill-light mt0'>ellipsis(width: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)?, lines: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>width</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>lines</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
            = <code>1</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...ellipsis(<span class="hljs-string">'250px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{ellipsis(<span class="hljs-string">'250px'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span>: {
  <span class="hljs-string">'display'</span>: <span class="hljs-string">'inline-block'</span>,
  <span class="hljs-string">'maxWidth'</span>: <span class="hljs-string">'250px'</span>,
  <span class="hljs-string">'overflow'</span>: <span class="hljs-string">'hidden'</span>,
  <span class="hljs-string">'textOverflow'</span>: <span class="hljs-string">'ellipsis'</span>,
  <span class="hljs-string">'whiteSpace'</span>: <span class="hljs-string">'nowrap'</span>,
  <span class="hljs-string">'wordWrap'</span>: <span class="hljs-string">'normal'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='fluidrange'>
      fluidRange
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns a set of media queries that resizes a property (or set of properties) between a provided fromSize and toSize. Accepts optional minScreen (defaults to '320px') and maxScreen (defaults to '1200px') to constrain the interpolation.</p>


  <div class='pre p1 fill-light mt0'>fluidRange(cssProp: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#fluidrangeconfiguration">FluidRangeConfiguration</a>> | <a href="#fluidrangeconfiguration">FluidRangeConfiguration</a>), minScreen: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, maxScreen: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cssProp</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#fluidrangeconfiguration">FluidRangeConfiguration</a>> | <a href="#fluidrangeconfiguration">FluidRangeConfiguration</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>minScreen</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&#39;320px&#39;</code>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>maxScreen</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&#39;1200px&#39;</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  ...fluidRange(
   {
       prop: <span class="hljs-string">'padding'</span>,
       fromSize: <span class="hljs-string">'20px'</span>,
       toSize: <span class="hljs-string">'100px'</span>,
     },
     <span class="hljs-string">'400px'</span>,
     <span class="hljs-string">'1000px'</span>,
   )
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  ${fluidRange(
     {
       prop: <span class="hljs-string">'padding'</span>,
       fromSize: <span class="hljs-string">'20px'</span>,
       toSize: <span class="hljs-string">'100px'</span>,
     },
     <span class="hljs-string">'400px'</span>,
     <span class="hljs-string">'1000px'</span>,
   )}
`

<span class="hljs-comment">// CSS as JS Output</span>

div: {
  <span class="hljs-string">"@media (min-width: 1000px)"</span>: <span class="hljs-built_in">Object</span> {
    <span class="hljs-string">"padding"</span>: <span class="hljs-string">"100px"</span>,
  },
  <span class="hljs-string">"@media (min-width: 400px)"</span>: <span class="hljs-built_in">Object</span> {
    <span class="hljs-string">"padding"</span>: <span class="hljs-string">"calc(-33.33333333333334px + 13.333333333333334vw)"</span>,
  },
  <span class="hljs-string">"padding"</span>: <span class="hljs-string">"20px"</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='fontface'>
      fontFace
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>CSS for a @font-face declaration. Defaults to check for local copies of the font on the user's machine. You can disable this by passing <code>null</code> to localFonts.</p>


  <div class='pre p1 fill-light mt0'>fontFace($0: <a href="#fontfaceconfiguration">FontFaceConfiguration</a>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>$0</span> <code class='quiet'>(<a href="#fontfaceconfiguration">FontFaceConfiguration</a>)</code>
	    
          </div>
          
          <table class='mt1 mb2 fixed-table h5 col-12'>
            <colgroup>
              <col width='30%' />
              <col width='70%' />
            </colgroup>
            <thead>
              <tr class='bold fill-light'>
                <th>Name</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody class='mt1'>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fontFamily</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fontFilePath</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fontStretch</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fontStyle</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fontVariant</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fontWeight</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fileFormats</span> <code class='quiet'>any</code>
                  
                    (default <code>[&#39;eot&#39;,&#39;woff2&#39;,&#39;woff&#39;,&#39;ttf&#39;,&#39;svg&#39;]</code>)
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.formatHint</span> <code class='quiet'>any</code>
                  
                    (default <code>false</code>)
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.localFonts</span> <code class='quiet'>any</code>
                  
                    (default <code>[fontFamily]</code>)
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.unicodeRange</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fontDisplay</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fontVariationSettings</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fontFeatureSettings</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
            </tbody>
          </table>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object basic usage</span>
<span class="hljs-keyword">const</span> styles = {
   ...fontFace({
     <span class="hljs-symbol">'fontFamily</span>': <span class="hljs-symbol">'Sans</span>-Pro',
     <span class="hljs-symbol">'fontFilePath</span>': <span class="hljs-symbol">'path</span>/to/file'
   })
}

<span class="hljs-comment">// styled-components basic usage</span>
<span class="hljs-keyword">const</span> GlobalStyle = createGlobalStyle`${
  fontFace({
    <span class="hljs-symbol">'fontFamily</span>': <span class="hljs-symbol">'Sans</span>-Pro',
    <span class="hljs-symbol">'fontFilePath</span>': <span class="hljs-symbol">'path</span>/to/file'
  }
)}`

<span class="hljs-comment">// CSS as JS Output</span>

'@font-face': {
  <span class="hljs-symbol">'fontFamily</span>': <span class="hljs-symbol">'Sans</span>-Pro',
  <span class="hljs-symbol">'src</span>': <span class="hljs-symbol">'url</span>(<span class="hljs-string">"path/to/file.eot"</span>), url(<span class="hljs-string">"path/to/file.woff2"</span>), url(<span class="hljs-string">"path/to/file.woff"</span>), url(<span class="hljs-string">"path/to/file.ttf"</span>), url(<span class="hljs-string">"path/to/file.svg"</span>)',
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='hidetext'>
      hideText
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>CSS to hide text to show a background image in a SEO-friendly way.</p>


  <div class='pre p1 fill-light mt0'>hideText(): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-string">'backgroundImage'</span>: <span class="hljs-string">'url(logo.png)'</span>,
  ...hideText(),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  backgroundImage: url(logo.png);
  <span class="hljs-subst">${hideText()}</span>;
`</span>

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-string">'div'</span>: {
  <span class="hljs-string">'backgroundImage'</span>: <span class="hljs-string">'url(logo.png)'</span>,
  <span class="hljs-string">'textIndent'</span>: <span class="hljs-string">'101%'</span>,
  <span class="hljs-string">'overflow'</span>: <span class="hljs-string">'hidden'</span>,
  <span class="hljs-string">'whiteSpace'</span>: <span class="hljs-string">'nowrap'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='hidevisually'>
      hideVisually
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>CSS to hide content visually but remain accessible to screen readers.
from <a href="https://github.com/h5bp/html5-boilerplate/blob/9a176f57af1cfe8ec70300da4621fb9b07e5fa31/src/css/main.css#L121">HTML5 Boilerplate</a></p>


  <div class='pre p1 fill-light mt0'>hideVisually(): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  ...hideVisually(),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  <span class="hljs-subst">${hideVisually()}</span>;
`</span>

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-string">'div'</span>: {
  <span class="hljs-string">'border'</span>: <span class="hljs-string">'0'</span>,
  <span class="hljs-string">'clip'</span>: <span class="hljs-string">'rect(0 0 0 0)'</span>,
  <span class="hljs-string">'height'</span>: <span class="hljs-string">'1px'</span>,
  <span class="hljs-string">'margin'</span>: <span class="hljs-string">'-1px'</span>,
  <span class="hljs-string">'overflow'</span>: <span class="hljs-string">'hidden'</span>,
  <span class="hljs-string">'padding'</span>: <span class="hljs-string">'0'</span>,
  <span class="hljs-string">'position'</span>: <span class="hljs-string">'absolute'</span>,
  <span class="hljs-string">'whiteSpace'</span>: <span class="hljs-string">'nowrap'</span>,
  <span class="hljs-string">'width'</span>: <span class="hljs-string">'1px'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='hidpi'>
      hiDPI
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Generates a media query to target HiDPI devices.</p>


  <div class='pre p1 fill-light mt0'>hiDPI(ratio: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ratio</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>
            = <code>1.3</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
 [hiDPI(<span class="hljs-number">1.5</span>)]: {
   width: <span class="hljs-number">200</span>px;
 }
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  ${hiDPI(<span class="hljs-number">1.5</span>)} {
    width: <span class="hljs-number">200</span>px;
  }
`

<span class="hljs-comment">// CSS as JS Output</span>

'@media only screen and (-webkit-<span class="hljs-built_in">min</span>-device-pixel-ratio: <span class="hljs-number">1.5</span>),
 only screen and (<span class="hljs-built_in">min</span>--moz-device-pixel-ratio: <span class="hljs-number">1.5</span>),
 only screen and (-o-<span class="hljs-built_in">min</span>-device-pixel-ratio: <span class="hljs-number">1.5</span>/<span class="hljs-number">1</span>),
 only screen and (<span class="hljs-built_in">min</span>-resolution: <span class="hljs-number">144</span>dpi),
 only screen and (<span class="hljs-built_in">min</span>-resolution: <span class="hljs-number">1.5</span>dppx)': {
  'width': '<span class="hljs-number">200</span>px',
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='lineargradient'>
      linearGradient
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>CSS for declaring a linear gradient, including a fallback background-color. The fallback is either the first color-stop or an explicitly passed fallback color.</p>


  <div class='pre p1 fill-light mt0'>linearGradient($0: <a href="#lineargradientconfiguration">LinearGradientConfiguration</a>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>$0</span> <code class='quiet'>(<a href="#lineargradientconfiguration">LinearGradientConfiguration</a>)</code>
	    
          </div>
          
          <table class='mt1 mb2 fixed-table h5 col-12'>
            <colgroup>
              <col width='30%' />
              <col width='70%' />
            </colgroup>
            <thead>
              <tr class='bold fill-light'>
                <th>Name</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody class='mt1'>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.colorStops</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fallback</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.toDirection</span> <code class='quiet'>any</code>
                  
                    (default <code>&#39;&#39;</code>)
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
            </tbody>
          </table>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...linearGradient({
<span class="hljs-string">colorStops:</span> [<span class="hljs-string">'#00FFFF 0%'</span>, <span class="hljs-string">'rgba(0, 0, 255, 0) 50%'</span>, <span class="hljs-string">'#0000FF 95%'</span>],
<span class="hljs-string">toDirection:</span> <span class="hljs-string">'to top right'</span>,
<span class="hljs-string">fallback:</span> <span class="hljs-string">'#FFF'</span>,
})
}

<span class="hljs-comment">// styled-components usage</span>
const div = styled.div`
  ${linearGradient({
<span class="hljs-string">colorStops:</span> [<span class="hljs-string">'#00FFFF 0%'</span>, <span class="hljs-string">'rgba(0, 0, 255, 0) 50%'</span>, <span class="hljs-string">'#0000FF 95%'</span>],
<span class="hljs-string">toDirection:</span> <span class="hljs-string">'to top right'</span>,
<span class="hljs-string">fallback:</span> <span class="hljs-string">'#FFF'</span>,
})}
`

<span class="hljs-comment">// CSS as JS Output</span>
<span class="hljs-symbol">
div:</span> {
  <span class="hljs-string">'backgroundColor'</span>: <span class="hljs-string">'#FFF'</span>,
  <span class="hljs-string">'backgroundImage'</span>: <span class="hljs-string">'linear-gradient(to top right, #00FFFF 0%, rgba(0, 0, 255, 0) 50%, #0000FF 95%)'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='normalize'>
      normalize
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>CSS to normalize abnormalities across browsers (normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css)</p>


  <div class='pre p1 fill-light mt0'>normalize(): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#styles">Styles</a>></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#styles">Styles</a>></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
   ...normalize(),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> GlobalStyle = createGlobalStyle`${normalize()}`

<span class="hljs-comment">// CSS as JS Output</span>

html {
  lineHeight: <span class="hljs-number">1.15</span>,
  textSizeAdjust: <span class="hljs-number">100</span>%,
} ...</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='radialgradient'>
      radialGradient
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>CSS for declaring a radial gradient, including a fallback background-color. The fallback is either the first color-stop or an explicitly passed fallback color.</p>


  <div class='pre p1 fill-light mt0'>radialGradient($0: <a href="#radialgradientconfiguration">RadialGradientConfiguration</a>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>$0</span> <code class='quiet'>(<a href="#radialgradientconfiguration">RadialGradientConfiguration</a>)</code>
	    
          </div>
          
          <table class='mt1 mb2 fixed-table h5 col-12'>
            <colgroup>
              <col width='30%' />
              <col width='70%' />
            </colgroup>
            <thead>
              <tr class='bold fill-light'>
                <th>Name</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody class='mt1'>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.colorStops</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.extent</span> <code class='quiet'>any</code>
                  
                    (default <code>&#39;&#39;</code>)
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.fallback</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.position</span> <code class='quiet'>any</code>
                  
                    (default <code>&#39;&#39;</code>)
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.shape</span> <code class='quiet'>any</code>
                  
                    (default <code>&#39;&#39;</code>)
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
            </tbody>
          </table>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'>// Styles <span class="hljs-keyword">as</span> object usage
const styles = {
  ...radialGradient({
    colorStop<span class="hljs-variable">s:</span> [<span class="hljs-string">'#00FFFF 0%'</span>, <span class="hljs-string">'rgba(0, 0, 255, 0) 50%'</span>, <span class="hljs-string">'#0000FF 95%'</span>],
    exten<span class="hljs-variable">t:</span> <span class="hljs-string">'farthest-corner at 45px 45px'</span>,
    position: <span class="hljs-string">'center'</span>,
    shape: <span class="hljs-string">'ellipse'</span>,
  })
}

// styled-components usage
const div = styled.div`
  ${radialGradient({
    colorStop<span class="hljs-variable">s:</span> [<span class="hljs-string">'#00FFFF 0%'</span>, <span class="hljs-string">'rgba(0, 0, 255, 0) 50%'</span>, <span class="hljs-string">'#0000FF 95%'</span>],
    exten<span class="hljs-variable">t:</span> <span class="hljs-string">'farthest-corner at 45px 45px'</span>,
    position: <span class="hljs-string">'center'</span>,
    shape: <span class="hljs-string">'ellipse'</span>,
  })}
`

// CSS <span class="hljs-keyword">as</span> JS Output

<span class="hljs-keyword">di</span><span class="hljs-variable">v:</span> {
  <span class="hljs-string">'backgroundColor'</span>: <span class="hljs-string">'#00FFFF'</span>,
  <span class="hljs-string">'backgroundImage'</span>: <span class="hljs-string">'radial-gradient(center ellipse farthest-corner at 45px 45px, #00FFFF 0%, rgba(0, 0, 255, 0) 50%, #0000FF 95%)'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='retinaimage'>
      retinaImage
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>A helper to generate a retina background image and non-retina
background image. The retina background image will output to a HiDPI media query. The mixin uses
a _2x.png filename suffix by default.</p>


  <div class='pre p1 fill-light mt0'>retinaImage(filename: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, backgroundSize: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, extension: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, retinaFilename: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, retinaSuffix: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>filename</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>backgroundSize</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>extension</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&#39;png&#39;</code>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>retinaFilename</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>retinaSuffix</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&#39;_2x&#39;</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
 ...retinaImage('my-img')
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  ${retinaImage('my-img')}
`

<span class="hljs-comment">// CSS as JS Output</span>
div {
  backgroundImage: 'url(my-img.png)',
  '@media only screen and (-webkit-<span class="hljs-built_in">min</span>-device-pixel-ratio: <span class="hljs-number">1.3</span>),
   only screen and (<span class="hljs-built_in">min</span>--moz-device-pixel-ratio: <span class="hljs-number">1.3</span>),
   only screen and (-o-<span class="hljs-built_in">min</span>-device-pixel-ratio: <span class="hljs-number">1.3</span>/<span class="hljs-number">1</span>),
   only screen and (<span class="hljs-built_in">min</span>-resolution: <span class="hljs-number">144</span>dpi),
   only screen and (<span class="hljs-built_in">min</span>-resolution: <span class="hljs-number">1.5</span>dppx)': {
    backgroundImage: 'url(my-img_2x.png)',
  }
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='timingfunctions'>
      timingFunctions
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>String to represent common easing functions as demonstrated here: (github.com/jaukia/easie).</p>


  <div class='pre p1 fill-light mt0'>timingFunctions(timingFunction: <a href="#timingfunction">TimingFunction</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>timingFunction</span> <code class='quiet'>(<a href="#timingfunction">TimingFunction</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-string">'transitionTimingFunction'</span>: timingFunctions(<span class="hljs-string">'easeInQuad'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
 <span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  transitionTimingFunction: <span class="hljs-subst">${timingFunctions(<span class="hljs-string">'easeInQuad'</span>)}</span>;
`</span>

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-string">'div'</span>: {
  <span class="hljs-string">'transitionTimingFunction'</span>: <span class="hljs-string">'cubic-bezier(0.550,  0.085, 0.680, 0.530)'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='triangle'>
      triangle
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>CSS to represent triangle with any pointing direction with an optional background color.</p>


  <div class='pre p1 fill-light mt0'>triangle($0: <a href="#triangleconfiguration">TriangleConfiguration</a>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>$0</span> <code class='quiet'>(<a href="#triangleconfiguration">TriangleConfiguration</a>)</code>
	    
          </div>
          
          <table class='mt1 mb2 fixed-table h5 col-12'>
            <colgroup>
              <col width='30%' />
              <col width='70%' />
            </colgroup>
            <thead>
              <tr class='bold fill-light'>
                <th>Name</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody class='mt1'>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.pointingDirection</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.height</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.width</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.foregroundColor</span> <code class='quiet'>any</code>
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
                <tr>
                  <td class='break-word'><span class='code bold'>$0.backgroundColor</span> <code class='quiet'>any</code>
                  
                    (default <code>&#39;transparent&#39;</code>)
                  </td>
                  <td class='break-word'><span></span></td>
                </tr>
              
            </tbody>
          </table>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>

const styles = {
  ...triangle({ <span class="hljs-string">pointingDirection:</span> <span class="hljs-string">'right'</span>, <span class="hljs-string">width:</span> <span class="hljs-string">'100px'</span>, <span class="hljs-string">height:</span> <span class="hljs-string">'100px'</span>, <span class="hljs-string">foregroundColor:</span> <span class="hljs-string">'red'</span> })
}


<span class="hljs-comment">// styled-components usage</span>
const div = styled.div`
  ${triangle({ <span class="hljs-string">pointingDirection:</span> <span class="hljs-string">'right'</span>, <span class="hljs-string">width:</span> <span class="hljs-string">'100px'</span>, <span class="hljs-string">height:</span> <span class="hljs-string">'100px'</span>, <span class="hljs-string">foregroundColor:</span> <span class="hljs-string">'red'</span> })}


<span class="hljs-comment">// CSS as JS Output</span>
<span class="hljs-symbol">
div:</span> {
 <span class="hljs-string">'borderColor'</span>: <span class="hljs-string">'transparent transparent transparent red'</span>,
 <span class="hljs-string">'borderStyle'</span>: <span class="hljs-string">'solid'</span>,
 <span class="hljs-string">'borderWidth'</span>: <span class="hljs-string">'50px 0 50px 100px'</span>,
 <span class="hljs-string">'height'</span>: <span class="hljs-string">'0'</span>,
 <span class="hljs-string">'width'</span>: <span class="hljs-string">'0'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='wordwrap'>
      wordWrap
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Provides an easy way to change the <code>wordWrap</code> property.</p>


  <div class='pre p1 fill-light mt0'>wordWrap(wrap: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>wrap</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>&#39;break-word&#39;</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  ...wordWrap(<span class="hljs-string">'break-word'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  <span class="hljs-subst">${wordWrap(<span class="hljs-string">'break-word'</span>)}</span>
`</span>

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-attr">overflowWrap</span>: <span class="hljs-string">'break-word'</span>,
  <span class="hljs-attr">wordWrap</span>: <span class="hljs-string">'break-word'</span>,
  <span class="hljs-attr">wordBreak</span>: <span class="hljs-string">'break-all'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <div class='keyline-top-not py2'><section class='section py2 clearfix'>

  <h2 class="section__heading" id='color' class='mt0'>
    Color
  </h2>

  
    

  
</section>
</div>
        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='adjusthue'>
      adjustHue
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Changes the hue of the color. Hue is a number between 0 to 360. The first
argument for adjustHue is the amount of degrees the color is rotated around
the color wheel, always producing a positive hue value.</p>


  <div class='pre p1 fill-light mt0'>adjustHue(degree: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>degree</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: adjustHue(<span class="hljs-number">180</span>, <span class="hljs-string">'#448'</span>),
  <span class="hljs-built_in">background</span>: adjustHue(<span class="hljs-string">'180'</span>, <span class="hljs-string">'rgba(101,100,205,0.7)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${adjustHue(<span class="hljs-number">180</span>, <span class="hljs-string">'#448'</span>)};
  <span class="hljs-built_in">background</span>: ${adjustHue(<span class="hljs-string">'180'</span>, <span class="hljs-string">'rgba(101,100,205,0.7)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>
element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#888844"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(136,136,68,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='complement'>
      complement
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns the complement of the provided color. This is identical to adjustHue(180, <color>).</p>


  <div class='pre p1 fill-light mt0'>complement(color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: complement(<span class="hljs-string">'#448'</span>),
  <span class="hljs-built_in">background</span>: complement(<span class="hljs-string">'rgba(204,205,100,0.7)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${complement(<span class="hljs-string">'#448'</span>)};
  <span class="hljs-built_in">background</span>: ${complement(<span class="hljs-string">'rgba(204,205,100,0.7)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>
element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#884"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(153,153,153,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='darken'>
      darken
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns a string value for the darkened color.</p>


  <div class='pre p1 fill-light mt0'>darken(amount: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>amount</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: darken(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#FFCD64'</span>),
  <span class="hljs-built_in">background</span>: darken(<span class="hljs-string">'0.2'</span>, <span class="hljs-string">'rgba(255,205,100,0.7)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${darken(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#FFCD64'</span>)};
  <span class="hljs-built_in">background</span>: ${darken(<span class="hljs-string">'0.2'</span>, <span class="hljs-string">'rgba(255,205,100,0.7)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#ffbd31"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(255,189,49,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='desaturate'>
      desaturate
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Decreases the intensity of a color. Its range is between 0 to 1. The first
argument of the desaturate function is the amount by how much the color
intensity should be decreased.</p>


  <div class='pre p1 fill-light mt0'>desaturate(amount: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>amount</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: desaturate(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#CCCD64'</span>),
  <span class="hljs-built_in">background</span>: desaturate(<span class="hljs-string">'0.2'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${desaturate(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#CCCD64'</span>)};
  <span class="hljs-built_in">background</span>: ${desaturate(<span class="hljs-string">'0.2'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>
element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#b8b979"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(184,185,121,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='getcontrast'>
      getContrast
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns the contrast ratio between two colors based on
<a href="http://www.w3.org/TR/WCAG20/#contrast-ratiodef">W3's recommended equation for calculating contrast</a>.</p>


  <div class='pre p1 fill-light mt0'>getContrast(color1: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, color2: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color1</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color2</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-attribute">const contrastRatio</span> = getContrast(<span class="hljs-string">'#444'</span>, <span class="hljs-string">'#fff'</span>);</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='getluminance'>
      getLuminance
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns a number (float) representing the luminance of a color.</p>


  <div class='pre p1 fill-light mt0'>getLuminance(color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: getLuminance(<span class="hljs-string">'#CCCD64'</span>) &gt;= getLuminance(<span class="hljs-string">'#0000ff'</span>) ? <span class="hljs-string">'#CCCD64'</span> : <span class="hljs-string">'#0000ff'</span>,
  <span class="hljs-built_in">background</span>: getLuminance(<span class="hljs-string">'rgba(58, 133, 255, 1)'</span>) &gt;= getLuminance(<span class="hljs-string">'rgba(255, 57, 149, 1)'</span>) ?
                            <span class="hljs-string">'rgba(58, 133, 255, 1)'</span> :
                            <span class="hljs-string">'rgba(255, 57, 149, 1)'</span>,
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${getLuminance(<span class="hljs-string">'#CCCD64'</span>) &gt;= getLuminance(<span class="hljs-string">'#0000ff'</span>) ? <span class="hljs-string">'#CCCD64'</span> : <span class="hljs-string">'#0000ff'</span>};
  <span class="hljs-built_in">background</span>: ${getLuminance(<span class="hljs-string">'rgba(58, 133, 255, 1)'</span>) &gt;= getLuminance(<span class="hljs-string">'rgba(255, 57, 149, 1)'</span>) ?
                            <span class="hljs-string">'rgba(58, 133, 255, 1)'</span> :
                            <span class="hljs-string">'rgba(255, 57, 149, 1)'</span>};

<span class="hljs-comment">// CSS in JS Output</span>

div {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#CCCD64"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(58, 133, 255, 1)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='grayscale'>
      grayscale
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Converts the color to a grayscale, by reducing its saturation to 0.</p>


  <div class='pre p1 fill-light mt0'>grayscale(color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: grayscale(<span class="hljs-string">'#CCCD64'</span>),
  <span class="hljs-built_in">background</span>: grayscale(<span class="hljs-string">'rgba(204,205,100,0.7)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${grayscale(<span class="hljs-string">'#CCCD64'</span>)};
  <span class="hljs-built_in">background</span>: ${grayscale(<span class="hljs-string">'rgba(204,205,100,0.7)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>
element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#999"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(153,153,153,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='hsl'>
      hsl
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns a string value for the color. The returned result is the smallest possible hex notation.</p>


  <div class='pre p1 fill-light mt0'>hsl(value: (<a href="#hslcolor">HslColor</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), saturation: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?, lightness: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>value</span> <code class='quiet'>((<a href="#hslcolor">HslColor</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>saturation</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>lightness</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-string">//</span> <span class="hljs-string">Styles</span> <span class="hljs-string">as</span> <span class="hljs-string">object</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">styles</span> <span class="hljs-string">=</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">hsl(359,</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-number">0.4</span><span class="hljs-string">),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">hsl({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">360</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.4</span> <span class="hljs-string">}),</span>
<span class="hljs-string">}</span>

<span class="hljs-string">//</span> <span class="hljs-string">styled-components</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">div</span> <span class="hljs-string">=</span> <span class="hljs-string">styled.div`</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${hsl(359,</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-number">0.4</span><span class="hljs-string">)};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${hsl({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">360</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.4</span> <span class="hljs-string">})};</span>
<span class="hljs-string">`</span>

<span class="hljs-string">//</span> <span class="hljs-string">CSS</span> <span class="hljs-string">in</span> <span class="hljs-string">JS</span> <span class="hljs-string">Output</span>

<span class="hljs-string">element</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"#b3191c"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"#b3191c"</span><span class="hljs-string">;</span>
<span class="hljs-string">}</span></pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='hsla'>
      hsla
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns a string value for the color. The returned result is the smallest possible rgba or hex notation.</p>


  <div class='pre p1 fill-light mt0'>hsla(value: (<a href="#hslacolor">HslaColor</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), saturation: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?, lightness: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?, alpha: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>value</span> <code class='quiet'>((<a href="#hslacolor">HslaColor</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>saturation</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>lightness</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>alpha</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-string">//</span> <span class="hljs-string">Styles</span> <span class="hljs-string">as</span> <span class="hljs-string">object</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">styles</span> <span class="hljs-string">=</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">hsla(359,</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-number">0.4</span><span class="hljs-string">,</span> <span class="hljs-number">0.7</span><span class="hljs-string">),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">hsla({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">360</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.4</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0</span><span class="hljs-string">,7</span> <span class="hljs-string">}),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">hsla(359,</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-number">0.4</span><span class="hljs-string">,</span> <span class="hljs-number">1</span><span class="hljs-string">),</span>
<span class="hljs-string">}</span>

<span class="hljs-string">//</span> <span class="hljs-string">styled-components</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">div</span> <span class="hljs-string">=</span> <span class="hljs-string">styled.div`</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${hsla(359,</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-number">0.4</span><span class="hljs-string">,</span> <span class="hljs-number">0.7</span><span class="hljs-string">)};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${hsla({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">360</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.4</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0</span><span class="hljs-string">,7</span> <span class="hljs-string">})};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${hsla(359,</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-number">0.4</span><span class="hljs-string">,</span> <span class="hljs-number">1</span><span class="hljs-string">)};</span>
<span class="hljs-string">`</span>

<span class="hljs-string">//</span> <span class="hljs-string">CSS</span> <span class="hljs-string">in</span> <span class="hljs-string">JS</span> <span class="hljs-string">Output</span>

<span class="hljs-string">element</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"rgba(179,25,28,0.7)"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"rgba(179,25,28,0.7)"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"#b3191c"</span><span class="hljs-string">;</span>
<span class="hljs-string">}</span></pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='hsltocolorstring'>
      hslToColorString
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Converts a HslColor or HslaColor object to a color string.
This util is useful in case you only know on runtime which color object is
used. Otherwise we recommend to rely on <code>hsl</code> or <code>hsla</code>.</p>


  <div class='pre p1 fill-light mt0'>hslToColorString(color: (<a href="#hslcolor">HslColor</a> | <a href="#hslacolor">HslaColor</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>((<a href="#hslcolor">HslColor</a> | <a href="#hslacolor">HslaColor</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-string">//</span> <span class="hljs-string">Styles</span> <span class="hljs-string">as</span> <span class="hljs-string">object</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">styles</span> <span class="hljs-string">=</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">hslToColorString({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">240</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">1</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.5</span> <span class="hljs-string">}),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">hslToColorString({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">360</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.4</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.72</span> <span class="hljs-string">}),</span>
<span class="hljs-string">}</span>

<span class="hljs-string">//</span> <span class="hljs-string">styled-components</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">div</span> <span class="hljs-string">=</span> <span class="hljs-string">styled.div`</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${hslToColorString({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">240</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">1</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.5</span> <span class="hljs-string">})};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${hslToColorString({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">360</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.4</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.72</span> <span class="hljs-string">})};</span>
<span class="hljs-string">`</span>

<span class="hljs-string">//</span> <span class="hljs-string">CSS</span> <span class="hljs-string">in</span> <span class="hljs-string">JS</span> <span class="hljs-string">Output</span>
<span class="hljs-string">element</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"#00f"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"rgba(179,25,25,0.72)"</span><span class="hljs-string">;</span>
<span class="hljs-string">}</span></pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='invert'>
      invert
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Inverts the red, green and blue values of a color.</p>


  <div class='pre p1 fill-light mt0'>invert(color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: invert(<span class="hljs-string">'#CCCD64'</span>),
  <span class="hljs-built_in">background</span>: invert(<span class="hljs-string">'rgba(101,100,205,0.7)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${invert(<span class="hljs-string">'#CCCD64'</span>)};
  <span class="hljs-built_in">background</span>: ${invert(<span class="hljs-string">'rgba(101,100,205,0.7)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#33329b"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(154,155,50,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='lighten'>
      lighten
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns a string value for the lightened color.</p>


  <div class='pre p1 fill-light mt0'>lighten(amount: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>amount</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: lighten(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#CCCD64'</span>),
  <span class="hljs-built_in">background</span>: lighten(<span class="hljs-string">'0.2'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${lighten(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#FFCD64'</span>)};
  <span class="hljs-built_in">background</span>: ${lighten(<span class="hljs-string">'0.2'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#e5e6b1"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(229,230,177,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='meetscontrastguidelines'>
      meetsContrastGuidelines
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Determines which contrast guidelines have been met for two colors.
Based on the <a href="https://www.w3.org/WAI/WCAG21/Understanding/contrast-enhanced.html">contrast calculations recommended by W3</a>.</p>


  <div class='pre p1 fill-light mt0'>meetsContrastGuidelines(color1: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, color2: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="#contrastscores">ContrastScores</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color1</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color2</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#contrastscores">ContrastScores</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-attribute">const scores</span> = meetsContrastGuidelines(<span class="hljs-string">'#444'</span>, <span class="hljs-string">'#fff'</span>);</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='mix'>
      mix
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Mixes the two provided colors together by calculating the average of each of the RGB components weighted to the first color by the provided weight.</p>


  <div class='pre p1 fill-light mt0'>mix(weight: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, otherColor: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>weight</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>otherColor</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: mix(<span class="hljs-number">0.5</span>, <span class="hljs-string">'#f00'</span>, <span class="hljs-string">'#00f'</span>)
  <span class="hljs-built_in">background</span>: mix(<span class="hljs-number">0.25</span>, <span class="hljs-string">'#f00'</span>, <span class="hljs-string">'#00f'</span>)
  <span class="hljs-built_in">background</span>: mix(<span class="hljs-string">'0.5'</span>, <span class="hljs-string">'rgba(255, 0, 0, 0.5)'</span>, <span class="hljs-string">'#00f'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${mix(<span class="hljs-number">0.5</span>, <span class="hljs-string">'#f00'</span>, <span class="hljs-string">'#00f'</span>)};
  <span class="hljs-built_in">background</span>: ${mix(<span class="hljs-number">0.25</span>, <span class="hljs-string">'#f00'</span>, <span class="hljs-string">'#00f'</span>)};
  <span class="hljs-built_in">background</span>: ${mix(<span class="hljs-string">'0.5'</span>, <span class="hljs-string">'rgba(255, 0, 0, 0.5)'</span>, <span class="hljs-string">'#00f'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#7f007f"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#3f00bf"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(63, 0, 191, 0.75)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='opacify'>
      opacify
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Increases the opacity of a color. Its range for the amount is between 0 to 1.</p>


  <div class='pre p1 fill-light mt0'>opacify(amount: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>amount</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: opacify(<span class="hljs-number">0.1</span>, <span class="hljs-string">'rgba(255, 255, 255, 0.9)'</span>);
  <span class="hljs-built_in">background</span>: opacify(<span class="hljs-number">0.2</span>, <span class="hljs-string">'hsla(0, 0%, 100%, 0.5)'</span>),
  <span class="hljs-built_in">background</span>: opacify(<span class="hljs-string">'0.5'</span>, <span class="hljs-string">'rgba(255, 0, 0, 0.2)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${opacify(<span class="hljs-number">0.1</span>, <span class="hljs-string">'rgba(255, 255, 255, 0.9)'</span>)};
  <span class="hljs-built_in">background</span>: ${opacify(<span class="hljs-number">0.2</span>, <span class="hljs-string">'hsla(0, 0%, 100%, 0.5)'</span>)},
  <span class="hljs-built_in">background</span>: ${opacify(<span class="hljs-string">'0.5'</span>, <span class="hljs-string">'rgba(255, 0, 0, 0.2)'</span>)},
`

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#fff"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(255,255,255,0.7)"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(255,0,0,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='parsetohsl'>
      parseToHsl
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns an HslColor or HslaColor object. This utility function is only useful
if want to extract a color component. With the color util <code>toColorString</code> you
can convert a HslColor or HslaColor object back to a string.</p>


  <div class='pre p1 fill-light mt0'>parseToHsl(color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): (<a href="#hslcolor">HslColor</a> | <a href="#hslacolor">HslaColor</a>)</div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>(<a href="#hslcolor">HslColor</a> | <a href="#hslacolor">HslaColor</a>)</code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-string">//</span> <span class="hljs-string">Assigns</span> <span class="hljs-string">`{</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">0</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">1</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.5</span> <span class="hljs-string">}`</span> <span class="hljs-string">to</span> <span class="hljs-string">color1</span>
<span class="hljs-string">const</span> <span class="hljs-string">color1</span> <span class="hljs-string">=</span> <span class="hljs-string">parseToHsl('rgb(255,</span> <span class="hljs-number">0</span><span class="hljs-string">,</span> <span class="hljs-number">0</span><span class="hljs-string">)');</span>
<span class="hljs-string">//</span> <span class="hljs-string">Assigns</span> <span class="hljs-string">`{</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">128</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">1</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.5</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.75</span> <span class="hljs-string">}`</span> <span class="hljs-string">to</span> <span class="hljs-string">color2</span>
<span class="hljs-string">const</span> <span class="hljs-string">color2</span> <span class="hljs-string">=</span> <span class="hljs-string">parseToHsl('hsla(128,</span> <span class="hljs-number">100</span><span class="hljs-string">%,</span> <span class="hljs-number">50</span><span class="hljs-string">%,</span> <span class="hljs-number">0.75</span><span class="hljs-string">)');</span></pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='parsetorgb'>
      parseToRgb
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns an RgbColor or RgbaColor object. This utility function is only useful
if want to extract a color component. With the color util <code>toColorString</code> you
can convert a RgbColor or RgbaColor object back to a string.</p>


  <div class='pre p1 fill-light mt0'>parseToRgb(color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): (<a href="#rgbcolor">RgbColor</a> | <a href="#rgbacolor">RgbaColor</a>)</div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>(<a href="#rgbcolor">RgbColor</a> | <a href="#rgbacolor">RgbaColor</a>)</code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-string">//</span> <span class="hljs-string">Assigns</span> <span class="hljs-string">`{</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">0</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">0</span> <span class="hljs-string">}`</span> <span class="hljs-string">to</span> <span class="hljs-string">color1</span>
<span class="hljs-string">const</span> <span class="hljs-string">color1</span> <span class="hljs-string">=</span> <span class="hljs-string">parseToRgb('rgb(255,</span> <span class="hljs-number">0</span><span class="hljs-string">,</span> <span class="hljs-number">0</span><span class="hljs-string">)');</span>
<span class="hljs-string">//</span> <span class="hljs-string">Assigns</span> <span class="hljs-string">`{</span> <span class="hljs-attr">red:</span> <span class="hljs-number">92</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">102</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">112</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.75</span> <span class="hljs-string">}`</span> <span class="hljs-string">to</span> <span class="hljs-string">color2</span>
<span class="hljs-string">const</span> <span class="hljs-string">color2</span> <span class="hljs-string">=</span> <span class="hljs-string">parseToRgb('hsla(210,</span> <span class="hljs-number">10</span><span class="hljs-string">%,</span> <span class="hljs-number">40</span><span class="hljs-string">%,</span> <span class="hljs-number">0.75</span><span class="hljs-string">)');</span></pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='readablecolor'>
      readableColor
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns black or white (or optional passed colors) for best
contrast depending on the luminosity of the given color.
When passing custom return colors, strict mode ensures that the
return color always meets or exceeds WCAG level AA or greater. If this test
fails, the default return color (black or white) is returned in place of the
custom return color. You can optionally turn off strict mode.</p>
<p>Follows <a href="https://www.w3.org/TR/WCAG20-TECHS/G18.html">W3C specs for readability</a>.</p>


  <div class='pre p1 fill-light mt0'>readableColor(color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, returnIfLightColor: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, returnIfDarkColor: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, strict: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>returnIfLightColor</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>defaultReturnIfLightColor</code>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>returnIfDarkColor</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
            = <code>defaultReturnIfDarkColor</code>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>strict</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>
            = <code>true</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">color</span>: readableColor(<span class="hljs-string">'#000'</span>),
  <span class="hljs-built_in">color</span>: readableColor(<span class="hljs-string">'black'</span>, <span class="hljs-string">'#001'</span>, <span class="hljs-string">'#ff8'</span>),
  <span class="hljs-built_in">color</span>: readableColor(<span class="hljs-string">'white'</span>, <span class="hljs-string">'#001'</span>, <span class="hljs-string">'#ff8'</span>),
  <span class="hljs-built_in">color</span>: readableColor(<span class="hljs-string">'red'</span>, <span class="hljs-string">'#333'</span>, <span class="hljs-string">'#ddd'</span>, <span class="hljs-keyword">true</span>)
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">color</span>: ${readableColor(<span class="hljs-string">'#000'</span>)};
  <span class="hljs-built_in">color</span>: ${readableColor(<span class="hljs-string">'black'</span>, <span class="hljs-string">'#001'</span>, <span class="hljs-string">'#ff8'</span>)};
  <span class="hljs-built_in">color</span>: ${readableColor(<span class="hljs-string">'white'</span>, <span class="hljs-string">'#001'</span>, <span class="hljs-string">'#ff8'</span>)};
  <span class="hljs-built_in">color</span>: ${readableColor(<span class="hljs-string">'red'</span>, <span class="hljs-string">'#333'</span>, <span class="hljs-string">'#ddd'</span>, <span class="hljs-keyword">true</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>
element {
  <span class="hljs-built_in">color</span>: <span class="hljs-string">"#fff"</span>;
  <span class="hljs-built_in">color</span>: <span class="hljs-string">"#ff8"</span>;
  <span class="hljs-built_in">color</span>: <span class="hljs-string">"#001"</span>;
  <span class="hljs-built_in">color</span>: <span class="hljs-string">"#000"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='rgb'>
      rgb
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns a string value for the color. The returned result is the smallest possible hex notation.</p>


  <div class='pre p1 fill-light mt0'>rgb(value: (<a href="#rgbcolor">RgbColor</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), green: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?, blue: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>value</span> <code class='quiet'>((<a href="#rgbcolor">RgbColor</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>green</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>blue</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-string">//</span> <span class="hljs-string">Styles</span> <span class="hljs-string">as</span> <span class="hljs-string">object</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">styles</span> <span class="hljs-string">=</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">rgb(255,</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-number">100</span><span class="hljs-string">),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">rgb({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span> <span class="hljs-string">}),</span>
<span class="hljs-string">}</span>

<span class="hljs-string">//</span> <span class="hljs-string">styled-components</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">div</span> <span class="hljs-string">=</span> <span class="hljs-string">styled.div`</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${rgb(255,</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-number">100</span><span class="hljs-string">)};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${rgb({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span> <span class="hljs-string">})};</span>
<span class="hljs-string">`</span>

<span class="hljs-string">//</span> <span class="hljs-string">CSS</span> <span class="hljs-string">in</span> <span class="hljs-string">JS</span> <span class="hljs-string">Output</span>

<span class="hljs-string">element</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"#ffcd64"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"#ffcd64"</span><span class="hljs-string">;</span>
<span class="hljs-string">}</span></pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='rgba'>
      rgba
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns a string value for the color. The returned result is the smallest possible rgba or hex notation.</p>
<p>Can also be used to fade a color by passing a hex value or named CSS color along with an alpha value.</p>


  <div class='pre p1 fill-light mt0'>rgba(firstValue: (<a href="#rgbacolor">RgbaColor</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), secondValue: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?, thirdValue: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?, fourthValue: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>firstValue</span> <code class='quiet'>((<a href="#rgbacolor">RgbaColor</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>secondValue</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>thirdValue</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>fourthValue</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-string">//</span> <span class="hljs-string">Styles</span> <span class="hljs-string">as</span> <span class="hljs-string">object</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">styles</span> <span class="hljs-string">=</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">rgba(255,</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-number">100</span><span class="hljs-string">,</span> <span class="hljs-number">0.7</span><span class="hljs-string">),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">rgba({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.7</span> <span class="hljs-string">}),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">rgba(255,</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-number">100</span><span class="hljs-string">,</span> <span class="hljs-number">1</span><span class="hljs-string">),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">rgba('#ffffff',</span> <span class="hljs-number">0.4</span><span class="hljs-string">),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">rgba('black',</span> <span class="hljs-number">0.7</span><span class="hljs-string">),</span>
<span class="hljs-string">}</span>

<span class="hljs-string">//</span> <span class="hljs-string">styled-components</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">div</span> <span class="hljs-string">=</span> <span class="hljs-string">styled.div`</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${rgba(255,</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-number">100</span><span class="hljs-string">,</span> <span class="hljs-number">0.7</span><span class="hljs-string">)};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${rgba({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.7</span> <span class="hljs-string">})};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${rgba(255,</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-number">100</span><span class="hljs-string">,</span> <span class="hljs-number">1</span><span class="hljs-string">)};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${rgba('#ffffff',</span> <span class="hljs-number">0.4</span><span class="hljs-string">)};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${rgba('black',</span> <span class="hljs-number">0.7</span><span class="hljs-string">)};</span>
<span class="hljs-string">`</span>

<span class="hljs-string">//</span> <span class="hljs-string">CSS</span> <span class="hljs-string">in</span> <span class="hljs-string">JS</span> <span class="hljs-string">Output</span>

<span class="hljs-string">element</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"rgba(255,205,100,0.7)"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"rgba(255,205,100,0.7)"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"#ffcd64"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"rgba(255,255,255,0.4)"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"rgba(0,0,0,0.7)"</span><span class="hljs-string">;</span>
<span class="hljs-string">}</span></pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='rgbtocolorstring'>
      rgbToColorString
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Converts a RgbColor or RgbaColor object to a color string.
This util is useful in case you only know on runtime which color object is
used. Otherwise we recommend to rely on <code>rgb</code> or <code>rgba</code>.</p>


  <div class='pre p1 fill-light mt0'>rgbToColorString(color: (<a href="#rgbcolor">RgbColor</a> | <a href="#rgbacolor">RgbaColor</a>)): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>((<a href="#rgbcolor">RgbColor</a> | <a href="#rgbacolor">RgbaColor</a>))</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-string">//</span> <span class="hljs-string">Styles</span> <span class="hljs-string">as</span> <span class="hljs-string">object</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">styles</span> <span class="hljs-string">=</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">rgbToColorString({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span> <span class="hljs-string">}),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">rgbToColorString({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.72</span> <span class="hljs-string">}),</span>
<span class="hljs-string">}</span>

<span class="hljs-string">//</span> <span class="hljs-string">styled-components</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">div</span> <span class="hljs-string">=</span> <span class="hljs-string">styled.div`</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${rgbToColorString({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span> <span class="hljs-string">})};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${rgbToColorString({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.72</span> <span class="hljs-string">})};</span>
<span class="hljs-string">`</span>

<span class="hljs-string">//</span> <span class="hljs-string">CSS</span> <span class="hljs-string">in</span> <span class="hljs-string">JS</span> <span class="hljs-string">Output</span>
<span class="hljs-string">element</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"#ffcd64"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"rgba(255,205,100,0.72)"</span><span class="hljs-string">;</span>
<span class="hljs-string">}</span></pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='saturate'>
      saturate
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Increases the intensity of a color. Its range is between 0 to 1. The first
argument of the saturate function is the amount by how much the color
intensity should be increased.</p>


  <div class='pre p1 fill-light mt0'>saturate(amount: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>amount</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: saturate(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#CCCD64'</span>),
  <span class="hljs-built_in">background</span>: saturate(<span class="hljs-string">'0.2'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${saturate(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#FFCD64'</span>)};
  <span class="hljs-built_in">background</span>: ${saturate(<span class="hljs-string">'0.2'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#e0e250"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(224,226,80,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='sethue'>
      setHue
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Sets the hue of a color to the provided value. The hue range can be
from 0 and 359.</p>


  <div class='pre p1 fill-light mt0'>setHue(hue: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>hue</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: setHue(<span class="hljs-number">42</span>, <span class="hljs-string">'#CCCD64'</span>),
  <span class="hljs-built_in">background</span>: setHue(<span class="hljs-string">'244'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${setHue(<span class="hljs-number">42</span>, <span class="hljs-string">'#CCCD64'</span>)};
  <span class="hljs-built_in">background</span>: ${setHue(<span class="hljs-string">'244'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>
element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#cdae64"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(107,100,205,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='setlightness'>
      setLightness
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Sets the lightness of a color to the provided value. The lightness range can be
from 0 and 1.</p>


  <div class='pre p1 fill-light mt0'>setLightness(lightness: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>lightness</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: setLightness(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#CCCD64'</span>),
  <span class="hljs-built_in">background</span>: setLightness(<span class="hljs-string">'0.75'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${setLightness(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#CCCD64'</span>)};
  <span class="hljs-built_in">background</span>: ${setLightness(<span class="hljs-string">'0.75'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>
element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#4d4d19"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(223,224,159,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='setsaturation'>
      setSaturation
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Sets the saturation of a color to the provided value. The saturation range can be
from 0 and 1.</p>


  <div class='pre p1 fill-light mt0'>setSaturation(saturation: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>saturation</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: setSaturation(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#CCCD64'</span>),
  <span class="hljs-built_in">background</span>: setSaturation(<span class="hljs-string">'0.75'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${setSaturation(<span class="hljs-number">0.2</span>, <span class="hljs-string">'#CCCD64'</span>)};
  <span class="hljs-built_in">background</span>: ${setSaturation(<span class="hljs-string">'0.75'</span>, <span class="hljs-string">'rgba(204,205,100,0.7)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>
element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#adad84"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(228,229,76,0.7)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='shade'>
      shade
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shades a color by mixing it with black. <code>shade</code> can produce
hue shifts, where as <code>darken</code> manipulates the luminance channel and therefore
doesn't produce hue shifts.</p>


  <div class='pre p1 fill-light mt0'>shade(percentage: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>percentage</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: shade(<span class="hljs-number">0.25</span>, <span class="hljs-string">'#00f'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${shade(<span class="hljs-number">0.25</span>, <span class="hljs-string">'#00f'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#00003f"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='tint'>
      tint
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Tints a color by mixing it with white. <code>tint</code> can produce
hue shifts, where as <code>lighten</code> manipulates the luminance channel and therefore
doesn't produce hue shifts.</p>


  <div class='pre p1 fill-light mt0'>tint(percentage: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>percentage</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: <span class="hljs-built_in">tint</span>(<span class="hljs-number">0.25</span>, <span class="hljs-string">'#00f'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${<span class="hljs-built_in">tint</span>(<span class="hljs-number">0.25</span>, <span class="hljs-string">'#00f'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"#bfbfff"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='tocolorstring'>
      toColorString
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Converts a RgbColor, RgbaColor, HslColor or HslaColor object to a color string.
This util is useful in case you only know on runtime which color object is
used. Otherwise we recommend to rely on <code>rgb</code>, <code>rgba</code>, <code>hsl</code> or <code>hsla</code>.</p>


  <div class='pre p1 fill-light mt0'>toColorString(color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">Object</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">Object</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-string">//</span> <span class="hljs-string">Styles</span> <span class="hljs-string">as</span> <span class="hljs-string">object</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">styles</span> <span class="hljs-string">=</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">toColorString({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span> <span class="hljs-string">}),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">toColorString({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.72</span> <span class="hljs-string">}),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">toColorString({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">240</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">1</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.5</span> <span class="hljs-string">}),</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">toColorString({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">360</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.4</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.72</span> <span class="hljs-string">}),</span>
<span class="hljs-string">}</span>

<span class="hljs-string">//</span> <span class="hljs-string">styled-components</span> <span class="hljs-string">usage</span>
<span class="hljs-string">const</span> <span class="hljs-string">div</span> <span class="hljs-string">=</span> <span class="hljs-string">styled.div`</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${toColorString({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span> <span class="hljs-string">})};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${toColorString({</span> <span class="hljs-attr">red:</span> <span class="hljs-number">255</span><span class="hljs-string">,</span> <span class="hljs-attr">green:</span> <span class="hljs-number">205</span><span class="hljs-string">,</span> <span class="hljs-attr">blue:</span> <span class="hljs-number">100</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.72</span> <span class="hljs-string">})};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${toColorString({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">240</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">1</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.5</span> <span class="hljs-string">})};</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">${toColorString({</span> <span class="hljs-attr">hue:</span> <span class="hljs-number">360</span><span class="hljs-string">,</span> <span class="hljs-attr">saturation:</span> <span class="hljs-number">0.75</span><span class="hljs-string">,</span> <span class="hljs-attr">lightness:</span> <span class="hljs-number">0.4</span><span class="hljs-string">,</span> <span class="hljs-attr">alpha:</span> <span class="hljs-number">0.72</span> <span class="hljs-string">})};</span>
<span class="hljs-string">`</span>

<span class="hljs-string">//</span> <span class="hljs-string">CSS</span> <span class="hljs-string">in</span> <span class="hljs-string">JS</span> <span class="hljs-string">Output</span>
<span class="hljs-string">element</span> <span class="hljs-string">{</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"#ffcd64"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"rgba(255,205,100,0.72)"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"#00f"</span><span class="hljs-string">;</span>
  <span class="hljs-attr">background:</span> <span class="hljs-string">"rgba(179,25,25,0.72)"</span><span class="hljs-string">;</span>
<span class="hljs-string">}</span></pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='transparentize'>
      transparentize
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Decreases the opacity of a color. Its range for the amount is between 0 to 1.</p>


  <div class='pre p1 fill-light mt0'>transparentize(amount: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), color: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>amount</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>color</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-built_in">background</span>: transparentize(<span class="hljs-number">0.1</span>, <span class="hljs-string">'#fff'</span>),
  <span class="hljs-built_in">background</span>: transparentize(<span class="hljs-number">0.2</span>, <span class="hljs-string">'hsl(0, 0%, 100%)'</span>),
  <span class="hljs-built_in">background</span>: transparentize(<span class="hljs-string">'0.5'</span>, <span class="hljs-string">'rgba(255, 0, 0, 0.8)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  <span class="hljs-built_in">background</span>: ${transparentize(<span class="hljs-number">0.1</span>, <span class="hljs-string">'#fff'</span>)};
  <span class="hljs-built_in">background</span>: ${transparentize(<span class="hljs-number">0.2</span>, <span class="hljs-string">'hsl(0, 0%, 100%)'</span>)};
  <span class="hljs-built_in">background</span>: ${transparentize(<span class="hljs-string">'0.5'</span>, <span class="hljs-string">'rgba(255, 0, 0, 0.8)'</span>)};
`

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(255,255,255,0.9)"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(255,255,255,0.8)"</span>;
  <span class="hljs-built_in">background</span>: <span class="hljs-string">"rgba(255,0,0,0.3)"</span>;
}</pre>
    
  

  

  

  
</section>

        
      
        
          <div class='keyline-top-not py2'><section class='section py2 clearfix'>

  <h2 class="section__heading" id='math' class='mt0'>
    Math
  </h2>

  
    

  
</section>
</div>
        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='math'>
      math
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Helper for doing math with CSS Units. Accepts a formula as a string. All values in the formula must have the same unit (or be unitless). Supports complex formulas utliziing addition, subtraction, multiplication, division, square root, powers, factorial, min, max, as well as parentheses for order of operation.</p>
<p>In cases where you need to do calculations with mixed units where one unit is a <a href="https://developer.mozilla.org/en-US/docs/Web/CSS/length#Relative_length_units">relative length unit</a>, you will want to use <a href="https://developer.mozilla.org/en-US/docs/Web/CSS/calc">CSS Calc</a>.</p>
<p><em>warning</em> While we've done everything possible to ensure math safely evalutes formulas expressed as strings, you should always use extreme caution when passing <code>math</code> user provided values.</p>


  <div class='pre p1 fill-light mt0'>math(formula: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, additionalSymbols: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">Object</a>?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>formula</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>additionalSymbols</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object">Object</a>?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  fontSize: math(<span class="hljs-string">'12rem + 8rem'</span>),
  fontSize: math(<span class="hljs-string">'(12px + 2px) * 3'</span>),
  fontSize: math(<span class="hljs-string">'3px^2 + sqrt(4)'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  fontSize: <span class="hljs-symbol">$</span>{math(<span class="hljs-string">'12rem + 8rem'</span>)};
  fontSize: <span class="hljs-symbol">$</span>{math(<span class="hljs-string">'(12px + 2px) * 3'</span>)};
  fontSize: <span class="hljs-symbol">$</span>{math(<span class="hljs-string">'3px^2 + sqrt(4)'</span>)};
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span>: {
  fontSize: <span class="hljs-string">'20rem'</span>,
  fontSize: <span class="hljs-string">'42px'</span>,
  fontSize: <span class="hljs-string">'11px'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <div class='keyline-top-not py2'><section class='section py2 clearfix'>

  <h2 class="section__heading" id='shorthands' class='mt0'>
    Shorthands
  </h2>

  
    

  
</section>
</div>
        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='animation'>
      animation
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand for easily setting the animation property. Allows either multiple arrays with animations
or a single animation spread over the arguments.</p>


  <div class='pre p1 fill-light mt0'>animation(args: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>args</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...animation([<span class="hljs-string">'rotate'</span>, <span class="hljs-string">'1s'</span>, <span class="hljs-string">'ease-in-out'</span>], [<span class="hljs-string">'colorchange'</span>, <span class="hljs-string">'2s'</span>])
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{animation([<span class="hljs-string">'rotate'</span>, <span class="hljs-string">'1s'</span>, <span class="hljs-string">'ease-in-out'</span>], [<span class="hljs-string">'colorchange'</span>, <span class="hljs-string">'2s'</span>])}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'animation'</span>: <span class="hljs-string">'rotate 1s ease-in-out, colorchange 2s'</span>
}</pre>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...animation(<span class="hljs-string">'rotate'</span>, <span class="hljs-string">'1s'</span>, <span class="hljs-string">'ease-in-out'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{animation(<span class="hljs-string">'rotate'</span>, <span class="hljs-string">'1s'</span>, <span class="hljs-string">'ease-in-out'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'animation'</span>: <span class="hljs-string">'rotate 1s ease-in-out'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='backgroundimages'>
      backgroundImages
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand that accepts any number of backgroundImage values as parameters for creating a single background statement.</p>


  <div class='pre p1 fill-light mt0'>backgroundImages(properties: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>properties</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  ...backgroundImages(<span class="hljs-symbol">'url</span>(<span class="hljs-string">"/image/background.jpg"</span>)', <span class="hljs-symbol">'linear</span>-gradient(red, green)')
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  ${backgroundImages(<span class="hljs-symbol">'url</span>(<span class="hljs-string">"/image/background.jpg"</span>)', <span class="hljs-symbol">'linear</span>-gradient(red, green)')}
`

<span class="hljs-comment">// CSS as JS Output</span>

div {
  <span class="hljs-symbol">'backgroundImage</span>': <span class="hljs-symbol">'url</span>(<span class="hljs-string">"/image/background.jpg"</span>), linear-gradient(red, green)'
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='backgrounds'>
      backgrounds
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand that accepts any number of background values as parameters for creating a single background statement.</p>


  <div class='pre p1 fill-light mt0'>backgrounds(properties: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>properties</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  ...backgrounds(<span class="hljs-symbol">'url</span>(<span class="hljs-string">"/image/background.jpg"</span>)', <span class="hljs-symbol">'linear</span>-gradient(red, green)', <span class="hljs-symbol">'center</span> no-repeat')
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div`
  ${backgrounds(<span class="hljs-symbol">'url</span>(<span class="hljs-string">"/image/background.jpg"</span>)', <span class="hljs-symbol">'linear</span>-gradient(red, green)', <span class="hljs-symbol">'center</span> no-repeat')}
`

<span class="hljs-comment">// CSS as JS Output</span>

div {
  <span class="hljs-symbol">'background</span>': <span class="hljs-symbol">'url</span>(<span class="hljs-string">"/image/background.jpg"</span>), linear-gradient(red, green), center no-repeat'
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='border'>
      border
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand for the border property that splits out individual properties for use with tools like Fela and Styletron. A side keyword can optionally be passed to target only one side's border properties.</p>


  <div class='pre p1 fill-light mt0'>border(sideKeyword: (<a href="#sidekeyword">SideKeyword</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), values: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>sideKeyword</span> <code class='quiet'>((<a href="#sidekeyword">SideKeyword</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>values</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  ...border(<span class="hljs-string">'1px'</span>, <span class="hljs-string">'solid'</span>, <span class="hljs-string">'red'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  <span class="hljs-subst">${border(<span class="hljs-string">'1px'</span>, <span class="hljs-string">'solid'</span>, <span class="hljs-string">'red'</span>)}</span>
`</span>

<span class="hljs-comment">// CSS as JS Output</span>

div {
  <span class="hljs-string">'borderColor'</span>: <span class="hljs-string">'red'</span>,
  <span class="hljs-string">'borderStyle'</span>: <span class="hljs-string">'solid'</span>,
  <span class="hljs-string">'borderWidth'</span>: <span class="hljs-string">`1px`</span>,
}

<span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  ...border(<span class="hljs-string">'top'</span>, <span class="hljs-string">'1px'</span>, <span class="hljs-string">'solid'</span>, <span class="hljs-string">'red'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  <span class="hljs-subst">${border(<span class="hljs-string">'top'</span>, <span class="hljs-string">'1px'</span>, <span class="hljs-string">'solid'</span>, <span class="hljs-string">'red'</span>)}</span>
`</span>

<span class="hljs-comment">// CSS as JS Output</span>

div {
  <span class="hljs-string">'borderTopColor'</span>: <span class="hljs-string">'red'</span>,
  <span class="hljs-string">'borderTopStyle'</span>: <span class="hljs-string">'solid'</span>,
  <span class="hljs-string">'borderTopWidth'</span>: <span class="hljs-string">`1px`</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='bordercolor'>
      borderColor
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand that accepts up to four values, including null to skip a value, and maps them to their respective directions.</p>


  <div class='pre p1 fill-light mt0'>borderColor(values: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>values</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...borderColor(<span class="hljs-string">'red'</span>, <span class="hljs-string">'green'</span>, <span class="hljs-string">'blue'</span>, <span class="hljs-string">'yellow'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{borderColor(<span class="hljs-string">'red'</span>, <span class="hljs-string">'green'</span>, <span class="hljs-string">'blue'</span>, <span class="hljs-string">'yellow'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'borderTopColor'</span>: <span class="hljs-string">'red'</span>,
  <span class="hljs-string">'borderRightColor'</span>: <span class="hljs-string">'green'</span>,
  <span class="hljs-string">'borderBottomColor'</span>: <span class="hljs-string">'blue'</span>,
  <span class="hljs-string">'borderLeftColor'</span>: <span class="hljs-string">'yellow'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='borderradius'>
      borderRadius
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand that accepts a value for side and a value for radius and applies the radius value to both corners of the side.</p>


  <div class='pre p1 fill-light mt0'>borderRadius(side: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, radius: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>side</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>radius</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...borderRadius(<span class="hljs-string">'top'</span>, <span class="hljs-string">'5px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{borderRadius(<span class="hljs-string">'top'</span>, <span class="hljs-string">'5px'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'borderTopRightRadius'</span>: <span class="hljs-string">'5px'</span>,
  <span class="hljs-string">'borderTopLeftRadius'</span>: <span class="hljs-string">'5px'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='borderstyle'>
      borderStyle
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand that accepts up to four values, including null to skip a value, and maps them to their respective directions.</p>


  <div class='pre p1 fill-light mt0'>borderStyle(values: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>values</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...borderStyle(<span class="hljs-string">'solid'</span>, <span class="hljs-string">'dashed'</span>, <span class="hljs-string">'dotted'</span>, <span class="hljs-string">'double'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{borderStyle(<span class="hljs-string">'solid'</span>, <span class="hljs-string">'dashed'</span>, <span class="hljs-string">'dotted'</span>, <span class="hljs-string">'double'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'borderTopStyle'</span>: <span class="hljs-string">'solid'</span>,
  <span class="hljs-string">'borderRightStyle'</span>: <span class="hljs-string">'dashed'</span>,
  <span class="hljs-string">'borderBottomStyle'</span>: <span class="hljs-string">'dotted'</span>,
  <span class="hljs-string">'borderLeftStyle'</span>: <span class="hljs-string">'double'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='borderwidth'>
      borderWidth
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand that accepts up to four values, including null to skip a value, and maps them to their respective directions.</p>


  <div class='pre p1 fill-light mt0'>borderWidth(values: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>values</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...borderWidth(<span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{borderWidth(<span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'borderTopWidth'</span>: <span class="hljs-string">'12px'</span>,
  <span class="hljs-string">'borderRightWidth'</span>: <span class="hljs-string">'24px'</span>,
  <span class="hljs-string">'borderBottomWidth'</span>: <span class="hljs-string">'36px'</span>,
  <span class="hljs-string">'borderLeftWidth'</span>: <span class="hljs-string">'48px'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='buttons'>
      buttons
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Populates selectors that target all buttons. You can pass optional states to append to the selectors.</p>


  <div class='pre p1 fill-light mt0'>buttons(states: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#interactionstate">InteractionState</a>>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>states</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#interactionstate">InteractionState</a>>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  [buttons(<span class="hljs-symbol">'activ</span>e')]: {
    <span class="hljs-symbol">'borde</span>r': <span class="hljs-symbol">'non</span>e'
  }
}

<span class="hljs-comment">// styled-components usage</span>
const div = styled.div`
  &gt; ${buttons(<span class="hljs-symbol">'activ</span>e')} {
    border: none;
  }
`

<span class="hljs-comment">// CSS in JS Output</span>

 <span class="hljs-symbol">'button</span>:active,
 <span class="hljs-symbol">'input</span>[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"button"</span>]:active,
 <span class="hljs-symbol">'input</span>[<span class="hljs-class"><span class="hljs-keyword">type</span><span class="hljs-title">=\</span>"<span class="hljs-title">reset\</span>"]</span>:active,
 <span class="hljs-symbol">'input</span>[<span class="hljs-class"><span class="hljs-keyword">type</span><span class="hljs-title">=\</span>"<span class="hljs-title">submit\</span>"]</span>:active: {
  <span class="hljs-symbol">'borde</span>r': <span class="hljs-symbol">'non</span>e'
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='margin'>
      margin
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand that accepts up to four values, including null to skip a value, and maps them to their respective directions.</p>


  <div class='pre p1 fill-light mt0'>margin(values: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>values</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...margin(<span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{margin(<span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'marginTop'</span>: <span class="hljs-string">'12px'</span>,
  <span class="hljs-string">'marginRight'</span>: <span class="hljs-string">'24px'</span>,
  <span class="hljs-string">'marginBottom'</span>: <span class="hljs-string">'36px'</span>,
  <span class="hljs-string">'marginLeft'</span>: <span class="hljs-string">'48px'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='padding'>
      padding
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand that accepts up to four values, including null to skip a value, and maps them to their respective directions.</p>


  <div class='pre p1 fill-light mt0'>padding(values: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>values</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...padding(<span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{padding(<span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'paddingTop'</span>: <span class="hljs-string">'12px'</span>,
  <span class="hljs-string">'paddingRight'</span>: <span class="hljs-string">'24px'</span>,
  <span class="hljs-string">'paddingBottom'</span>: <span class="hljs-string">'36px'</span>,
  <span class="hljs-string">'paddingLeft'</span>: <span class="hljs-string">'48px'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='position'>
      position
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand accepts up to five values, including null to skip a value, and maps them to their respective directions. The first value can optionally be a position keyword.</p>


  <div class='pre p1 fill-light mt0'>position(firstValue: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | null)?, values: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>firstValue</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | null)?)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>values</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...position(<span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{position(<span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'top'</span>: <span class="hljs-string">'12px'</span>,
  <span class="hljs-string">'right'</span>: <span class="hljs-string">'24px'</span>,
  <span class="hljs-string">'bottom'</span>: <span class="hljs-string">'36px'</span>,
  <span class="hljs-string">'left'</span>: <span class="hljs-string">'48px'</span>
}

<span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...position(<span class="hljs-string">'absolute'</span>, <span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{position(<span class="hljs-string">'absolute'</span>, <span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'position'</span>: <span class="hljs-string">'absolute'</span>,
  <span class="hljs-string">'top'</span>: <span class="hljs-string">'12px'</span>,
  <span class="hljs-string">'right'</span>: <span class="hljs-string">'24px'</span>,
  <span class="hljs-string">'bottom'</span>: <span class="hljs-string">'36px'</span>,
  <span class="hljs-string">'left'</span>: <span class="hljs-string">'48px'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='size'>
      size
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Shorthand to set the height and width properties in a single statement.</p>


  <div class='pre p1 fill-light mt0'>size(height: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), width: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>height</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>width</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
            = <code>height</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...<span class="hljs-keyword">size</span>(<span class="hljs-string">'300px'</span>, <span class="hljs-string">'250px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-keyword">div</span> = styled.<span class="hljs-keyword">div</span>`
  ${<span class="hljs-keyword">size</span>(<span class="hljs-string">'300px'</span>, <span class="hljs-string">'250px'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-keyword">div</span> {
  <span class="hljs-string">'height'</span>: <span class="hljs-string">'300px'</span>,
  <span class="hljs-string">'width'</span>: <span class="hljs-string">'250px'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='textinputs'>
      textInputs
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Populates selectors that target all text inputs. You can pass optional states to append to the selectors.</p>


  <div class='pre p1 fill-light mt0'>textInputs(states: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#interactionstate">InteractionState</a>>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>states</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="#interactionstate">InteractionState</a>>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  [textInputs(<span class="hljs-symbol">'activ</span>e')]: {
    <span class="hljs-symbol">'borde</span>r': <span class="hljs-symbol">'non</span>e'
  }
}

<span class="hljs-comment">// styled-components usage</span>
const div = styled.div`
  &gt; ${textInputs(<span class="hljs-symbol">'activ</span>e')} {
    border: none;
  }
`

<span class="hljs-comment">// CSS in JS Output</span>

 <span class="hljs-symbol">'input</span>[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"color"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"date"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"datetime"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"datetime-local"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"email"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"month"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"number"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"password"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"search"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"tel"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"text"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"time"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"url"</span>]:active,
 input[<span class="hljs-class"><span class="hljs-keyword">type</span></span>=<span class="hljs-string">"week"</span>]:active,
 input:not([<span class="hljs-class"><span class="hljs-keyword">type</span>])</span>:active,
 textarea:active': {
  <span class="hljs-symbol">'borde</span>r': <span class="hljs-symbol">'non</span>e'
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='transitions'>
      transitions
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Accepts any number of transition values as parameters for creating a single transition statement. You may also pass an array of properties as the first parameter that you would like to apply the same transition values to (second parameter).</p>


  <div class='pre p1 fill-light mt0'>transitions(properties: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>)>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>properties</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>)>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...transitions(<span class="hljs-string">'opacity 1.0s ease-in 0s'</span>, <span class="hljs-string">'width 2.0s ease-in 2s'</span>),
  ...transitions([<span class="hljs-string">'color'</span>, <span class="hljs-string">'background-color'</span>], <span class="hljs-string">'2.0s ease-in 2s'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{transitions(<span class="hljs-string">'opacity 1.0s ease-in 0s'</span>, <span class="hljs-string">'width 2.0s ease-in 2s'</span>)};
  <span class="hljs-symbol">$</span>{transitions([<span class="hljs-string">'color'</span>, <span class="hljs-string">'background-color'</span>], <span class="hljs-string">'2.0s ease-in 2s'</span>),};
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'transition'</span>: <span class="hljs-string">'opacity 1.0s ease-in 0s, width 2.0s ease-in 2s'</span>
  <span class="hljs-string">'transition'</span>: <span class="hljs-string">'color 2.0s ease-in 2s, background-color 2.0s ease-in 2s'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <div class='keyline-top-not py2'><section class='section py2 clearfix'>

  <h2 class="section__heading" id='helpers' class='mt0'>
    Helpers
  </h2>

  
    

  
</section>
</div>
        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='cssvar'>
      cssVar
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Fetches the value of a passed CSS Variable in the :root scope, or otherwise returns a defaultValue if provided.</p>


  <div class='pre p1 fill-light mt0'>cssVar(cssVariable: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, defaultValue: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)?): (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>cssVariable</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>defaultValue</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-string">'background'</span>: cssVar(<span class="hljs-string">'--background-color'</span>),
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  background: <span class="hljs-subst">${cssVar(<span class="hljs-string">'--background-color'</span>)}</span>;
`</span>

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-string">'background'</span>: <span class="hljs-string">'red'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='directionalproperty'>
      directionalProperty
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Enables shorthand for direction-based properties. It accepts a property (hyphenated or camelCased) and up to four values that map to top, right, bottom, and left, respectively. You can optionally pass an empty string to get only the directional values as properties. You can also optionally pass a null argument for a directional value to ignore it.</p>


  <div class='pre p1 fill-light mt0'>directionalProperty(property: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, values: ...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)>): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>property</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>values</span> <code class='quiet'>(...<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>? | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>?)>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  ...directionalProperty(<span class="hljs-string">'padding'</span>, <span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
  <span class="hljs-symbol">$</span>{directionalProperty(<span class="hljs-string">'padding'</span>, <span class="hljs-string">'12px'</span>, <span class="hljs-string">'24px'</span>, <span class="hljs-string">'36px'</span>, <span class="hljs-string">'48px'</span>)}
`

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-built-in">div</span> {
  <span class="hljs-string">'paddingTop'</span>: <span class="hljs-string">'12px'</span>,
  <span class="hljs-string">'paddingRight'</span>: <span class="hljs-string">'24px'</span>,
  <span class="hljs-string">'paddingBottom'</span>: <span class="hljs-string">'36px'</span>,
  <span class="hljs-string">'paddingLeft'</span>: <span class="hljs-string">'48px'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='em'>
      em
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Convert pixel value to ems. The default base value is 16px, but can be changed by passing a
second argument to the function.</p>


  <div class='pre p1 fill-light mt0'>em(pxval: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), base: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</div>
  
    <p>
      Type:
      function (value: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), base: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>pxval</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>base</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
            = <code>&#39;16px&#39;</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-string">'height'</span>: em(<span class="hljs-string">'16px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  height: <span class="hljs-subst">${em(<span class="hljs-string">'16px'</span>)}</span>
`</span>

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-string">'height'</span>: <span class="hljs-string">'1em'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='getvalueandunit'>
      getValueAndUnit
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns a given CSS value and its unit as elements of an array.</p>


  <div class='pre p1 fill-light mt0'>getValueAndUnit(value: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)): any</div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>value</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>any</code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-string">'--dimension'</span>: getValueAndUnit(<span class="hljs-string">'100px'</span>)[<span class="hljs-number">0</span>],
  <span class="hljs-string">'--unit'</span>: getValueAndUnit(<span class="hljs-string">'100px'</span>)[<span class="hljs-number">1</span>],
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  --dimension: <span class="hljs-subst">${getValueAndUnit(<span class="hljs-string">'100px'</span>)[<span class="hljs-number">0</span>]}</span>;
  --unit: <span class="hljs-subst">${getValueAndUnit(<span class="hljs-string">'100px'</span>)[<span class="hljs-number">1</span>]}</span>;
`</span>

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-string">'--dimension'</span>: <span class="hljs-number">100</span>,
  <span class="hljs-string">'--unit'</span>: <span class="hljs-string">'px'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='important'>
      important
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Helper for targeting rules in a style block generated by polished modules that need !important-level specificity. Can optionally specify a rule (or rules) to target specific rules.</p>


  <div class='pre p1 fill-light mt0'>important(styleBlock: <a href="#styles">Styles</a>, rules: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)?): <a href="#styles">Styles</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>styleBlock</span> <code class='quiet'>(<a href="#styles">Styles</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>rules</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#styles">Styles</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
  <span class="hljs-params">...</span>important(cover())
}

<span class="hljs-comment">// styled-components usage</span>
const div = styled.div<span class="hljs-string">`
  ${important(cover())}
`</span>

<span class="hljs-comment">// CSS as JS Output</span>

div: {
  <span class="hljs-string">'position'</span>: <span class="hljs-string">'absolute !important'</span>,
  <span class="hljs-string">'top'</span>: <span class="hljs-string">'0 !important'</span>,
  <span class="hljs-string">'right: '</span><span class="hljs-number">0</span> !important<span class="hljs-string">',
  '</span>bottom<span class="hljs-string">': '</span><span class="hljs-number">0</span> !important<span class="hljs-string">',
  '</span>left: <span class="hljs-string">'0 !important'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='modularscale'>
      modularScale
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Establish consistent measurements and spacial relationships throughout your projects by incrementing an em or rem value up or down a defined scale. We provide a list of commonly used scales as pre-defined variables.</p>


  <div class='pre p1 fill-light mt0'>modularScale(steps: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, base: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), ratio: <a href="#modularscaleratio">ModularScaleRatio</a>): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>steps</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>base</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)
            = <code>&#39;1em&#39;</code>)</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>ratio</span> <code class='quiet'>(<a href="#modularscaleratio">ModularScaleRatio</a>
            = <code>1.333</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
const styles = {
   <span class="hljs-comment">// Increment two steps up the default scale</span>
  <span class="hljs-string">'fontSize'</span>: modularScale(<span class="hljs-number">2</span>)
}

<span class="hljs-comment">// styled-components usage</span>
const <span class="hljs-built-in">div</span> = styled.<span class="hljs-built-in">div</span>`
   <span class="hljs-comment">// Increment two steps up the default scale</span>
  fontSize: <span class="hljs-symbol">$</span>{modularScale(<span class="hljs-number">2</span>)}
`

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-string">'fontSize'</span>: <span class="hljs-string">'1.77689em'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='rem'>
      rem
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Convert pixel value to rems. The default base value is 16px, but can be changed by passing a
second argument to the function.</p>


  <div class='pre p1 fill-light mt0'>rem(pxval: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), base: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</div>
  
    <p>
      Type:
      function (value: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), base: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>pxval</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>base</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)
            = <code>&#39;16px&#39;</code>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-string">'height'</span>: rem(<span class="hljs-string">'16px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  height: <span class="hljs-subst">${rem(<span class="hljs-string">'16px'</span>)}</span>
`</span>

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-string">'height'</span>: <span class="hljs-string">'1rem'</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='remtopx'>
      remToPx
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Convert rem values to px. By default, the base value is pulled from the font-size property on the root element (if it is set in % or px). It defaults to 16px if not found on the root. You can also override the base value by providing your own base in % or px.</p>


  <div class='pre p1 fill-light mt0'>remToPx(value: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), base: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)?): <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>value</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>base</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)?)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-string">'height'</span>: remToPx(<span class="hljs-string">'1.6rem'</span>)
  <span class="hljs-string">'height'</span>: remToPx(<span class="hljs-string">'1.6rem'</span>, <span class="hljs-string">'10px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  height: <span class="hljs-subst">${remToPx(<span class="hljs-string">'1.6rem'</span>)}</span>
  height: <span class="hljs-subst">${remToPx(<span class="hljs-string">'1.6rem'</span>, <span class="hljs-string">'10px'</span>)}</span>
`</span>

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-string">'height'</span>: <span class="hljs-string">'25.6px'</span>,
  <span class="hljs-string">'height'</span>: <span class="hljs-string">'16px'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='stripunit'>
      stripUnit
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>Returns a given CSS value minus its unit of measure.</p>


  <div class='pre p1 fill-light mt0'>stripUnit(value: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)): (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>value</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>))</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-string">'--dimension'</span>: stripUnit(<span class="hljs-string">'100px'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
<span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  --dimension: <span class="hljs-subst">${stripUnit(<span class="hljs-string">'100px'</span>)}</span>;
`</span>

<span class="hljs-comment">// CSS in JS Output</span>

element {
  <span class="hljs-string">'--dimension'</span>: <span class="hljs-number">100</span>
}</pre>
    
  

  

  

  
</section>

        
      
        
          <div class='keyline-top-not py2'><section class='section py2 clearfix'>

  <h2 class="section__heading" id='easings' class='mt0'>
    Easings
  </h2>

  
    

  
</section>
</div>
        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='easein'>
      easeIn
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>String to represent common easing functions as demonstrated here: (github.com/jaukia/easie).</p>


  <div class='pre p1 fill-light mt0'>easeIn(functionName: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="#timingfunction">TimingFunction</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>functionName</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#timingfunction">TimingFunction</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-string">'transitionTimingFunction'</span>: easeIn(<span class="hljs-string">'quad'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
 <span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  transitionTimingFunction: <span class="hljs-subst">${easeIn(<span class="hljs-string">'quad'</span>)}</span>;
`</span>

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-string">'div'</span>: {
  <span class="hljs-string">'transitionTimingFunction'</span>: <span class="hljs-string">'cubic-bezier(0.550,  0.085, 0.680, 0.530)'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='easeinout'>
      easeInOut
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>String to represent common easing functions as demonstrated here: (github.com/jaukia/easie).</p>


  <div class='pre p1 fill-light mt0'>easeInOut(functionName: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="#timingfunction">TimingFunction</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>functionName</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#timingfunction">TimingFunction</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-string">'transitionTimingFunction'</span>: easeInOut(<span class="hljs-string">'quad'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
 <span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  transitionTimingFunction: <span class="hljs-subst">${easeInOut(<span class="hljs-string">'quad'</span>)}</span>;
`</span>

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-string">'div'</span>: {
  <span class="hljs-string">'transitionTimingFunction'</span>: <span class="hljs-string">'cubic-bezier(0.455,  0.030, 0.515, 0.955)'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='easeout'>
      easeOut
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  <p>String to represent common easing functions as demonstrated here: (github.com/jaukia/easie).</p>


  <div class='pre p1 fill-light mt0'>easeOut(functionName: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>): <a href="#timingfunction">TimingFunction</a></div>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
        <div class='space-bottom0'>
          <div>
            <span class='code bold'>functionName</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
	    
          </div>
          
        </div>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
      <div class='py1 quiet mt1 prose-big'>Returns</div>
      <code><a href="#timingfunction">TimingFunction</a></code>
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
      
      <pre class='p1 overflow-auto round fill-light'><span class="hljs-comment">// Styles as object usage</span>
<span class="hljs-keyword">const</span> styles = {
  <span class="hljs-string">'transitionTimingFunction'</span>: easeOut(<span class="hljs-string">'quad'</span>)
}

<span class="hljs-comment">// styled-components usage</span>
 <span class="hljs-keyword">const</span> div = styled.div<span class="hljs-string">`
  transitionTimingFunction: <span class="hljs-subst">${easeOut(<span class="hljs-string">'quad'</span>)}</span>;
`</span>

<span class="hljs-comment">// CSS as JS Output</span>

<span class="hljs-string">'div'</span>: {
  <span class="hljs-string">'transitionTimingFunction'</span>: <span class="hljs-string">'cubic-bezier(0.250,  0.460, 0.450, 0.940)'</span>,
}</pre>
    
  

  

  

  
</section>

        
      
        
          <div class='keyline-top-not py2'><section class='section py2 clearfix'>

  <h2 class="section__heading" id='types' class='mt0'>
    Types
  </h2>

  
    

  
</section>
</div>
        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='fluidrangeconfiguration'>
      FluidRangeConfiguration
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>FluidRangeConfiguration</div>
  
    <p>
      Type:
      {prop: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, fromSize: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>), toSize: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)}
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>prop</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fromSize</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>toSize</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
          
          
        </div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='fontfaceconfiguration'>
      FontFaceConfiguration
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>FontFaceConfiguration</div>
  
    <p>
      Type:
      {fontFamily: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, fontFilePath: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, fontStretch: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, fontStyle: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, fontVariant: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, fontWeight: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, fileFormats: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>?, formatHint: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?, localFonts: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>> | null)?, unicodeRange: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, fontDisplay: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, fontVariationSettings: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, fontFeatureSettings: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?}
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fontFamily</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fontFilePath</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fontStretch</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fontStyle</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fontVariant</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fontWeight</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fileFormats</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>formatHint</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>localFonts</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>? | null))</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>unicodeRange</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fontDisplay</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fontVariationSettings</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fontFeatureSettings</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='hslcolor'>
      HslColor
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>HslColor</div>
  
    <p>
      Type:
      {hue: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, saturation: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, lightness: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>}
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>hue</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>saturation</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>lightness</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='hslacolor'>
      HslaColor
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>HslaColor</div>
  
    <p>
      Type:
      {hue: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, saturation: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, lightness: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, alpha: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>}
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>hue</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>saturation</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>lightness</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>alpha</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='interactionstate'>
      InteractionState
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>InteractionState</div>
  
    <p>
      Type:
      (any | null | <code>"active"</code> | <code>"focus"</code> | <code>"hover"</code>)
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='modularscaleratio'>
      ModularScaleRatio
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>ModularScaleRatio</div>
  
    <p>
      Type:
      (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <code>"minorSecond"</code> | <code>"majorSecond"</code> | <code>"minorThird"</code> | <code>"majorThird"</code> | <code>"perfectFourth"</code> | <code>"augFourth"</code> | <code>"perfectFifth"</code> | <code>"minorSixth"</code> | <code>"goldenSection"</code> | <code>"majorSixth"</code> | <code>"minorSeventh"</code> | <code>"majorSeventh"</code> | <code>"octave"</code> | <code>"majorTenth"</code> | <code>"majorEleventh"</code> | <code>"majorTwelfth"</code> | <code>"doubleOctave"</code>)
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='radialgradientconfiguration'>
      RadialGradientConfiguration
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>RadialGradientConfiguration</div>
  
    <p>
      Type:
      {colorStops: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>, extent: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, fallback: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, position: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, shape: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?}
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>colorStops</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>extent</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fallback</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>position</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>shape</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='rgbacolor'>
      RgbaColor
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>RgbaColor</div>
  
    <p>
      Type:
      {red: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, green: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, blue: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, alpha: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>}
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>red</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>green</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>blue</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>alpha</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='rgbcolor'>
      RgbColor
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>RgbColor</div>
  
    <p>
      Type:
      {red: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, green: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>, blue: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>}
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>red</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>green</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>blue</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='sidekeyword'>
      SideKeyword
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>SideKeyword</div>
  
    <p>
      Type:
      (<code>"top"</code> | <code>"topRight"</code> | <code>"right"</code> | <code>"bottomRight"</code> | <code>"bottom"</code> | <code>"bottomLeft"</code> | <code>"left"</code> | <code>"topLeft"</code>)
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='styles'>
      Styles
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>Styles</div>
  
    <p>
      Type:
      {}
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>ruleOrSelector</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="#styles">Styles</a>)?)</code>
          
          
        </div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='triangleconfiguration'>
      TriangleConfiguration
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>TriangleConfiguration</div>
  
    <p>
      Type:
      {backgroundColor: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, foregroundColor: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>, height: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), width: (<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>), pointingDirection: <a href="#sidekeyword">SideKeyword</a>}
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>backgroundColor</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>foregroundColor</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>height</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>height</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>height</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>width</span> <code class='quiet'>((<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number">number</a> | <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>))</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>pointingDirection</span> <code class='quiet'>(<a href="#sidekeyword">SideKeyword</a>)</code>
          
          
        </div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='contrastscores'>
      ContrastScores
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>ContrastScores</div>
  
    <p>
      Type:
      {AA: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>, AALarge: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>, AAA: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>, AAALarge: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>}
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>AA</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>AALarge</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>AAA</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>AAALarge</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean">boolean</a>)</code>
          
          
        </div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='lineargradientconfiguration'>
      LinearGradientConfiguration
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>LinearGradientConfiguration</div>
  
    <p>
      Type:
      {colorStops: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>, toDirection: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?, fallback: <a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?}
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
        <div class='space-bottom0'>
          <span class='code bold'>colorStops</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array">Array</a>&#x3C;<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>>)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>toDirection</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
        <div class='space-bottom0'>
          <span class='code bold'>fallback</span> <code class='quiet'>(<a href="https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String">string</a>?)</code>
          
          
        </div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
        
          <section class='p2 mb2 clearfix bg-white minishadow'>

  
  <div class='clearfix'>
    
    <h3 class='fl m0' id='timingfunction'>
      TimingFunction
    </h3>
    
    
      <a class='fr fill-darken0 round round pad1x quiet h5' href='[object Object]'>
      <span></span>
      </a>
    
  </div>
  

  

  <div class='pre p1 fill-light mt0'>TimingFunction</div>
  
    <p>
      Type:
      (<code>"easeInBack"</code> | <code>"easeInCirc"</code> | <code>"easeInCubic"</code> | <code>"easeInExpo"</code> | <code>"easeInQuad"</code> | <code>"easeInQuart"</code> | <code>"easeInQuint"</code> | <code>"easeInSine"</code> | <code>"easeOutBack"</code> | <code>"easeOutCubic"</code> | <code>"easeOutCirc"</code> | <code>"easeOutExpo"</code> | <code>"easeOutQuad"</code> | <code>"easeOutQuart"</code> | <code>"easeOutQuint"</code> | <code>"easeOutSine"</code> | <code>"easeInOutBack"</code> | <code>"easeInOutCirc"</code> | <code>"easeInOutCubic"</code> | <code>"easeInOutExpo"</code> | <code>"easeInOutQuad"</code> | <code>"easeInOutQuart"</code> | <code>"easeInOutQuint"</code> | <code>"easeInOutSine"</code>)
    </p>
  
  
    <p>
      Extends
      
        
      
    </p>
  

  
  
  
  
  

  
    <div class='py1 quiet mt1 prose-big'>Parameters</div>
    <div class='prose'>
      
    </div>
  

  
    <div class='py1 quiet mt1 prose-big'>Properties</div>
    <div>
      
    </div>
  

  
    
  

  
    <div class='py1 quiet mt1 prose-big'>Throws</div>
    <ul>
      
    </ul>
  

  
    <div class='py1 quiet mt1 prose-big'>Example</div>
    
  

  

  

  
</section>

        
      
    </div>
  </div>
</div>

  
  
    <script src='/assets/anchor.js'></script>
    <script src='/assets/docs.js'></script>
  
  <script defer src="/assets/polished.js"></script>
  <script defer src="/assets/script.js"></script>
  <img src="https://static.scarf.sh/a.png?x-pxid=8ab52548-9fc9-4161-9e8f-d122cdb6c880" />
</body>
</html>
